<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="50 36 514 624"><!--oldViewBox="0 0 616 752"--><style>.B{fill:#231a1a}.C{fill:#121212}.D{fill:#1b1617}.E{fill:#6d1c1d}.F{fill:#991a1c}.G{fill:#971e22}.H{fill:#2f1b1c}.I{fill:#1f1819}.J{fill:#2d2522}.K{fill:#341c1d}.L{fill:#a91b21}.M{fill:#b81418}.N{fill:#9c2023}.O{fill:#241e1e}.P{fill:#2a1b1b}.Q{fill:#891d1e}.R{fill:#421d1e}.S{fill:#b2161b}.T{fill:#bd1116}.U{fill:#af191d}.V{fill:#861f22}.W{fill:#b51515}.X{fill:#591b1d}.Y{fill:#3c1e1e}.Z{fill:#45191b}.a{fill:#501c1d}.b{fill:#1b1a1b}.c{fill:#171515}.d{fill:#a11d21}.e{fill:#68181c}.f{fill:#5c1a1b}.g{fill:#4f1a1c}.h{fill:#c90a0e}.i{fill:#2d2725}.j{fill:#6d1d1e}.k{fill:#1b1819}.l{fill:#672424}.m{fill:#121112}.n{fill:#391a1d}.o{fill:#b6151b}.p{fill:#49181a}.q{fill:#c30f14}.r{fill:#cf0608}.s{fill:#252420}.t{fill:#2f1d1e}.u{fill:#56191c}.v{fill:#602726}.w{fill:#403a32}.x{fill:#655c42}.y{fill:#4f4737}.z{fill:#887d47}.AA{fill:#d20607}.AB{fill:#2b2723}.AC{fill:#9a8e4a}.AD{fill:#635941}.AE{fill:#4a4534}.AF{fill:#3e3a2d}.AG{fill:#555039}.AH{fill:#aba04b}.AI{fill:#5d5742}.AJ{fill:#847849}.AK{fill:#736844}.AL{fill:#9d9046}.AM{fill:#a19442}.AN{fill:#e0d247}.AO{fill:#7d714a}.AP{fill:#968b4c}.AQ{fill:#cabc4e}.AR{fill:#7a6e41}.AS{fill:#f4ea29}.AT{fill:#bdb24e}.AU{fill:#b6a848}.AV{fill:#f5eb48}.AW{fill:#746c4c}.AX{fill:#d50402}.AY{fill:#ded04c}.AZ{fill:#faf04f}.Aa{fill:#604b3d}</style><path d="M525 623l7-1s1 1 0 1l-5 2c1 0 1-1 2-1h0l-4-1z" class="r"></path><path d="M532 622l10-3c-2 2-5 4-8 5h0v-1h0-1-1c1 0 0-1 0-1z" class="W"></path><path d="M403 632c-3 0-5 1-7 2l-9 2-25 5c-8 2-17 2-25 3-13 1-26 1-38 1-20-1-40-3-60-7-47-10-92-33-125-69-10-11-19-24-28-37-14-22-24-49-28-76-4-17-3-35-3-53v-42-57c0-21-1-41 4-62 10-46 40-87 77-117 8-6 17-12 26-17 15-10 32-18 50-24 10-3 20-6 31-8 14-3 28-5 42-6 29-2 59 1 88 7 60 14 118 51 151 104 11 18 21 38 25 60 3 19 3 39 3 58v54 63 35c-1 9-2 18-4 26-8 37-29 71-55 97-9 9-18 17-29 24 5 6 9 13 16 18 12 10 30 10 45 7l4 1h0c-1 0-1 1-2 1l5-2h1 1 0v1h0l-10 7c-4 4-8 7-13 10-7 3-15 5-22 7-10 3-20 6-29 7-4 1-8 1-11 0-7-1-13-4-19-7-9-5-18-12-27-16z" class="AX"></path><path d="M306 487l2 1h0v2c-1-1-2-2-2-3z" class="r"></path><path d="M180 236h1v4l-2-2 1-2z" class="m"></path><path d="M429 474c1 1 1 2 1 4v2c-1-1-1-2-1-3v-3z" class="I"></path><path d="M302 446h1c2 0 3 0 4-1l-1 3c-1-2-2-2-4-2z" class="T"></path><path d="M436 551h0l-1 1 1 1h0 2c0 1-1 2-1 2-1-1-2-1-2-2s1-2 1-2z" class="G"></path><path d="M274 455c1 0 2-1 2-1 1 0 1 0 2 1l-2 1-1-1c-1 0-1 1-1 0h0z" class="AA"></path><path d="M459 588l2 1v1 1h0c0 1 0 1-1 1v-1l-2-2v-1h1z" class="T"></path><path d="M308 485l1 1c1 0 1 1 1 1-1 1-1 1-2 1h0l-2-1h-1c2 0 2-1 3-2z" class="h"></path><path d="M429 477c0 1 0 2 1 3l-1 4-1-1 1-6z" class="H"></path><path d="M181 230v1l1 1-1 4h-1c0-2 0-4 1-6z" class="I"></path><path d="M432 226l1 6-2 2-1-7h1 0l1-1zm-362 2l1 1v1 1c0 2-1 4-1 5-1 0-1-1-2-1l2-7zm120 110c-1-1-1-2 0-3h2 0v3 1c-1 0-2-1-2-1z" class="c"></path><path d="M433 232v6c-1 1-1 3-1 5l-1-9 2-2z" class="m"></path><path d="M429 466c2 2 1 6 1 9v3c0-2 0-3-1-4v-8z" class="H"></path><path d="M352 543c1-1 2-1 3 0l6 1v2h-1l-1-1h-1l-6-2z" class="k"></path><path d="M436 553l2-1 1 2h0l1 2-1 1h-1-1 0v-2s1-1 1-2h-2z" class="N"></path><path d="M353 439h0c2 0 4-1 6 0-1 1 0 1-1 1-1 1-2 2-4 2v-1h0v-1l-1-1z" class="B"></path><path d="M310 599c1 1 1 1 1 2l3 2-1 1-6-1v-1h1c1-1 2-1 2-3z" class="O"></path><path d="M310 599c1 1 1 1 1 2s-1 1-2 1h-1c1-1 2-1 2-3z" class="i"></path><path d="M300 600h3l2 1v2h2-1c-1 1-4 0-5 0-1-1-1-2-1-3z" class="H"></path><path d="M349 545c2 0 4 2 6 4h0l-1 1h1c0 2 1 3 1 4l-7-9zm88 33l2 3 1 2h-3c0-1 0-1-1-2h-2l-1-1 3-1 1-1z" class="P"></path><path d="M439 581l1 2h-3c0-1 0-1-1-2h3z" class="D"></path><path d="M461 639c1 0 2-1 3-2l1 2h-1c-1 1-3 1-4 1v1h-5 0v-1c-1 0-1-1-1-1 0-1 1-1 2-1 2 0 4 1 5 1z" class="h"></path><path d="M309 598h4c1 0 0-1 1-1s1 1 2 1h0v2h-1v1c-1 0-1 1-1 2h1v1h-2l1-1-3-2c0-1 0-1-1-2l-1-1z" class="N"></path><path d="M435 575l1 1c1 1 1 1 1 2l-1 1-3 1h-1c0-1-1-1-2-1l1-1h0l-1-2c1-1 3-1 5-1z" class="F"></path><path d="M436 576c1 1 1 1 1 2l-1 1c-1-1-2 0-3-2l3-1z" class="S"></path><path d="M435 575l1 1-3 1-2 1-1-2c1-1 3-1 5-1z" class="i"></path><path d="M421 621h3l-3 2c-1 0-2 0-2 1 1 1 5 4 6 5l-13-8h2c1 1 2 1 4 0h3z" class="c"></path><path d="M421 621h3l-3 2h-2l-1-2h3z" class="C"></path><path d="M430 454c1-1-1-6 0-8 0 1 1 2 1 4h0v17c0 3 0 5-1 8 0-3 1-7-1-9v-4l1-8z" class="n"></path><path d="M470 638v-1c0-1 0-1 1-2 2-1 4-1 6 0s4 0 6 0v1c-4 1-9 1-13 2z" class="J"></path><path d="M310 487c1 1 1 1 2 1h0c1-1 1-1 2-1h0 2v1l-2 1h2v1c-1 0-3 1-4 1l-2 1h-1c-1-1-1-1-1-2v-2c1 0 1 0 2-1z" class="o"></path><path d="M309 492c1-2 4-3 5-3h2v1c-1 0-3 1-4 1l-2 1h-1z" class="C"></path><path d="M365 127h1c2 2 4 3 5 4 2 1 4 3 5 4v2l-12-9h1v-1z" class="k"></path><path d="M466 584l1 1c1-1 2-1 2-1 1-1 1-1 3-1h0c-1 0-1 1-2 2-1 0-2 1-2 2-2 1-5 3-7 4h0v-1-1l-2-1 2-1 5-3z" class="o"></path><path d="M461 587h2c1 0 1 0 0 1 0 1-1 1-2 2v-1l-2-1 2-1z" class="M"></path><path d="M145 585h1s1 1 2 1l5 5c2 2 4 3 7 5l1 1c2 0 3 2 4 2 1 1 2 2 3 2l-1 1-1-1c-4-2-7-5-10-6-2-2-4-4-6-5-2-2-4-3-6-5h1z" class="W"></path><path d="M79 500c1 1 1 2 1 3 1 2 3 5 4 8l1 1 1-1 1 2 4 7h0c-1 0-2-1-3-2l-1-1h0-1c-3-5-6-11-7-16v-1z" class="M"></path><path d="M86 511l1 2h-1c-1 0-2-1-2-2l1 1 1-1z" class="T"></path><path d="M439 554c1-1 2-1 3 0 0 0 1 0 1 1l1 1v3l-3 2v1l1 1v3l-5-9h1 1l1-1-1-2z" class="Y"></path><path d="M439 554c1-1 2-1 3 0 0 0 1 0 1 1l-1 1h-2 0l-1-2z" class="p"></path><path d="M444 638c2-1 5 0 7-1h0c1 0 1 0 2-1v1c1 0 2 1 3 1-1 0-2 0-2 1 0 0 0 1 1 1v1s-1 0-2-1h0c-1 0-1 0-2 1h0c-2 0-4 0-6-1-1 0-3 0-4-2-1 0 0 0 0-1 0 1 1 1 2 2h2v-1h-1 0z" class="o"></path><path d="M428 483l1 1c-1 11-3 23-8 32 0-3 2-8 3-11 2-7 3-15 4-22z" class="B"></path><path d="M179 238l2 2-2 26h-1v-5c-1 1 0 3 0 4s-1 1-1 1c0-6 1-12 1-18 1-3 1-7 1-10z" class="I"></path><path d="M430 414v-1c0-1 0-3 1-4v41h0c0-2-1-3-1-4-1 2 1 7 0 8v-40z" class="E"></path><path d="M430 387v3c0-2 1-4 0-7v-7-15l1-1v17 24 6 2c-1 1-1 3-1 4v1-27z" class="p"></path><path d="M433 487l2-43v2h0c1 0 0 0 1 1v23c-1 6-1 12-2 18v-3h0c0 1 0 1-1 2z" class="H"></path><path d="M392 612l3 3h1v1l6 2h0c0 1 0 1-1 1h-1c-5 0-9-1-14-3l1-1v-1l1-1h1l1-1h2z" class="X"></path><path d="M392 612l3 3-1 1c-1-1-2-1-3-2l-2-1 1-1h2z" class="O"></path><path d="M387 615c1 0 1 0 2-1l2 2c1 1 3 1 4 1v1h4c0 1 1 1 1 1-5 0-9-1-14-3l1-1z" class="D"></path><path d="M184 608l29 13c-1 1-2 1-4 0h0c-1 0-3-1-4-1l-3-1c-1-1-1-1-2-1l-1-1h-1c-1 0-1-1-2-1 0 0-1 0-1-1l-3-1h-1l-1-1c-1 0-2-1-3-1-1-1-2-1-3-1v-1c-1 0-2-1-2-1 0-1 1 0 2 0v-1z" class="AA"></path><path d="M430 266c1 8 1 16 1 24v27c-1 0-1-1-1-1v-12c0-1 1-3 0-4v14 5c0-1 0-2-1-3v-39c0 1 1 1 1 2h0v-12-1z" class="n"></path><path d="M112 554c3 3 6 5 9 8l13 13c3 3 7 6 11 10h-1l-13-10c-7-7-14-14-20-21h1z" class="D"></path><path d="M356 419h1v-2h1v1l1-1c1 1 1 2 1 4 1 0 1 1 1 1l1 9-6 1v-11l1-1-1-1z" class="C"></path><path d="M378 619h0c2 1 3 2 4 4 5 4 12 4 18 4 3 0 5 0 7 1 3 1 6 3 9 4l18 10c5 3 10 5 16 6l4 1h-1c-1 0-2 1-3 0h-2l-1-1h-1-1l-2-1h0c-1 0-1 0-2-1h-1l-3-1v-1l-1 1h1-1c-1 0-2 0-3-1s-2-1-3-1h0v-1h1 2l-3-1h0c-1-1-2-1-2-1-1-1-2-1-3-2l-6-3-12-6s-1 0-1-1h-1-4-1c-1-1-5 0-6 0 0-1-4-1-5-1 0-1-1 0-1 0-1 0-1-1-2-1h0c-2-1-6-3-7-5l-1-2h0z" class="h"></path><path d="M384 606h5 1c0 1 1 2 2 2-1 1-1 1-1 3 1 0 1 1 1 1h-2l-1 1h-1l-1 1v1l-1 1c-1 0-2-1-3-2v-1c-2-2-3-4-4-7 2 1 4 1 5 0z" class="E"></path><path d="M383 614c2-1 3-1 4-2l1 1-1 1v1l-1 1c-1 0-2-1-3-2z" class="B"></path><path d="M390 606c0 1 1 2 2 2-1 1-1 1-1 3 1 0 1 1 1 1h-2l-1 1h-1l-1-1 2-2v-1-1-1l1-1z" class="H"></path><path d="M387 612l2-2 1 2-1 1h-1l-1-1z" class="I"></path><path d="M384 606h5 1l-1 1v1l-4 2h-3v1h1v2c-2-2-3-4-4-7 2 1 4 1 5 0z" class="n"></path><path d="M384 606h5 1l-1 1v1l-4 2c-2 0-3 0-4-1 1-1 4-1 5-2h0l-2-1z" class="I"></path><path d="M433 547h2c1 1 2 1 3 1h2 2l1-1c1 0 2 1 2 1l1 1-1 1c2 0 3-1 4 0h0c-1 1-3 4-4 5l1 1h-1-1l-1-1c0-1-1-1-1-1-1-1-2-1-3 0h0l-1-2-2 1h0l-1-1 1-1h0l-1-1c-2 0-2-2-2-3z" class="R"></path><path d="M440 548h2l1 1h-2l-1-1z" class="H"></path><path d="M443 547c1 0 2 1 2 1l1 1-1 1h0c0-1-1-1-2-1l-1-1 1-1z" class="Z"></path><path d="M438 552c2 0 4-1 6-1 1-1 3-1 5-1-1 1-3 4-4 5l1 1h-1-1l-1-1c0-1-1-1-1-1-1-1-2-1-3 0h0l-1-2z" class="V"></path><path d="M392 608c1 0 2 1 3 2h0l1-1h1v1l1 1h0c1-1 2-2 3-4l2 2c-1 1-2 2-2 3s0 2 1 2v1c2 1 3 2 5 2l1 2h0v1l-6-2-6-2v-1h-1l-3-3s0-1-1-1c0-2 0-2 1-3z" class="U"></path><path d="M396 615c2 0 3 0 4 1l8 3h0v1l-6-2-6-2v-1h0z" class="b"></path><path d="M392 608c1 0 2 1 3 2h0l1-1h1v1l1 1h0c0 2 1 3 2 5-1-1-2-1-4-1h0-1l-3-3s0-1-1-1c0-2 0-2 1-3z" class="d"></path><path d="M396 612v2h-1v-2l1-1v1z" class="S"></path><path d="M397 610l1 1h0c0 2 1 3 2 5-1-1-2-1-4-1 1-1 1-2 0-2v-1-1l1-1z" class="I"></path><path d="M397 610l1 1h0l-1 1-1 1v-1-1l1-1z" class="s"></path><path d="M429 316c1 1 1 2 1 3v-5-14c1 1 0 3 0 4v12s0 1 1 1v43l-1 1v15 7c1 3 0 5 0 7v-3c-1-2-1-4-1-6v-14-51z" class="Z"></path><path d="M477 573v1c-1 1-2 2-4 3 0 0-1 0-1 1l1-1 1 1h-1l1 1c1 0 2-1 2-2h1c1 0 1 0 2-1l3-2h1c-1 1-2 2-4 3-1 1-1 2-3 2 0 1 0 1-1 2h-1c0 1-1 1-2 2h0 0c-2 0-2 0-3 1 0 0-1 0-2 1l-1-1-5 3-2 1h-1l-2-2h2c0-1 1 0 1 0l3-3 15-10z" class="T"></path><path d="M459 586h1c3-2 6-3 9-5h0l-3 3-5 3-2 1h-1l-2-2h2c0-1 1 0 1 0z" class="D"></path><path d="M316 490h0 3v1h4 5l-1 1h0c0 1 1 1 1 1v1h-1-1 0c-1 1-2 2-2 4h-1c0-1 0-2 1-3h0-1c-1 2-2 4-4 6 0-2 2-5 2-7-1 1-3 4-4 5l-1-1 1-1h-1c-1 0-1 1-2 1-1-1-2-2-2-3l2-2v-1c-1-1-1-1-2-1 1 0 3-1 4-1z" class="C"></path><path d="M316 490h0 3v1c-2 1-3 1-5 2v-1c-1-1-1-1-2-1 1 0 3-1 4-1z" class="W"></path><path d="M420 554h3l3 7c1 2 3 4 4 6l5 8c-2 0-4 0-5 1h0c-1 0-1 0-1-1v-3l-2-3c-1-3-4-5-6-8v-1h0l-1-1 1-1 1 1 1-2c-1-1-2-1-3-3h0z" class="J"></path><path d="M422 559l4 3c-1 0-3-1-5-2h0l-1-1 1-1 1 1z" class="q"></path><path d="M380 628c-1 0-2 1-2 2h-4-1-1l-1 1h-1 0-1c-1 0-1 0-2 1h-1-2-2-1v1h-3-2-2c2 1 3 0 5 0h0c1 0 2 0 3-1 1 0 4 1 5 0 1 0 2 0 2-1h2 0c2-1 3 0 4 0s1-1 2-1h0 2 0 1c1 0 2-1 3-1s2-1 2-1h2c1-1 2-1 3-1l1 1c-1 0-2 0-4 1h5 1v1h1 0 2 1-2c-1-1-1-1-2-1h0c1-1 2-1 3-1 0 1 1 2 2 2v1h-3-1c-2-2-6-2-9-1h0c-3 1-5 2-8 2l-4 1v-1h-1-2c-2 1-4 1-6 1h-1c-2 1-3 1-5 1h-2c-1 0-2 1-2 1h-8v-1l34-6z" class="M"></path><path d="M299 587c1 0 1 0 1-1 3 4 6 6 10 7l6 3v2h0c-1 0-1-1-2-1s0 1-1 1h-4l1 1c0 2-1 2-2 3h-1v1h0-2v-2l-2-1h-3-1v-3-1-1h2l-1-4-1-4z" class="D"></path><path d="M307 603c-1-1 0-2 0-4h2v-1l1 1c0 2-1 2-2 3h-1v1h0z" class="B"></path><path d="M300 591c1 0 2 1 2 1 2 2 4 3 5 5l-1 1v2c-1 0-1 1-1 1l-2-1h-3-1v-3-1-1h2l-1-4z" class="H"></path><path d="M302 599l2 1h2c-1 0-1 1-1 1l-2-1-1-1z" class="J"></path><path d="M304 600v-1h0c0-1 0-1-1-2h0v-2h2v3h1 0v2h-2z" class="P"></path><path d="M300 591c1 0 2 1 2 1v4 2 1l1 1h-3-1v-3-1-1h2l-1-4z" class="i"></path><path d="M299 596c2 1 1 2 3 2v1l1 1h-3-1v-3-1z" class="I"></path><path d="M241 629l9 2 5 1s1 0 1 1h1 2l1-1c1 1 2 1 3 0h0 0l3-1h6 0c-1 1-2 1-3 1h0c1 0 1 1 2 1l15 2h0c1 1 2 1 3 1h2 0-2-1v1h-4c-2-1-3 0-5-1h-6-1 0-8-2l-5-1h-1l-6-2c-1 0-3 0-4-1-2 1-2 0-4 0-1 0-5-1-6-2-1 0-2 0-3-1h0 1 1c1 1 4 1 6 1v-1h0z" class="AA"></path><path d="M250 631l5 1s1 0 1 1h0-5c-1-1-2-1-3-1v-1h2z" class="AX"></path><path d="M266 631h6 0c-1 1-2 1-3 1h0c1 0 1 1 2 1l-8-1h0l3-1z" class="k"></path><path d="M459 611h1c1 1 5 4 5 6-1 1-1 4-1 6v1 2c-1 1-1 1-1 2v1h-1c-1-1-1-3-1-4l-1 1v2h-1c0-1-1-1-1-2l-1 1h0-1c-1-1-1-3-3-4v2h0c-2-1-1-3-3-5v-3l1-1 1 1h-1 1 0 1v1c-1 0-1 0-1 1h0c0 1 1 1 1 1v-2l3-3s1 0 2 1c-1-1-1-1-2-1-1 1-4 0-5 1-1 0-1 1-2 1h-2l2-1 1-1 7-1 1-1 1-1h0v-1z" class="o"></path><path d="M459 611h1c1 1 5 4 5 6-1 1-1 4-1 6v1 2c-1 1-1 1-1 2-1-2 0-6-1-8s-3-4-5-6l1-1 1-1h0v-1z" class="J"></path><path d="M457 614l1-1c0 1 0 1 1 1h3v1c0 1 1 2 1 2 0 1 0 2-1 3-1-2-3-4-5-6z" class="D"></path><path d="M457 626c-1-2-2-3-3-5v-3c1-1 1-1 3-2 3 2 2 5 3 8l1 1-1 1v2h-1c0-1-1-1-1-2l-1 1h0v-1z" class="P"></path><path d="M457 626l1-2-2-2 1-1c0 1 1 2 1 3h1s1 1 1 2v2h-1c0-1-1-1-1-2l-1 1h0v-1z" class="F"></path><defs><linearGradient id="A" x1="443.989" y1="632.154" x2="441.923" y2="624.572" xlink:href="#B"><stop offset="0" stop-color="#141515"></stop><stop offset="1" stop-color="#241819"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M433 631c-1-1-1-2-1-3h0 0c1-1 0-2 0-3v-1l1-1c1 1 2 1 2 2h2v-1c0-1 0-1 1-1h2c0-1 0-1 1-2h1 1 0c1-1 2-1 3-1h0c1 0 1 1 2 1h1c0-1 0-2 1-4v3c2 2 1 4 3 5h0v-2c2 1 2 3 3 4h-2 0 0c-1 1-1 0-1 1-5-1-9-1-12 2-2 1-4 3-4 5l-1 1h-1c-1-1-1-2-2-3v-2z"></path><path d="M433 631c-1-1-1-2-1-3h0 0c1-1 0-2 0-3v-1l1-1c1 1 2 1 2 2h2v-1c0-1 0-1 1-1h2c0-1 0-1 1-2h1 1 0c1-1 2-1 3-1h0c1 0 1 1 2 1h1c0-1 0-2 1-4v3 6c-1-1-2-3-3-4v-1l-2 2c0-1 0-1-1-1-1 1 0 2-1 3l-2-3v1c0 1 1 2 0 3-1 0-1-2-3-2h0v1c1 1 1 1 1 2h-1l-1-1-2 2c1 1 1 1 0 1v2l-2 2v-2z" class="T"></path><path d="M433 631v-3c0-1 0-2 1-3 1 0 1 2 1 3 1 1 1 1 0 1v2l-2 2v-2z" class="H"></path><path d="M436 528c0 1 0 1 1 2 2 2 4 3 6 4l2 1c1 1 2 1 3 2 0 0-1 0-1 1-2 0-3 0-4 2-1 1-1 3-1 4s1 2 1 3l-1 1h-2-2c-1 0-2 0-3-1h-2c-1-3-3-5-2-7 1-1 2-1 2-1v-3c-1-1-1-2-1-4h-1l2-2c1 0 2-1 3-2z" class="d"></path><path d="M431 540h1v1c1 3 4 4 6 7-1 0-2 0-3-1h-2c-1-3-3-5-2-7z" class="t"></path><path d="M437 530c2 2 4 3 6 4l2 1-1 1c-2 0-2 1-3 2-2 3-2 4-2 7h0c-3-2-4-4-5-6v-4l3-5z" class="D"></path><path d="M376 591h1 1 0c1 1 1 1 2 1h2c1 1 2 2 3 2s1 0 2 1h2c0 2-1 2-1 4 1 1 2 1 4 2s3 4 4 6v2l-1 1h0c-1-1-2-2-3-2s-2-1-2-2h-1-5c-1 1-3 1-5 0-1-5-3-10-3-15z" class="C"></path><path d="M390 603l2 2h-1c-1 0-2 0-4-1l1-1h2z" class="e"></path><path d="M384 602c2 1 4 1 6 1h-2l-1 1h-1-2v-2z" class="u"></path><path d="M386 604c-1 0-4 1-5 0l-1-1c0-1 0-2 1-3h1l2 2v2h2z" class="g"></path><path d="M347 122c7 3 13 6 19 11 25 16 44 43 54 71 6 17 9 33 10 51v11 1 12h0c0-1-1-1-1-2-1-12 0-25-2-37-1-16-6-32-12-47-8-18-20-34-34-47-10-10-22-18-34-23v-1zm86 365c1-1 1-1 1-2h0v3 6l-3 18c0 1-1 3-1 4s1 3 1 3l1 3c1 3 1 5 1 8l-2 2-2 2v1h-1l-4-11s-1-2-1-3 1-2 2-3c1-2 2-5 3-7 2-8 3-16 5-24z" class="m"></path><defs><linearGradient id="C" x1="127.12" y1="567.268" x2="131.547" y2="562.593" xlink:href="#B"><stop offset="0" stop-color="#cb0a0f"></stop><stop offset="1" stop-color="#a22126"></stop></linearGradient></defs><path fill="url(#C)" d="M108 548h2l1 1c2 2 4 5 6 7 4 4 8 7 11 10l2-1h1 0l3 3h1l-1-5 3 4c0 1 0 1 1 1 2 4 3 8 6 11 1 1 4 4 6 4 1 2 1 2 1 4l-6-3 1 1h-1c-4-4-8-7-11-10l-13-13c-3-3-6-5-9-8l-4-6z"></path><defs><linearGradient id="D" x1="132.998" y1="565.871" x2="132.259" y2="570.115" xlink:href="#B"><stop offset="0" stop-color="#33191c"></stop><stop offset="1" stop-color="#2b221f"></stop></linearGradient></defs><path fill="url(#D)" d="M130 565h1 0l3 3h1c1 1 2 4 2 5l-9-7 2-1z"></path><path d="M437 635c0-2 2-4 4-5 3-3 7-3 12-2 4 2 9 5 11 9-1 1-2 2-3 2s-3-1-5-1c-1 0-2-1-3-1v-1c-1 1-1 1-2 1h0c-2 1-5 0-7 1h0 1v1h-2c-1-1-2-1-2-2 0 1-1 1 0 1l-2-1v-2h0v-1-1s0 1-1 1l-1 1z" class="T"></path><path d="M444 638l-1-1c1-1 1-1 2-1 2-2 2-3 3-5 1 2 1 3 2 4h1l1-1v-2c1 0 1 0 2 1 1 2 1 3 2 4h3c1 1 2 1 2 2-1 0-3-1-5-1-1 0-2-1-3-1v-1c-1 1-1 1-2 1h0c-2 1-5 0-7 1z" class="l"></path><path d="M277 78c9-1 17-2 26-2 13-1 26 1 39 3 26 3 51 9 75 20 8 3 16 8 23 12 6 4 13 7 19 12l1 1c-3 0-6-3-9-5l-20-11-15-8c-6-3-13-5-19-8l-11-3c-15-4-30-7-45-10-12-1-25-2-37-2h-16c-3 0-7 1-10 1h-1z" class="e"></path><path d="M346 634v1h8s1-1 2-1h2c2 0 3 0 5-1h1c2 0 4 0 6-1h2 1v1l-4 1h-4c-2 1-5 1-7 1-7 1-14 2-20 2-2 1-4 0-6 1h-7-1-40-2c-1-1-2 0-3-1-2 0-3 1-5 0h-2v-1h0 1 6c2 1 3 0 5 1h4v-1h1 2 0-2c-1 0-2 0-3-1h0l26 1c3-1 5-1 8-1h9l17-1z" class="U"></path><path d="M286 635l26 1c3-1 5-1 8-1l12 1h-3c-1 0-1 1-2 1h-43 0 4v-1h1 2 0-2c-1 0-2 0-3-1h0z" class="T"></path><path d="M286 635l26 1h2-1 2c-3 1-6 1-8 1h-19v-1h1 2 0-2c-1 0-2 0-3-1h0z" class="h"></path><path d="M277 580c1 0 2 0 2 1l2-3c1 0 1 0 1 1s-1 2-2 3l-1 1v1h1c1 1 0 1 1 2 1 0 1 1 2 2v1c0 3 0 5-1 7h1c-1 2-4 6-6 7h0-1c-2-1-5-6-7-8l-1-1v-1h-1-1c-1 0-4 2-5 1-1 0-1 0-2-1h1c3 1 5-2 7-2s3-1 4-1l1-1c2-1 4-7 5-9z" class="J"></path><path d="M278 590h-1c1-1 1-2 1-2l-1-1v-1h0 1v2l1 1s0 1 1 1v1 1h-1c0-1-1-1-1-2z" class="B"></path><path d="M279 583v1h1c1 1 0 1 1 2-1 1-2 2-2 3l-1-1v-2c1-1 1-2 1-3z" class="O"></path><path d="M281 586c1 0 1 1 2 2v1h-1c-1 1-2 0-2 1-1 0-1-1-1-1 0-1 1-2 2-3z" class="R"></path><path d="M278 590c0 1 1 1 1 2h1v-1 2c0 1-1 1-1 2s0 2-1 4c-1-1-1-3-1-4l1-1h0c0-1 0-2-1-3h0l1-1z" class="AB"></path><path d="M273 590l1-1c1 1 2 4 2 5v1l-1 1-2-1h-2-2l-1-1h2v-2-1c1 0 2-1 3-1z" class="K"></path><path d="M270 592h2v1c-1 1-1 1-2 1v-2z" class="H"></path><path d="M273 590l1-1c1 1 2 4 2 5v1l-1 1-2-1v-2h0l2 2c0-1-2-4-2-5z" class="v"></path><path d="M280 590c0-1 1 0 2-1h1c0 3 0 5-1 7h1c-1 2-4 6-6 7h0v-2l1-2c1-2 1-3 1-4s1-1 1-2v-2-1z" class="l"></path><path d="M280 590c0-1 1 0 2-1v4h-1-1v-2-1z" class="a"></path><path d="M296 114h17c4 0 9 1 13 2h4l10 3c2 1 5 2 7 3v1c-26-11-57-11-83 0-34 13-58 40-72 73-3 5-5 11-6 16l-3 14c0 1-1 4-1 6l-1-1v-1-2c5-30 19-59 42-80 6-6 13-11 20-16 16-9 34-17 53-18z" class="B"></path><path d="M181 230h1v2l-1-1v-1z" class="C"></path><path d="M533 623h1 0v1h0l-10 7c-4 4-8 7-13 10-7 3-15 5-22 7-10 3-20 6-29 7-4 1-8 1-11 0-7-1-13-4-19-7-9-5-18-12-27-16h2 0l2 1c1 1 2 1 3 2 1 0 2 1 3 1 2 1 4 3 6 4l1 1c2 2 5 3 7 4l6 3 6 3h0l1 1h1l1 1h1c1 0 1 0 2 1h1 2 0c0 1 1 1 1 1h1 6 3c0-1 1 0 1 0 1-1 1-1 2-1h1c3 0 5-2 8-2h0 1 1s0-1 1-1h0 1 2v-1h1 0 2 1c0-1 1-1 1-1h2c1-1 1-1 2-1h1 0c1-1 2-1 3-1s1 0 1-1l1 1c1-1 1-1 2-1s1 0 1-1h1 1 0c1-1 3-1 4-1 0-1 2-1 2-1h1l2-1h0c1 0 2-1 3-1 0 0 1 0 1-1h1c1 0 2-1 2-2l3-2c1 0 1 0 1-1h1c-1 0-2 0-3 1h-1l-1 1h-1-1l-1 1h-1-1c-1 0-2 1-2 1-2 0-3 1-4 1h-1c-1 1-1 1-2 1h-1-2c-1 1-2 1-2 1h-1 0c-1 0-1 0-1 1h-2 0l-7 2h-3c-1 1-1 1-2 1h0-1-1c-1 1-1 0-2 1-1 0-2 0-3 1-1 0-2-1-2 0-2 0-3 0-4 1 0 0-1-1-1 0h-4c0 1-2 0-3 0s-2 1-4 0h1l-4-1 1-1c8 0 16-2 24-3 1 0 2 0 2 1 2 0 5-1 7-2l18-4c4-1 9-2 12-4 5-2 10-5 14-8 1-1 4-2 5-4z" class="M"></path><path d="M451 647c8 0 16-2 24-3 1 0 2 0 2 1-7 2-16 4-23 4l-4-1 1-1z" class="k"></path><path d="M402 505l2 3-3 3h0c-1 1-2 1-3 2h-2v1h2c1-1 1-1 2-1 1-1 2-1 3-2s1-1 2-1l1 2h0l3 5h-2c-1 1-2-1-2 1h-2-3c-1 1-2 1-3 3l-1 1v-2l1-1 1-1h-1 0c-2 2-5 2-7 1h0-1l-1 1h-2c-1 0-2 0-2-1-1 0-1-1-2-1l-1 1h0c-1-2-2-3-2-5l2 1h0 0v-2c2-1 3-2 4-3l3 3c1-1 2-1 3-1v-1c-3 0-4-1-6-3h0l1-1c2 2 5 3 8 3s6-2 8-5z" class="o"></path><path d="M379 514l2 1c2 2 5 3 8 4l-1 1h-2c-1 0-2 0-2-1-1 0-1-1-2-1l-1 1h0c-1-2-2-3-2-5z" class="J"></path><path d="M406 512h0l3 5h-2c-1 1-2-1-2 1h-2-3c-1 1-2 1-3 3l-1 1v-2l1-1 1-1h-1 0c-2 2-5 2-7 1h0c5 0 9-2 13-5 1-1 2-2 3-2h0z" class="B"></path><path d="M400 518c2-2 4-2 6-4h0v3h0c-2 0-2 0-3 1h-3z" class="Y"></path><path d="M406 512l3 5h-2c-1 1-2-1-2 1h-2c1-1 1-1 3-1h0v-3h0v-2z" class="H"></path><path d="M513 533h0c2-1 3-3 4-4l-1 1v1 2l-1 1c0 1-1 1-1 2l-1 1v1 1l-4 6-2 2c0 1-1 1-1 2l-1 1-2 2c0 1-1 1-1 2l-2 2c0 1-1 1-1 2l-3 3-4 4c0 1-1 2-2 2-1 2-3 3-4 4l-3 3h-1l-3 2c-1 1-1 1-2 1h-1c0 1-1 2-2 2l-1-1h1l-1-1-1 1c0-1 1-1 1-1 2-1 3-2 4-3v-1l1-2c1-1 3-2 4-3 0-2 1-4 2-6v2c1-1 1-1 1-2l1 1c1 0 2-2 3-3l1 1c3-3 6-6 8-9 6-6 10-13 15-19z" class="r"></path><path d="M489 560l1 1c-2 2-5 5-8 7 0-2 1-4 2-6v2c1-1 1-1 1-2l1 1c1 0 2-2 3-3zm-268 16c1-1 1-1 2-1 1 3 5 6 8 7 1 1 6 4 6 5 2 2 4 3 6 4 17 10 36 15 56 17 22 2 45-3 66-13v1c-1 1-5 2-6 3-13 5-25 9-39 10h-21v1h-3-4l-4-1h-3c-1 0-2 0-3-1-2 0-5-1-7-1v-2l-13-4c-1 0-1 1-2 1-5-1-11-4-16-7l-4-2c1 0 2 0 2-1-3-2-7-4-10-7l-11-9z" class="H"></path><path d="M295 609h4v1h-3-4 0v-1h3z" class="AA"></path><path d="M275 605l20 4h-3v1h0l-4-1h-3c-1 0-2 0-3-1-2 0-5-1-7-1v-2z" class="r"></path><path d="M285 609c1-1 2-1 4-1v1h0-1-3z" class="AA"></path><path d="M242 592l20 9c-1 0-1 1-2 1-5-1-11-4-16-7l-4-2c1 0 2 0 2-1z" class="r"></path><path d="M401 607l2-2c1 1 3 2 4 3h0 1c1 0 1 0 2 1 2 1 3 3 5 3h1l1-1c3 3 8 4 12 5h7 6c0 1 0 1-1 1 1 0 1 0 2 1l-19 3h-3-3c-2 1-3 1-4 0h-2c0 1-3-1-4-1v-1h0l-1-2c-2 0-3-1-5-2v-1c-1 0-1-1-1-2s1-2 2-3l-2-2z" class="c"></path><path d="M403 609c1 1 1 2 2 3-1 0-1 0-2 1h-1l-1-1v1c1 1 1 1 1 2v-1c-1 0-1-1-1-2s1-2 2-3z" class="H"></path><path d="M429 616h7 6c0 1 0 1-1 1-5 1-11 1-16 0 2 0 4 1 5 0v-1h-1z" class="F"></path><path d="M401 607l2-2c1 1 3 2 4 3h-1v1l-1 1 1 2h-1 0c-1-1-1-2-2-3l-2-2z" class="N"></path><path d="M407 617l14 3v1h-3c-2 1-3 1-4 0h-2c0 1-3-1-4-1v-1h0l-1-2z" class="h"></path><defs><linearGradient id="E" x1="422.147" y1="619.828" x2="414.483" y2="606.211" xlink:href="#B"><stop offset="0" stop-color="#d10707"></stop><stop offset="1" stop-color="#aa1a22"></stop></linearGradient></defs><path fill="url(#E)" d="M407 608h0 1c1 0 1 0 2 1 2 1 3 3 5 3h1l1-1c3 3 8 4 12 5h1v1c-1 1-3 0-5 0-5 0-11-1-14-4-1-1-2-3-3-4h-2v-1h1z"></path><path d="M475 644l26-6c4-1 9-2 13-4 3-1 6-3 8-5-1 0-3 0-5 1-6 1-14 1-20 0-8-1-16-5-23-10-9-7-15-18-22-27l-8-12c-6-8-12-17-16-25-7-16-10-33-18-47-3-6-8-11-11-16l-27-35c-4-5-7-11-8-17 0-2-1-4 0-6v4c1 4 2 7 4 11 3 6 8 11 11 16l20 26c5 7 10 13 14 21 7 14 10 31 17 45 4 9 10 16 15 24l14 19c4 6 9 12 14 17 4 4 9 7 14 8 12 4 29 5 40-1l5-2h1c-1 2-4 3-5 4-4 3-9 6-14 8-3 2-8 3-12 4l-18 4c-2 1-5 2-7 2 0-1-1-1-2-1z" class="C"></path><path d="M463 628c0-1 0-1 1-2v-2-1c0-2 0-5 1-6 7 6 16 13 25 15 3 0 7 0 10 1-3 1-7 1-11 2-2 0-4 1-6 1v-1c-2 0-4 1-6 0s-4-1-6 0c-1 1-1 1-1 2v1l-2 1h-3l-1-2c-2-4-7-7-11-9 0-1 0 0 1-1h0 0 2 1 0l1-1c0 1 1 1 1 2h1v-2l1-1c0 1 0 3 1 4h1v-1z" class="D"></path><path d="M477 632l1-1v1h-1c2 2 6 2 6 3-2 0-4 1-6 0v-1h2c-1 0-1 0-2-1v-1z" class="T"></path><path d="M468 639c-1-4-4-11-2-16h0l3 5c1 0 1-1 2-1 0 0 0 1-1 2h1c1 0 1 0 1-1h1c0 1 0 2 1 3h1c1 1 1 1 2 1h0v1c1 1 1 1 2 1h-2v1c-2-1-4-1-6 0-1 1-1 1-1 2v1l-2 1z" class="q"></path><defs><linearGradient id="F" x1="382.625" y1="592.314" x2="389.618" y2="585.809" xlink:href="#B"><stop offset="0" stop-color="#101314"></stop><stop offset="1" stop-color="#1f1616"></stop></linearGradient></defs><path fill="url(#F)" d="M373 584c0-2-1-4-1-6l-3-8c2 1 4 3 6 4 1 1 3 4 3 4v-4c1 2 2 4 3 5h1c1 1 1 1 1 2h1c2 1 2 1 3 3 3 2 5 5 8 7l6 6c0 1 0 4-1 4 0 1-1 2-1 2l-3 3-2-5c-1-1-2-2-4-2l-1-1c1-1 2-1 2-2-1 0-1-1-2-1h-2c-1-1-1-1-2-1s-2-1-3-2h-2c-1 0-1 0-2-1h0-1-1v-1c-1-1-2-2-2-4l-1-2z"></path><path d="M384 581c2 1 2 1 3 3-1 0-1 1-2 1h0v-1c0-1-1-2-1-3z" class="C"></path><path d="M397 598v-1c1 1 2 1 2 2 0 2-1 2-1 3s0 1-1 1c0-2 1-4 0-5z" class="j"></path><path d="M373 584c2 0 4 0 6 1l3 3c3 2 5 3 7 5 2 0 3 1 4 2s1 2 2 3h2c1 1 0 3 0 5 1 0 1 0 1-1l1 1-3 3-2-5c-1-1-2-2-4-2l-1-1c1-1 2-1 2-2-1 0-1-1-2-1h-2c-1-1-1-1-2-1s-2-1-3-2h-2c-1 0-1 0-2-1h0-1-1v-1c-1-1-2-2-2-4l-1-2z" class="D"></path><path d="M389 593c2 0 3 1 4 2s1 2 2 3c0 0 1 1 1 2h-1c-1-1-2-1-3-2 0 0 0-2-1-2-1-1-2-2-2-3h0z" class="Q"></path><path d="M373 584c2 0 4 0 6 1l3 3c3 2 5 3 7 5h0l-8-4c-1-1-2-2-3-2v1 1h-1c-1-1-2-2-2-3h-1 0l-1-2z" class="W"></path><path d="M331 491l1 1c1 0 2 0 3 1s3 1 4 2v1l8 8 7 7-1 1c-1-1-1-1-1-2h-1l-1-1c-1-1-2-1-3-1s-1-1-2-1h0c0 1 0 1 1 1 1 1 4 2 5 4h-1-2c0 1-1 1-1 2h-2c0-2 1-1 2-3h-3l-1-1c-4 0-8 1-11 5-1-1-3 0-4-1-1 0-3-2-3-4-1-1-1-2-1-3h-1v-1l1-1s1-1 2-1v-2h1c1 0 2 0 2-1h3-1v-1h-1l-1 1h-1 0-1l1-2h-1s-1 0-1 1l-1 1v-1c-1 0-1-1 0-2l1-4h1 1v-1s-1 0-1-1h0l1-1h3z" class="T"></path><path d="M331 491l1 1-1 1c-2 1-4 4-6 6h0v-1l1-4h1 1v-1s-1 0-1-1h0l1-1h3z" class="m"></path><path d="M339 496l8 8h-3s-1 0-1-1v-1c-1 0-2 0-2-1-1 0-1-1-1-1h0c-1 0-1 1-2 1 0 0 0-1-1-1h-1v-1c1 0 2-2 3-3z" class="S"></path><defs><linearGradient id="G" x1="336.339" y1="496.06" x2="336.608" y2="497.855" xlink:href="#B"><stop offset="0" stop-color="#1e1417"></stop><stop offset="1" stop-color="#1d1c19"></stop></linearGradient></defs><path fill="url(#G)" d="M332 492c1 0 2 0 3 1s3 1 4 2v1c-1 1-2 3-3 3v1l-1 1h-1c-1 0-1 0-2-1v-1c-1-1 0-2 1-4h0c-2 0-4 4-5 6 1-3 1-5 3-7v-1l1-1z"></path><path d="M334 497h0v-2h1v2c0 1 1 1 1 2v1l-1 1h-1c-1 0-1 0-2-1v-1c1 0 1-1 2-2z" class="U"></path><path d="M334 497v3 1c-1 0-1 0-2-1v-1c1 0 1-1 2-2z" class="S"></path><path d="M324 505h1 1 1c1-1 1-1 2-1h2l2 2h0l-1-3h2 0c1 1 1 1 2 1l1 1h1c1 1 2 3 3 3l2 2c-4 0-8 1-11 5-1-1-3 0-4-1-1 0-3-2-3-4-1-1-1-2-1-3h-1v-1l1-1z" class="h"></path><path d="M496 550h2v1h0v1c-2 3-5 6-8 9l-1-1c-1 1-2 3-3 3l-1-1c0 1 0 1-1 2v-2c-1 2-2 4-2 6-1 1-3 2-4 3l-1 2-15 10-3 3s-1-1-1 0h-2v-1l1-1v-1l-2-1h1 2c2 0 4-1 5-2 1 0 2-1 2-1 1-1 1-1 2-1 2-2 3-4 4-7l1-2 1-3h0l1-5c0-3-1-5-1-9h1l1-1 2 2h0 2v1h1c1-1 2-1 2-3 0 0-1 0-1-1h2 0 9 4z" class="K"></path><path d="M479 559h1v1c0 1-1 2-2 2v-1l1-2z" class="H"></path><path d="M457 584h2s1 0 1-1h1 1l-3 3s-1-1-1 0h-2v-1l1-1z" class="C"></path><path d="M471 571l1-2v3h0c1-1 2-1 2-1-2 3-3 6-6 7h-1c2-2 3-4 4-7z" class="G"></path><path d="M484 558l4-1-3 5c0 1 0 1-1 2v-2h-1l-2 1v-1l3-3v-1z" class="B"></path><path d="M481 563l2-1h1c-1 2-2 4-2 6-1 1-3 2-4 3h-1 0c-1 2-3 4-5 5 1-2 2-3 3-4 0-1 1-2 2-2 0-2 1-3 1-4l3-3z" class="L"></path><path d="M474 552l1-1 2 2h0 2v1 1h-1l1 4h0l-1 2h-1c0 3-1 7-3 10 0 0-1 0-2 1h0v-3l1-3h0l1-5c0-3-1-5-1-9h1z" class="U"></path><path d="M477 556l1-1 1 4h0l-1 2h-1v-5z" class="Y"></path><path d="M474 552l1-1 2 2h0 2v1 1h-1l-1 1c-1-2-2-3-4-4h1z" class="C"></path><path d="M496 550h2v1h0v1c-2 3-5 6-8 9l-1-1c-1 1-2 3-3 3l-1-1 3-5-4 1c-2 0-3 1-4 2v-1h-1 0l-1-4h1v-1h1c1-1 2-1 2-3 0 0-1 0-1-1h2 0 9 4z" class="p"></path><path d="M483 550h8l-3 2h0c-3 2-6 3-9 3v-1h1c1-1 2-1 2-3 0 0-1 0-1-1h2z" class="B"></path><path d="M488 552l1 1-1 1h1l-1 3-4 1c-2 0-3 1-4 2v-1h-1 0l-1-4h1c3 0 6-1 9-3z" class="c"></path><path d="M488 552l1 1-1 1c-2 0-6 1-7 3l-1 2h0-1 0l-1-4h1c3 0 6-1 9-3z" class="g"></path><path d="M496 550h2v1h0v1c-2 3-5 6-8 9l-1-1c1-1 1-2 2-4 0-2 2-3 3-6h0 2z" class="D"></path><path d="M175 597h6v1h1 2s0 1-1 1h0c1 1 1 2 2 2l-1 1 8 3c5 2 10 3 14 6h1l12 6h-2 0c8 4 17 7 25 10 4 1 9 2 13 4v1l-5-1-9-2-28-8-29-13-7-4c-2-1-4-2-5-4 0 0 1-1 2-1v-1h1v-1z" class="C"></path><path d="M206 611h1c-1 1-1 1-1 2l-2-1 2-1z" class="M"></path><path d="M192 605c5 2 10 3 14 6l-2 1-12-6v-1z" class="T"></path><path d="M207 611l12 6h-2 0l-11-4c0-1 0-1 1-2z" class="o"></path><path d="M175 597h6v1h1 2s0 1-1 1h0c1 1 1 2 2 2l-1 1 8 3v1c-3-1-5-2-7-3s-6 0-8 1c-2-1-4-2-5-4 0 0 1-1 2-1v-1h1v-1z" class="F"></path><path d="M184 602c-1-1-3 0-4-1 0-1 2-1 3-2 1 1 1 2 2 2l-1 1z" class="k"></path><path d="M175 597h6v1h1l-8 1v-1h1v-1z" class="B"></path><path d="M450 601h1 1 0v1l1 1h2c1 2 5 6 5 8h0-1v1h0l-1 1-1 1-7 1-1 1-2 1c-2 0-3 1-4 1-1-1-1-1-2-1 1 0 1 0 1-1h-6-7c-4-1-9-2-12-5l-1 1h-1c-2 0-3-2-5-3l1-1c0-2 1-3 1-4v-2h2c2 1 4 2 7 2l9-1h3l12 1 1-1c1-1 2-2 4-2z" class="c"></path><path d="M413 607v-2h1l1 1-1 1h-1z" class="E"></path><path d="M415 606c2 1 3 2 5 2 2 1 4 1 6 2h-5 0c-3-1-5-1-8-3l4 4-1 1c0-1-2-2-3-3v-2h1l1-1z" class="j"></path><path d="M430 611v1h12c4 0 8-1 12-1v1c-2 1-8 2-10 2h-3-5c-3 0-5-1-8-2l2-1z" class="V"></path><path d="M430 611v1h12 0c-2 1-4 1-6 1h-3 0 1 0 2l8 1h-3-5c-3 0-5-1-8-2l2-1z" class="j"></path><path d="M450 601h1 1 0v1l1 1h2c1 2 5 6 5 8h0-1c-1-1-11-6-12-6l-2-1 1-1c1-1 2-2 4-2z" class="U"></path><path d="M446 603c1-1 2-2 4-2v2c-1 0-1 0-2 1h-1v1l-2-1 1-1z" class="T"></path><path d="M417 611l-4-4c3 2 5 2 8 3h0 5c1 0 3 0 4 1l-2 1c3 1 5 2 8 2h5c2 1 3 1 5 1h0-2c2 1 4 0 6 0l-1 1-2 1c-2 0-3 1-4 1-1-1-1-1-2-1 1 0 1 0 1-1h-6-7c-4-1-9-2-12-5z" class="k"></path><path d="M436 616c4-1 9 0 13 0l-2 1c-2 0-3 1-4 1-1-1-1-1-2-1 1 0 1 0 1-1h-6z" class="e"></path><path d="M426 610c1 0 3 0 4 1l-2 1-7-2h0 5z" class="E"></path><path d="M376 137v-2l8 7 12 11c4 5 9 10 13 16l11 19 8 18v1h1c0-1 1-2 1-3 2-2 3-4 5-5 1-1 2-3 2-4 1-1 3-1 4-2 1 0 1 1 2 0 0 0 0-1 1-1 1-1 3-1 5-2h0v1c2 0 3-1 4-1h0c-1 2-3 3-6 4h3c0 1-1 1-1 2 1 1 1 0 2 0l1 1c-4 2-8 3-10 8 0 1 0 4-2 5h0c-1 0-1-1-2-1 0 1-1 2-1 2h0-1v2h-1l-1 6h1 0l1-1v1c0 1-1 4-1 4v7 4c0 3-1 6-1 9l-1-5v-6l-1-6-1 1h0-1c-2-10-4-20-8-29-10-24-26-45-46-61z" class="m"></path><path d="M449 191c2 0 3-1 4-1h0c-1 2-3 3-6 4-1 0-2 0-3 1v2c-1 2-2 3-3 5v3l-1-1v-1l-1-2c1-2 2-3 3-4 2-2 2-4 4-4l3-2z" class="E"></path><path d="M437 203l2-2 1 2v1l1 1c0 2-1 3-1 5-1 0-1-1-2-1 0 1-1 2-1 2h0-1v2h-1v-4l-1-1c0-2 2-4 3-5z" class="G"></path><path d="M437 203l2-2 1 2v1c-1 1-1 1-1 2l-1 1-1-1c0-1 1-2 0-2v-1z" class="J"></path><path d="M440 204l1 1c0 2-1 3-1 5-1 0-1-1-2-1 0 1-1 2-1 2h0v-1c-1-1 0-2 0-2h1 1v-2c0-1 0-1 1-2z" class="N"></path><path d="M441 205v-3c1-2 2-3 3-5v-2c1-1 2-1 3-1h3c0 1-1 1-1 2 1 1 1 0 2 0l1 1c-4 2-8 3-10 8 0 1 0 4-2 5h0c0-2 1-3 1-5z" class="I"></path><path d="M432 226c0-3-1-6-1-9 0-2-1-4-1-7h1c1 3 1 7 2 10l1-1h1 0l1-1v1c0 1-1 4-1 4v7 4c0 3-1 6-1 9l-1-5v-6l-1-6z" class="R"></path><path d="M433 225l1 1c0 1 1 4 0 5-1-2-1-4-1-6z" class="H"></path><path d="M434 226l1-3v7 4c-1-1-1-2-1-3 1-1 0-4 0-5z" class="Y"></path><path d="M434 219h1 0l1-1v1c0 1-1 4-1 4l-1 3-1-1v-5l1-1z" class="k"></path><path d="M243 454c10 1 21 2 29 7 6 3 11 7 15 12 12 13 22 28 33 43l34 45 9 15c3 6 6 13 8 19 2 7 3 15 7 21 5 6 10 7 18 8 3 0 6 0 9 1 4 1 8 3 11 5 11 6 23 15 35 17l-1 1c-6-1-11-3-16-6l-18-10c-3-1-6-3-9-4-2-1-4-1-7-1-6 0-13 0-18-4-1-2-2-3-4-4h0c-4-6-6-13-8-19-2-8-4-15-8-22-3-5-6-10-10-15l-29-39-18-24c-6-9-12-17-20-25-3-4-8-9-12-11-8-4-17-6-25-7-2-1-5-1-7-1h-1v-2h2c0 1 0 0 1 0z" class="m"></path><path d="M349 545v-2c3 1 7 3 9 2h1l1 1h1v-2l5 2h0l-1-1h2c7 4 13 9 18 15 1 1 2 3 3 4l-1 1h0c0 1 1 1 1 2v2c2 3 4 7 6 10v1c1 1 1 2 1 3v1c0 2 2 4 4 5l1-1c1 1 2 3 4 4h0c2 2 3 5 3 7l-3-2c-3-2-5-5-8-7l-12-14s-1-1-2-1c-1-2-2-4-4-5l-1 1h-1v-1c-1-1-3-1-4-2-1 0-3-2-4-2 0 1-1 2-2 2-3-3-5-6-7-9h-1c0-1-1-2-1-3-1 0-1-1-1-2s-1-2-1-4h-1l1-1h0c-2-2-4-4-6-4z" class="h"></path><path d="M370 554c1-1 2-1 3-1l3 3c-1 0-2 1-3 0-1 0-1-1-1-2h-2z" class="Z"></path><path d="M359 559h0c1 0 3 3 4 3l1-2h6 1c0 1 1 1 1 1 2 0 3 1 4 2h1l-1-2h1c1 0 1 1 2 1-1 2-1 3-1 4v2l1 1-2 2h-1v-1c-1-1-3-1-4-2-1 0-3-2-4-2 0 1-1 2-2 2-3-3-5-6-7-9z" class="c"></path><path d="M372 568l1-1c-1-1-1-2-2-2l1-1c1 1 3 3 3 4 1 1 1 2 1 2-1-1-3-1-4-2z" class="P"></path><path d="M379 562v1l16 21c0 2 2 4 4 5l1-1c1 1 2 3 4 4h0c2 2 3 5 3 7l-3-2c-3-2-5-5-8-7l-12-14s-1-1-2-1c-1-2-2-4-4-5l-1 1 2-2-1-1v-2c0-1 0-2 1-4z" class="D"></path><path d="M399 589l1-1c1 1 2 3 4 4h0-1c-2 0-2 0-3-1l1-1-2-1z" class="B"></path><path d="M379 562v1c0 2 2 4 3 5 1 3 2 6 2 8 0 0-1-1-2-1-1-2-2-4-4-5l-1 1 2-2-1-1v-2c0-1 0-2 1-4z" class="V"></path><path d="M349 545v-2c3 1 7 3 9 2h1l1 1h1v-2l5 2h0l-1-1h2c7 4 13 9 18 15 1 1 2 3 3 4l-1 1h0c0 1 1 1 1 2v2c2 3 4 7 6 10v1c-5-7-8-15-14-21-1-1-3-2-4-3l-3-3c-1 0-2 0-3 1v1l-1 1v-2-1h-1c0 1-1 1-1 2h0c-1 0-1-1-2-1h0l-1 1v-2-1c-1 0-1 1-2 1l-1-2h-1c1 1 1 2 0 2v1c-1-1-1-3-2-4h0v3h-1l-2-4h0c-2-2-4-4-6-4z" class="m"></path><path d="M366 546h0l-1-1h2c7 4 13 9 18 15 1 1 2 3 3 4l-1 1h0c0 1 1 1 1 2v2c-2-3-4-6-5-9-4-6-12-10-17-14z" class="M"></path><path d="M326 103c5 5 10 7 16 9l3 1c1 1 1 0 2 0 2 1 3 2 4 3v1c1 0 1 1 2 1 2 1 3 2 4 3l3 3 5 3v1h-1c-7-3-15-8-22-10-1 0-1 1-2 1l-10-3h-4c-4-1-9-2-13-2h-17c-19 1-37 9-53 18v-1s-1 0-1-1c0 0 1 0 1-1h0c6-5 12-9 19-13 1 0 5-2 6-2l6-2 2-1c2-1 4-1 6-3 1 0 2-2 3-3h1l4 6 6-6h2c5 0 9 0 14 1 1 1 2 1 3 2s1 3 3 3c2-1 4-4 6-6l1 1c2 0 4 2 6 2h-1c-2-1-3-3-3-4l-1-1z" class="C"></path><path d="M328 114l5 1c-1 0-2 0-3 1h0-4v-1h0c0-1 1-1 2-1z" class="AA"></path><path d="M333 115l9 3c-1 0-1 1-2 1l-10-3h0c1-1 2-1 3-1z" class="r"></path><path d="M342 112l3 1c1 1 1 0 2 0 2 1 3 2 4 3v1l-4-1-6-2 1-2z" class="G"></path><path d="M347 116l4 1c1 0 1 1 2 1 2 1 3 2 4 3l3 3c-2-1-3-1-4-3l-1 1h0c-3-1-5-3-7-4-1 0-1-1-1-2z" class="H"></path><path d="M326 103c5 5 10 7 16 9l-1 2c-5-3-12-4-16-8 2 0 4 2 6 2h-1c-2-1-3-3-3-4l-1-1z" class="T"></path><path d="M291 113c12-2 25-1 37 1-1 0-2 0-2 1h0v1c-4-1-9-2-13-2h-17v-1c-3 0-5 1-7 1 2-1 6 0 7-1h-5z" class="h"></path><path d="M243 129c4-3 10-5 15-7 10-5 22-8 33-9h5c-1 1-5 0-7 1 2 0 4-1 7-1v1c-19 1-37 9-53 18v-1s-1 0-1-1c0 0 1 0 1-1z" class="r"></path><path d="M375 485c3 0 6 1 8 2l2-1 1 1c1 0 1 0 2 1 1 0 0 0 1 1v1h1 0c1 3 5 5 6 7l1 1h0v2h0 1 2l2 5c-2 3-5 5-8 5s-6-1-8-3l-1 1h0c2 2 3 3 6 3v1c-1 0-2 0-3 1l-3-3c-1 1-2 2-4 3v2h0 0l-2-1c0 2 1 3 2 5v1h-2 0c-1-1-1-1-2-1v6h-1l-1-1h0c-1-2-2-7-4-8-1-1-2-2-2-4v-1c-1-2-3-4-3-6l-1-2c0 1-1 1-1 2l-1-3c-1-2-1-3-2-5 0-1-1-1-2-2v-1c1-1 1-3 1-4l1 1h1c0-1 0-3 1-4h2 2 2v-1h0 3 0l2-1h1z" class="U"></path><path d="M383 496h1c-1 2-2 4-2 6l1 1v1l-2 2-1-2h-2v-1h0c1 0 2 0 3-1 0-1 0-1-1-1v-1c1-1 2-3 3-4z" class="b"></path><path d="M382 487c1 0 1 1 2 1h0 1v3h-1 0c0-1-1-2-2-2-2 1-4 2-6 4-2 3-2 7-1 10v2h0-1 0c-1-2-1-4-2-7 0-2 0-3 1-5 1-3 3-5 5-5h3l1-1z" class="k"></path><path d="M385 486l1 1c1 0 1 0 2 1 1 0 0 0 1 1v1h1 0c1 3 5 5 6 7-1-1-2-1-3-2h-1l-1 1c-1 1-2 0-3 1 0-1 0-1-1-1 1-1 2-1 2-1l1-1-2-1c-2 1-3 2-4 3h-1l-1-1c-1 0-3 4-4 4v-1c1-2 2-3 4-4v-1h0v-1c1 0 1 0 2-1h0 0 1v-3h-1l-1-1 2-1z" class="B"></path><path d="M385 486l1 1 1 5c-1 1-3 2-5 2v-1h0v-1c1 0 1 0 2-1h0 0 1v-3h-1l-1-1 2-1z" class="F"></path><path d="M384 496c1-1 2-2 4-3l2 1-1 1s-1 0-2 1c1 0 1 0 1 1 1-1 2 0 3-1l1-1h1c1 1 2 1 3 2l1 1h0v2h0 1 2l2 5c-2 3-5 5-8 5s-6-1-8-3l-1 1c-1-1-2-2-2-4v-1l-1-1c0-2 1-4 2-6z" class="O"></path><path d="M395 503l1-1c1 0 1-1 2-1h1c0 1 0 2-1 3-1 2-4 3-6 3s-3-1-4-2v-1h2l1 1h1 0 1c1-1 2-1 3-2h-1z" class="S"></path><path d="M384 496c1-1 2-2 4-3l2 1-1 1s-1 0-2 1c-2 1-3 2-3 5 0 2 0 3 1 5l1 1-1 1c-1-1-2-2-2-4v-1l-1-1c0-2 1-4 2-6z" class="W"></path><path d="M391 496l1-1h1c1 1 2 1 3 2l1 1h0v2h0 1 2l-1 1h-1c-1 0-1 1-2 1l-1 1h1c-1 1-2 1-3 2h-1 0-1l-1-1h-2-1v-5h1l1-1-1-1c1-1 2 0 3-1z" class="M"></path><path d="M388 504h-1v-5h1l2 1h-1v1s1 1 1 2c1 1 2 1 4 1 0-1 1-1 1-1h1c-1 1-2 1-3 2h-1 0-1l-1-1h-2z" class="G"></path><path d="M391 496l1-1h1c1 1 2 1 3 2l1 1c0 1 0 1-1 2s-3 1-4 1-1-1-2-1l-2-1 1-1-1-1c1-1 2 0 3-1z" class="c"></path><path d="M391 496c1 0 2 1 2 2v1c-2 0-3-1-4-1l-1-1c1-1 2 0 3-1z" class="G"></path><path d="M375 485c3 0 6 1 8 2l1 1h0c-1 0-1-1-2-1l-1 1h-3c-2 0-4 2-5 5-1 2-1 3-1 5 1 3 1 5 2 7h0 1c0 1 0 1 1 2h1v-1h3 1l2-2c0 2 1 3 2 4h0c2 2 3 3 6 3v1c-1 0-2 0-3 1l-3-3c-1 1-2 2-4 3v2h0 0l-2-1c0 2 1 3 2 5v1h-2 0c-1-1-1-1-2-1v6h-1l-1-1h0c-1-2-2-7-4-8-1-1-2-2-2-4v-1c-1-2-3-4-3-6l-1-2c0 1-1 1-1 2l-1-3c-1-2-1-3-2-5 0-1-1-1-2-2v-1c1-1 1-3 1-4l1 1h1c0-1 0-3 1-4h2 2 2v-1h0 3 0l2-1h1z" class="m"></path><path d="M365 490c1 0 2 0 2 1v1h-3v-1l1-1zm2-3h2v-1h0 3c-2 1-3 3-4 4-1-1-2-1-3-2h0v-1h2z" class="S"></path><path d="M377 513c1 0 1 0 2 1h0 0c0 2 1 3 2 5v1h-2 0c-1-1-1-1-2-1v-6z" class="M"></path><path d="M371 501c-1-2-1-5-1-7 1-2 2-5 4-6 2-2 5-1 8-1l-1 1h-3c-2 0-4 2-5 5-1 2-1 3-1 5v-1l-1 1h0v3z" class="L"></path><path d="M363 487h2v1h0l-2 1c1 0 1 0 2 1l-1 1v1 1l3 2v1c-2 0-4-2-4 0 0 0 0 1 1 1s2 0 3 1l-1 1h-2v1c1 0 1 1 2 1l-1 1h-1l1 1c0 1-1 1-1 2l-1-3c-1-2-1-3-2-5 0-1-1-1-2-2v-1c1-1 1-3 1-4l1 1h1c0-1 0-3 1-4z" class="W"></path><path d="M363 502s0-1 1-1h0v1l1 1c0 1-1 1-1 2l-1-3z" class="S"></path><path d="M359 494c1-1 1-3 1-4l1 1h1c0 2 0 4-1 6 0-1-1-1-2-2v-1z" class="K"></path><path d="M371 501v-3h0l1-1v1c1 3 1 5 2 7h0 1c0 1 0 1 1 2h1v-1h3 1l2-2c0 2 1 3 2 4h0c2 2 3 3 6 3v1c-1 0-2 0-3 1l-3-3c-1 1-2 2-4 3v2h0 0l-2-1h0 0c-1-1-1-1-2-1l-1-2c-2-3-4-7-5-10z" class="P"></path><path d="M376 511c0-1 0-2-1-3 1 0 2 0 2 1h1l1 1h1 0c1-1 2-1 3-1h0c-1 2-1 2-3 2v1l1 1v2h0 0l-2-1h0 0c-1-1-1-1-2-1l-1-2z" class="q"></path><path d="M379 514l-1-1 1-1v-1l1 1 1 1v2h0 0l-2-1h0 0z" class="r"></path><path d="M371 501v-3h0l1-1v1c1 3 1 5 2 7h0 1c0 1 0 1 1 2h1v-1h3l-2 1v1c2 0 2-1 3-1h1c0 1 0 1-1 1 0 1-1 1-2 1v1l-1-1h-1c0-1-1-1-2-1 1 1 1 2 1 3-2-3-4-7-5-10z" class="o"></path><path d="M189 598h7c3 0 5 0 7 1 4 2 8 4 13 5 2 0 4 1 5 1l1-1h2c1 0 1 1 2 1l1-1h0c-1-1-4-2-5-2 0 0 1 0 2-1h2c1 1 2 2 2 3l1 1h1l2 1c1 0 2 1 3 1h1 1l-1 2c3 4 7 7 11 10l6 3 1 1c3 2 8 5 12 5 2 1 4 1 6 0l1 1h1c1 1 2 1 3 1v-1h1s1 0 2-1c2 0 3-1 4-2v1c-1 1-1 1-2 1-1 1-1 1-2 1h0c-2 1-4 1-5 3-1 0-2-1-3-1h-6l-3 1h0 0c-1 1-2 1-3 0l-1 1h-2-1c0-1-1-1-1-1v-1c-4-2-9-3-13-4-8-3-17-6-25-10h0 2l-12-6h-1c-4-3-9-4-14-6l-8-3 1-1c-1 0-1-1-2-2h0 2l4-1z" class="C"></path><path d="M183 599h2 2l1 1-3 1c-1 0-1-1-2-2h0z" class="J"></path><path d="M224 610c2 0 4 0 6 1s4 2 5 4v1l-1-1c-1 0-3-1-4-2h-4c-2 0-3-1-4-2 1 0 1 0 2-1z" class="L"></path><path d="M189 598h7c3 0 5 0 7 1-1 1-1 1-2 1l5 2h0l-1 1c-1 0-4-2-6-2-3-1-7-1-11-1l-1-1h-2l4-1z" class="u"></path><path d="M189 598h7c3 0 5 0 7 1-1 1-1 1-2 1-4-1-10-1-14-1h-2l4-1z" class="k"></path><path d="M201 600c1 0 1 0 2-1 4 2 8 4 13 5 2 0 4 1 5 1h1v1h-2l-1 1h-1-4c-3-1-6-2-9-4l1-1h0l-5-2z" class="C"></path><path d="M215 605l5 1-1 1h-1-4c0-1 1-1 1-2z" class="Q"></path><path d="M206 602c3 1 6 3 9 3 0 1-1 1-1 2-3-1-6-2-9-4l1-1zm11 15h0 2l30 11c4 1 10 4 14 4h0 0c-1 1-2 1-3 0l-1 1h-2-1c0-1-1-1-1-1v-1c-4-2-9-3-13-4-8-3-17-6-25-10z" class="W"></path><path d="M224 601h2c1 1 2 2 2 3l1 1h1l2 1c1 0 2 1 3 1h1 1l-1 2c3 4 7 7 11 10l6 3 1 1c3 2 8 5 12 5 2 1 4 1 6 0l1 1h1c1 1 2 1 3 1v-1h1s1 0 2-1c2 0 3-1 4-2v1c-1 1-1 1-2 1-1 1-1 1-2 1h0c-2 1-4 1-5 3-1 0-2-1-3-1h-6c-3 0-6-1-8-3-10-4-17-11-26-17-1-1-3-3-5-3l-9-1h1l1-1h2v-1h-1l1-1h2c1 0 1 1 2 1l1-1h0c-1-1-4-2-5-2 0 0 1 0 2-1z" class="T"></path><path d="M224 601v1c2 1 3 1 4 2v1h-1s-1 0-1 1l-1 1s1 0 2 1l-9-1h1l1-1h2v-1h-1l1-1h2c1 0 1 1 2 1l1-1h0c-1-1-4-2-5-2 0 0 1 0 2-1z" class="V"></path><path d="M457 531v1c3 1 4 5 5 8l2 2c1 1 2 2 2 3l2-1 1 1c1 2 2 3 4 4 1 0 2 1 3 2h-1l-1 1h-1c0 4 1 6 1 9l-1 5h0l-1 3-1 2c-1 3-2 5-4 7-1 0-1 0-2 1 0 0-1 1-2 1-1 1-3 2-5 2h-2-1v1h-1c-1-1-2-2-2-3l-3-4h0c-1-1-1-2-2-3l-2-3h0c-1-2-1-2-2-3l-1-1v-3l-1-1v-1l3-2v-3h1 1l-1-1c1-1 3-4 4-5h0c-1-1-2 0-4 0l1-1-1-1s-1-1-2-1c0-1-1-2-1-3s0-3 1-4c1-2 2-2 4-2 0-1 1-1 1-1h3c1 1 3 1 3 2v2 1l1-2h2c1 0 2 1 3 2v-1l-2-4c0-1-1-3-2-4l1-2z" class="I"></path><path d="M463 551h2c1 2 1 3 1 5l-7 4c1-3 1-7 3-9h1z" class="E"></path><path d="M443 567l1-2c2-1 5-3 8-3 2 0 5 0 7-1h1l1 1c-1 0-1 1-1 1-1 0-2 0-2 1h0c-5 0-9 1-13 5v1c-1-2-1-2-2-3z" class="N"></path><path d="M458 564h1c1 1 1 2 1 3v1c-1 1-2 1-4 0h-4 0l-2 1c-1 1-3 2-3 4l-2-3h0v-1c4-4 8-5 13-5z" class="Z"></path><path d="M452 566h2c-1 1-1 2-2 2l-2 1c-1-1-1-1-2-1 1-1 3-1 4-2z" class="F"></path><path d="M448 568c1 0 1 0 2 1-1 1-3 2-3 4l-2-3 3-2z" class="G"></path><path d="M452 566c2-1 3-1 5-1h1l1 1-1 1h-2 0c-1 1-3 1-4 1h0c1 0 1-1 2-2h-2z" class="j"></path><path d="M449 550h1c3 0 6-2 9-4 0 5 1 9-4 13-1 1-2 2-3 2-2 0-4 0-4-1v-1h1 0l-2-1h1v-1c-2 1-3 2-4 2v-3h1 1l-1-1c1-1 3-4 4-5h0z" class="N"></path><path d="M447 555l7-3c-1 2-1 3-3 4h0c-2-1-3 0-4-1z" class="Z"></path><path d="M446 556l1-1c1 1 2 0 4 1h0l-3 1c-2 1-3 2-4 2v-3h1 1z" class="e"></path><path d="M454 552h1 0c0 2 0 4-2 5-1 2-3 2-5 2h1 0l-2-1h1v-1l3-1c2-1 2-2 3-4z" class="B"></path><path d="M448 537h3c1 1 3 1 3 2v2 1h2s1 0 1 1l2 1v2c-3 2-6 4-9 4h-1c-1-1-2 0-4 0l1-1-1-1s-1-1-2-1c0-1-1-2-1-3s0-3 1-4c1-2 2-2 4-2 0-1 1-1 1-1z" class="T"></path><path d="M443 547c0-1-1-2-1-3s0-3 1-4c1-2 2-2 4-2 1 0 2 1 3 1-1 1-1 1-2 1-1 1-2 1-4 2v3l2 2h0l-1 1s-1-1-2-1z" class="Y"></path><path d="M444 542c1 0 1-1 1-1 0-1 0-1 1-2 1 0 1 1 2 1-1 1-2 1-4 2z" class="Z"></path><path d="M456 542s1 0 1 1l2 1v2c-3 2-6 4-9 4h-1c-1-1-2 0-4 0l1-1-1-1 1-1c1 0 2 1 3 0 2-1 6-3 7-5z" class="P"></path><defs><linearGradient id="H" x1="462.05" y1="557.925" x2="469.294" y2="549.707" xlink:href="#B"><stop offset="0" stop-color="#141516"></stop><stop offset="1" stop-color="#261b1a"></stop></linearGradient></defs><path fill="url(#H)" d="M457 531v1c3 1 4 5 5 8l2 2c1 1 2 2 2 3l2-1 1 1c1 2 2 3 4 4 1 0 2 1 3 2h-1l-1 1h-1c0 4 1 6 1 9l-1 5h0c-2 0-4 1-5 2l-1-1c0-1 0-2-1-3l-2 2v-2 1c-2 0-2 1-3 2h-1c0-1 0-2-1-3h-1 0c0-1 1-1 2-1 0 0 0-1 1-1l-1-1 6-4h0 1c0-1-1-1-1-1 0-2 0-3-1-5h-2-1c-1-3-2-6-2-9v-1l-2-4c0-1-1-3-2-4l1-2z"></path><path d="M460 541c2 3 3 7 3 10h-1c-1-3-2-6-2-9v-1z" class="l"></path><path d="M457 532c3 1 4 5 5 8l2 2c1 1 2 2 2 3h0c1 2 2 3 4 5 1 2 1 4 2 6v3c-1-1-1-2-1-2-1-1-3-6-4-7-1-2-2-4-4-6l-6-12z" class="F"></path><path d="M468 544l1 1c1 2 2 3 4 4 1 0 2 1 3 2h-1l-1 1h-1c0 4 1 6 1 9l-1 1v-3c0-1-1-2-1-3-1-2-1-4-2-6-2-2-3-3-4-5h0l2-1z" class="b"></path><path d="M468 544l1 1c1 2 2 3 4 4 1 0 2 1 3 2h-1l-1 1c-3-1-6-5-8-7l2-1z" class="o"></path><path d="M472 556c0 1 1 2 1 3v3l1-1-1 5h0c-2 0-4 1-5 2l-1-1c0-1 0-2-1-3l-2 2v-2 1c-2 0-2 1-3 2h-1c0-1 0-2-1-3h-1 0c0-1 1-1 2-1 0 0 0-1 1-1l-1-1 6-4h0 1 0c0 1 0 1-1 2 0 1 1 4 1 5h4s0-1 1-1c0-2-1-4-1-6 0 0 0 1 1 2v-3z" class="e"></path><path d="M464 560c1 2 1 2 1 4h1l-2 2v-2-3-1z" class="X"></path><path d="M472 556c0 1 1 2 1 3 0 2 0 4-1 5v-1c0-2-1-4-1-6 0 0 0 1 1 2v-3z" class="Q"></path><path d="M466 557v1c0 1-1 1-1 2h-1v1 3 1c-2 0-2 1-3 2h-1c0-1 0-2-1-3h-1 0c0-1 1-1 2-1 0 0 0-1 1-1l-1-1 6-4z" class="G"></path><path d="M458 564h2c0-1 1-1 1-1 1-1 1 0 2-1l1-1v3 1c-2 0-2 1-3 2h-1c0-1 0-2-1-3h-1 0z" class="E"></path><defs><linearGradient id="I" x1="464.408" y1="578.595" x2="458.496" y2="567.386" xlink:href="#B"><stop offset="0" stop-color="#121314"></stop><stop offset="1" stop-color="#341b1c"></stop></linearGradient></defs><path fill="url(#I)" d="M464 564v2l2-2c1 1 1 2 1 3l1 1c1-1 3-2 5-2l-1 3-1 2c-1 3-2 5-4 7-1 0-1 0-2 1 0 0-1 1-2 1-1 1-3 2-5 2h-2-1v1h-1c-1-1-2-2-2-3l-3-4h0c-1-1-1-2-2-3 0-2 2-3 3-4l2-1h0 4c2 1 3 1 4 0v-1h1c1-1 1-2 3-2v-1z"></path><path d="M464 575c1-2 2-3 4-4 0 0 0-1 1 0v1h0c-2 1-3 3-5 5l-2 1-1-1c1-1 1-1 3-2z" class="a"></path><path d="M468 568c1-1 3-2 5-2l-1 3-1 2s-1 1-2 1h0v-1c-1-1-1 0-1 0-2 1-3 2-4 4-1-1-1-1-1-2s1-2 2-3h1l1-1v-1h1z" class="k"></path><path d="M464 564v2l2-2c1 1 1 2 1 3l1 1h-1v1l-1 1h-1c-1 1-2 2-2 3-1 0-1-3-2-4l-1-1v-1h1c1-1 1-2 3-2v-1z" class="a"></path><path d="M465 570v-3h1l1 2-1 1h-1z" class="u"></path><path d="M464 564v2c0 1 0 3-1 4-1 0-1 0-2-1l-1-1v-1h1c1-1 1-2 3-2v-1z" class="e"></path><path d="M452 568h4v1l-1 1h1c1 1 2 1 2 3 0 3 0 5-2 7-1 1-2 2-2 3-1-1-2-2-2-3l-3-4h0c-1-1-1-2-2-3 0-2 2-3 3-4l2-1h0z" class="T"></path><path d="M452 571h1 1v1c-1 2-2 5-2 8l-3-4 1-1c0 1 0 1 1 1h0c0-2 0-3 1-4v-1z" class="i"></path><path d="M447 573c2 0 3-2 5-3v1 1c-1 1-1 2-1 4h0c-1 0-1 0-1-1l-1 1h0c-1-1-1-2-2-3z" class="G"></path><path d="M452 568h4v1l-1 1h1l-2 2v-1h-1-1v-1c-2 1-3 3-5 3 0-2 2-3 3-4l2-1h0z" class="K"></path><path d="M350 512h1c-1-2-4-3-5-4-1 0-1 0-1-1h0c1 0 1 1 2 1s2 0 3 1l1 1h1c0 1 0 1 1 2l1-1 9 9c3 5 5 10 8 14 1 2 3 1 4 3 3 3 3 8 5 11s4 6 6 8c5 6 10 12 16 16 5 3 10 5 15 7l-1 1c-8-3-15-7-21-13-3-2-5-5-7-7h-1 0c-1 0-1 0-2-1v1c-5-6-11-11-18-15h-2l1 1h0l-5-2-6-1c-1-1-2-1-3 0h-1c-1-1-2-1-3-1h0l-2-2c-2-2-4-5-6-7-1 0-1-1-1-2l-1-1c-2-2-3-4-5-7-1-1-2-2-2-4-1-1 0-3 1-4 3-4 7-5 11-5l1 1h3c-1 2-2 1-2 3h2c0-1 1-1 1-2h2z" class="k"></path><path d="M341 526l5 2c-1 0-2 1-3 1-1-1-2-1-3-2l1-1z" class="h"></path><path d="M344 524l-1-1c1-1 2-2 3-2h1v2 1h-3z" class="S"></path><path d="M343 533l2-1c2 0 4 1 6 1-1 0-1 1-2 1-1 1-3 0-4 0-1-1-1-1-2-1z" class="i"></path><path d="M338 528h0l-3-5 6 3-1 1c1 1 2 1 3 2h0v1h-2v-1c-1 0-1-1-2-1h-1z" class="L"></path><path d="M355 543c2-1 3-1 4-2 3 1 6 2 8 4h-2l1 1h0l-5-2-6-1z" class="S"></path><path d="M344 524h3c1 1 3 1 5 1h1c0 1 1 1 2 1 1 1 2 2 2 3-4-1-9-2-13-5z" class="T"></path><path d="M346 540h6l1-1c2 0 4 2 6 2-1 1-2 1-4 2-1-1-2-1-3 0h-1c-1-1-2-1-3-1h0l-2-2z" class="L"></path><path d="M337 515l1-1c2 0 3 2 5 3l-3 4h-1c-1 1-2 0-3-1s-2-1-2-2c1-2 1-2 3-3z" class="S"></path><path d="M346 528c2 0 3 0 4 1 9 5 19 10 25 18-2 0-6-5-8-6-5-3-11-5-16-8h0c-2 0-4-1-6-1l-2 1h-3 0c-1 0-1-1-1-2s0-2-1-3h1c1 0 1 1 2 1v1h2v-1h0c1 0 2-1 3-1z" class="M"></path><path d="M346 528c2 0 3 0 4 1l-1 1c-2-1-3-1-4-1h-2 0c1 0 2-1 3-1z" class="W"></path><path d="M338 528h1c1 0 1 1 2 1v1c1 1 1 1 0 2l-1 1h0c-1 0-1-1-1-2s0-2-1-3z" class="h"></path><path d="M350 512h1c-1-2-4-3-5-4-1 0-1 0-1-1h0c1 0 1 1 2 1s2 0 3 1l1 1h1c0 1 0 1 1 2l1-1 9 9c3 5 5 10 8 14 1 2 3 1 4 3 3 3 3 8 5 11s4 6 6 8c5 6 10 12 16 16 5 3 10 5 15 7l-1 1c-8-3-15-7-21-13-3-2-5-5-7-7h-1c-2-3-5-6-7-9s-3-7-5-10c-2-2-4-4-7-6l-11-6c0-1-1-2-2-3-1 0-2 0-2-1h-1c-2 0-4 0-5-1v-1-2h-1c1-1 2-1 3-1 2 1 5 3 7 4 2 2 2 3 5 4l2 1h0c0-1 0-1-1-1-1-2-1-3-1-6-3-4-7-6-11-10z" class="U"></path><path d="M347 523c0-1 1-1 2-1s3 1 3 2h2c1 1 1 1 1 2-1 0-2 0-2-1h-1c-2 0-4 0-5-1v-1z" class="i"></path><path d="M361 522l8 11c-2-1-5-3-5-5l-1 1c0-1 0-1-1-1-1-2-1-3-1-6z" class="J"></path><path d="M277 78h1c3 0 7-1 10-1h16c12 0 25 1 37 2 15 3 30 6 45 10l11 3c6 3 13 5 19 8l15 8 20 11c3 2 6 5 9 5 1 0 1 0 1 1 2 0 3 1 4 2h0l-1 1v-1h-2 1c0 1 0 0 1 1 0 0 0 1 1 1l1 1c-1 0-1-1-2-1v1l3 2c1 1 2 2 2 4l-1 1h0v2c0 1 1 3 2 4v1l-1 1-4-9c0 3 1 7 0 9-1 1-1 2-1 2v2h-3l-1-2h-1l-2 3c-3 1-6-2-8-3h-1 0c-1-1-1-2-1-2v-4-2l1-1c2 2 2 5 4 5s3-2 3-4c1-1 1-3 0-5-2-4-6-6-10-7v-1h4c0-1 0-1 1-1h0c-1-1-2-2-3-2l-6-5c-2-2-5-4-8-6-5-4-11-7-17-9-11-5-22-9-34-13-21-6-42-9-63-11-7 0-13-1-20 0-18 0-36 4-54 8-8 1-15 3-23 5-18 5-35 13-51 22l-1-1h-2c0-1 1-1 1-1 1-1 1-1 2-1h0c1-1 2-2 3-2 0-1 1-1 1-1 1-1 2-1 3-2h1c1-1 2-1 3-2 1 0 2 0 2-1l22-9c1-1 1-1 2-1l8-3h2l4-2h1 2c1 0 2-1 3-1 1-1 3 0 4-1l4-1h1c6-2 12-2 18-4h2c3-1 5-1 8-2h2c1 0 3 0 4-1h3 2c0-1 1-1 1 0z" class="r"></path><path d="M456 133v-3s1 1 2 1c0 1 0 1-1 1l-1 1z" class="t"></path><path d="M458 131h1c0 2 1 4 2 6 0 1 1 2 1 4h0c1 1 1 2 2 2l1 2c-1 1-1 2-1 2v2h-3l-1-2h-1v-1-4l-3-9 1-1c1 0 1 0 1-1z" class="K"></path><path d="M461 142l1-1h0c1 1 1 2 2 2l1 2c-1 1-1 2-1 2v2h-3l-1-2h-1v-1-4l1 3h0l1-3z" class="G"></path><path d="M461 142l1-1h0c1 1 1 2 2 2l1 2c-1 1-1 2-1 2l-2-1v-3l-1-1z" class="f"></path><defs><linearGradient id="J" x1="458.235" y1="136.013" x2="465.308" y2="137.885" xlink:href="#B"><stop offset="0" stop-color="#0b0d0e"></stop><stop offset="1" stop-color="#241919"></stop></linearGradient></defs><path fill="url(#J)" d="M464 130l3 2c1 1 2 2 2 4l-1 1h0v2c0 1 1 3 2 4v1l-1 1-4-9c0 3 1 7 0 9l-1-2c-1 0-1-1-2-2h0c0-2-1-3-1-4-1-2-2-4-2-6l2 1c1 0 2-1 3-2z"></path><path d="M464 130l3 2c1 1 2 2 2 4l-1 1-7-5c1 0 2-1 3-2z" class="M"></path><path d="M385 560v-1c1 1 1 1 2 1h0 1c2 2 4 5 7 7 6 6 13 10 21 13l1-1 2 1c1-1 0-1 1-1h2 1v-1c3 1 5 1 8 0l-1 1c1 0 2 0 2 1h1l1 1h2c1 1 1 1 1 2h3c1 0 2 1 2 2 2 2 4 5 5 7s2 4 4 6c1 2 2 3 4 5h-2l-1-1v-1h0-1-1c-2 0-3 1-4 2l-1 1-12-1h-3l-9 1c-3 0-5-1-7-2h-2v2c0 1-1 2-1 4l-1 1c-1-1-1-1-2-1h-1 0c-1-1-3-2-4-3l-2 2c-1 2-2 3-3 4h0l-1-1v-1h-1v-2c-1-2-2-5-4-6s-3-1-4-2c0-2 1-2 1-4 1 0 1 1 2 1 0 1-1 1-2 2l1 1c2 0 3 1 4 2l2 5 3-3s1-1 1-2c1 0 1-3 1-4l-6-6c1 0 1 0 1-1 3 2 5 5 8 7l3 2c0-2-1-5-3-7h0c-2-1-3-3-4-4l-1 1c-2-1-4-3-4-5v-1c0-1 0-2-1-3v-1c-2-3-4-7-6-10v-2c0-1-1-1-1-2h0l1-1c-1-1-2-3-3-4z" class="C"></path><path d="M417 579l2 1c1 0 2 0 3 1-1 0-1 1-1 2l-5-3 1-1z" class="F"></path><path d="M431 598l2 1h2c0 1-1 2-1 3h0v1h-1-3v-1c0-2 1-3 1-4z" class="o"></path><path d="M431 598l2 1h0c-1 1-1 1-1 2l-1 1h-1c0-2 1-3 1-4z" class="L"></path><path d="M428 599l2-1h1c0 1-1 2-1 4v1l-9 1 1-1 2-2h1c1 0 1 1 1 1h2v-3z" class="q"></path><path d="M424 583c1-1 1 0 3 0 0 0 1 1 2 1h2v1h1 2l1 1c-4 0-8 1-11-1v-2z" class="p"></path><path d="M424 583c1-1 1 0 3 0 0 0 1 1 2 1h2l-1 1h-4c-1 0-2-1-2-2z" class="K"></path><path d="M433 593c1 0 3 0 4-1 1 1 2 1 3 1 0 1 0 2 1 2l-1 1h0v1h-1-2v-1c-1 0-2 1-2 1-2 0-4-2-5-3v-1l1-1 1 2 1-1z" class="L"></path><path d="M437 596c1-1 1-1 3-1v1 1h-1-2v-1z" class="m"></path><path d="M433 593c1 0 3 0 4-1 1 1 2 1 3 1-2 0-4 2-6 2l-1-2z" class="B"></path><path d="M431 592c0-1 0-2-1-3h0 0c1-1 3-1 4-1l1 1 1 1h2 2 2c0 1 0 2 1 2v1c0 1-1 2-2 2s-1-1-1-2c-1 0-2 0-3-1-1 1-3 1-4 1l-1 1-1-2z" class="M"></path><path d="M440 590h2c0 1 0 2 1 2h-2c-1-1-2 0-3-1v-1h2z" class="W"></path><path d="M437 592h4 2v1c0 1-1 2-2 2s-1-1-1-2c-1 0-2 0-3-1z" class="I"></path><path d="M423 578c3 1 5 1 8 0l-1 1c1 0 2 0 2 1h1l1 1c-1 1-1 1-2 1s-1 0-1 1h0-2v1c-1 0-2-1-2-1-2 0-2-1-3 0v2c-1-1-2-1-3-2 0-1 0-2 1-2-1-1-2-1-3-1 1-1 0-1 1-1h2 1v-1z" class="t"></path><path d="M425 581h7v1c-1 0-1 0-1 1h0-2v1c-1 0-2-1-2-1v-2h-2z" class="Z"></path><path d="M427 581l2 2h2 0-2v1c-1 0-2-1-2-1v-2z" class="p"></path><path d="M422 581h3 2v2c-2 0-2-1-3 0v2c-1-1-2-1-3-2 0-1 0-2 1-2z" class="g"></path><path d="M423 578c3 1 5 1 8 0l-1 1c1 0 2 0 2 1-3 0-7 0-10-1h1v-1z" class="d"></path><path d="M424 601c1-1 2-3 2-4s-1-2-2-3c-2-5-7-7-11-10-2-1-3-1-5-2h4c6 2 13 5 16 11v1h-2c0 1 1 3 2 4v1 3h-2s0-1-1-1h-1z" class="M"></path><path d="M409 590l7 2c2-1 2-3 3-4 1 3 4 8 3 10 0 1-1 2-3 3 0 0-2 0-2-1-1 0-2-2-2-3-2-2-3-4-6-5l-1-1 1-1z" class="q"></path><path d="M434 581h2c1 1 1 1 1 2h3c1 0 2 1 2 2 2 2 4 5 5 7l-1 1c-1-2-2-2-4-3h-2-2-2l-1-1-1-1h1c1-1 0-1 0-2l-1-1h-2-1v-1h-2v-1h2 0c0-1 0-1 1-1s1 0 2-1z" class="l"></path><path d="M442 588c1 0 1 0 2 1h-3v-1h1z" class="G"></path><path d="M440 583c1 0 2 1 2 2h-4-1v-1c-1 0-1 0-1-1h1 3z" class="X"></path><path d="M435 586h1c2 0 5 0 5 1h1v1h-1v1l-1 1h-2-2l-1-1-1-1h1c1-1 0-1 0-2z" class="b"></path><path d="M441 589c-2-1-4 0-5-1v-1h6v1h-1v1z" class="E"></path><path d="M434 581h2c1 1 1 1 1 2h-1c0 1 0 1 1 1v1l-1 1h-1l-1-1h-2-1v-1h-2v-1h2 0c0-1 0-1 1-1s1 0 2-1z" class="u"></path><path d="M431 583h0l1 1v1h-1v-1h-2v-1h2z" class="X"></path><path d="M434 581h2c1 1 1 1 1 2h-1-5 0 0c0-1 0-1 1-1s1 0 2-1z" class="H"></path><path d="M388 569v-2c0-1-1-1-1-2h0l1-1 7 11c1 2 2 3 3 5 0 1 1 1 1 2l6 9-1 1c-2-1-3-3-4-4l-1 1c-2-1-4-3-4-5v-1c0-1 0-2-1-3v-1c-2-3-4-7-6-10z" class="q"></path><path d="M394 579c1 1 2 2 2 3 1 2 3 5 4 6l-1 1c-2-1-4-3-4-5v-1c0-1 0-2-1-3v-1z" class="t"></path><path d="M442 590c2 1 3 1 4 3l1-1c1 2 2 4 4 6 1 2 2 3 4 5h-2l-1-1v-1h0-1-1c-2 0-3 1-4 2l-1 1-12-1h1v-1h0 1c0-1 1-2 1-2v-1c1-1 2-1 3-2h1v-1h0l1-1c1 0 2-1 2-2v-1c-1 0-1-1-1-2z" class="c"></path><path d="M440 601l4-4c1 0 2-1 3-2l1 1c-1 1-3 2-4 3v2c-1 0-2 1-3 1s0 0-1-1z" class="M"></path><path d="M442 590c2 1 3 1 4 3-1 1-3 3-5 4l-1-1 1-1c1 0 2-1 2-2v-1c-1 0-1-1-1-2z" class="h"></path><path d="M440 597c0 2 0 3-1 5h0l1-1c1 1 0 1 1 1s2-1 3-1c0 0 1 0 1-1 1-1 2-1 4-2h0l1 1c-1 1-1 0-2 1 0 0-2 2-2 3l-1 1-12-1h1v-1h0 1c0-1 1-2 1-2v-1c1-1 2-1 3-2h1z" class="U"></path><path d="M405 591v1h1c0-2-4-7-5-9-1-1-2-2-2-3 1 0 3 2 4 3 3 3 8 4 12 7-2 0-4 0-6-1h-1l1 1-1 1 1 1h0v1c2 2 4 4 5 7 1 1 2 2 4 3h4l-1 1c-3 0-5-1-7-2h-2v2c0 1-1 2-1 4l-1 1c-1-1-1-1-2-1h-1 0c-1-1-3-2-4-3l-2 2c-1 2-2 3-3 4h0l-1-1v-1h-1v-2c-1-2-2-5-4-6s-3-1-4-2c0-2 1-2 1-4 1 0 1 1 2 1 0 1-1 1-2 2l1 1c2 0 3 1 4 2l2 5 3-3s1-1 1-2c1 0 1-3 1-4l-6-6c1 0 1 0 1-1 3 2 5 5 8 7l3 2c0-2-1-5-3-7h0l1-1z" class="o"></path><path d="M396 607c1 0 4-3 5-4l1-7 3 3c1 1 2 1 3 1 1-2-1-4-1-6h1c3 2 3 5 6 7v1h-2v2c0 1-1 2-1 4l-1 1c-1-1-1-1-2-1h-1 0c-1-1-3-2-4-3l-2 2c-1 2-2 3-3 4h0l-1-1v-1h-1v-2z" class="D"></path><path d="M408 603c1-1 1-1 3-1h1v2c0 1-1 2-1 4l-1 1c-1-1-1-1-2-1h-1l1-1c-1-1-3-2-4-3l-1-1 1-1c1 0 1 0 2 1 0 1 0 1 1 2h0c0-1 1-1 1-2z" class="K"></path><path d="M408 603c1-1 1-1 3-1h1v2c0 1-1 2-1 4l-1-1c-1-1-2-2-2-4z" class="S"></path><path d="M436 470c0 1 0 1 1 2 2 0 4 2 5 4l1 1 1 1v1c1 0 1 1 2 2l2 4 5 3 4 3h1c2 1 4 2 7 3 1 0 2 1 3 1s2 0 4 1h1v1l-1 1s0 1 1 2l1 4-1 1v-1l-1 1v1 4h0v1 2h-1v3 4l-1-2h0-1c-1 5-2 9-2 14h-1v-1l-1 1-4-8v3c2 6 3 12 7 17l-2 1c0-1-1-2-2-3l-2-2c-1-3-2-7-5-8v-1l-1 2c1 1 2 3 2 4l2 4v1c-1-1-2-2-3-2h-2l-1 2v-1-2c0-1-2-1-3-2h-3c-1-1-2-1-3-2l-2-1c-2-1-4-2-6-4-1-1-1-1-1-2-1 1-2 2-3 2 0-3 0-5-1-8l-1-3s-1-2-1-3 1-3 1-4l3-18v-6c1-6 1-12 2-18z" class="h"></path><path d="M438 507h0c1 1 1 7 1 8h-2c0-3 0-6 1-8z" class="t"></path><path d="M455 531c-1-1-2-4-2-6v-1c2 2 3 5 4 7l-1 2-1-2z" class="n"></path><path d="M437 519v1h1l1-3h0 0c1 1 1 3 0 4v2c-1 1 0 3 0 4v1l-2-1-1 1c0-1 0-1 1-2v-7z" class="N"></path><path d="M431 512l1 2c-1 1-1 2-1 3 1 1 1 1 1 2h1v-1l1 1v-2c0-1 0-2-1-3h1v-3h0c1 1 1 3 2 4v2h0-1v5c0 1 0 1-1 1l-2-1-1-3s-1-2-1-3 1-3 1-4z" class="Q"></path><path d="M431 519c1 1 2 1 2 1 1 1 1 2 2 2 0 1 0 1-1 1l-2-1-1-3z" class="F"></path><path d="M464 542h0 2 0-1v-2c-1 0-1-1-1-2s0-1-1-2v-2-1l-1-2h0c-1-1-1-2-1-2v3 1h0l-1-1c0-1-1-2-1-3-1-1-2-2-2-3v-1l2 1h1l1 1c2 6 3 12 7 17l-2 1c0-1-1-2-2-3z" class="M"></path><path d="M435 522v-5h1l1 2v7c-1 1-1 1-1 2h0c-1 1-2 2-3 2 0-3 0-5-1-8l2 1c1 0 1 0 1-1z" class="V"></path><path d="M449 511c4 3 9 9 12 13v3l-1-1c-2-4-5-7-8-10-1-1-3-3-3-5h0z" class="b"></path><path d="M442 525v-5h1c1 1 1 3 1 4 1 1 3 2 4 4 0 1 1 1 1 2 1 1 2 2 3 2h2s1 0 1-1h0l1 2c1 1 2 3 2 4l2 4v1c-1-1-2-2-3-2h-2l-1 2v-1-2c0-1-2-1-3-2h-3c-1-1-2-1-3-2l-2-1c-2-1-4-2-6-4-1-1-1-1-1-2h0l1-1 2 1v-1c0-1-1-3 0-4v2c1 1 1 1 2 1l1-1z" class="T"></path><path d="M442 525v-5h1c1 1 1 3 1 4 0 4 3 8 7 10h0-1l-4-2c-1-3-3-4-4-7z" class="H"></path><path d="M451 534c3 0 5 5 7 3l2 4v1c-1-1-2-2-3-2h-2l-1 2v-1-2c0-1-2-1-3-2h0c0-1-1-1-2-1l1-2h1z" class="d"></path><path d="M437 527l2 1v-1c0-1-1-3 0-4v2c1 1 1 1 2 1l1-1c1 3 3 4 4 7l4 2-1 2c1 0 2 0 2 1h0-3c-1-1-2-1-3-2l-2-1c-2-1-4-2-6-4-1-1-1-1-1-2h0l1-1z" class="b"></path><path d="M446 532l4 2-1 2c-1-1-3-2-4-3 0-1 1-1 1-1z" class="W"></path><path d="M436 528l1-1c1 3 6 5 6 7-2-1-4-2-6-4-1-1-1-1-1-2h0z" class="U"></path><path d="M442 525c1 3 3 4 4 7 0 0-1 0-1 1-2-2-4-5-6-8 1 1 1 1 2 1l1-1z" class="o"></path><path d="M436 470c0 1 0 1 1 2 2 0 4 2 5 4l1 1 1 1v1c1 0 1 1 2 2l2 4 5 3 4 3c1 1 3 2 4 3l-7-2-1 2h0c-1-1-1-1-3-1h0c-1 0-2-1-3-2h-1v1l-1 1v2 2c1 1 2 2 3 4 1 1 2 4 3 5h0c2 2 4 4 5 6v1h0c1 1 1 1 2 1l2 4h1c0 2 0 3 1 5h0c1 3 3 5 4 8l-1 1-4-8c-3-4-8-10-12-13h0l-6-6c-2-2-3-4-4-6-2-1-2-2-3-4l-1-1h1c2 0 3 2 4 4s3 4 5 6c1 1 3 2 4 3s1 2 2 3v1l1 1h0l1-2c0 1 1 1 1 1-2-3-6-5-8-7-2-3-5-6-6-10-1-1-1-2-2-3v-1-1l-1-1h1l-1-1 1-1h0c-1-1-1-2-1-2 0-1 0-1-1-2h0 0c-1 0 0 3-1 4v3 4l-1 1v-6c1-6 1-12 2-18z" class="S"></path><path d="M462 523c-1 0-1 0-1-1-2-4-4-6-7-10h0 0l2 1h0c1 1 1 1 2 1l2 4h1c0 2 0 3 1 5h0zm-26-28h1c2 3 4 6 6 8s5 4 7 6c0 1-1 1-1 2h0l-6-6c-2-2-3-4-4-6-2-1-2-2-3-4z" class="B"></path><path d="M440 493s0-1 1-1l1 1 3 4c1 1 2 2 3 4 1 1 2 4 3 5h0c-5-3-9-8-11-13z" class="D"></path><path d="M437 481c1-1 1-4 1-5v-1h-1v-1c2 0 3 2 4 3 1 0 2 1 3 2 1 0 1 1 2 2l2 4 5 3 4 3c1 1 3 2 4 3l-7-2-1 2h0c-1-1-1-1-3-1h0c-1 0-2-1-3-2h-1v1l-1 1v2 2l-3-4-1-1c-1 0-1 1-1 1-1-2-2-4-1-6 0-1-1-4-2-6z" class="W"></path><path d="M446 481l2 4h-1c0-1-1-1-1-2v1c-1-1-1-1-2-1 1 1 1 2 1 3h1 1-2c-1-1-1-2-3-3l1-1c1-1 1 0 2 0l1-1z" class="S"></path><path d="M437 481c2 2 3 5 6 6 2 0 3 2 4 3 2 1 5 2 7 2l-1 2h0c-1-1-1-1-3-1h0c-1 0-2-1-3-2h-1v1l-1 1v2 2l-3-4-1-1c-1 0-1 1-1 1-1-2-2-4-1-6 0-1-1-4-2-6z" class="K"></path><path d="M442 490v-2h1c1 1 1 2 3 3v1l-1 1c-1 0-1-1-1-1v-1c-1 0-1-1-2-1z" class="N"></path><path d="M442 490c1 0 1 1 2 1v1s0 1 1 1v2 2l-3-4v-3z" class="G"></path><path d="M458 491c2 1 4 2 7 3 1 0 2 1 3 1s2 0 4 1h1v1l-1 1s0 1 1 2l1 4-1 1v-1l-1 1v1 4h0v1 2h-1v3 4l-1-2h0-1c-1 5-2 9-2 14h-1v-1c-1-3-3-5-4-8h0c-1-2-1-3-1-5h-1l-2-4c-1 0-1 0-2-1h0v-1c-1-2-3-4-5-6h0c-1-1-2-4-3-5-1-2-2-3-3-4v-2-2l1-1v-1h1c1 1 2 2 3 2h0c2 0 2 0 3 1h0l1-2 7 2c-1-1-3-2-4-3h1z" class="C"></path><path d="M450 493h0c2 0 2 0 3 1h0l4 3h0c-3-1-5-2-7-4z" class="p"></path><path d="M461 518c0-1 0-1 1-1 0 1 0 3 1 3 0 1 1 1 1 1v1c-1 0-1 1-2 1-1-2-1-3-1-5z" class="j"></path><path d="M456 512l1 1h1c-1-2-1-3-3-4 0-1-2-2-2-3v-1c2 1 4 5 5 6 2 2 3 4 4 6-1 0-1 0-1 1h-1l-2-4c-1 0-1 0-2-1h0v-1z" class="f"></path><path d="M445 495h1v-1h1c1 3 5 9 4 12-1-1-2-4-3-5-1-2-2-3-3-4v-2z" class="X"></path><path d="M472 496h1v1l-1 1s0 1 1 2l1 4-1 1v-1l-1 1v1 4h0v1 2h-1v3 4l-1-2h0-1v-5c0-2 0-4-1-6 0-3-1-6-2-8 0-1 0-1 1-2 1 0 3 1 5 0v-1z" class="Z"></path><path d="M472 510v1h-1v-1h1z" class="g"></path><path d="M469 513c0-1 0-3 1-4v1c0 1 1 2 0 3l1 3v4l-1-2h0-1v-5z" class="E"></path><path d="M470 518v-1c0-1-1-3 0-4h0l1 3v4l-1-2h0z" class="Q"></path><path d="M466 499h2v2l2 1v1h0c1 1 0 0 0 1 1 2 1 5 0 6v-1c-1 1-1 3-1 4 0-2 0-4-1-6 0-3-1-6-2-8z" class="a"></path><path d="M472 496h1v1l-1 1s0 1 1 2l1 4-1 1v-1l-1 1-1-2h0c-1-2-1-3-3-4h-2c0-1 0-1 1-2 1 0 3 1 5 0v-1z" class="g"></path><path d="M473 500l1 4-1 1v-1c-1-2-1-3 0-4z" class="Z"></path><path d="M436 447c0-5 1-8 5-11 1-2 6-5 8-4h1c-1 0-2 1-3 1-4 2-8 6-7 11 0 2 2 5 3 6 2 2 3 2 5 2l1 2c5 0 9-3 14-6v2l1 1h0v1h1c2-1 6-1 8-1h4v-2l1 2h1 1c1 0 2 1 3 1v1h0c2 2 3 4 5 6l2 2c1 1 2 3 2 5 6 6 12 10 20 12h-4s-1 1-2 1h0l-5 1c-5 1-9 4-13 7-2 1-5 3-6 5v1c-1 0-3 1-4 1s-1 1-2 2l-3 1v-1h-1c-2-1-3-1-4-1s-2-1-3-1c-3-1-5-2-7-3h-1l-4-3-5-3-2-4c-1-1-1-2-2-2v-1l-1-1-1-1c-1-2-3-4-5-4-1-1-1-1-1-2v-23z" class="C"></path><path d="M465 482h0c3-1 5-1 7-1 1 0 2 0 2 1-1 0-4 1-5 0s-2 0-3 0h-1z" class="e"></path><path d="M455 470l1 1h0c0 1 1 2 2 3h1l1 1c-2 1-4 1-5 1-1-1-2-1-3-1h0l1-1c1-1 1-1 0-2v-1h2v-1z" class="l"></path><path d="M457 468c2 0 5 4 7 5 2 0 3 1 4 2h-2 0c0-1-1-1-2-1l-1-1c-1 0-1 0-2-1s-1-1-2-1v3h0-1c-1-1-2-2-2-3h0l-1-1-1-1v-1c1 0 2 1 3 0z" class="b"></path><path d="M449 475h0v-1c-1-1-1-3-2-4 2 1 3 3 5 4v1h0v1c4 3 12 1 17 1-3 2-7 3-11 3-2 0-4-1-6-2l-1-1c-1 0-2-1-2-2z" class="g"></path><path d="M443 474l2 2c1-1 1-1 2-1h2c0 1 1 2 2 2l1 1c2 1 4 2 6 2l-1 1 6 1h2 1c1 0 2-1 3 0l-2 1v1 1c-1 1-2 1-2 2h1l1 1-6 2v1c-1 0-2-1-3-1v1h-1l-4-3-5-3-2-4c-1-1-1-2-2-2v-1l-1-1-1-1c0-1 0-1 1-2z" class="P"></path><path d="M458 483c3 1 5 1 7 1-1 0-3 0-5 1h0 0c1 1 2 1 2 1-1 1-1 1-2 1h-1-1c-1 0-2-1-4-2h0v-1c1 0 2 1 3 1l2-1-1-1z" class="X"></path><path d="M465 482h1c1 0 2-1 3 0l-2 1v1 1l-2-1c-2 0-4 0-7-1l-1-1h6 2z" class="j"></path><path d="M447 478l2 2h1c1 0 1 0 2 1 1 0 1 0 1 1v-1l1-1c1 1 2 1 3 1l6 1h-6l1 1 1 1-2 1c-1 0-2-1-3-1v1h0c-3-1-6-4-8-7h1z" class="R"></path><path d="M450 480c1 0 1 0 2 1 1 0 1 0 1 1 1 0 1 1 3 0v1c-1 1-1 1-2 1-1-1-3-2-4-4z" class="P"></path><path d="M443 474l2 2c1-1 1-1 2-1h2c0 1 1 2 2 2l1 1c2 1 4 2 6 2l-1 1c-1 0-2 0-3-1l-1 1v1c0-1 0-1-1-1-1-1-1-1-2-1h-1l-2-2h-1-2l-1-1-1-1c0-1 0-1 1-2z" class="Y"></path><path d="M443 474l2 2 2 2h-1-2l-1-1-1-1c0-1 0-1 1-2z" class="h"></path><path d="M444 478h2c2 3 5 6 8 7 2 1 3 2 4 2h1 1 2c0 1-1 2-1 3v1c-1 0-2-1-3-1v1h-1l-4-3-5-3-2-4c-1-1-1-2-2-2v-1z" class="D"></path><path d="M453 488h2c1 0 2 1 3 2v1h-1l-4-3z" class="K"></path><path d="M463 448v2l1 1h0v1 2 1l-1 2c0 2 0 4 1 6h0l1 1 1 1v-2c2 1 3 1 4 0v1 1 1h3-3c-1 2-1 2-2 3l4 5c-3-1-7-3-9-6l-3-6-1-1c-1-1-2-1-3-1l-7-1c2 2 2 2 2 5l-2-2c-3-1-5-5-7-8h7c5 0 9-3 14-6z" class="Z"></path><path d="M464 463v2h-1v-3l1 1h0z" class="E"></path><path d="M466 465v-2c2 1 3 1 4 0v1 1 1h3-3c-1 2-1 2-2 3l-1-2c0 1 0 1-1 1h-1c0-2 0-2 1-3z" class="X"></path><path d="M467 467v-1h3c-1 2-1 2-2 3l-1-2z" class="O"></path><path d="M463 448v2l1 1h0v1 2 1l-1 2s-1 0-1-1h-1v1h-10v1h-4c0 1 1 2 2 2l1 1-1 1c-3-1-5-5-7-8h7c5 0 9-3 14-6z" class="C"></path><path d="M463 453v-3l1 1h0v1 2l-1-1z" class="u"></path><path d="M462 456l1-3 1 1v1l-1 2s-1 0-1-1z" class="E"></path><path d="M447 458c-1 0-2-1-2-2h0l6 1v1h-4z" class="v"></path><path d="M436 447c0-5 1-8 5-11 1-2 6-5 8-4h1c-1 0-2 1-3 1-4 2-8 6-7 11 0 2 2 5 3 6 2 2 3 2 5 2l1 2h-7c2 3 4 7 7 8l2 2c2 2 4 3 6 4-1 1-2 0-3 0v1l1 1v1h-2v1c1 1 1 1 0 2l-1 1v-1c-2-1-3-3-5-4 1 1 1 3 2 4v1h0-2c-1 0-1 0-2 1l-2-2c-1 1-1 1-1 2-1-2-3-4-5-4-1-1-1-1-1-2v-23z" class="AA"></path><path d="M441 472c-1 0-1 0-2-1v-4h1c1 2 2 2 3 3l-1 1 1 1h-1-1z" class="T"></path><path d="M443 470c2 1 3 3 4 5-1 0-1 0-2 1l-2-2-2-2h1 1l-1-1 1-1z" class="R"></path><path d="M477 449l1 2h1 1c1 0 2 1 3 1v1h0c2 2 3 4 5 6l2 2c1 1 2 3 2 5 6 6 12 10 20 12h-4s-1 1-2 1h0l-5 1c-5 1-9 4-13 7-2 1-5 3-6 5v1c-1 0-3 1-4 1s-1 1-2 2l-3 1v-1h-1c-2-1-3-1-4-1s-2-1-3-1c-3-1-5-2-7-3v-1c1 0 2 1 3 1v-1l6-2-1-1h-1c0-1 1-1 2-2v-1-1l2-1c1 1 4 0 5 0 0-1-1-1-2-1l3-1c2 0 5-2 6-4 1-1 1-2 1-3h1v-6s0-1-1-1c-2-1-6-1-9 0h-3v-1-1-1c-1 1-2 1-4 0v2l-1-1-1-1h0c-1-2-1-4-1-6l1-2v-1-2h1c2-1 6-1 8-1h4v-2z" class="T"></path><path d="M483 467l2 1c1 0 3 0 4 1s1 2 1 2l-2 1h-1c-1 0-2 2-3 2v-1h-1v-6z" class="H"></path><path d="M483 467l2 1v3l-1 2h-1v-6z" class="j"></path><path d="M479 490c3-2 6-3 8-4s2-2 3-3c2-3 5-4 8-4 2-1 6-1 8 0l-5 1c-5 1-9 4-13 7-2 1-5 3-6 5v1c-1 0-3 1-4 1s-1 1-2 2l-3 1v-1h-1c-2-1-3-1-4-1s-2-1-3-1l6-2c3 0 6-1 8-2z" class="H"></path><path d="M468 495c1-2 6-2 8-3 1 1 2 1 2 2-1 0-1 1-2 2l-3 1v-1h-1c-2-1-3-1-4-1z" class="V"></path><path d="M480 453h1 2c2 2 3 4 5 6l2 2c1 1 2 3 2 5 6 6 12 10 20 12h-4c-1 0-2-1-3-1-2-1-5-2-7-4-3-2-5-5-8-7l-12-6h0 0l1-3h2c0-1-2-2-2-3v-1h1z" class="P"></path><path d="M480 453h1 2c2 2 3 4 5 6l2 2c1 1 2 3 2 5-2-1-3-3-5-4l-6-5c0-1-2-2-2-3v-1h1z" class="j"></path><path d="M480 453h1 2c2 2 3 4 5 6l2 2h-1c-1 0-7-6-9-8z" class="I"></path><path d="M477 449l1 2h1 1c1 0 2 1 3 1v1h0-2-1-1v1c0 1 2 2 2 3h-2l-1 3h0 0c-3 0-5 2-7 3h-1c-1 1-2 1-4 0v2l-1-1-1-1h0c-1-2-1-4-1-6l1-2v-1-2h1c2-1 6-1 8-1h4v-2z" class="m"></path><path d="M477 449l1 2h1 1c-1 1-1 1-3 2v-1c-1-1-4 0-5 0v-1h1 4v-2z" class="H"></path><path d="M480 451c1 0 2 1 3 1v1h0-2-1-1v1c0 1 2 2 2 3h-2c-1 0-2-1-2-2s-1-1 0-2c2-1 2-1 3-2z" class="Z"></path><path d="M464 452h1c2-1 6-1 8-1h-1v1c-2 0-4 0-6 1v2h3c1-1 2-1 2 0h1c-1 0-1 1-2 1 0 1-1 0-2 1-1-1-3-1-4-2v-1-2z" class="D"></path><path d="M470 462h-1c-2 0-2 0-3-1 0-2 0-2 1-3 1 0 1 1 3 1-1-1-1-1-1-2 2 0 3 2 4 3h5 0c-3 0-5 2-7 3l-1-1z" class="X"></path><path d="M473 460h5 0c-3 0-5 2-7 3l-1-1h-2v-1l1-1h4z" class="l"></path><path d="M488 472l2-1 1 2-1 1v1h3v1c-3 3-8 8-12 10l-3 2h0c1 1 1 0 1 1h0v1c-2 1-5 2-8 2l-6 2c-3-1-5-2-7-3v-1c1 0 2 1 3 1v-1l6-2-1-1h-1c0-1 1-1 2-2v-1-1l2-1c1 1 4 0 5 0 0-1-1-1-2-1l3-1c2 0 5-2 6-4 1-1 1-2 1-3h1 1v1c1 0 2-2 3-2h1z" class="B"></path><path d="M478 482h1c2 0 4-2 7-4l1 1c-2 1-4 4-6 5-1 0-2 0-2 1h-1-2-2c2 0 2-1 3-2l1-1z" class="D"></path><path d="M467 484c4 0 7-1 11-2h0l-1 1c-4 2-7 4-11 4h-1c0-1 1-1 2-2v-1z" class="Q"></path><path d="M484 473v1c1 0 2-2 3-2h1c-3 3-9 9-13 10h-1c0-1-1-1-2-1l3-1c2 0 5-2 6-4 1-1 1-2 1-3h1 1z" class="a"></path><path d="M466 487c4 0 7-2 11-4-1 1-1 2-3 2h2 2 1v1h2l-3 2h0c1 1 1 0 1 1h0v1c-2 1-5 2-8 2l-6 2c-3-1-5-2-7-3v-1c1 0 2 1 3 1v-1l6-2-1-1z" class="F"></path><path d="M466 487c4 0 7-2 11-4-1 1-1 2-3 2h2 2c-2 1-4 2-6 2l-5 1-1-1z" class="O"></path><path d="M479 486h2l-3 2h0c1 1 1 0 1 1h0v1c-2 1-5 2-8 2h-5l1-1c-1 0-3 1-5 1v-1c1-1 3-1 4-1 5-1 9-2 13-4z" class="I"></path><path d="M467 491c2 0 4-1 6-1 2-1 3-1 5-2h0c1 1 1 0 1 1h0v1c-2 1-5 2-8 2h-5l1-1z" class="o"></path><path d="M402 399c0-2-1-6 0-7h0c0-3-1-9 0-11l16 23h0c1 2 1 19 1 22 0 18 1 37-1 55 0 8-2 16-4 24l-36-51-1-1s5-7 6-7c6-10 12-20 15-31 2-4 3-9 4-14 2 4 6 7 9 10 1 1 2 3 3 3l-12-15z" class="C"></path><path d="M321 473l2 2 1-1 2 5c4 4 9 7 14 10 8 5 16 11 22 20 1-1 1-1 1-2v-1l-4-10v-1c1 1 2 1 2 2 1 2 1 3 2 5l1 3c0-1 1-1 1-2l1 2c0 2 2 4 3 6v1c0 2 1 3 2 4 2 1 3 6 4 8h0l1 1h1v-6c1 0 1 0 2 1h0 2v-1h0l1-1c1 0 1 1 2 1 0 1 1 1 2 1h2l1-1h1 0c2 1 5 1 7-1h0 1l-1 1-1 1v2l1-1c1-2 2-2 3-3h3 2c0-2 1 0 2-1h2l2 4c0 1 1 2 1 3l3 7c0 2 2 5 2 7 0 1 0 1 1 2s1 2 1 4c0 1 1 2 1 3 0 0 1 1 1 2v1c1 1 1 1 2 3v1h-3 0c1 2 2 2 3 3l-1 2-1-1-1 1 1 1h0v1c2 3 5 5 6 8l2 3v3c0 1 0 1 1 1h0l1 2h0c-3 1-5 1-8 0v1h-1-2c-1 0 0 0-1 1l-2-1c-5-2-10-4-15-7-6-4-11-10-16-16-2-2-4-5-6-8s-2-8-5-11c-1-2-3-1-4-3-3-4-5-9-8-14l-9-9-7-7-8-8v-1c-1-1-3-1-4-2s-2-1-3-1l-1-1h-3-5-4v-1h-3 0v-1h-2l2-1v-1h-2 0c-1 0-1 0-2 1h0c-1 0-1 0-2-1 0 0 0-1-1-1l-1-1c-1 1-1 2-3 2l-2-4c1-2 2-3 3-4h2c4 0 6 1 10 2h1 1v-1c-1-1-2-1-3-2h1s1 0 2 1h0c1 0 1 1 2 1h1c0 1 1 1 1 1h1-1l-2-2-2-2 1-1v-1l-1-1s0-1 1-1z" class="b"></path><path d="M365 509h0c2 1 2 2 3 3l1-1v1c0 1 0 2-1 3l-3-6z" class="G"></path><path d="M405 568s1 0 1 1l2 2-1 1c-1-1-3-2-4-3v-1h2z" class="r"></path><path d="M364 505c0-1 1-1 1-2l1 2c0 2 2 4 3 6l-1 1c-1-1-1-2-3-3h0c0-1 0-1-1-2v-2z" class="W"></path><path d="M364 505c0-1 1-1 1-2l1 2v2h-2v-2z" class="T"></path><path d="M320 486c-3-1-8-4-10-3-1 0-2 1-2 1h-1c1-1 1-1 1-2 0 0 1-1 2-1 3 0 7 2 10 4v1z" class="M"></path><path d="M369 512c0 2 1 3 2 4 2 1 3 6 4 8h0l-1 1c-2-3-4-6-6-10 1-1 1-2 1-3z" class="Q"></path><path d="M380 538l1-1 7 8c-1 1-1 2-2 3v4h-1v-2-6c-2-2-4-4-5-6z" class="T"></path><path d="M308 485c3 0 5-1 8 0 1 1 2 1 4 2v-1h0v-1c1 1 3 2 4 3h-8v-1h-2 0c-1 0-1 0-2 1h0c-1 0-1 0-2-1 0 0 0-1-1-1l-1-1z" class="S"></path><path d="M395 561h2c1 0 1 1 1 1 1 0 1-1 1-1 1 1 2 1 3 1h0v2c1 0 2 1 3 2v1 1h-2v1l-3-3-5-5z" class="F"></path><path d="M402 562h0v2h-1l-1-1v-1h2z" class="M"></path><path d="M400 566l1-1c1 0 1 0 2 1l2 1v1h-2v1l-3-3z" class="W"></path><path d="M316 488h8v2c2-1 6-1 7 0v1h-3-5-4v-1h-3 0v-1h-2l2-1z" class="F"></path><path d="M316 490h6 3 0c-1 1-1 1-2 1h-4v-1h-3z" class="Q"></path><path d="M316 488h8v2h-2-6 0v-1h-2l2-1z" class="m"></path><path d="M405 566c1 1 3 3 5 3h2v1l2 2v2c3 2 6 3 9 4v1h-1-2c-5-2-9-4-13-7l1-1-2-2c0-1-1-1-1-1v-1-1z" class="W"></path><path d="M405 566c1 1 3 3 5 3h2v1l2 2v2c-1 0-2-2-4-3-1-1-2-2-4-2 0-1-1-1-1-1v-1-1z" class="i"></path><path d="M317 478h1s1 0 2 1h0c1 0 1 1 2 1h1c0 1 1 1 1 1h1-1l-2-2 13 8c-1 0-1 1-2 1l-2-1h0-2 0v1h0c-4-2-7-5-11-7h1 1v-1c-1-1-2-1-3-2z" class="s"></path><path d="M320 480c4 2 7 4 11 7h0-2 0v1h0c-4-2-7-5-11-7h1 1v-1z" class="Q"></path><path d="M321 473l2 2 1-1 2 5c4 4 9 7 14 10 8 5 16 11 22 20 2 3 5 6 6 10-4-4-6-9-10-13-6-8-15-14-23-19l-13-8-2-2 1-1v-1l-1-1s0-1 1-1z" class="S"></path><path d="M321 473l2 2 1-1 2 5c-2 0-3-2-5-3v-1l-1-1s0-1 1-1zm8 15h0v-1h0 2 0l2 1 11 8c3 2 5 3 7 5s4 3 5 4c3 3 5 7 8 10 1 2 3 4 5 6 1 2 3 5 5 7 2 3 4 6 7 9l-1 1c-10-6-15-17-22-26-2-3-5-6-9-9l-9-9c-3-2-7-4-11-6z" class="F"></path><path d="M400 546h2v1h2 0 1l1 1h0l-1 1h2v3h-1v-2h-1v1c0 1-1 1-1 2h0 2s1 0 1 1h0 0v2c0-1-1-2-2-2 0 0-1 0-1 1l-1 1h0c-1 1-1 2-2 4l-2 1s0 1-1 1c0 0 0-1-1-1h-2l-3-3c-1 1-1 1-1 2h-1c-1-1-1-2-2-4s-2-4-1-7c1-1 2-2 3-2 1-1 3 0 4 0v2l1 1s1-1 1-2h1 1 1l1-1v-1z" class="q"></path><path d="M395 551c-1 1-1 3-2 4-2-1-2-1-3-2v-3c1-1 3-1 4-1l1 1v1z" class="C"></path><path d="M400 546h2v1h2 0 1l1 1h0l-1 1h2v3h-1v-2h-1v1c0 1-1 1-1 2l-2-2-1 2h0c-1 0-1-1-3 0v-1h0c-1 0-1 0-2-1h-1v-1s1-1 1-2h1 1 1l1-1v-1z" class="F"></path><path d="M395 550s1-1 1-2h1 1c-1 1-1 2 0 3v1h0c-1 0-1 0-2-1h-1v-1z" class="Q"></path><path d="M400 546h2v1h2 0 1l1 1h-7l1-1v-1z" class="c"></path><path d="M406 548l2-1c5-2 6-4 9-9 0 1 0 1 1 2s1 2 1 4c0 1 1 2 1 3 0 0 1 1 1 2v1c1 1 1 1 2 3v1h-3 0c1 2 2 2 3 3l-1 2-1-1-1 1 1 1h0v1c2 3 5 5 6 8l2 3v3c0 1 0 1 1 1h0l1 2h0c-3 1-5 1-8 0s-6-2-9-4v-2l-2-2v-1h-2c-2 0-4-2-5-3s-2-2-3-2v-2h0c-1 0-2 0-3-1l2-1c1-2 1-3 2-4h0l1-1c0-1 1-1 1-1 1 0 2 1 2 2v-2h0 0c0-1-1-1-1-1h-2 0c0-1 1-1 1-2v-1h1v2h1v-3h-2l1-1z" class="U"></path><path d="M403 556h2v-1l1 1c0 1-1 2-1 3v-1h-1c-1 1-2 1-3 2 1-2 1-3 2-4z" class="H"></path><path d="M416 550h0l1 2c-1 1-1 0-1 1-1 2-2 2-4 2h-3l-1 1v-1h0v-1h1 1l-1-2 1-1c1 1 2 1 3 2v-1h1l2-2z" class="d"></path><path d="M406 548l2-1c5-2 6-4 9-9 0 1 0 1 1 2-2 1-3 3-3 4v1c0 1 1 1 0 2v2h1v1l-2 2h-1 0l-1-1v-3l-2 2c-1 0-2-1-2-1 0 1 0 2-1 3v-3h-2l1-1z" class="G"></path><path d="M413 552c0-3 0-5 2-8v1c0 1 1 1 0 2v2h1v1l-2 2h-1 0z" class="b"></path><path d="M418 540c1 1 1 2 1 4 0 1 1 2 1 3 0 0 1 1 1 2v1c1 1 1 1 2 3l-1-1c-1 1-4 1-5 0l-1-2h0v-1h-1v-2c1-1 0-1 0-2v-1c0-1 1-3 3-4z" class="I"></path><path d="M416 549h0c0-1 0-1 1-2v-2h1v2c1 1 1 1 1 2h2v1 1h-1c0-1-1-1-2-1 0 0 0-1-1-1l-1 1h0v-1z" class="C"></path><path d="M416 550l1-1c1 0 1 1 1 1 1 0 2 0 2 1h1v-1c1 1 1 1 2 3l-1-1c-1 1-4 1-5 0l-1-2z" class="F"></path><path d="M421 550c1 1 1 1 2 3l-1-1h-2l-1-1h1 1v-1z" class="S"></path><path d="M422 552l1 1v1h-3 0c1 2 2 2 3 3l-1 2-1-1-1 1 1 1h0v1c-1 0-2 0-3-1l-1-1h-1c-2 0-4 1-6 1-1 3-1 5 1 8l1 2v-1h-2c-2 0-4-2-5-3s-2-2-3-2v-2h0c-1 0-2 0-3-1l2-1c1-1 2-1 3-2h1v1h1l2-3 1-1h3c2 0 3 0 4-2 0-1 0 0 1-1 1 1 4 1 5 0z" class="c"></path><path d="M402 562c3 1 6 4 8 7-2 0-4-2-5-3s-2-2-3-2v-2z" class="T"></path><path d="M410 560c2-2 3-3 5-4v-1l1-1h4 0c1 2 2 2 3 3l-1 2-1-1-1 1 1 1h0v1c-1 0-2 0-3-1l-1-1h-1c-2 0-4 1-6 1z" class="L"></path><path d="M417 558l1-1c1 0 1 0 2 1h1 0l-1 1c-1 0-2-1-3-1z" class="U"></path><path d="M416 559h-2v-1h1l1-1s1 0 1 1c1 0 2 1 3 1l1 1h0v1c-1 0-2 0-3-1l-1-1h-1z" class="d"></path><path d="M416 559h1l1 1c1 1 2 1 3 1 2 3 5 5 6 8l2 3v3c0 1 0 1 1 1h0l1 2h0c-3 1-5 1-8 0s-6-2-9-4v-2l-2-2-1-2c-2-3-2-5-1-8 2 0 4-1 6-1z" class="q"></path><path d="M427 569l2 3h-3l-1-1v-1h2v-1z" class="F"></path><path d="M426 572h3v3l-1 1c-4 1-8 0-11-2 1 0 1-1 2-1 1 1 3 2 5 2 1 0 1 0 1-1v-1h1v1c0 1 0 1-1 2 1 0 2-1 3-1v-1-1h-1l-1-1z" class="S"></path><path d="M414 572c1 1 2 2 3 2 3 2 7 3 11 2l1-1c0 1 0 1 1 1h0l1 2h0c-3 1-5 1-8 0s-6-2-9-4v-2z" class="I"></path><path d="M419 573l-6-7c1-1 0-1 1-1h2c1 1 1 2 2 3s2 1 3 2 3 2 4 3v1c0 1 0 1-1 1-2 0-4-1-5-2z" class="B"></path><path d="M405 518c0-2 1 0 2-1h2l2 4c0 1 1 2 1 3l3 7c0 2 2 5 2 7-3 5-4 7-9 9l-2 1h0l-1-1h-1 0-2v-1h-2v1l-1 1h-1-1v-2c-7-3-13-8-17-13l-6-8 1-1 1 1h1v-6c1 0 1 0 2 1h0 2v-1h0l1-1c1 0 1 1 2 1 0 1 1 1 2 1h2l1-1h1 0c2 1 5 1 7-1h0 1l-1 1-1 1v2l1-1c1-2 2-2 3-3h3 2z" class="c"></path><path d="M397 535h1v1l1 1v1c1 0 2 0 3-1h0 2l-1 1c0 1 2 2 1 3-2 1-5-1-7-2 1 0 1-1 1-2-1 0-1-1-1-1v-1z" class="L"></path><path d="M377 519c1 0 1 0 2 1h-1c0 4 1 8 3 11l-1 2-6-8 1-1 1 1h1v-6z" class="F"></path><path d="M381 519h0c0 1 1 3 1 3 0 3 1 4 3 6 1 1 2 2 3 2 0 1 1 2 1 3v1c-5-4-8-8-10-14h2v-1z" class="U"></path><path d="M405 535c1-1 4-3 5-3s1 0 2 1 2 2 2 4-1 4-2 6l-2 1-1-1c1 0 1-1 2-2s2-3 2-5c-1-1-1-2-2-2h-2v1 2c0 1-1 2-2 3l-1-1s1-1 1-2h-1l-1-2z" class="S"></path><path d="M381 531c4 6 11 13 18 14 3 0 6 0 10-2l1 1c-3 1-5 2-8 2h-2v1l-1 1h-1-1v-2c-7-3-13-8-17-13l1-2z" class="U"></path><path d="M397 546h3v1l-1 1h-1-1v-2z" class="C"></path><path d="M388 530c1 1 3 1 4 1h1 2c1 0 2 0 3 1l1 1v1l-1 1h-1v1s0 1 1 1c0 1 0 2-1 2-3-1-5-3-8-5v-1c0-1-1-2-1-3z" class="E"></path><path d="M395 531c1 0 2 0 3 1l-1 1c-1 0-2-1-4-2h2z" class="u"></path><path d="M389 533l2 1 1-1h1v1h-1c0 1 0 1 1 1s2 1 3 0h1v1s0 1 1 1c0 1 0 2-1 2-3-1-5-3-8-5v-1z" class="N"></path><path d="M381 519l1-1c1 0 1 1 2 1 0 1 1 1 2 1h2 4l-2 1v4c0 1 1 2 1 4 1 0 2 1 4 2h-2-1c-1 0-3 0-4-1-1 0-2-1-3-2-2-2-3-3-3-6 0 0-1-2-1-3z" class="I"></path><path d="M382 522c1 1 2 2 3 2v2s1 1 2 1h0c2 1 3 0 4 2 1 0 2 1 4 2h-2-1c-1 0-3 0-4-1-1 0-2-1-3-2-2-2-3-3-3-6z" class="D"></path><path d="M405 518c0-2 1 0 2-1h2l2 4c0 1 1 2 1 3l3 7c0 2 2 5 2 7-3 5-4 7-9 9l-2 1h0l-1-1h-1 0-2v-1c3 0 5-1 8-2l2-1c1-2 2-4 2-6s-1-3-2-4-1-1-2-1-4 2-5 3l1 2h-2-2 0c-1 1-2 1-3 1v-1l-1-1v-1l1-1v-1l-1-1c-1-1-2-1-3-1-2-1-3-2-4-2 0-2-1-3-1-4v-4l2-1h-4l1-1h1 0c2 1 5 1 7-1h0 1l-1 1-1 1v2l1-1c1-2 2-2 3-3h3 2z" class="b"></path><path d="M402 537l3-2 1 2h-2-2z" class="M"></path><path d="M399 533c1 0 2 0 3 1h2c-2 0-3 2-5 3l-1-1v-1l1-1v-1z" class="S"></path><path d="M411 524h1l3 7h-1v-1h-1c-1-1-2-3-2-4v-2zm-19-4h1 1v1c0 2 0 3 1 5h1c1 1 2 2 2 3v1l-1-1c-3-2-4-5-4-8l-1-1z" class="O"></path><path d="M392 520l1 1c0 3 1 6 4 8l1 1c2 2 4 2 7 3l-1 1h-2c-1-1-2-1-3-1l-1-1c-1-1-2-1-3-1-2-1-3-2-4-2 0-2-1-3-1-4v-4l2-1h0z" class="M"></path><path d="M405 518c0-2 1 0 2-1h2l2 4c0 1 1 2 1 3h-1c-2-1-4-3-6-2-1 1-2 1-2 2 2 1 3 2 5 4-2-1-3-2-5-3h-4l-3-3 1-1c1-2 2-2 3-3h3 2z" class="W"></path><path d="M405 518c0-2 1 0 2-1h2l2 4h-1c-2-2-3-2-5-3z" class="B"></path><defs><linearGradient id="K" x1="240.497" y1="557.877" x2="296.685" y2="555.728" xlink:href="#B"><stop offset="0" stop-color="#0e1011"></stop><stop offset="1" stop-color="#271d1d"></stop></linearGradient></defs><path fill="url(#K)" d="M265 525h0l6 9c1 0 2 1 4 2h0 4c6-1 11-2 14 5 0 1 1 4 0 6l3 3c1 1 2 3 2 4 1 2 1 2 0 3v1c-1-1-1-1-2-1 1 2 3 8 2 11 0 2-1 4-1 6h-1s-1-1-1-2c-1-1-2-2-4-3v1c1 0 2 1 2 2 1 1 7 13 7 14s0 1-1 1l1 4 1 4h-2v1 1 3c-1 0-2 1-3 2l-2 1v1c-1 0-2-2-3-3-2-1-6-5-8-5h-1c1-2 1-4 1-7v-1c-1-1-1-2-2-2-1-1 0-1-1-2h-1v-1l1-1c1-1 2-2 2-3s0-1-1-1l-2 3c0-1-1-1-2-1-1 2-3 8-5 9l-1 1c-1 0-2 1-4 1s-4 3-7 2h-1v-1s0-1-1-1c-1-1-2-1-3-2l1-2v-1l-3 3-1 2-1 1c-1 0-1 0-1-1-3-1-6-4-7-7 0-2 0-3 1-4v-1c5-8-1-15-4-23-2-3-3-6-4-9h2v2h1 0v-1c0-1 0-1 1-2h4c4-1 8-2 12-4h1c1-1 2-2 3-4 0-1 2-3 3-5h0l1-1h0c0-2 0-6 1-7z"></path><path d="M261 552h1v2l-1 2-1-1c0-1 1-2 1-3z" class="P"></path><path d="M262 552h0 4v1 1h-1l1-1h-1c-1 0-2 1-3 1v-2z" class="O"></path><path d="M275 543h2l2 2c-2 0-4 0-5-1v-1h1z" class="i"></path><path d="M273 547h1v5c-2 0-3 1-4 1l-1-1h2l1-1c0-2 1-3 1-4z" class="O"></path><path d="M279 547c1 1 1 1 1 2s-1 1-1 2l-1 1h-1v-1-1-1-1-1c1 1 1 1 2 0zm14 8l-4-4c-1-1-2-2-3-2 0-1 2-1 3-1s1 1 2 1l2 2h-1c1 1 2 2 2 3s0 1-1 1z" class="H"></path><path d="M293 551c1 1 3 2 5 3h0c1 2 1 2 0 3v1c-1-1-1-1-2-1s-2-2-3-2c1 0 1 0 1-1s-1-2-2-3h1z" class="y"></path><path d="M275 543l1-1c3 0 5 1 7 2l1 1c-1 0-1 1-2 1l1 1h-1 0c-1-1-2-1-3-2h0l-2-2h-2z" class="K"></path><path d="M280 539c2-1 4-1 6 0 2 0 2 0 4 1 0 1 1 1 2 1h1c0 1 1 4 0 6-1-1-2-2-3-2v-1h1v-1c-1-1-3-3-5-4-1-1-4 1-6 0z" class="i"></path><path d="M254 555h0c-2 0-4 0-5-1l1-1h2c1-1 2-1 3-1 2-1 4-1 6 0 0 1-1 2-1 3v1h1c-1 0-2 0-3-1h-1l-1-1h-1l-1 1z" class="J"></path><path d="M254 555l1-1h1l1 1h1c1 1 2 1 3 1h0 1c1-1 2 0 3 0h1c1 0 1 1 2 1 1 1 2 1 3 1 0 1 1 1 1 2l1-1v-1h0c0 1 0 2 1 3l1-1-2-2h1v1c1 0 1 0 1 1s1 2 1 3l-1-1h-3 0-1c-3-2-6-4-9-4v-1h-1v1c-2-1-5-2-7-3z" class="i"></path><path d="M272 562c-2-2-5-3-5-4 2 0 5 2 7 3l1 1h-3 0z" class="j"></path><path d="M280 539h-1c-1-1-3-1-4-1h0l-2-1h-1c-1-1-1-2-1-3h0c1 0 2 1 4 2h0 4c6-1 11-2 14 5h-1c-1 0-2 0-2-1-2-1-2-1-4-1-2-1-4-1-6 0z" class="D"></path><path d="M251 557h2v1c2 0 2 2 3 3 1 2 2 2 2 4h1v-1-1l-2-2c0-1 0-1-1-2v-1h0c4 1 8 3 10 6v1h0c-1 0-1-1-2-1h-1c-1 0-1 1-2 2l1 1h1 1v-1l1 2c0 2-1 3-1 5v2c-1 2-2 4-4 6v-2l-1 1v-4c0-2 0-5-1-7-1 0-1-2-1-3s-1-1-1-2c-1-1-1-2-2-3h1c-1-2-3-3-4-4z" class="P"></path><path d="M261 572v-1l-1-1v-1-4h1v2c1 2 2 1 3 1h1c0 2-1 3-1 5v2-3h-1v2h-1c0-1 0-1-1-2h0z" class="K"></path><path d="M261 572h0c1 1 1 1 1 2h1v-2h1v3c-1 2-2 4-4 6v-2l-1 1v-4l1-2h0 1v-2z" class="R"></path><path d="M259 576l1-2h0v5l-1 1v-4z" class="O"></path><path d="M261 558v-1h1v1c3 0 6 2 9 4 4 5 6 10 6 16v2c-1 2-3 8-5 9l-1 1c-1 0-2 1-4 1s-4 3-7 2c2-1 3-2 4-3l-1-2c1-2 1-4 2-6 1-1 2-1 3-2l2-2h1c0 4 0 7-1 10v1h1c1-1 2-3 3-5 2-4 2-9 0-14-2-6-7-9-13-12z" class="l"></path><path d="M265 582c1-1 2-1 3-2l2-2-3 6-3 6-1-2c1-2 1-4 2-6z" class="H"></path><path d="M280 556h2c0 1 1 2 1 3l1-1h0c0-1 0-1 1-1l2-2v-1c1 1 2 2 3 2 2 3 3 6 5 8l-1 1c0 1 1 3 1 4l1 2h0l-1 1c-1-1-2-2-4-3v1h0-4-1c0-4-3-7-5-10v1c-1-1-1-2-1-2v-1h0v-2z" class="Y"></path><path d="M290 556c2 3 3 6 5 8l-1 1c0 1 1 3 1 4l-2-1h0c0-1-1-3-2-4 0-1 0-3-1-5 0 0 1 0 1-1 0 0-1-1-1-2z" class="P"></path><path d="M265 568l-1-2v1h-1-1l-1-1c1-1 1-2 2-2h1c1 0 1 1 2 1h0c1 1 1 3 1 4v1 3h1v2s0 1-1 2h1c1 1 2 1 3 1h0-1l-2 2c-1 1-2 1-3 2-1 2-1 4-2 6l1 2c-1 1-2 2-4 3h-1v-1s0-1-1-1c-1-1-2-1-3-2l1-2c1-1 2-3 3-4h1v-2c2-2 3-4 4-6v-2c0-2 1-3 1-5z" class="O"></path><path d="M265 582h-1c-1 0-1 0-1 1h0-1 0c1-1 1-2 1-3 1 0 1-1 2-1v-2c1-1 2-2 3-2 0 0 0 1-1 2h1c1 1 2 1 3 1h0-1l-2 2c-1 1-2 1-3 2z" class="J"></path><path d="M275 560c0-1 1-1 1-2h0 0v-2-1h1v2c1 0 1 0 1 1v1l1-2 1-1v2h0v1s0 1 1 2v-1c2 3 5 6 5 10-1 1-2 3-2 5 0 4 3 8 4 12 0 2 0 3-1 5 1 0 1 1 1 2h0c2 2 6 7 6 9h0v1c-1 0-2-2-3-3-2-1-6-5-8-5h-1c1-2 1-4 1-7v-1c-1-1-1-2-2-2-1-1 0-1-1-2h-1v-1l1-1c1-1 2-2 2-3s0-1-1-1l-2 3c0-1-1-1-2-1v-2c0-6-2-11-6-16h1 0 3l1 1c0-1-1-2-1-3z" class="D"></path><path d="M275 560c0-1 1-1 1-2h0 0v-2-1h1v2c1 0 1 0 1 1v1c1 3 2 6 0 10h-1v-1c-1-2-3-4-5-6h3l1 1c0-1-1-2-1-3z" class="v"></path><path d="M275 560c0-1 1-1 1-2h0 0v-2-1h1v2c1 0 1 0 1 1l-1 1h0c1 2 1 4 1 6l-1 1v-1c0-1-1-2-1-2 0-1-1-2-1-3z" class="I"></path><path d="M281 561v-1c2 3 5 6 5 10-1 1-2 3-2 5 0 4 3 8 4 12 0 2 0 3-1 5s-2 3-3 4c0-2 1-3 2-5 2-4-1-9-2-13l-1-1c-1-2 1-5 2-7v-1l-4-8z" class="j"></path><path d="M286 570h1 4 0c1 0 2 1 2 2 1 1 7 13 7 14s0 1-1 1l1 4 1 4h-2v1 1 3c-1 0-2 1-3 2l-2 1h0c0-2-4-7-6-9h0c0-1 0-2-1-2 1-2 1-3 1-5-1-4-4-8-4-12 0-2 1-4 2-5z" class="I"></path><path d="M290 586h0c1 0 2 1 3 1l-3 2c-1-1-1-2 0-3z" class="P"></path><path d="M287 570h4 0c1 0 2 1 2 2h0-1-1c-1-1-2-2-4-2z" class="B"></path><path d="M293 572c1 1 7 13 7 14s0 1-1 1l1 4 1 4h-2-1v-2c-1-2 1-5 0-7 0 0-1 0-1-1-2-1-4-2-5-4-2-1-3-4-4-6h0v-2-1c1 0 2 1 3 1h0c0-1-1 0-1-1h1 1 1 0z" class="P"></path><path d="M298 593h0l1-6h0l1 4 1 4h-2-1v-2z" class="v"></path><path d="M290 586c-1 1-1 2 0 3l3-2c1 1 2 2 2 3h1c0 1 0 2 1 3v2h1 1v1 1 3c-1 0-2 1-3 2l-2 1h0c0-2-4-7-6-9h0c0-1 0-2-1-2 1-2 1-3 1-5 1 0 1 0 2-1z" class="R"></path><path d="M296 602v-5h1l1 1 1-1v3c-1 0-2 1-3 2z" class="K"></path><path d="M290 586c-1 1-1 2 0 3-1 1-2 4-2 5h0c0-1 0-2-1-2 1-2 1-3 1-5 1 0 1 0 2-1z" class="J"></path><defs><linearGradient id="L" x1="254.309" y1="561.771" x2="237.311" y2="569.843" xlink:href="#B"><stop offset="0" stop-color="#141313"></stop><stop offset="1" stop-color="#2d1d1d"></stop></linearGradient></defs><path fill="url(#L)" d="M236 547h2v2h1 0v-1h1c3 1 4 5 7 7h0c1 0 1 0 2 1 0 0 1 1 2 1 1 1 3 2 4 4h-1c1 1 1 2 2 3 0 1 1 1 1 2s0 3 1 3c1 2 1 5 1 7v4l1-1v2 2h-1c-1 1-2 3-3 4v-1l-3 3-1 2-1 1c-1 0-1 0-1-1-3-1-6-4-7-7 0-2 0-3 1-4v-1c5-8-1-15-4-23-2-3-3-6-4-9z"></path><path d="M248 563v-2h0 0l-1 1c0-1 0-1-1-2v1l-1 1-1-2-1-1v-2h0c2 1 4 1 5 2v4z" class="K"></path><path d="M250 582c0-4 3-7 5-10l1 8c0-1 0-1-1-1h-1v-3c0-1 0-1-1-1v4c-1 0-2 1-2 2l-1 1z" class="v"></path><path d="M258 569c1 2 1 5 1 7v4l1-1v2 2h-1c-1 1-2 3-3 4v-1s0-1 1-2v-3c1-4 1-8 1-12z" class="i"></path><path d="M245 556c2 0 4 1 6 2l1 1c1 0 1 1 2 2-2 0-3-2-4-2h0s1 1 1 2h-1c1 2 2 2 1 4v1h-3v-1-1-1h0v-4c-1-1-3-1-5-2h0v-1h1c0 1 0 1 1 1v-1z" class="I"></path><path d="M240 548c3 1 4 5 7 7h0c1 0 1 0 2 1 0 0 1 1 2 1 1 1 3 2 4 4h-1 0c-1-1-1-2-2-2l-1-1c-2-1-4-2-6-2v1c-1 0-1 0-1-1h-1-1 0c-1-1-1-3-2-4l1-1c0-1-1-1-1-2v-1z" class="i"></path><path d="M243 556c0-2-1-2-1-4h0c1 1 3 2 3 4v1c-1 0-1 0-1-1h-1z" class="O"></path><path d="M250 582l1-1c0-1 1-2 2-2v-4c1 0 1 0 1 1v3h1c1 0 1 0 1 1s0 3-1 5v1c0 1-1 1-2 2v-1l-1 1h1v1l-1 2c-2-2-2-3-3-5 0-1 0-3 1-4z" class="l"></path><path d="M254 584c1 1 1 1 1 2s-1 1-2 2v-1-2l1-1z" class="V"></path><path d="M253 581h0 1 1c1 0 0 3 0 4v1c0-1 0-1-1-2v-1l-2-1 1-1z" class="v"></path><path d="M253 581l1 1v1l-2-1 1-1z" class="V"></path><path d="M365 595v-1h1v2h0v1c-1 0-2 0-2 1h-1c1 0 2-1 4 0v3h1c-1 1 0 1-1 1h0c0 1-1 2-1 2h0 0c1 0 1-1 1-1h1c1 1 1 2 1 2h1v2c1 1 1 1 1 2s1 2 1 2v1 1s1 2 1 3c-10 6-20 12-32 13h-1c-2 0-4 1-7 1h2c0 1 0 1 1 1 2 0 4 0 6 1 2 0 5-1 7-2 8-2 18-6 24-12h1c1 1 2 3 3 4 1 2 3 4 5 5v1h-2l-34 6-17 1h-9c-3 0-5 0-8 1l-26-1-15-2c-1 0-1-1-2-1h0c1 0 2 0 3-1h0c1 0 2 1 3 1 1-2 3-2 5-3h0c1 0 1 0 2-1 1 0 1 0 2-1v-1c-1 1-2 2-4 2-1 1-2 1-2 1h-1v1c-1 0-2 0-3-1h-1l-1-1c-2 1-4 1-6 0-4 0-9-3-12-5l-1-1-6-3 1-2c0-2 0-4-1-6 0-2-1-4-2-6h0 0c1-1 1-2 2-2-1-1-2-2-2-3 0-2-1-2-2-3v-1l-2-1v-1c1 1 2 1 3 1 5 3 11 6 16 7 1 0 1-1 2-1l13 4v2c2 0 5 1 7 1 1 1 2 1 3 1h3l4 1h4 3v-1h21c14-1 26-5 39-10 1-1 5-2 6-3v-1z" class="m"></path><path d="M251 611h1c1 0 1 1 2 2h0-1-1l-1-1v-1z" class="J"></path><path d="M248 610h2 0c0 1 1 1 1 1v1 1l-1-1h-1l-1-2z" class="B"></path><path d="M294 616l1 4-2 3v-5c0-1 1-2 1-2z" class="M"></path><path d="M264 610v2h0c-1 1-3 3-3 5h-1c-1-1-2-1-2-2v-1h2l4-4z" class="E"></path><path d="M267 624h-3c-1 1-2 1-2 1-1-1-3-2-3-3s1-2 1-2h1c1 1 1 1 2 3h3l1 1z" class="G"></path><path d="M290 626c0-1 1-2 2-2h0 2c2 1 6 3 7 4-3 0-9 0-11-2z" class="S"></path><path d="M262 601l13 4v2l-15-5c1 0 1-1 2-1z" class="AA"></path><path d="M311 615h1v1c-1 2-3 7-5 9-1 0-2 1-3 1-1-1-1-2-2-2-1-1-2-2-2-3 1 0 2 1 2 2 1 1 2 1 3 1 2-2 3-4 4-6-1 1-3 3-5 3h0c1-2 2-2 4-3l3-3z" class="V"></path><path d="M278 623v1h1 1c-2 3-4 4-8 4-2 1-4 1-6 0h3v-2c2-1 5-1 7-2 0 0 1-1 2-1z" class="B"></path><path d="M278 623v1h1c-2 2-5 3-8 3h-1l-1-1c2-1 5-1 7-2 0 0 1-1 2-1z" class="S"></path><path d="M320 628c-2 1-6 1-8 1s-3 0-4-1v-1l2-1s3-2 4-2l1 1c1 1 2 2 4 2h0l1 1z" class="L"></path><path d="M247 603l5 5h0c-1 0-5-3-6-4 1 2 2 4 2 6l1 2h1l1 1v3 1s0 2-1 2v-1h0-1l-1-1c0-2 0-4-1-6 0-2-1-4-2-6h0 0c1-1 1-2 2-2z" class="f"></path><path d="M249 612h1l1 1v3 1s0 2-1 2v-1h0l-1-6z" class="C"></path><path d="M251 612l1 1h0c1 2 1 2 3 3v1 1l-1 1 1 1v2c-1 0-1 0-1 1l-1-1-6-3 1-2 1 1h1 0v1c1 0 1-2 1-2v-1-3-1z" class="t"></path><path d="M252 619v-3h0l2 2c-1 1-1 1-1 2l-1-1z" class="c"></path><path d="M250 618h0v1c1 0 1-2 1-2v-1c0 2 0 3 1 4v-1l1 1c0-1 0-1 1-2l-1 3 2 1c-1 0-1 0-1 1l-1-1-6-3 1-2 1 1h1z" class="O"></path><path d="M299 609h21v1h2c-8 2-18 1-26 0h3v-1z" class="W"></path><path d="M366 599l1 2h1c-1 1 0 1-1 1s-1 0-2 1c-1 0-1 0-2 1-1 0-3 1-4 2l-3 2h-2l-1 1h-2-2l-1-1 12-5c1-1 4-1 6-3v-1z" class="M"></path><path d="M280 614l5 1c-1 2-2 3-2 5h0c-1 1-2 2-3 4h-1-1v-1l1-1-1-1h0v-2-1h0l-1-1c1-1 1-2 1-3h2z" class="h"></path><path d="M278 621c0-1 0-1 1-2 1 0 1 1 1 2h1l2-1h0c-1 1-2 2-3 4h-1-1v-1l1-1-1-1z" class="o"></path><path d="M365 595v-1h1v2h0v1c-1 0-2 0-2 1h-1c1 0 2-1 4 0v3l-1-2c0-1 0-1-1-1s-4 2-5 2l-11 5c-9 3-18 4-27 5h-2v-1c14-1 26-5 39-10 1-1 5-2 6-3v-1z" class="r"></path><path d="M283 620c1-2 2-4 3-5 1-2 3-3 5-4 1 1 3 3 3 5 0 0-1 1-1 2v5l-1 1c-1 0-2 1-2 2-5 4-9 5-15 6 1-2 3-2 5-3h0c1 0 1 0 2-1 1 0 1 0 2-1v-1c-1 1-2 2-4 2-1 1-2 1-2 1h-1v1c-1 0-2 0-3-1h-1l-1-1c4 0 6-1 8-4 1-2 2-3 3-4z" class="h"></path><path d="M319 627h0c-2-2-6-8-6-10 1-3 4-4 6-5l2 3 1 1c0 4 3 8 6 10 4 3 8 3 13 3h-1c-2 0-4 1-7 1h2c0 1 0 1 1 1 2 0 4 0 6 1h-2c-2 0-2 1-4 1-4 0-13-2-16-5l-1-1z" class="r"></path><defs><linearGradient id="M" x1="276.909" y1="621.273" x2="262.441" y2="615.112" xlink:href="#B"><stop offset="0" stop-color="#d0050c"></stop><stop offset="1" stop-color="#ab1c22"></stop></linearGradient></defs><path fill="url(#M)" d="M264 610l1-2c1 0 2 0 3 1 4 2 7 4 12 5h-2c0 1 0 2-1 3l1 1h0v1 2c-2 2-3 3-6 3-1 0-4-1-5 0l-1-1-6-6h1c0-2 2-4 3-5h0v-2z"></path><path d="M268 609c4 2 7 4 12 5h-2c0 1 0 2-1 3 0 0 0-1-1-1l-3-2c0-1-4-2-5-3l-1-1h0l1-1z" class="T"></path><path d="M264 612l6 5c1 0 3 1 3 2s0 1-2 1l-4-3c-1 0-2-1-2-1-1 0-3 1-4 1 0-2 2-4 3-5z" class="D"></path><path d="M365 603c1-1 1-1 2-1h0c0 1-1 2-1 2h0 0c1 0 1-1 1-1h1c1 1 1 2 1 2h1v2c1 1 1 1 1 2s1 2 1 2v1 1s1 2 1 3c-10 6-20 12-32 13-5 0-9 0-13-3-3-2-6-6-6-10l-1-1c3 0 5 0 8-1l11-3h0l8-3 1 1h2 2l1-1h2l3-2c1-1 3-2 4-2 1-1 1-1 2-1z" class="C"></path><path d="M321 615c3 0 5 0 8-1v1c-1 1-1 1 0 2v1l1 1c-1 0-1 0-1 1h-1c0 1 0 1 1 2v1l1 1h0s1 0 1 1c1 0 1 0 1 1h0c-6 0-6-7-10-10l-1-1z" class="h"></path><path d="M341 622l1-1h1l1-1 2-2h2l-1 7-2-2c-4 1-6 3-10 3h-1-2 0c0-1 0-1-1-1 0-1-1-1-1-1h0l-1-1v-1c-1-1-1-1-1-2 1 0 1 0 2 1h4 2 0 3 1 0l1 1z" class="G"></path><path d="M330 621h4 2 0 3 1 0l1 1-3 1-1 1h-2-1c-1 0-2-1-3-1 0-1-1-2-1-2z" class="o"></path><path d="M365 603c1-1 1-1 2-1h0c0 1-1 2-1 2h0 0c1 0 1-1 1-1h1c1 1 1 2 1 2h1v2c1 1 1 1 1 2s1 2 1 2v1 1l-4-9c-3 4-6 7-8 12l-1 1-1-1h-1l-1 1v-1l1-1-1-1 1-1h-1c0-1 1-2 1-2h-1l-1-2 1-1 3-2c1-1 3-2 4-2 1-1 1-1 2-1z" class="T"></path><path d="M363 604c1-1 1-1 2-1-3 4-5 8-6 13h1l-1 1-1-1h-1l-1 1v-1l1-1-1-1 1-1c2-2 5-6 6-9z" class="I"></path><path d="M359 606c1-1 3-2 4-2-1 3-4 7-6 9h-1c0-1 1-2 1-2h-1l-1-2 1-1 3-2z" class="Q"></path><path d="M359 606h1l-1 1c-1 1-1 2-2 4h-1l-1-2 1-1 3-2z" class="D"></path><path d="M348 608l1 1h2 2l1-1h2l-1 1 1 2h1s-1 1-1 2h1l-1 1c-1 1-2 3-4 3h-1-2c0 1-1 1-1 1h-2l-2 2-1 1h-1l-1 1-1-1h0-1-3 0-2-4c-1-1-1-1-2-1h1c0-1 0-1 1-1l-1-1v-1c-1-1-1-1 0-2v-1l11-3h0l8-3z" class="F"></path><path d="M353 609v2c-1 2-3 2-4 3h-1v-3c1-1 2-1 3-2h2z" class="B"></path><path d="M348 615c4 0 5-3 7-5v-1l1 2h1s-1 1-1 2h1l-1 1c-1 1-2 3-4 3h-1-2 0v-1l-1-1z" class="H"></path><path d="M329 614l11-3 1 1c-1 0-1 1-2 1 0 0-1 0-2 1h-3v1h-1-1c-1 1-2 1-3 2h2 0c1-1 3-1 4-1l1-1h2l2-1c1-1 2-1 3 0-2 1-3 1-5 2-1 2-3 3-4 5h-4c-1-1-1-1-2-1h1c0-1 0-1 1-1l-1-1v-1c-1-1-1-1 0-2v-1z" class="q"></path><path d="M343 614c1-1 1-1 2-1l3 2 1 1v1h0c0 1-1 1-1 1h-2l-2 2-1 1h-1l-1 1-1-1h0-1-3 0-2c1-2 3-3 4-5 2-1 3-1 5-2z" class="I"></path><path d="M336 621h2c2-1 3-3 4-5l1-1v1h1c2 1 3 1 4 1h1 0c0 1-1 1-1 1h-2l-2 2-1 1h-1l-1 1-1-1h0-1-3 0z" class="L"></path><path d="M512 478c1 0 2 1 3 2 3 1 5 1 8 2 1 1 3 4 5 4 0 0 1 0 1 1s1 3 2 4c0 1-1 5-2 6 1 1 1 1 2 1h1l-5 13-3 7c-1 1-2 3-2 4 0 2-3 7-4 9-1 1-2 3-3 5 0 1-1 1-2 2v-1l1-1c0-1 1-1 1-2l1-1v-2-1l1-1c-1 1-2 3-4 4h0c-5 6-9 13-15 19v-1h0v-1h-2-4-9 0-2c0 1 1 1 1 1 0 2-1 2-2 3h-1v-1h-2 0l-2-2h1c-1-1-2-2-3-2-2-1-3-2-4-4l-1-1c-4-5-5-11-7-17v-3l4 8 1-1v1h1c0-5 1-9 2-14h1 0l1 2v-4-3h1v-2-1h0v-4-1l1-1v1l1-1-1-4c-1-1-1-2-1-2l1-1 3-1c1-1 1-2 2-2s3-1 4-1v-1c1-2 4-4 6-5 4-3 8-6 13-7l5-1h0c1 0 2-1 2-1h4z" class="C"></path><path d="M473 519l1-1h1v1 3l-2-2v-1z" class="l"></path><path d="M482 531c1 0 2 1 2 2l-1 2-1 2v-1-5z" class="P"></path><path d="M514 524l4-5c0 1 0 1 1 2-1 2-2 3-3 5h-1l1-1v-1h0v-1l-1 2-1-1z" class="W"></path><path d="M522 511c0 1 0 2-1 3v2h0 1c-1 2-2 4-3 5-1-1-1-1-1-2 1-2 3-5 4-8z" class="h"></path><path d="M494 536v-1c2 0 3 0 4 1s2 3 2 4v1c-1-2-1-2-2-3s-1-1-2-1c-2 0-3 1-5 2h0-1v-1c1-1 2-2 4-2z" class="U"></path><path d="M517 503v5c-1 1-1 2-2 3l-1-1h0l1-1c-1 1-1 1-2 1v-1-1c2-1 1-3 1-4h2l1-1z" class="T"></path><path d="M475 522h0c1 2 2 2 3 3v1h0c-2 0-3 2-4 4h0c1 0 2 0 2-1l2 1-1 1s-2 1-3 1c-1-1-2-1-3-1l-1 1v-1h-1c0-1 1-1 1-2 1 0 1-1 2-1h1c2-2 2-3 2-6z" class="a"></path><path d="M472 513c0 2 0 4 1 6v1l2 2h0c0 3 0 4-2 6h-1l-1-3v-5-4-3h1z" class="D"></path><path d="M472 513c0 2 0 4 1 6v1s-1 0-1-1c-1 2-1 4-1 6v-5-4-3h1z" class="K"></path><path d="M491 547c3-1 5-2 8-1 0 1 0 1 1 1-1 1 0 2-1 2l-1 1h-2-4c-1-1-3 0-4-1l1-1 1-1h1z" class="q"></path><path d="M490 532v-1h0c0-1 0-2-1-3l-1-1h1c3 0 8 2 11 1h1c-3 2-7 5-10 5-1 0-1 0-1-1z" class="M"></path><path d="M480 522l2-4h1c2 3 2 7 6 8v1h-1l1 1c1 1 1 2 1 3h0v1c-1 0-3-2-4-3-2-2-5-4-6-7z" class="N"></path><path d="M511 513h2l4-3c-4 5-9 9-14 10h-3-2c0-1-2-1-2-2l-2-1 1-1c1 1 2 1 4 1 1 1 3 1 5 0 2 0 4-2 5-4h2z" class="L"></path><path d="M474 504l1 4 1 3v-1c1-1 2-1 2-2 0 2 0 5-1 6l-1 2h0c-1 1-1 2-1 3v-1h-1l-1 1c-1-2-1-4-1-6v-2-1h0v-4-1l1-1v1l1-1z" class="E"></path><path d="M475 518l-1-1c-1-2 0-6 0-8l1-1v8h1c-1 1-1 2-1 3v-1z" class="K"></path><path d="M475 508l1 3v-1c1-1 2-1 2-2 0 2 0 5-1 6l-1 2h0-1v-8h0z" class="N"></path><path d="M523 515h0v-2h0l2-2h1 1l-3 7c-1 1-2 3-2 4 0 2-3 7-4 9-1 1-2 3-3 5 0 1-1 1-2 2v-1l1-1c0-1 1-1 1-2l1-1v-2-1l1-1c-1 1-2 3-4 4h0c3-6 7-11 10-18z" class="h"></path><path d="M514 524l1 1 1-2v1h0v1l-1 1h1c-2 3-6 7-8 10-3 3-5 7-8 9h0v-1c0-3 11-17 14-20z" class="F"></path><path d="M479 533l1-1c0-1 1-1 2-1h0v5 1c1 2 2 4 5 6h0l3 1c1 0 1 0 1 1v2h-1c-1 0-2 0-3-1-3 0-6 0-8-1h0 2c0-1 0-1-1-1h0 3 0c-1-2-3-3-3-5-1-2-1-4-1-6z" class="L"></path><path d="M479 533l1-1c0-1 1-1 2-1h0v5c-1-1-1-1-2-1 0 1-1 1-1 2 1 1 1 2 1 2-1-2-1-4-1-6z" class="G"></path><path d="M487 543l3 1c1 0 1 0 1 1v2h-1c-1 0-2 0-3-1-3 0-6 0-8-1h0 2c0-1 0-1-1-1h0 3l1 1 1-1v1h-1c1 0 2 1 2 1h1c1 0 2-1 2-1l1-1h-3v-1z" class="S"></path><path d="M491 545v2h-1c-1 0-2 0-3-1 1 0 2-1 4-1z" class="c"></path><path d="M476 529c2 0 3 0 5 1l1 1c-1 0-2 0-2 1l-1 1c0 2 0 4 1 6 0 2 2 3 3 5h0-3c-1-2-3-2-4-4-2-1-3-2-3-4 0-1-1-1-1-2s1-2 2-2 3-1 3-1l1-1-2-1z" class="I"></path><path d="M473 536c1 1 1 1 3 2v2c-2-1-3-2-3-4z" class="J"></path><path d="M476 529c2 0 3 0 5 1l1 1c-1 0-2 0-2 1l-1 1v-1l-2 2h-1v-1h0l2-2h-1l1-1-2-1z" class="B"></path><path d="M486 520v-1h2c3 0 4 3 8 3 1 0 1 1 2 1h1l-1-3h2v1c0 2 1 2 1 4v1h1v1s0 1-1 1h-1c-3 1-8-1-11-1v-1h1l-4-4v-2h0z" class="K"></path><path d="M486 520l2 2c0 1 1 1 2 2h0c2 1 5 2 7 2v1h0c1 0 2 1 3 1-3 1-8-1-11-1v-1h1l-4-4v-2z" class="Z"></path><path d="M519 487c1 1 2 1 3 2l3 3c1 1 2 1 3 2 0 1 0 2 1 3s1 1 2 1h1l-5 13h-1-1l-2 2h0v2h0l-1 1h-1 0v-2c1-1 1-2 1-3l1-6c1-2 1-10 0-11 0-1 0-2-1-3 0 0-1-2-2-2 0 0-1-1-1-2z" class="r"></path><path d="M482 493v-1c1-2 4-4 6-5 1 1 1 1 2 1h1v1c-1 1-4 3-5 4-2 2-2 4-3 7v3 2h1c0-2 0-2 1-3l-1 4v7l1 1v2 2s0 1 1 2h0v2l4 4h-1c-4-1-4-5-6-8h-1l-2 4c0-2 0-2-1-3 0-1-1-2-1-3h-2l1-2c1-1 1-4 1-6 0 1-1 1-2 2v1l-1-3-1-4-1-4c-1-1-1-2-1-2l1-1 3-1c1-1 1-2 2-2s3-1 4-1z" class="J"></path><path d="M482 493v-1c1-2 4-4 6-5 1 1 1 1 2 1l-6 6-2-1zm1 7v3 2h1c0-2 0-2 1-3l-1 4v7 2c-2-4-2-11-1-15z" class="G"></path><path d="M480 501h1c0 4-1 8 0 11 0 2 0 4-1 5-1 0-1-1-1-1l-2-2c1-1 1-4 1-6 1-1 2-2 2-3 1-2 0-3 0-4z" class="l"></path><path d="M484 513l1 1v2 2s0 1 1 2h0v2l4 4h-1c-4-1-4-5-6-8h-1l-2 4c0-2 0-2-1-3 0-1-1-2-1-3h-2l1-2 2 2s0 1 1 1c1-1 1-3 1-5 0 2 0 3 1 4 0 1 0 1 1 2l1-1s-1-1 0-2v-2z" class="Y"></path><path d="M476 496l6-1c-1 1-4 4-4 5l2 1c0 1 1 2 0 4 0 1-1 2-2 3 0 1-1 1-2 2v1l-1-3-1-4-1-4c-1-1-1-2-1-2l1-1 3-1z" class="U"></path><path d="M470 518h0l1 2v5l1 3c-1 0-1 1-2 1 0 1-1 1-1 2h1v1l1-1c1 0 2 0 3 1-1 0-2 1-2 2s1 1 1 2c0 2 1 3 3 4 1 2 3 2 4 4h0c1 0 1 0 1 1h-2 0c2 1 5 1 8 1 1 1 2 1 3 1l-1 1-1 1c1 1 3 0 4 1h-9 0-2c0 1 1 1 1 1 0 2-1 2-2 3h-1v-1h-2 0l-2-2h1c-1-1-2-2-3-2-2-1-3-2-4-4l-1-1c-4-5-5-11-7-17v-3l4 8 1-1v1h1c0-5 1-9 2-14h1z" class="I"></path><path d="M470 532l1-1c1 0 2 0 3 1-1 0-2 1-2 2s1 1 1 2c0 2 1 3 3 4 1 2 3 2 4 4h0c1 0 1 0 1 1h-2 0-1c-3-1-5-3-6-5-1-3-3-5-2-8z" class="d"></path><path d="M474 548c0-1-1-1-1-2h0c4 1 7 1 11 2h5l-1 1c1 1 3 0 4 1h-9 0-2c0 1 1 1 1 1 0 2-1 2-2 3h-1v-1h-2 0l-2-2h1c-1-1-2-2-3-2l1-1z" class="U"></path><path d="M474 548c3 1 6 2 9 2h0-2c0 1 1 1 1 1 0 2-1 2-2 3h-1v-1h-2 0l-2-2h1c-1-1-2-2-3-2l1-1z" class="i"></path><path d="M479 551h3c0 2-1 2-2 3h-1v-1h-2l1-2h1z" class="X"></path><path d="M477 553l1-2h1l1 1-1 1h-2z" class="E"></path><path d="M470 518h0l1 2v5l1 3c-1 0-1 1-2 1 0 1-1 1-1 2-1 4 0 9 0 12v1c-2-3-3-8-4-11v-1l1-1v1h1c0-5 1-9 2-14h1z" class="N"></path><defs><linearGradient id="N" x1="512.717" y1="483.096" x2="491.268" y2="511.129" xlink:href="#B"><stop offset="0" stop-color="#de0000"></stop><stop offset="1" stop-color="#a81c21"></stop></linearGradient></defs><path fill="url(#N)" d="M512 478c1 0 2 1 3 2 3 1 5 1 8 2 1 1 3 4 5 4 0 0 1 0 1 1s1 3 2 4c0 1-1 5-2 6-1-1-1-2-1-3-1-1-2-1-3-2l-3-3c-1-1-2-1-3-2 0 1 1 2 1 2 1 0 2 2 2 2 1 1 1 2 1 3-1 1-4 2-4 4 0 0 1 1 2 1v1c-1 1-3 1-4 1h-1v1l1 1-1 1h-2c0 1 1 3-1 4h0-1c-3-3-1-8-1-12 0-2-2-3-3-4-2-2-5-2-8-2l-3 1c-5 1-8 3-11 8l-1 3c-1 1-1 1-1 3h-1v-2-3c1-3 1-5 3-7 1-1 4-3 5-4v-1h-1c-1 0-1 0-2-1 4-3 8-6 13-7l5-1h0c1 0 2-1 2-1h4z"></path><path d="M512 478c1 0 2 1 3 2 3 1 5 1 8 2 1 1 3 4 5 4 0 0 1 0 1 1s1 3 2 4c0 1-1 5-2 6-1-1-1-2-1-3-1-1-2-1-3-2l-3-3c-1-1-2-1-3-2-2-1-4-1-7-2-1 0-3-1-4-1-2-1-3-1-4-1h-1v-1h1l2-1-1-1h-4l5-1h0c1 0 2-1 2-1h4z" class="D"></path><path d="M512 485c-1 0-3-1-4-1-2-1-3-1-4-1h-1v-1h1 0 4c1 0 5 0 6 1h0 1l-1 1s-1 0-2 1z" class="v"></path><path d="M512 478c1 0 2 1 3 2l-9 1-1-1h-4l5-1h0c1 0 2-1 2-1h4z" class="W"></path><path d="M514 483c5 0 9 3 12 6 1 2 1 3 2 5-1-1-2-1-3-2l-3-3c-1-1-2-1-3-2-2-1-4-1-7-2 1-1 2-1 2-1l1-1h-1 0z" class="j"></path><path d="M514 484c2 1 5 2 7 3 1 1 1 0 1 1v1c-1-1-2-1-3-2-2-1-4-1-7-2 1-1 2-1 2-1z" class="a"></path><path d="M500 490c3 0 6 0 8 2 1 1 3 2 3 4 0 4-2 9 1 12h1 0v1 1c1 0 1 0 2-1l-1 1h0l1 1c-2 0-3 1-4 2h-2c-1 2-3 4-5 4-2 1-4 1-5 0-2 0-3 0-4-1l-1 1 2 1c0 1 2 1 2 2l1 3h-1c-1 0-1-1-2-1-4 0-5-3-8-3h-2v1c-1-1-1-2-1-2v-2-2l-1-1v-7l1-4 1-3c3-5 6-7 11-8l3-1z" class="c"></path><path d="M496 507c1-1 3-1 4 0 1 0 3 1 4 2l-1 1v-1c-2-1-4-1-7-1l1-1h-1z" class="f"></path><path d="M513 508h0v1 1c1 0 1 0 2-1l-1 1h0l1 1c-2 0-3 1-4 2h-2c1-1 1-2 1-3v-2c1 0 1 1 2 1l1-1z" class="o"></path><path d="M496 503h2c2-1 4 0 6 0l-1-1c-1 0-1 1-2 0-1 0-1-1-2-1 2-1 2 0 4-1 0 1 0 1 1 1 1 1 3 3 4 5l-2-2c-3-1-7-1-10-1z" class="n"></path><path d="M493 515c-1-2-1-5 0-6h0c1-1 2-2 3-2h1l-1 1c-1 1-2 3-1 5v1h0l2 2h3l-1 1c-2 0-3 0-4-1l-1 1s-1-1-1-2z" class="Q"></path><path d="M493 515v-2h1v1h1l2 2h3l-1 1c-2 0-3 0-4-1l-1 1s-1-1-1-2z" class="N"></path><path d="M484 506c1 1 1 2 1 4v5c0 1 0 1 1 1l1-1 1 1c1 0 0-1 2-1h0c0 1 1 1 1 2 1 0 1 1 2 1 1 1 2 1 3 2h1l-1-1v-1c0 1 2 1 2 2l1 3h-1c-1 0-1-1-2-1-4 0-5-3-8-3h-2v1c-1-1-1-2-1-2v-2-2l-1-1v-7z" class="H"></path><path d="M496 508c3 0 5 0 7 1v1l1-1 2 2c0 1 0 2-1 2-1 2-2 3-4 3h-1-3l-2-2h0v-1c-1-2 0-4 1-5z" class="V"></path><path d="M502 511h0c1 0 1 1 1 1 1 0 1 0 2 1h-2-3c0-1 1-2 2-2z" class="H"></path><path d="M504 509l2 2c0 1 0 2-1 2h0c-1-1-1-1-2-1 0 0 0-1-1-1h0v-1l1-1v1l1-1z" class="B"></path><path d="M496 512h2l1 1h1 2c-2 1-3 2-4 2s-1 0-1 1l-2-2h0l1-2z" class="d"></path><path d="M496 512h2l1 1h1c-1 1-1 1-2 1-1-1-2 0-3 0h0l1-2z" class="G"></path><path d="M503 513h2 0c-1 2-2 3-4 3h-1-3c0-1 0-1 1-1s2-1 4-2h1z" class="L"></path><path d="M496 508c3 0 5 0 7 1l-1 1c-2 0-2-1-4 0l-2 2-1 2v-1c-1-2 0-4 1-5z" class="k"></path><path d="M497 491l3-1h0l2 2c2 1 5 4 6 7 0 1 1 3 1 4-1-1-2-3-3-4l-1-1c-1 0-1 1-2 2h0c-2 1-2 0-4 1 1 0 1 1 2 1 1 1 1 0 2 0l1 1c-2 0-4-1-6 0h-2c0 1-1 1-2 1s-2 1-3 2c-2 3-1 5 0 8l-4-4c-1-2-1-4 0-5 1-4 2-7 5-9 2-1 3-1 4-1 2 1 2 2 2 4h0c-2 0-3-1-4 0-1 0-2 1-2 2-1 0-1 0 0 0h1c1-1 3-1 4 0h0c1 0 2-1 2-2 1-2 0-4 0-6h0l-1-1c0-1 0-1-1-1z" class="a"></path><path d="M519 189c10 17 19 34 23 54 3 19 2 38 2 57v61 54c0 12 1 25 0 37s-3 23-6 34c-2 7-4 14-7 20l-5 9c-1 2-2 5-4 7 0-1 1-3 2-4l3-7 5-13h-1c-1 0-1 0-2-1 1-1 2-5 2-6l1-2v-1l1 1h0l2-8v-1l2-25c0-7 0-15 1-23 0-7 0-15 2-23l1 1c1-6 3-13 1-19-1-4-3-7-3-10l-2-3-1 1c-3-6-9-13-14-18v-1h1l-2-2c-4-2-5-4-9-4v-1l-2-3h-1l-2-2c-1-1-1 0-3 0l-1-1 1-1 1-1h-1v-1h0l-1-1v-1l3-3h-3c1-2 1-3 2-4 1-2 2-3 3-4 2-3 4-5 7-7v-1c1-1 1-1 1-2 1-1 3-2 4-3 0-1 1-2 1-2h1 1l2-5c1-2 3-5 3-7l1-1c1-4 2-7 3-10v-1h1 0v-3l-1-1 2-11c0-4 1-8 2-13v-8-6h-1c0-1 1-5 1-6l-2-17c0-1-1-4-1-5-1-5-3-10-5-15l-4-10c-2-2-5-6-5-8z" class="C"></path><path d="M536 256s1-1 2-1c-1 3 0 7-1 10v-3l-1 2v-8z" class="F"></path><path d="M536 244h1c0 2 0 5 1 8v3c-1 0-2 1-2 1v-6h-1c0-1 1-5 1-6z" class="d"></path><path d="M537 242l1-1c-1-1-1-2-1-3h1l2 12c0 1 0 2-1 3h0v-1l-2-10z" class="M"></path><path d="M536 264l1-2v3l-2 12h-1c0-4 1-8 2-13zm-4 225v-1l1 1-1 9h-1c-1 0-1 0-2-1 1-1 2-5 2-6l1-2z" class="U"></path><path d="M540 250c1 4 1 8 1 13v2c-1 0-1 0-1-1v-2h-1v-10 1h0c1-1 1-2 1-3zm-4 123h1s0-1 1-1c1 2 1 5 3 7v-1h1v-3c0 3 1 5 0 7h-1c-1 0-1-1-2-2-1-2-2-4-3-7z" class="S"></path><path d="M538 282v2h1v6c0 3 0 7-1 10h0c-1-2-1-4-1-6l1-12z" class="W"></path><path d="M534 227c2 2 3 5 3 8 1 1 1 2 1 3h-1c0 1 0 2 1 3l-1 1v2h-1l-2-17zm-2 61l2-11h1c-1 3-1 7-1 10v11-1h-1v3-8h0v-3l-1-1zm7-26h1v2c0 1 0 1 1 1v-2c0 7-1 14-2 21h-1v-2c1-7 2-13 1-20z" class="F"></path><path d="M539 343c2-1 2-5 2-6v-9c0-2 0-4 1-6 1 1 0 10 0 12v14c-1-1 0-3-1-4h-1l-1-1z" class="d"></path><path d="M537 455c1-3 0-11 1-13 0 3 0 6 1 9h0c0 8-1 15-2 22 0 2-1 5-2 8v-1l2-25z" class="W"></path><path d="M540 409l1 1c-1 8-2 15-2 23v18h0c-1-3-1-6-1-9-1 2 0 10-1 13 0-7 0-15 1-23 0-7 0-15 2-23z" class="E"></path><path d="M529 303c1-4 2-7 3-10v-1h1v8-3h1v1c0 3 0 7 2 9l1 1h0c1 3 0 6 1 9v8c0 3 0 6 1 9v9l1 1h1c1 1 0 3 1 4v21 6 3h-1v1c-2-2-2-5-3-7-1 0-1 1-1 1h-1c1 3 2 5 3 7v1l-2-3-1 1c-3-6-9-13-14-18v-1h1l-2-2c-4-2-5-4-9-4v-1l-2-3h-1l-2-2c-1-1-1 0-3 0l-1-1 1-1 1-1h-1v-1h0l-1-1v-1l3-3h-3c1-2 1-3 2-4 1-2 2-3 3-4 2-3 4-5 7-7v-1c1-1 1-1 1-2 1-1 3-2 4-3 0-1 1-2 1-2h1 1l2-5c1-2 3-5 3-7l1-1z" class="C"></path><path d="M533 300v-3h1v1c0 3 0 7 2 9l1 1h-1c-1-1-2-1-2-2 0 3 0 7-1 10v-6-10z" class="W"></path><path d="M521 319c-1 3-3 6-5 8 0 1 0 3-1 4 0 2 0 5-1 7v-6c0-1 1-2 0-3 1-2 3-4 3-6 0-1 3-3 4-4z" class="g"></path><path d="M528 357c2 2 4 5 6 7l2 3c-1 0-2 0-3-1l-1 1-2-3 1-1-2-3h0c-1-1-1-2-1-3z" class="F"></path><path d="M531 363c0 1 1 1 2 2l1-1 2 3c-1 0-2 0-3-1l-1 1-2-3 1-1zm-17-34c1 1 0 2 0 3v6c0 3 0 6 2 8 0 1 1 2 2 3l-1 1v3c0-1-1-1-1-1v-2s-1-2-1-3c-1-2-2-5-3-7 1-1 0-2 0-3v-1c0-3 0-5 2-7z" class="Q"></path><path d="M537 357c-1-1-2-2-4-2-1 0-2 0-3-1l2-1c1 0 1 0 2-1-1-2-2-2-4-3h-1 1c1 0 1-1 2 0 1 0 1 1 2 0v-1h1 0v-1-1h0c1 1 2 2 2 3h1v2l-1 1h0v5z" class="F"></path><path d="M518 350c0-1 1-1 0-2 0-1-1-1-1-2v-1c-1-2-1-3-1-5h1 0v1l1 1v1c0 3 2 6 4 9 1 1 6 4 6 5s0 2 1 3h0l2 3-1 1c-2-3-5-5-9-7l-4-4v-3l1-1v1z" class="i"></path><path d="M518 349v1c3 4 7 7 11 10l2 3-1 1c-2-3-5-5-9-7l-4-4v-3l1-1z" class="o"></path><path d="M515 323c1-1 1-1 1-2 1-1 3-2 4-3 0-1 1-2 1-2h1l-1 3c-1 1-4 3-4 4 0 2-2 4-3 6-2 2-2 4-2 7h-2c0 1-2 2-3 3h-1 0-3c1-2 1-3 2-4 1-2 2-3 3-4 2-3 4-5 7-7v-1z" class="G"></path><path d="M517 323c0 2-2 4-3 6-2 2-2 4-2 7h-2c0 1-2 2-3 3h-1v-2c0-2 4-5 5-6 2-3 4-5 6-8z" class="P"></path><path d="M521 357c4 2 7 4 9 7l2 3 1-1c1 1 2 1 3 1l2 5c-1 0-1 1-1 1h-1c1 3 2 5 3 7v1l-2-3-1 1c-3-6-9-13-14-18v-1h1l-2-2v-1z" class="b"></path><path d="M532 367l1-1c1 1 2 1 3 1l2 5c-1 0-1 1-1 1h-1l-4-6z" class="F"></path><path d="M522 360c5 3 8 6 11 10 2 3 3 6 4 8l-1 1c-3-6-9-13-14-18v-1z" class="h"></path><path d="M506 339h1c1-1 3-2 3-3h2v1c0 1 1 2 0 3 1 2 2 5 3 7 0 1 1 3 1 3v2s1 0 1 1l4 4v1c-4-2-5-4-9-4v-1l-2-3h-1l-2-2c-1-1-1 0-3 0l-1-1 1-1 1-1h-1v-1h0l-1-1v-1l3-3h0z" class="a"></path><path d="M508 342h0c-1 2-1 3-3 3h-1v-1h0l4-2z" class="f"></path><path d="M511 346v-1h0c1 2 2 3 3 5l1 2h-1c-2-1-2-4-3-6z" class="D"></path><path d="M510 350l1-1-1-1v-2h1c1 2 1 5 3 6l-1 1h-1l-2-3z" class="f"></path><path d="M506 339h1c1-1 3-2 3-3h2v1 1c-1 1-4 3-4 4l-4 2-1-1v-1l3-3h0z" class="D"></path><path d="M512 340c1 2 2 5 3 7 0 1 1 3 1 3v2s1 0 1 1l4 4v1c-4-2-5-4-9-4v-1h1l1-1h1l-1-2v-3c0-1-1-2-1-3l-1-1v-3z" class="J"></path><path d="M533 316c1-3 1-7 1-10 0 1 1 1 2 2h1 0c1 3 0 6 1 9v8c0 3 0 6 1 9v9l1 1h1c1 1 0 3 1 4v21 6 3h-1c-1-1-1-3-1-5l-1-1c-1-5-1-10-2-15v-5h0l1-1v-2c-1-3-2-5-4-8-1-3-2-6-3-8v-1c1 1 1 1 2 1 0 1 0 1 1 1 0-2 1-4 0-6v-3c-1-3 0-6-1-9z" class="q"></path><path d="M542 369v6 3h-1c-1-1-1-3-1-5h0 0s0 1 1 1v2h0v-6l1-1z" class="W"></path><path d="M359 439c0 2 1 3 1 5h0v1l3 9c1 3 3 4 5 6l6 8c1 1 1 2 2 3l6 7 4 5c2 3 3 5 4 7h0-1v-1c-1-1 0-1-1-1-1-1-1-1-2-1l-1-1-2 1c-2-1-5-2-8-2h-1l-2 1h0-3 0v1h-2-2-2c-1 1-1 3-1 4h-1l-1-1c0 1 0 3-1 4v1 1l4 10v1c0 1 0 1-1 2-6-9-14-15-22-20-5-3-10-6-14-10l-2-5-1 1-2-2c-1 0-1 1-1 1l1 1v1l-1 1 2 2 2 2h1-1s-1 0-1-1h-1c-1 0-1-1-2-1h0c-1-1-2-1-2-1h-1c1 1 2 1 3 2v1h-1-1c-4-1-6-2-10-2h-2v-1h-2c-1 1-3 0-4 1-1-1-1-1-2-3l-2-2v-2c-1 0-1 0-2-1-1 0-2-1-3-2l-2-2c-1-1-2-2-2-3h-1 0c-1-1-2-2-3-4 0 0-6-3-7-4l2-1c-1-1-1-1-2-1 0 0-1 1-2 1h0l-8-2 29-6c1 0 2 0 3-1h1 3c2 0 3 0 4 2l1-3h3l11-2h4c1-1 2-1 3-1 3 0 5-1 8-1l17-2 1 1v1h0v1c2 0 3-1 4-2 1 0 0 0 1-1z" class="D"></path><path d="M321 443h4v1h-3l-1-1z" class="m"></path><path d="M287 464c1 0 2-1 3 0h2-3v2 1c-1-1-2-2-2-3z" class="W"></path><path d="M284 453h0c1 0 1 0 1 1h1 4c0 1 0 1-1 2l-2-1c-2-1-4-1-6-1h3v-1z" class="T"></path><path d="M339 444s1 0 2 1l-1 1h1l-1 1c-1-2-4-1-6-1h-1l1-2h5z" class="M"></path><path d="M277 451c3 0 4 2 7 1v1 1h-3l-3 1c-1-1-1-1-2-1h0c1-1 1-1 1-3z" class="W"></path><path d="M274 455c0-1-2-2-3-2h-1c1-1 3-1 4-2h1 2c0 2 0 2-1 3h0s-1 1-2 1z" class="U"></path><path d="M275 451h2c0 2 0 2-1 3v-1h-1v-2z" class="S"></path><path d="M291 452c2-1 4 0 6 1-1 1 0 1 0 2l-1 1c-2 0-3-1-5-2l1-1-1-1z" class="F"></path><path d="M319 468l-1-1h-1c-1-2-1-3-1-6l1 2h3 0c0 1 1 3 1 4-1 0-1 0-2 1z" class="M"></path><path d="M317 463h3l-1 1h-2v-1h0z" class="T"></path><path d="M321 454v3h1l-1 1c0 1-1 1-1 2v3h0-3l-1-2h1v-1l1-2c1-1 2-2 3-4z" class="L"></path><path d="M284 452c-1-1-3-1-4-1v-1c3-1 8 1 11 2l1 1-1 1h-1 0-4-1c0-1 0-1-1-1h0v-1z" class="M"></path><path d="M297 453c4 2 6 4 8 8l1 2c-2-2-4-4-8-6h0c-1 1 0 1-1 2-1-1-1-2-1-3l1-1c0-1-1-1 0-2z" class="U"></path><path d="M310 452c2-2 4-3 6-3 1 0 2 1 3 1v1c0 1-1 3-2 4-1-1-1-3-2-3h-4-1z" class="M"></path><path d="M349 450c0 2 1 4 1 6-1 1-2 2-4 3h-2c-1-2 0-4 0-5 0 0 0 1 1 2 1-1 1-2 2-3v-2s1 0 2-1z" class="T"></path><path d="M307 458h0c0-2 1-4 3-6h1c1 2 3 1 3 3-1 1-1 2-1 3v1h-1c-1-1-1-1-3-1h0v2c-1 0-1-1-2-2z" class="S"></path><path d="M313 458c-1-1-2-1-2-1l-1-1h0c1-1 2 0 3-1h1c-1 1-1 2-1 3z" class="B"></path><path d="M307 458c1 1 1 2 2 2v-2h0c2 0 2 0 3 1h1l-1 1c-1-1-1-1-2-1v1l2 2 1 1v1l-3-2v1c1 1 1 1 2 1 1 1 1 2 2 3 1 0 1 1 2 1h-4c-3-2-4-6-5-10z" class="W"></path><path d="M302 446c2 0 3 0 4 2-1 1-1 4-3 5v-3h-1 0v1 1c-3-2-5-3-7-5 1 0 2 0 3-1h1 3z" class="o"></path><path d="M334 446c-2 0-6 1-7 2 1 1 2 1 3 2h-2c-4 1-5-2-8-3h0v-1h1c3-2 3-1 6-1 4-1 7-1 12-1h-5l-1 2h1z" class="S"></path><path d="M319 468c1-1 1-1 2-1h0c0 2 0 3 1 4 0 1 1 2 2 3l-1 1-2-2c-1 0-1 1-1 1l1 1v1l-1 1c-3-3-6-6-8-9h4c0 1 1 1 1 1l2-1z" class="Q"></path><path d="M319 468c1-1 1-1 2-1h0c0 2 0 3 1 4 0 1 1 2 2 3l-1 1-2-2-2-2h0-2l-1-1v-1h1l2-1z" class="T"></path><path d="M298 476c2 0 3-1 5-1h0c1-1 4-1 5-2 2 1 3 2 5 3 1 0 2 1 4 2 1 1 2 1 3 2v1h-1-1c-4-1-6-2-10-2h-2v-1h-2c-1 1-3 0-4 1-1-1-1-1-2-3z" class="S"></path><path d="M304 478h3c1-1 2-1 4-1v1c-1 0-2 0-3 1h-2v-1h-2z" class="T"></path><path d="M292 464h2c7 1 13 4 17 9-2-1-6-3-9-2v-1l-2 1c-1 1-3 2-4 3v-2c-1 0-1 0-2-1-1 0-2-1-3-2l-2-2v-1-2h3z" class="M"></path><path d="M292 464h2l1 2h0-4-2v-2h3z" class="h"></path><path d="M289 466h2c2 1 6 0 7 2-1 1-3 1-5 0l-2 1-2-2v-1z" class="K"></path><path d="M291 469l2-1c2 1 4 1 5 0h1 1c1 1 2 1 2 2l-2 1c-1 1-3 2-4 3v-2c-1 0-1 0-2-1-1 0-2-1-3-2z" class="d"></path><path d="M294 471c2 0 3 0 5-1 1 0 1 0 1 1-1 1-3 2-4 3v-2c-1 0-1 0-2-1z" class="W"></path><path d="M341 445c3 1 6 3 8 5-1 1-2 1-2 1v2c-1 1-1 2-2 3-1-1-1-2-1-2-1-2-1-2-3-2s-2 1-3 3c-1 0-1 1-1 1v2 1c3 3 5 4 9 4h-4l-3-2c-1 0-3-3-3-4h-1c-1-2-2-3-3-4-2-1-5 0-7 1 0-1 0-1-1-2h2c1-1 3-1 5-1 1 0 3 2 4 2v-5l5-1 1-1h-1l1-1z" class="AA"></path><path d="M359 439c0 2 1 3 1 5h0v1l3 9c-1 0-2 0-3 1h-1-2-1l-1 1s-1 1-2 1c1-3 1-5 1-8v-1c-1-1-1-2-2-3l-5-2c3 0 5 0 7-1 2 0 3-1 4-2 1 0 0 0 1-1z" class="M"></path><path d="M359 451c0-2 1-1 2-3-1 0-2 0-3 1v-1c1-1 2-1 2-3h0l3 9c-1 0-2 0-3 1h-1-2-1s0-1 1-1h-1l3-3z" class="B"></path><path d="M359 451h1 1c-1 2-3 3-4 4h-1s0-1 1-1h-1l3-3z" class="W"></path><path d="M321 454c1 0 2-1 3-2 1 1 1 1 1 2 2-1 5-2 7-1 1 1 2 2 3 4h1c0 1 2 4 3 4l3 2h4c-4 0-6-1-9-4v-1-2s0-1 1-1c0 2 0 3 1 5 2 1 4 2 7 2 2 0 4-1 6-3l1-1v-1c1 0 2-1 2-1l1-1h1 2 1c1-1 2-1 3-1 1 3 3 4 5 6l6 8c1 1 1 2 2 3l6 7 4 5c2 3 3 5 4 7h0-1v-1c-1-1 0-1-1-1-1-1-1-1-2-1l-1-1-2 1c-2-1-5-2-8-2h-1l-2 1h0-3 0v1h-2-2-2c-1 1-1 3-1 4h-1l-1-1c0 1 0 3-1 4v1 1l4 10v1c0 1 0 1-1 2-6-9-14-15-22-20-5-3-10-6-14-10l-2-5c-1-1-2-2-2-3-1-1-1-2-1-4h0c0-1-1-3-1-4v-3c0-1 1-1 1-2l1-1h-1v-3z" class="c"></path><path d="M321 467h2c0 2 0 3-1 4-1-1-1-2-1-4z" class="b"></path><path d="M327 470c-1-1-1-2-1-3h1c1 1 1 1 1 2h3-1c-1 0-2 0-3 1z" class="B"></path><path d="M347 477h1v1l2 2v-2l2 3c-2 0-3-1-4-2 0 0 0-1-1-2z" class="b"></path><path d="M321 458c1 1 1 1 2 1s2-1 3-1h1l-2 2h-3-2c0-1 1-1 1-2z" class="J"></path><path d="M321 454c1 0 2-1 3-2 1 1 1 1 1 2l-3 3h-1v-3z" class="U"></path><path d="M333 468l4 4 3 3c-1 1-1 1-1 2h0c-1-1-2-2-2-3v-1l-2-1c-1 1-2 0-3 0-1-1-2 0-3 0h-3l-1-1 2-1c1-1 2-1 3-1h1c1 0 1 0 2-1z" class="T"></path><path d="M333 468l4 4 3 3c-1 1-1 1-1 2h0c-1-1-2-2-2-3v-1l-2-1c-1-1-3-2-5-3h1c1 0 1 0 2-1z" class="s"></path><path d="M347 479c-1-1-3-3-3-5 0-1 0-4 1-5h0v-1c2-1 5-2 7-1 1 0 1 0 1 1v1c-1 0-3-1-4 0-1 0-2 1-2 2-1 2 0 4 0 6h0c1 1 1 2 1 2h-1z" class="S"></path><path d="M329 472c1 0 2-1 3 0 1 0 2 1 3 0l2 1v1c0 1 1 2 2 3h-1c0 1 0 1-1 1-2 1-4 1-6 0v1 1h-1c-1-1-2-2-2-4 0-1 0-2 1-2v-2z" class="T"></path><path d="M329 472c1 0 2-1 3 0 1 0 2 1 3 0l2 1v1c-3-1-5-1-8 0v-2z" class="J"></path><path d="M331 478v-1c0-1 0-1 1-2 3 0 4 1 6 2 0 1 0 1-1 1-2 1-4 1-6 0z" class="C"></path><g class="S"><path d="M356 455h1 2 1l-5 5c-1 1-2 2-4 2-3 2-6 2-9 2-3-1-6-3-7-6v-1h1c0 1 2 4 3 4l3 2h4c-4 0-6-1-9-4v-1-2s0-1 1-1c0 2 0 3 1 5 2 1 4 2 7 2 2 0 4-1 6-3l1-1v-1c1 0 2-1 2-1l1-1z"></path><path d="M333 468c-2-2-4-4-5-6 1-1 1-2 2-3h3v1l-1 1c-1 1-1 1 0 2h5 2v2c0 1-1 1-1 2h0c-1 0-1 0-2 1l3 3h-2v1l-4-4z"></path></g><path d="M332 463h5c-1 1-1 2-1 3h-1 0l-3-3z" class="O"></path><path d="M355 466h0v-2c0-1 1-3 3-4 2 0 3 0 4 1 1 0 3 2 3 3h0c0 1-1 1-1 2 2 1 3 1 5 1-1 1-1 1-2 1l-6 1h-2c-1 0-2 0-2-1v-1c-1-1-1-1-2-1z" class="G"></path><path d="M359 466c-1 0-1 0-2-1 0-1 0-1 1-2s1-1 3-1c1 0 2 1 3 2l-2 2h-3z" class="B"></path><path d="M355 466h0v-2c0-1 1-3 3-4 2 0 3 0 4 1l-1 1c-2 0-2 0-3 1s-1 1-1 2c1 1 1 1 2 1v1h2v1 1h-2c-1 0-2 0-2-1v-1c-1-1-1-1-2-1z" class="o"></path><path d="M337 472v-1h2c2 3 5 6 7 8h1 1c1 1 2 2 4 2 1 2 2 6 4 7l1 1c0 1-1 1-1 2l3 5 4 10v1c-3-3-4-7-5-10-5-9-13-13-21-15l-6-3v-1c2 1 4 1 6 0 1 0 1 0 1-1h1 0c0-1 0-1 1-2l-3-3z" class="M"></path><path d="M331 478c2 1 4 1 6 0 1 2 2 1 3 2l-1 1c-1 0-1 0-2 1l-6-3v-1z" class="N"></path><path d="M340 475l13 13 1 1h-1c-3-3-6-5-10-7-2-2-4-3-4-5 0-1 0-1 1-2z" class="k"></path><path d="M337 472v-1h2c2 3 5 6 7 8h1 1c1 1 2 2 4 2 1 2 2 6 4 7l1 1c0 1-1 1-1 2-1-1-1-2-2-2l-1-1-13-13-3-3z" class="q"></path><path d="M347 479h1c1 1 2 2 4 2 1 2 2 6 4 7l1 1c0 1-1 1-1 2-1-1-1-2-2-2l-1-1v-1c-2-3-4-5-7-8h1z" class="s"></path><defs><linearGradient id="O" x1="356.184" y1="481.021" x2="378.125" y2="482.145" xlink:href="#B"><stop offset="0" stop-color="#141414"></stop><stop offset="1" stop-color="#25191a"></stop></linearGradient></defs><path fill="url(#O)" d="M355 466c1 0 1 0 2 1v1c0 1 1 1 2 1h2l6-1c1 0 1 0 2-1 1 0 3 0 4 1h1c1 1 1 2 2 3l6 7 4 5c2 3 3 5 4 7h0-1v-1c-1-1 0-1-1-1-1-1-1-1-2-1l-1-1-2 1c-2-1-5-2-8-2h-1l-2 1h0-3 0v1h-2-2-2c-1 1-1 3-1 4h-1l-1-1c0 1 0 3-1 4v1 1l-3-5c0-1 1-1 1-2l-1-1c-2-1-3-5-4-7l-2-3v-4c0-1 2-2 3-4l1-1c1-1 1-2 1-3z"></path><path d="M369 467c1 0 3 0 4 1h1c1 1 1 2 2 3h-1c-1-1-3-1-4-2s-3-1-4-1c1 0 1 0 2-1z" class="n"></path><path d="M360 481l2 1v1c-1 2-1 4-2 7 0 1 0 3-1 4-1-4 0-9 1-13z" class="U"></path><path d="M361 478v-1c1-2 2-4 4-5l-1 2v1h0c0 2 0 2 1 3-1 0-1 1-2 2v1 1s0 1-1 1v-1l-2-1 1-3z" class="G"></path><path d="M361 478h1v4l-2-1 1-3z" class="N"></path><path d="M365 472c2-1 5-1 7 0 1 0 1 1 2 2l-1 1-2-1h0v1l-2-1c-2 0-3 0-5 1v-1l1-2z" class="F"></path><path d="M355 466c1 0 1 0 2 1v1c-1 1 0 2 0 3 1 2 1 6-1 7-1-3-1-5-2-9 1-1 1-2 1-3z" class="U"></path><path d="M364 475c2-1 3-1 5-1l2 1c-1 1-2 1-3 2v4h0l-1 1h-4v-1-1c1-1 1-2 2-2-1-1-1-1-1-3h0z" class="P"></path><path d="M369 474l2 1c-1 1-2 1-3 2-1 0-2 0-3-1h1l1-1h1l1-1z" class="H"></path><path d="M365 478h2v1c-1 0-2 1-2 2l2 1h-4v-1-1c1-1 1-2 2-2z" class="F"></path><path d="M353 470l1-1c1 4 1 6 2 9v1l1 1c0 1 0 1-1 2h-1l-1-1c0 1 0 2 1 2l1 5c-2-1-3-5-4-7l-2-3v-4c0-1 2-2 3-4z" class="U"></path><path d="M353 470l1-1c1 4 1 6 2 9v1l1 1c0 1 0 1-1 2h-1l-1-1v-1c0-3-2-5-2-7 1-1 1-2 1-2v-1z" class="O"></path><path d="M371 475v-1h0l2 1 1-1 1 2v1h2 1l1 1v1h-1c-1 0-2 0-3 2h1 1c1-1 2-1 2-1 1-1 2-1 3-2l4 5c2 3 3 5 4 7h0-1v-1c-1-1 0-1-1-1-1-1-1-1-2-1l-1-1-2 1c-2-1-5-2-8-2h-1l-2 1h0-3 0v1h-2-2-2c-1 1-1 3-1 4h-1l-1-1c1-3 1-5 2-7 1 0 1-1 1-1h4l1-1h0v-4c1-1 2-1 3-2z" class="c"></path><path d="M368 483l1-1c1-1 3 0 5 0l-1 1h0l-3 1h-1l-1-1z" class="U"></path><path d="M363 487c1-2 3-4 5-4l1 1h1 0c-1 0-1 0-2 1 0 0 0 1-1 1h-1v1h1-2-2z" class="F"></path><path d="M373 483l2 2h-1l-2 1h0-3 0v1h-2-1v-1h1c1 0 1-1 1-1 1-1 1-1 2-1h0l3-1z" class="V"></path><path d="M374 482h1c4 2 7 2 10 4l-2 1c-2-1-5-2-8-2l-2-2h0l1-1z" class="W"></path><path d="M371 475v-1h0l2 1 1-1 1 2v1c-3 0-5 3-7 4h0v-4c1-1 2-1 3-2z" class="M"></path><path d="M419 132h1c8-6 15-7 25-5 4 1 8 3 10 7 1 2 1 4 0 5 0 2-1 4-3 4s-2-3-4-5l-1 1v2 4s0 1 1 2h0 1c2 1 5 4 8 3l2-3h1l1 2h3v-2s0-1 1-2c1-2 0-6 0-9l4 9c2 4 4 7 7 10 2 0 5 3 6 3 1-1 1-1 2-1 1 1 4 2 4 4h1 2c2 1 4 3 5 3 2 1 3 3 4 4v1c-1 0-1 1-1 1-1 0-6-3-7-3-3-1-5-1-7 0 0 0 0 1-1 2-1 0-2 1-3 3 1 1 0 0 0 1 1 1 1 3 1 4v1h-3-1l-1 2h0c-2 0-3 1-4 1v1-2c-1 0-1-1-1-1-1 0-1 1-2 1 0 1-1 2-1 3-1 0-2 2-2 2l-1 2-1-1c-2 3-2 4-2 7h-1v-3l-2 2v-1c-1-1-1-2-1-2 1-2 1-4 1-6h-1c0 5-1 9-5 12-1 1-2 1-2 2l-1-1c-1 0-1 1-2 0 0-1 1-1 1-2h-3c3-1 5-2 6-4h0c-1 0-2 1-4 1v-1h0c-2 1-4 1-5 2-1 0-1 1-1 1-1 1-1 0-2 0-1 1-3 1-4 2 0 1-1 3-2 4-2 1-3 3-5 5 0 1-1 2-1 3h-1v-1l-8-18-11-19v-2h1c0-1-1-4-1-5-1-4-1-8-1-13 0-2 0-5 1-7 1-6 5-9 9-12l1 1s1-1 2-1h0l-2 2h0z" class="C"></path><path d="M450 178c1-1 1-1 2 0h0l-1 1h-3v-1h2z" class="D"></path><path d="M450 194l3-1v1l-2 2c-1 0-1 1-2 0 0-1 1-1 1-2z" class="n"></path><path d="M427 158c1-2 2-3 3-5l2 2-4 3h-1z" class="e"></path><path d="M476 168h1c0 1-1 2-1 2-2 0-2 0-3 1l-2 2h0l-1 1-1-1c2-2 5-4 7-5z" class="K"></path><path d="M428 192v-2c-1-3 0-5-1-7h0c-1-2-2-3-4-4l1-1c2 1 4 4 4 6s0 4 1 6h1v1l-1-1-1 2z" class="O"></path><path d="M445 167h0 1c1 4 1 9-1 12v1h-1c2-5 1-9 1-13z" class="E"></path><path d="M431 162h0-1-1c-1 1-2 0-3 0 0-1 0-3 1-4h1l1 1h1s1 0 2-1v1h1v1h0c-1 1-2 1-2 2z" class="P"></path><path d="M421 163c2 1 6 2 8 3-1 1-2 1-3 1h-2c-1 0-2 0-3-1v-1l-1 1h0c0-1 1-2 1-3z" class="N"></path><path d="M441 191h2c2-1 4-2 7-2 1-1 2-1 4-1 1 0 1 0 1 1s-1 1-2 1h0c-1 0-2 1-4 1v-1h0c-2 1-4 1-5 2-1 0-1 1-1 1-1 1-1 0-2 0v-2z" class="n"></path><path d="M464 182c1-3 2-5 2-7s-2-4-3-5h0c1-1 4 3 5 3 1 1 1 0 1 0l1 1v1l-3 6h0-1c-1-1 1 0 0-1l-2 2z" class="Q"></path><path d="M430 159c1-1 3-3 4-3h1c1 0 1-1 2-2 0-1 0-1 1-2v2 2c-1 0-1 0-1 1s-1 2-2 3h0l-1 1c-1 1-2 1-3 1 0-1 1-1 2-2h0v-1h-1v-1c-1 1-2 1-2 1z" class="t"></path><path d="M414 139h1c3-1 5-4 7-6l-1 2h3v1c1 0 3-1 4-2v1l2 2h-1l-1 1s0-1-1-1-2 0-4-1c-1 1-3 2-5 3-1 1-3 2-5 2h0c0-1 1-1 1-2z" class="p"></path><path d="M420 166l1-1v1c1 1 2 1 3 1h2l-1 1h1 0c1 0 3 0 4 1l3 1h0c-2 1-4 1-6 1h-2 0v1c-2-2-4-4-5-6z" class="D"></path><path d="M430 169l3 1h0c-2 1-4 1-6 1-1-1-2-1-2-1v-1h5z" class="F"></path><path d="M429 190c1-1 1-2 2-3h1v1c-1 0-1 1-1 2h0c1 0 2-2 3-3h1c0 1-1 2-2 3l-1 1 1 1-1 1h-1c-1 0-1 0-1 1v3l-1 1h0l-2 2c-1-3 1-6 3-9v-1h-1z" class="I"></path><path d="M428 138l1-1h1c2 3 6 6 6 10v1h0c-2 2-2 5-4 7l-2-2c1-1 1-2 2-3-1-4-2-9-4-12z" class="E"></path><path d="M470 175s0 1 1 1h0c1 1 1 2 1 3-1 0-1 1-2 1 0 1-1 2-1 3-1 0-2 2-2 2l-1 2-1-1c-2 3-2 4-2 7h-1v-3l-2 2v-1c-1-1-1-2-1-2 1-2 1-4 1-6h0l1 4 1-1c0-1 1-3 2-4l2-2c1 1-1 0 0 1h1 0l3-6z" class="P"></path><path d="M464 182l2-2c1 1-1 0 0 1h1 0c-1 2-3 5-5 6v-1c0-1 1-3 2-4z" class="G"></path><path d="M471 176c1 1 1 2 1 3-1 0-1 1-2 1 0 1-1 2-1 3-1 0-2 2-2 2l-1 2-1-1c2-4 4-6 6-10h0z" class="E"></path><path d="M418 181c3 2 4 4 5 6s2 3 2 5c1 0 0 1 0 1h2l1-1 1-2 1 1c-2 3-4 6-3 9 1 2 2 4 1 6h0l-8-18c-1-1-1-1-1-2h0l1-1-2-4z" class="G"></path><path d="M425 196v-2-1h2c0 1-1 4-2 4v-1z" class="O"></path><path d="M418 181c3 2 4 4 5 6s2 3 2 5c1 0 0 1 0 1v1 2l-5-11-2-4z" class="J"></path><path d="M459 183v-1c-1-7-7-11-12-16h0 1v1c1 1 3 2 4 3 2 0 3 1 4 2h0c0-2 0-3-2-4-1-2-4-1-6-2 4 0 8-1 11 1 2 4 2 7 1 11v5h0-1z" class="q"></path><path d="M476 156c2 1 2 3 4 3 1 0 2 1 2 1v1h0c1 1 1 1 2 1l1 1h-1 0-2l-5 1c1 1 1 1 1 2h-2l-4 1c-1 1-2 1-2 1h-3c-1-2 7-7 8-9 1-1 1-2 1-3z" class="G"></path><defs><linearGradient id="P" x1="480.252" y1="164.593" x2="478.229" y2="161.427" xlink:href="#B"><stop offset="0" stop-color="#171614"></stop><stop offset="1" stop-color="#23181b"></stop></linearGradient></defs><path fill="url(#P)" d="M470 168c1-2 2-3 4-4 3-2 4-4 8-3h0c1 1 1 1 2 1l1 1h-1 0-2l-5 1c1 1 1 1 1 2h-2l-4 1c-1 1-2 1-2 1z"></path><path d="M472 167h-1c2-1 5-2 6-3 1 1 1 1 1 2h-2l-4 1z" class="G"></path><path d="M434 192c0-1 2-1 3-1h4v2c-1 1-3 1-4 2 0 1-1 3-2 4-2 1-3 3-5 5 0 1-1 2-1 3h-1v-1h0c1-2 0-4-1-6l2-2h0l1-1v-3c0-1 0-1 1-1h1l1-1h1z" class="X"></path><path d="M434 193h1 1l1 1-2 1c-1 0-1-1-1-2z" class="K"></path><path d="M435 195l-2 2h-1l1-1c0-1 0-1 1-2v-1c0 1 0 2 1 2z" class="p"></path><path d="M429 198h0l1-1v-3c0-1 0-1 1-1h1l-2 8h-1v-3z" class="D"></path><path d="M427 200l2-2v3h1v3h0c0 1-1 2-1 3h-1v-1h0c1-2 0-4-1-6z" class="B"></path><path d="M434 192c0-1 2-1 3-1h4v2c-1 1-3 1-4 2v-1h0l-1-1h-1 1v-1h-2z" class="Z"></path><path d="M436 193c1 0 2-1 2-1h1v1c-1 0-1 0-2 1h0l-1-1h-1 1z" class="n"></path><path d="M459 147h1l1 2h3-1c0 1-1 1-1 1h-1 0c1 0 1 0 0 1v1h2c1 1 2 1 2 2 1 1 1 1 0 2 0 2-2 3-3 3s-3 1-5 0h0v-1-1c-1 0-3-1-4-2-2-2-2-4-4-6h0v-2c2 1 5 4 8 3l2-3z" class="B"></path><path d="M459 147h1l1 2-1 1 1 1h-1c-1 0-2-1-3-1l2-3z" class="Z"></path><path d="M457 157v-1l1-1c2-2 3-3 5-3 1 1 2 1 2 2v1c-1-1-1-1-3-1-1 0-2 1-2 3l-1-1v-1h-1v1h0s0 1-1 1h0z" class="j"></path><path d="M460 157c0-2 1-3 2-3 2 0 2 0 3 1v-1c1 1 1 1 0 2 0 2-2 3-3 3s-3 1-5 0h0v-1-1h0 0c1 0 1-1 1-1h0v-1h1v1l1 1z" class="E"></path><path d="M460 157c0-2 1-3 2-3 2 0 2 0 3 1-1 1-1 2-2 2-1 1-1 1-2 1s-1-1-1-1z" class="I"></path><defs><linearGradient id="Q" x1="416.628" y1="163.652" x2="427.321" y2="160.413" xlink:href="#B"><stop offset="0" stop-color="#cb0c10"></stop><stop offset="1" stop-color="#90292b"></stop></linearGradient></defs><path fill="url(#Q)" d="M425 172c4 5 11 5 16 8l-6-1c-3-1-6-1-8-2-5-2-8-6-10-10-2-6-3-17 0-23 1-3 4-5 7-6l-3 3c-3 3-4 7-4 11s1 8 4 11h0c0 1-1 2-1 3h0c1 2 3 4 5 6z"></path><defs><linearGradient id="R" x1="407.338" y1="154.817" x2="421.291" y2="156.7" xlink:href="#B"><stop offset="0" stop-color="#c41313"></stop><stop offset="1" stop-color="#9c1d25"></stop></linearGradient></defs><path fill="url(#R)" d="M418 130l1 1s1-1 2-1h0l-2 2h0c-2 2-3 4-5 7 0 1-1 1-1 2-1 8-2 15-1 23 2 6 4 12 6 17l2 4-1 1h0c0 1 0 1 1 2l-11-19v-2h1c0-1-1-4-1-5-1-4-1-8-1-13 0-2 0-5 1-7 1-6 5-9 9-12z"></path><path d="M443 140h0l3 2c1 0 1 0 1-1v4s0 1 1 2h0 1v2h0c2 2 2 4 4 6 1 1 3 2 4 2v1 1h-1v1 1h0c-2 0-4 0-6-1-3-1-6-2-8-5-3-4-4-9-3-14 1-1 3-1 4-1z" class="L"></path><path d="M450 160h1c-1-1-2-1-2-2-1 0-2-1-3-1l-1-1-2-2-1-1v-1h1c1 2 3 3 5 5 1 1 2 0 2 0 2 2 4 3 6 3h0v1h0c-2 0-4 0-6-1z" class="d"></path><path d="M446 153l-4-4v-3-5l4 4c1 1 1 3 1 4l1 2h0c-1-1-2-3-3-5h0v4c1 1 1 2 2 2l-1 1z" class="P"></path><path d="M447 152c-1 0-1-1-2-2v-4h0c1 2 2 4 3 5h0l-1-2c1 0 1 1 2 1h0v-1h0c2 2 2 4 4 6 1 1 3 2 4 2v1 1h-1v1h0c-2 0-4-1-6-3l-4-4 1-1z" class="Q"></path><path d="M447 152c3 3 5 5 9 7v1h0c-2 0-4-1-6-3l-4-4 1-1z" class="I"></path><path d="M476 155c2 0 5 3 6 3 1-1 1-1 2-1 1 1 4 2 4 4h1 2c2 1 4 3 5 3 2 1 3 3 4 4v1c-1 0-1 1-1 1-1 0-6-3-7-3-3-1-5-1-7 0 0 0 0 1-1 2-1 0-2 1-3 3 1 1 0 0 0 1 1 1 1 3 1 4v1h-3-1l-1 2h0c-2 0-3 1-4 1v1-2c-1 0-1-1-1-1 0-1 0-2-1-3h0c-1 0-1-1-1-1v-1l1-1h0l2-2c1-1 1-1 3-1 0 0 1-1 1-2h-1v-2h2c0-1 0-1-1-2l5-1h2 0 1l-1-1c-1 0-1 0-2-1h0v-1s-1-1-2-1c-2 0-2-2-4-3v-1z" class="U"></path><path d="M478 166h2c-1 1-2 1-3 2h-1v-2h2z" class="D"></path><path d="M476 170l-1 1c-1 2-2 6-2 8 4 1 6-6 8-9 1-1 2-2 4-3h0s0 1-1 2c-1 0-2 1-3 3 1 1 0 0 0 1 1 1 1 3 1 4v1h-3-1l-1 2h0c-2 0-3 1-4 1v1-2c-1 0-1-1-1-1 0-1 0-2-1-3h0c-1 0-1-1-1-1v-1l1-1h0l2-2c1-1 1-1 3-1z" class="H"></path><path d="M471 173c1 1 1 1 1 2l-1 1h0c-1 0-1-1-1-1v-1l1-1z" class="D"></path><path d="M481 172c1 1 0 0 0 1 1 1 1 3 1 4v1h-3-1l3-6z" class="l"></path><path d="M476 155c2 0 5 3 6 3 1-1 1-1 2-1 1 1 4 2 4 4h1 2c2 1 4 3 5 3 2 1 3 3 4 4v1c-2-2-4-4-7-5h-6c-2 0-4-1-5-1h2 0 1l-1-1c-1 0-1 0-2-1h0v-1s-1-1-2-1c-2 0-2-2-4-3v-1z" class="b"></path><path d="M484 157c1 1 4 2 4 4l-6-3c1-1 1-1 2-1z" class="S"></path><path d="M482 160h2c0 1 0 1 1 1 0 0 1 0 1 1h1v2c-2 0-4-1-5-1h2 0 1l-1-1c-1 0-1 0-2-1h0v-1zm-63-28h1c8-6 15-7 25-5 4 1 8 3 10 7 1 2 1 4 0 5 0 2-1 4-3 4s-2-3-4-5l-1 1v2c0 1 0 1-1 1l-3-2h0c-1 0-3 0-4 1v-1c-2-2-8-4-11-5v-1c-1 1-3 2-4 2v-1h-3l1-2c-2 2-4 5-7 6h-1c2-3 3-5 5-7z" class="D"></path><path d="M422 133c2-1 4-3 6-4l1 1c-1 2-2 4-5 5h0-3l1-2z" class="g"></path><path d="M428 134c1-1 2-1 4-1 4 1 10 2 13 5v1c1 0 1 0 1 1 1-1 1-1 1-2h1l-1 1v2c0 1 0 1-1 1l-3-2h0c-1 0-3 0-4 1v-1c-2-2-8-4-11-5v-1z" class="Y"></path><path d="M435 135c2 0 6 1 8 3 1 0 1 0 1 1l-1 1h0-1c-3-1-5-3-7-5z" class="C"></path><path d="M428 134c1-1 2-1 4-1l2 2h1c2 2 4 4 7 5h1c-1 0-3 0-4 1v-1c-2-2-8-4-11-5v-1z" class="E"></path><path d="M286 486c1 1 2 1 2 2h0c1 1 1 2 2 2h0l3 3c0 1 0 1 1 1v1l1 1c2 2 4 4 4 6 1 0 2 0 2-1 2 0 2-1 3 0-1 1-1 1-2 1s-1 1-2 1c1 2 3 4 4 5 1 0 1 1 2 2 0 1 1 1 1 2l4 5c1 1 1 2 2 2 0 1 1 1 1 2l3 4 1 1v1h1l1 2c1 1 1 1 1 2 1 0 1 0 1 1l2 2c2 2 3 5 5 7l2 2c0 1 1 1 1 2l1 1c2 3 4 6 6 8 1 1 2 3 3 4 0 1 1 1 2 2l1 2 2 3c1 1 2 3 3 4v2h1l9 14c-13 7-26 11-41 12-1-1-2-2-3-4v1l-1 1h0c1 1 1 0 1 1l-6-3c-4-1-7-3-10-7 0-1-6-13-7-14 0-1-1-2-2-2v-1c2 1 3 2 4 3 0 1 1 2 1 2h1c0-2 1-4 1-6 1-3-1-9-2-11 1 0 1 0 2 1v-1c1-1 1-1 0-3 0-1-1-3-2-4l-3-3c1-2 0-5 0-6-3-7-8-6-14-5h-4 0c-2-1-3-2-4-2l-6-9c1 0 1 0 1-1s-1-2-1-2c-1-3-3-5-3-8 1-4 7-6 10-9h-2l2-2c6-5 10-10 14-17z" class="C"></path><path d="M297 549c2 2 3 5 4 7-1 1-2 3-2 4 0 2 1 4 1 6 0 4-2 7-1 11 2 5 4 10 9 13 3 1 6 2 8 3v1l-1 1h0c1 1 1 0 1 1l-6-3c-4-1-7-3-10-7 0-1-6-13-7-14 0-1-1-2-2-2v-1c2 1 3 2 4 3 0 1 1 2 1 2h1c0-2 1-4 1-6 1-3-1-9-2-11 1 0 1 0 2 1v-1c1-1 1-1 0-3 0-1-1-3-2-4l1-1z" class="h"></path><path d="M290 490h0l3 3c0 1 0 1 1 1v1l1 1c2 2 4 4 4 6 1 0 2 0 2-1 2 0 2-1 3 0-1 1-1 1-2 1s-1 1-2 1c1 2 3 4 4 5 1 0 1 1 2 2 0 1 1 1 1 2l4 5c1 1 1 2 2 2 0 1 1 1 1 2l3 4 1 1v1h1l1 2c1 1 1 1 1 2 1 0 1 0 1 1l2 2c2 2 3 5 5 7l2 2c0 1 1 1 1 2l1 1c2 3 4 6 6 8 1 1 2 3 3 4 0 1 1 1 2 2l1 2 2 3c1 1 2 3 3 4v2c-2-2-3-5-5-8l-11-14-35-46-3 5c2 4 4 8 5 12 1 7-3 12-7 17 1 3 1 7 2 10l1 2-1 1-3-3c1-2 0-5 0-6-3-7-8-6-14-5h-4 0c-2-1-3-2-4-2l-6-9c1 0 1 0 1-1l2 2c1-1 3-2 4-3l7-6h3v1l-12 11c1 0 2 1 2 1 2 2 3 3 4 3h5c4 0 7 0 11 2h0c3-3 5-7 6-11 1-6-2-11-5-15-2 0-4 2-5 4-1-1-1-1-1-2l11-9c-1-2-3-6-5-7-1 0-1-1-1-1v-1c-1-1-1-1-2-1l-2 2c0 1 0 1-1 1l3-5z" class="r"></path><g class="C"><path d="M288 513c1-2 3-4 5-4 3 4 6 9 5 15-1 4-3 8-6 11h0c-4-2-7-2-11-2h-5c-1 0-2-1-4-3 0 0-1-1-2-1l12-11 6-5z"></path><path d="M286 486c1 1 2 1 2 2h0c1 1 1 2 2 2l-3 5c1 0 1 0 1-1l2-2c1 0 1 0 2 1v1s0 1 1 1c2 1 4 5 5 7l-11 9c0 1 0 1 1 2l-6 5v-1h-3l-7 6c-1 1-3 2-4 3l-2-2c0-1-1-2-1-2-1-3-3-5-3-8 1-4 7-6 10-9h-2l2-2c6-5 10-10 14-17z"></path></g><path d="M279 517l8-6c0 1 0 1 1 2l-6 5v-1h-3z" class="h"></path><path d="M286 486c1 1 2 1 2 2h0c-2 1-3 3-4 4 0 2-1 3-2 4l-4 4c-2 2-4 3-6 5h-2l2-2c6-5 10-10 14-17z" class="S"></path><path d="M521 375c1-1 3-1 4-1 3-1 6 1 8 3s3 6 5 7l-2-5 1-1 2 3c0 3 2 6 3 10 2 6 0 13-1 19l-1-1c-2 8-2 16-2 23-1 8-1 16-1 23l-2 25v1l-2 8h0l-1-1v1l-1 2c-1-1-2-3-2-4s-1-1-1-1c-2 0-4-3-5-4-3-1-5-1-8-2-1-1-2-2-3-2-8-2-14-6-20-12 0-2-1-4-2-5l-2-2c-2-2-3-4-5-6h0v-1c-1 0-2-1-3-1h-1-1l-1-2v2h-4c-2 0-6 0-8 1h-1v-1h0l-1-1v-2s3-2 4-3h-1v-2l1-2v-1l1-1-1-1c-3 2-6 4-9 5-1-1-1-1-1-2l10-6c4-2 9-3 11-8 1 0 1 0 1-1 1-1 3 0 5 0v-1l-1-1h0c1-2 1-5 1-7s1-4 2-6v1c1 0 1-1 2-2 2 0 3-1 5 0 1 0 1 1 2 1l1-1c-1-1 0-1 0-2 0 0-1-1-1-2h1v-1l-4-1h-1l-1-1v-1s0 1 1 1 1 0 3-1h4c2 0 5-1 7-2l1 1c1 0 2-1 3-1 1-1 3-4 3-5 0-2-1-3-1-5s2-3 3-3c1 1 1 1 1 2l1-2v-1-4l1-2c0-1 1-3 2-4l2-1z" class="C"></path><path d="M514 423v-2l1-1 1 4h-1l-1-1z" class="R"></path><path d="M514 423c-1-2-1-4-1-7 1 2 2 3 2 4l-1 1v2z" class="n"></path><path d="M486 430v-1h4v1 1h-2c-1-1-1-1-2-1z" class="B"></path><path d="M515 424h1c0 2 1 3 1 5l-2-2v-3z" class="Y"></path><path d="M486 430c1 0 1 0 2 1h2 0v1h-2c-1 1-1 1-2 1v-2-1z" class="p"></path><path d="M526 419v1c0 2 1 3 0 5 0-1-1-2-2-3 0-2 1-1 2-3z" class="l"></path><path d="M500 432l-1-1h1 1l4 4h-1-2l-2-3z" class="j"></path><path d="M526 466c1 1 3 2 4 4v6h-1c-1-3 1-6-2-9h-1 0v-1z" class="R"></path><path d="M490 431c2 0 4 1 5 1l-2 1v1h-4v-1l-1-1h2v-1z" class="Y"></path><path d="M523 415c-1-1-2-3-2-4-1-1-1-3-2-5h1c1 0 1 0 2 1l-1 1h1c1 2 1 4 1 7zm-18-5v-2l3 1-1 1c0 1 1 1 1 2s-1 2-1 3h0c-1-1-2-4-2-5z" class="B"></path><path d="M489 424v-2c0-4 2-6 5-8h0v1c-1 1-2 2-2 3-1 1 0 2-2 3 0 1 0 2-1 2v1z" class="E"></path><path d="M522 463c2 1 3 1 4 3v1h0v1c0 2 1 5 2 7-2-2-3-5-5-7h0c1-1 1-1 1-2s-1-1-2-2v-1z" class="f"></path><path d="M500 407l-1-1h0l9-2v1 4l-3-1v2c-1-1-1-3-1-4-1 0-1 0-1 1-1 0-2-1-3 0z" class="p"></path><path d="M488 440c2-1 4-1 7-1 1 0 6 1 7 0 1 1 2 1 3 2-6 0-11-1-17 0v-1z" class="F"></path><path d="M489 411h3c1 1 2 1 3 1-1 1-4 2-5 4-1 1-2 3-3 4h0c-1 0-1 0-1-1s0-1 1-3c1-1 3-2 4-3v-1h-1 1l-1-1h-1z" class="p"></path><path d="M505 400l1 1c-2 1-5 2-7 2-1 1-2 1-3 1s-3-1-4 0h-1l-1-1v-1s0 1 1 1 1 0 3-1h4c2 0 5-1 7-2z" class="Q"></path><path d="M489 424v-1c1 0 1-1 1-2 2-1 1-2 2-3 0-1 1-2 2-3 0 1 0 1 1 2-1 1-2 3-2 5 0 1 1 3 1 4-2 0-2-1-4-2h0-1z" class="H"></path><path d="M483 453h0v-1c3 1 6 3 9 2l1 1c-1 1-1 1-2 1v1h-1c-1 0-2 1-2 2-2-2-3-4-5-6z" class="d"></path><path d="M492 454c6-1 11-2 16 1l1 1h0-5l-1 3c0-1 0-3-1-4 0 0-1 0-1-1h-2c-1 1-1 2-1 3 0-1-1-1-1-2h-4l-1-1z" class="F"></path><path d="M500 407c1-1 2 0 3 0 0-1 0-1 1-1 0 1 0 3 1 4 0 1 1 4 2 5-2 0-2-1-2-3-1 2 0 3 0 4-2-2-4-6-5-9h0z" class="V"></path><path d="M530 442l1 2 3 23h-1c0 3 1 7 0 11l-1-13c0-2 1-3 1-4s-1-2-1-3l-2-16z" class="G"></path><path d="M523 415c0-3 0-5-1-7h-1l1-1c1 1 2 2 3 2s2 0 3 1c0 0 0 1-1 2 0 1-1 2-2 3l-1 2c-1-1-1-2-1-2z" class="R"></path><path d="M525 409c1 0 2 0 3 1 0 0 0 1-1 2 0 1-1 2-2 3-1-1 0-4 0-6z" class="D"></path><path d="M502 417c-1 0-2-1-2-3l-1-1v-1c-1-1-1-2-2-2v-3c2 1 2 2 3 3v-3c1 3 3 7 5 9v1l-2 1c0-1 0-1-1-2v1z" class="B"></path><path d="M495 417c0 1 1 2 1 3v7c1 2 4 3 5 4h-1-1l1 1v1c-2-3-4-4-6-7 0-1-1-3-1-4 0-2 1-4 2-5z" class="g"></path><path d="M533 478c1-4 0-8 0-11h1v13h1v1l-2 8h0l-1-1v1c-1-1 0-3 0-4l1-7z" class="Q"></path><path d="M486 411v1c1 0 1-1 2-2l1 1h1l1 1h-1 1v1c-1 1-3 2-4 3-1 2-1 2-1 3s0 1 1 1h0l-2 6h-1v-1l-1-1h0c1-2 1-5 1-7s1-4 2-6z" class="Y"></path><path d="M486 411v1c1 0 1-1 2-2l1 1h1l1 1h-1c-2 1-3 2-4 4l-1 1h-1c0-2 1-4 2-6z" class="E"></path><path d="M496 410c0 1 1 2 1 3 1 2 4 5 6 6h0c0-1-1-1-1-2v-1c1 1 1 1 1 2l2-1c1 2 2 4 4 6 1 3 4 5 5 7h-1c-2-1-4-4-6-5-3-3-6-5-8-9-2-1-3-3-4-5l1-1z" class="I"></path><path d="M529 397v-1c-1-5-3-9-5-14 2 1 3 2 4 4 2 1 2 3 3 5v-7c3 4 4 6 3 11 0 1 0 2-1 3v-3c1-2 0-4 0-6 0 0-1-1-1-2h0c0 1-1 2 0 2 1 2 1 5 1 6-1 1-2 1-3 1h-1v1zm-3 23v-1h1l1 1c0 2 2 2 2 3v2l1 1v1c2-1 2-4 3-5-1 3-2 5-2 8s0 5-1 8v6l-1-2c0-5-2-9-2-14v-1l-2-2c1-2 0-3 0-5z" class="U"></path><path d="M526 420v-1h1l1 1c0 2 2 2 2 3v2s0-1-1-2c0 0-1-1-2-1v-1 1c1 1 0 3 1 4v1l-2-2c1-2 0-3 0-5z" class="d"></path><path d="M528 410l3-2h0v2 3h2v2c0 3-1 6-2 9v1 1l-1-1v-2c0-1-2-1-2-3l-1-1h-1v1-1c-1-1-1-2-2-2l1-2c1-1 2-2 2-3 1-1 1-2 1-2z" class="X"></path><path d="M531 410v3h2v2h-1v2c0 1 0 1-1 2v-9z" class="P"></path><path d="M531 419c1-1 1-1 1-2v-2h1c0 3-1 6-2 9v-5z" class="K"></path><path d="M527 412h1c0 1 0 3-1 4v3h-1v1-1c-1-1-1-2-2-2l1-2c1-1 2-2 2-3z" class="N"></path><path d="M528 420h0v-1c0-2 0-4 2-6h0c1 1 1 2 1 3v9 1l-1-1v-2c0-1-2-1-2-3z" class="F"></path><path d="M505 416c0-1-1-2 0-4 0 2 0 3 2 3h0c2 4 5 8 8 12l2 2c4 5 7 11 9 16 0 1 1 3 1 4-3-7-8-14-13-19-1-2-4-4-5-7-2-2-3-4-4-6v-1z" class="U"></path><path d="M518 462l4 1v1c1 1 2 1 2 2s0 1-1 2h0-3c0 1 0 1 1 1s2 1 2 2l1 1h-1 0c-1-1-1-1-2-1-1 1-1 2 0 3v1c-1 0-1-1-1-1h-4 0c0-2 1-3 1-5l-5 1v-1l-1-1v-1c2-2 4-3 7-5z" class="B"></path><path d="M518 462l4 1v1c-4 0-8 2-10 5l-1-1v-1c2-2 4-3 7-5z" class="V"></path><path d="M521 375c1-1 3-1 4-1 3-1 6 1 8 3s3 6 5 7l-2-5 1-1 2 3c0 3 2 6 3 10 2 6 0 13-1 19l-1-1c2-8 0-14-2-21-2-4-5-10-9-12-2-1-5-1-7 0l-1-1z" class="M"></path><path d="M503 459l1-3h5 0c3 2 5 4 8 6h1c-3 2-5 3-7 5v-1c-2 1-3 1-4 2h0l-2-2-2-2c-1-1 0-3 0-5z" class="O"></path><path d="M509 456c3 2 5 4 8 6h-3c-2-2-3-4-5-5v-1h0z" class="j"></path><path d="M505 466v-1c0-2-1-3 0-5 1 0 2 0 2-1 2 1 3 1 4 2 0 1 1 1 1 2-1 1-1 2-1 3-2 1-3 1-4 2h0l-2-2z" class="S"></path><defs><linearGradient id="S" x1="507.207" y1="438.5" x2="502.013" y2="444.742" xlink:href="#B"><stop offset="0" stop-color="#d70407"></stop><stop offset="1" stop-color="#a01f24"></stop></linearGradient></defs><path fill="url(#S)" d="M495 432l3 1h1 0 1v-1l2 3h2 1c2 1 4 2 5 5 7 6 12 14 16 23h-1c-2-1-3-4-3-6-4-7-10-13-17-16-1-1-2-1-3-2-4-2-9-3-14-2 0 0 0-1 1-1h0l-1-2h1 4v-1l2-1z"></path><path d="M495 432l3 1v2l-5-1v-1l2-1z" class="n"></path><path d="M500 433v-1l2 3-1 1-3-1v-2h1 0 1z" class="O"></path><path d="M505 435c2 1 4 2 5 5-3-2-6-3-9-4l1-1h2 1z" class="Z"></path><path d="M521 375l1 1c-2 3-3 6-2 10 0 5 2 12 6 14 0 1 0 1 1 1s1-1 1-2l1-2v-1h1c1 0 2 0 3-1 0-1 0-4-1-6-1 0 0-1 0-2h0c0 1 1 2 1 2 0 2 1 4 0 6v3c0 2-1 3-2 4-2 2-3 2-6 2-4 0-8-2-11-6 1 0 3-1 4-1l-3-8 1-2v-1-4l1-2c0-1 1-3 2-4l2-1z" class="h"></path><path d="M498 457c0-1 0-2 1-3h2c0 1 1 1 1 1 1 1 1 3 1 4 0 2-1 4 0 5l2 2 2 2h0c1-1 2-1 4-2v1 1l1 1v1l5-1c0 2-1 3-1 5h0 4s0 1 1 1c0 2 0 4 1 6s4 2 6 5c-2 0-4-3-5-4-3-1-5-1-8-2-1-1-2-2-3-2-8-2-14-6-20-12 0-2-1-4-2-5l-2-2c0-1 1-2 2-2h1v-1c1 0 1 0 2-1h4c0 1 1 1 1 2z" class="r"></path><path d="M511 466v1 1l1 1v1c-2 0-5 1-8 0v-1l3-1h0c1-1 2-1 4-2z" class="t"></path><path d="M493 455h4c0 1 1 1 1 2 1 1 1 3 2 5l-2 2h-1-1v-1h1 0v-3c0-1-1-2-2-3h-4v-1c1 0 1 0 2-1z" class="b"></path><path d="M477 431v-1c2-2 3-2 5-3 1 0 2 0 3 1v2h0c1 1 0 1 1 1h0v2c1 0 1 0 2-1l1 1v1h-1l1 2h0c-1 0-1 1-1 1 5-1 10 0 14 2-1 1-6 0-7 0-3 0-5 0-7 1v1h0c-1 1-2 1-3 2-3 2-5 4-6 8h-1l-1-2v2h-4c-2 0-6 0-8 1h-1v-1h0l-1-1v-2s3-2 4-3h-1v-2l1-2v-1l1-1-1-1c1-1 2-1 3-2 1 0 2-1 3-2l4-3z" class="Z"></path><path d="M488 432l1 1v1h-1l-3 2c0-1 0-2 1-3 1 0 1 0 2-1z" class="B"></path><path d="M488 437c5-1 10 0 14 2-1 1-6 0-7 0-3 0-5 0-7 1-3 1-5 2-7 4v-1h0c0-3 5-5 7-6z" class="c"></path><path d="M479 436c0-3 0-5 2-7h1l1 1h0v3h1c0-1 0-2 1-3h0c1 1 0 1 1 1h0v2c-1 1-1 2-1 3l-4 5c-1-2-2-3-2-5z" class="m"></path><path d="M479 436c0-3 0-5 2-7h1l1 1h0v3h1c0 1 0 2-1 3h0 0l-1-1c0-1-1-1-1-2s0-1-1-2h0l-1 5z" class="B"></path><path d="M482 429l1 1h0v3h1c0 1 0 2-1 3h0 0c0-2 0-5-1-7z" class="Y"></path><path d="M477 431c1 2-1 4-1 6 1 1 0 2 0 4 0 3 0 6 1 8v2h-4c-2 0-6 0-8 1h-1v-1h0l-1-1v-2s3-2 4-3h-1v-2l1-2v-1l1-1-1-1c1-1 2-1 3-2 1 0 2-1 3-2l4-3z" class="U"></path><path d="M467 441h1l-1 2h1l-1 2h-1v-2l1-2z" class="L"></path><path d="M467 438c1-1 2-1 3-2v1l-2 4h-1v-1l1-1-1-1z" class="E"></path><path d="M473 434l1 1c0 1-1 2-1 3h0l-3 3c-1 0-1 0-1-1 1-1 1-1 1-3v-1c1 0 2-1 3-2z" class="q"></path><path d="M473 438c0 2-4 5-3 7-2 2-4 3-6 6h0l-1-1v-2s3-2 4-3l1-2 2-2 3-3z" class="X"></path><path d="M477 431c1 2-1 4-1 6-2 3-4 6-6 8-1-2 3-5 3-7h0c0-1 1-2 1-3l-1-1 4-3z" class="d"></path><path d="M476 437c1 1 0 2 0 4 0 3 0 6 1 8v2h-4c-2 0-6 0-8 1h-1v-1c2-3 4-4 6-6s4-5 6-8z" class="C"></path><path d="M243 126v1c0 1-2 2-2 3v1l1-1c0 1 1 1 1 1v1c-7 5-14 10-20 16-23 21-37 50-42 80v2c-1 2-1 4-1 6l-1 2c0 3 0 7-1 10 0 6-1 12-1 18 0 0 1 0 1-1s-1-3 0-4v5h1l-1 111v27c0 4 1 8 0 11l1 32v5 6l1 7v3c2 20 5 39 12 58 3 8 7 17 11 25h1v2c0 2 1 5 2 8h0l-1-1-9-15c-7-11-11-24-15-37-6-22-8-44-9-67v-58-81l-3 6h0c-1 1-2 1-2 2l-1 1v-1c1-1 2-2 2-4h-1v1c-4 3-9 6-14 7v-1h1c5-2 10-6 12-11 1-2 0-2 0-4-2-3-5-5-8-6h-3l-1-1c1 0 2 0 2-1h0 3l1-1c-3-1-6-2-10-2h-5v-2c-2 2-2 3-3 5l-1 1c-1 0-1 0-2-1v1c-1 0-1 1-2 1v1s-1 1-2 1c-2 0-5 1-6 3h-1-2c-2 0-3 0-4-1h1c-1-1-3-2-4-2l1-1c1 1 1 0 1 0h0l-1-1c-1 0-1 0-2-1s-1-2-2-3c0-2 0-2-1-3l-12-8c0-1-2-2-2-2l3-2 3-1c1-1 2-1 4-1h3c2 0 3-3 4-4s1-2 1-3c2-1 3-2 5-3 1-2 6-4 6-6 1 0 1-1 2-1h0c1 1 1 1 2 1h4c3 1 5 1 9 1v-1l-1-1c-1-1-2-1-2-2h-1c1-1 3-1 4-1 3 0 5 1 7 2h1l1 1v-1-1h1v-1h0l1-1 2 3 1-1c-1-2-2-4-3-5l-7-6v-1c2 0 4 0 6 1 1 1 2 1 3 1l2-1v6l1 1c0-1 0-2 1-2 1-1 2-3 3-4 0-1 0-2 1-3h0c2-2 3-4 4-5l1 2c0-5 2-10 1-15-1-2-1-4-1-6l-1-8v-2l-6-6c-3-2-6-4-8-5l1-1v-2c1 1 5 1 7 1 3-1 5-2 9 0 0 0 1 1 1 0l1 3v1 2-1c1 0 2 0 2-1h2v-1h1l1-3c0-2 9-17 10-20 1-2 1-4 2-6 1 1 1 1 1 0 2-2 2-6 3-9v3h1l1-9v-7c0-1 0-2 1-3h2 0l1 1v3 2l2-1v2c1-2 1-4 2-6h1 1l6-1h0 1c2 0 2-1 4-2v1l6-5h0l2-2c0 1 0 1 1 2l2-2 2-1 8-6z" class="AX"></path><path d="M230 136h1l1 1-2 1v-1h-1l1-1z" class="D"></path><path d="M231 135l2-2 2 2c-1 0-2 1-3 2l-1-1h-1l1-1z" class="J"></path><path d="M179 452v6 3h-1v-8-1h1z" class="I"></path><path d="M179 458l1 7v3 3l-1-1v-1c-1-2-1-5-1-8h1v-3z" class="B"></path><path d="M179 469v-3l1-1v3 3l-1-1v-1z" class="k"></path><path d="M243 126v1c0 1-2 2-2 3v1l-6 4-2-2 2-1 8-6z" class="P"></path><path d="M172 249v5l1 1v5l-1 17h0c-1-10-1-19 0-28z" class="H"></path><path d="M157 280c4 2 8 4 9 8 1 1 1 2 1 3-1 0-2-1-3-2l-7-8v-1z" class="J"></path><path d="M143 252c1-1 3-1 4-1 3 0 5 1 7 2 1 1 3 2 4 3h0c-4 0-7-4-11-4l2 1c0 1 1 1 1 2h1c2 1 5 2 5 3v1s-1 0-1-1c-1-1-3-1-4-2h-4v-1l-1-1c-1-1-2-1-2-2h-1z" class="o"></path><path d="M157 251l1-1 2 3 1-1 4 10-3-3c-1-1-2-2-4-3-1-1-3-2-4-3h1l1 1v-1-1h1v-1h0z" class="H"></path><path d="M157 251l1-1 2 3v1h-4v-1-1h1v-1h0z" class="v"></path><path d="M177 418h0v-5c1 0 1 0 1 1-1 1 0 2 0 3h0v-2l1 32v5h-1v1c-1-2-1-5-1-8v-18-9z" class="H"></path><path d="M178 452v-4-1h1v5h-1z" class="B"></path><path d="M177 418h0v-5c1 0 1 0 1 1-1 1 0 2 0 3h0v9 3c-1 1 0 2 0 3-1-2-1-3-1-5v-9z" class="I"></path><path d="M155 258c0 1 1 1 1 1h0c3 3 4 5 6 8l2 5-8-4c-1-1-2-1-3-2 0 0-6-4-7-4 0-1 2-1 2-1 2-2 5-2 7-3z" class="K"></path><path d="M155 258c0 1 1 1 1 1h0l-1 1v1h1l1 1v1c-1 0-1-1-2-1-2-1-5-1-7-1 2-2 5-2 7-3z" class="W"></path><path d="M180 468c2 20 5 39 12 58 3 8 7 17 11 25h1v2c-3-5-6-9-8-14-10-22-15-45-17-69l1 1v-3z" class="B"></path><path d="M177 266s1 0 1-1-1-3 0-4v5h1l-1 111v27c0 4 1 8 0 11v2h0c0-1-1-2 0-3 0-1 0-1-1-1v5h0V266z" class="k"></path><path d="M230 133c0 1 0 1 1 2l-1 1-1 1h1v1c-7 6-14 12-20 19-20 24-32 55-36 86l-1 12-1-1v-5-5l1-8c0-5 2-10 1-15-1-2-1-4-1-6l-1-8v-2l-6-6c-3-2-6-4-8-5l1-1v-2c1 1 5 1 7 1 3-1 5-2 9 0 0 0 1 1 1 0l1 3v1 2-1c1 0 2 0 2-1h2v-1h1l1-3c0-2 9-17 10-20 1-2 1-4 2-6 1 1 1 1 1 0 2-2 2-6 3-9v3h1l1-9v-7c0-1 0-2 1-3h2 0l1 1v3 2l2-1v2c1-2 1-4 2-6h1 1l6-1h0 1c2 0 2-1 4-2v1l6-5h0l2-2z" class="C"></path><path d="M174 202c1-1 1-1 2-1s1 0 2 1c0 1-1 1-1 2l-1 1h0c0-1-1-2-2-3h0z" class="E"></path><path d="M172 199v-1l1 1c1-1 2-1 3-1v3h0c-1 0-1 0-2 1-1-1-1-2-2-3z" class="e"></path><path d="M172 199h0c-2-2-3-3-6-5 2 1 5 1 7 1 1 1 2 1 3 3-1 0-2 0-3 1l-1-1v1z" class="u"></path><path d="M159 191c1 1 5 1 7 1 3-1 5-2 9 0 0 0 1 1 1 0l1 3v1c-2-2-4-4-6-4-5 0-7 3-12 1v-2z" class="f"></path><path d="M230 133c0 1 0 1 1 2l-1 1-1 1-2 1c-2 2-4 3-5 5l-6 5c-1-1-1-1-2-1 3-2 5-4 8-7l6-5h0l2-2z" class="d"></path><path d="M230 133c0 1 0 1 1 2l-1 1-1 1-2 1h0c-1-1 1-3 1-3h0l2-2z" class="L"></path><path d="M182 195l1-3c1 2-1 4-2 6v1 1c0-1 1-2 1-2v-1l1-1h0l-6 17c0-3 0-6 1-9h-1c0-1 1-1 1-2-1-1-1-1-2-1h0l1-1v-2-1c1 0 2 0 2-1h2v-1h1z" class="W"></path><path d="M177 198v-1c1 0 2 0 2-1h2l-3 8h-1c0-1 1-1 1-2-1-1-1-1-2-1h0l1-1v-2z" class="R"></path><path d="M199 157v3h1v1c-1 2-3 6-2 8h1l-10 17c-2 3-4 7-6 10h0l-1 1v1s-1 1-1 2v-1-1c1-2 3-4 2-6 0-2 9-17 10-20 1-2 1-4 2-6 1 1 1 1 1 0 2-2 2-6 3-9z" class="L"></path><path d="M222 139v1c-3 3-5 5-8 7 1 0 1 0 2 1l-17 21h-1c-1-2 1-6 2-8v-1l1-9v-7c0-1 0-2 1-3h2 0l1 1v3 2l2-1v2c1-2 1-4 2-6h1 1l6-1h0 1c2 0 2-1 4-2z" class="r"></path><path d="M209 142h1v1 1 2c-1 1-1 2-1 3v1l-2 2-3 6 1-13v2l2-1v2c1-2 1-4 2-6z" class="Z"></path><path d="M207 146v2l-2 2v-3l2-1z" class="H"></path><path d="M222 139v1c-3 3-5 5-8 7l-5 3v-1c0-1 0-2 1-3v-2-1-1h1l6-1h0 1c2 0 2-1 4-2z" class="D"></path><path d="M210 144h3l-2 2h-1v-2z" class="n"></path><defs><linearGradient id="T" x1="105.549" y1="285.576" x2="129.984" y2="274.931" xlink:href="#B"><stop offset="0" stop-color="#d70308"></stop><stop offset="1" stop-color="#a61d22"></stop></linearGradient></defs><path fill="url(#T)" d="M130 255c1 0 1-1 2-1h0c1 1 1 1 2 1h4c3 1 5 1 9 1h4c1 1 3 1 4 2-2 1-5 1-7 3 0 0-2 0-2 1 1 0 7 4 7 4 1 1 2 1 3 2-1 1-2 2-3 2v2c1 1 1 1 1 2s-1 1-2 2v2c2 2 3 2 5 2v1l7 8s0 1-1 1h0c-2 1-5 0-7 0h3l1-1c-3-1-6-2-10-2h-5v-2c-2 2-2 3-3 5l-1 1c-1 0-1 0-2-1v1c-1 0-1 1-2 1v1s-1 1-2 1c-2 0-5 1-6 3h-1-2c-2 0-3 0-4-1h1c-1-1-3-2-4-2l1-1c1 1 1 0 1 0h0l-1-1c-1 0-1 0-2-1s-1-2-2-3c0-2 0-2-1-3l-12-8c0-1-2-2-2-2l3-2 3-1c1-1 2-1 4-1h3c2 0 3-3 4-4s1-2 1-3c2-1 3-2 5-3 1-2 6-4 6-6z"></path><path d="M119 282c-2 0-2-1-3-1v-2h1v1l3 1c-1 0-1 1-1 1z" class="l"></path><path d="M142 282l3 3c-2 2-2 3-3 5l-1 1c-1 0-1 0-2-1v1c-1 0-1 1-2 1v1s-1 1-2 1c-2 0-5 1-6 3h-1-2c-2 0-3 0-4-1h1c-1-1-3-2-4-2l1-1c1 1 1 0 1 0h0l-1-1c-1 0-1 0-2-1s-1-2-2-3c0-2 0-2-1-3 5 2 9 2 14 3 3 0 5 1 7 1l1-1-1-1c1-1 2-1 3-2l2-3h1 0z" class="D"></path><path d="M116 288c2 0 4 2 6 3v1l-2-1h0v1c-1 0-1 0-2-1s-1-2-2-3z" class="E"></path><path d="M122 291c1 1 3 2 4 3h-1c-1 0-1 0-2-1h-1c0 1 0 1 1 2h2l3 1v1h-2c-2 0-3 0-4-1h1c-1-1-3-2-4-2l1-1c1 1 1 0 1 0h0l-1-1v-1h0l2 1v-1z" class="u"></path><path d="M125 295c2 0 4-1 6-1-1-2-5-3-6-5h0c2-1 5 4 8 4 1 0 1 0 2 1-2 0-5 1-6 3h-1v-1l-3-1z" class="R"></path><path d="M142 282l3 3c-2 2-2 3-3 5l-1 1c-1 0-1 0-2-1 1-1 3-2 3-3h-1-1s0-1-1-2l2-3h1 0z" class="H"></path><path d="M130 255c1 0 1-1 2-1h0c1 1 1 1 2 1h4c3 1 5 1 9 1h4c1 1 3 1 4 2-2 1-5 1-7 3 0 0-2 0-2 1-1-1-2-1-3-2l-1 1h0l-2 1c-1 0-2 0-3 1 0 0-1 0-2 1 0 0-1 0-1 1h-1c-2 2-7 5-8 8 0 1 0 1-1 2l-1-1v2c0 2-1 4-2 6h-2s0-1 1-1l-3-1v-1l3-4c-3 1-6 2-8 4l-1 1v-1-1c2-3 5-3 7-5 1-1 2-3 3-4l4-7-1-1c1-2 6-4 6-6z" class="I"></path><path d="M117 280l2-2 1 1v2l-3-1z" class="e"></path><path d="M130 255c1 0 1-1 2-1h0c1 1 1 1 2 1-4 2-6 4-9 7l-1-1c1-2 6-4 6-6z" class="N"></path><path d="M136 262c0 1 0 1-1 1h-1c-3 2-5 4-8 6v-1l5-4c1-1 0 0 0-1 1 0 2-1 3-2h1v-1h2v-1c1 0 1 1 2 1l-3 2z" class="m"></path><path d="M151 256c1 1 3 1 4 2-2 1-5 1-7 3 0 0-2 0-2 1-1-1-2-1-3-2l-1 1h0l-2 1c-1 0-2 0-3 1l-1-1 3-2 2-1c2-1 4-1 6-2h1c1 0 2 0 3-1z" class="F"></path><path d="M151 256c1 1 3 1 4 2-2 1-5 1-7 3 0 0-2 0-2 1-1-1-2-1-3-2 2 0 4-1 6-1l2-1h0l-4-1h1c1 0 2 0 3-1z" class="t"></path><path d="M142 261h0l1-1c1 1 2 1 3 2 1 0 7 4 7 4 1 1 2 1 3 2-1 1-2 2-3 2v2c1 1 1 1 1 2s-1 1-2 2v2c2 2 3 2 5 2v1l7 8s0 1-1 1h0c-2 1-5 0-7 0h3l1-1c-3-1-6-2-10-2h-5v-2l-3-3h0-1l-2 3c-1 1-2 1-3 2-1-1-1-1-3-2-3-1-7 0-11-2 0-3 1-6 2-8 1-1 1-1 1-2 1-3 6-6 8-8h1c0-1 1-1 1-1 1-1 2-1 2-1 1-1 2-1 3-1l2-1z" class="C"></path><path d="M147 272c-1 0-2-1-2-2l-1-1c1-1 1-1 2-1 1 1 2 1 3 1-1 1-2 2-2 3z" class="H"></path><path d="M141 280v2l-2 3c-1 1-2 1-3 2-1-1-1-1-3-2h2c2 0 4-3 6-5z" class="j"></path><path d="M141 280c0-2-2-3-3-5v-1l1 1 6 5c-1 1-2 1-2 1l-1-1v2h-1v-2z" class="E"></path><path d="M142 261v1c1 1 2 0 3 1-2 1-4 2-5 3h1-1c-1 1-2 1-3 1v-1l1-1c0-2 2-2 3-3h-1l2-1z" class="Z"></path><path d="M142 261h0l1-1c1 1 2 1 3 2 1 0 7 4 7 4 1 1 2 1 3 2-1 1-2 2-3 2v2c1 1 1 1 1 2s-1 1-2 2h-1l-1 1h0c0-1-1-2-2-3 0 0-1-1-1-2s1-2 2-3c-1 0-2 0-3-1l5-1v-1c-4-2-7 0-10 0h-1c1-1 3-2 5-3-1-1-2 0-3-1v-1z" class="Q"></path><path d="M153 266c1 1 2 1 3 2-1 1-2 2-3 2v2h-2v-1-1c0-1-1-1-1-2h0 2c0-1 1-1 1-2z" class="G"></path><path d="M149 269h0c1 2 1 3 2 3l-2 2v1h1l1 1c-1 0-1 1-1 1 0-1-1-2-2-3 0 0-1-1-1-2s1-2 2-3z" class="p"></path><path d="M139 275h1v-2l-3-2v-1h0c1 1 2 2 3 2 0 0 0 1 1 1v1c2 1 5 5 8 6v-1c0-1 0-2-1-2v-3c1 1 2 2 2 3h0l1-1h1v2c2 2 3 2 5 2v1l7 8s0 1-1 1h0c-2 1-5 0-7 0h3l1-1c-3-1-6-2-10-2h-5v-2l-3-3h0v-2l1 1s1 0 2-1l-6-5z" class="D"></path><path d="M150 284l8 3c1 1 2 1 3 2h-1c-3-1-6-2-10-2v-3z" class="E"></path><path d="M150 277l1-1h1v2c2 2 3 2 5 2v1l7 8s0 1-1 1h0v-1c-1-2-3-4-5-5-1-1-6-3-7-4 0 0 1-1 0-1 0 0 0-1-1-2z" class="F"></path><path d="M145 280l2 2 3 2v3h-5v-2l-3-3h0v-2l1 1s1 0 2-1z" class="Z"></path><path d="M145 280l2 2c-1 1-1 1-1 2h-1c0-1-1-2-3-3v1h0v-2l1 1s1 0 2-1z" class="R"></path><path d="M68 235c1 0 1 1 2 1 0 1-1 2-1 3h1l1 2 1-1v7 2c0 8 2 13 7 20v4h0v1l1 1c0 2 1 3 2 5 1 0 1 1 1 1h-1-1l-1 1c5 2 8 7 12 10h1c1 2 6 4 8 4 1 0 1 0 1-1h3l1-1c-1 0-2 0-2-2 1 1 2 2 4 1h1c1-1 2-1 3-1h1 0v-2h0c2 2 4 3 7 3l-1 1c1 0 3 1 4 2h-1c1 1 2 1 4 1h2 0-2v1h1c0 1 1 4 0 5v1 2c0 2-1 4-1 6l-1 1v2h0 1v1h1c-1 1-4 1-5 1 1 1 1 1 3 1l-2 1-1 1c0 3 0 4-2 6h1v1l-1 1c0 1 0 1-1 2h0c-1 0-1 0-1 1l-1-1c-3 1-5 5-7 7l-1 2h-1-1-2c-2-1-3-1-5-2v1c1 2 2 4 5 5h2c-1 1 0 1-1 1l-4 2-1-1c0 1-1 2-1 2l-1 1c-1 1-1 2-1 3l-1 1h-1v-2h-1-2v1h-1v-1c-1 0-1 1-1 1-2 2-3 4-5 5-1 1-2 1-2 2v1h0c1 0 1 1 1 1-3 3-15 16-15 20h0c4-4 6-5 11-6 3 1 6 2 8 5 0 1 1 2 1 4v2l1-1 1 1 3 6c-1 2 0 4 1 6s3 4 5 5l4 1h1 2c1 1 2 0 2 2l1 1v3 2l1 1-9 9c-2 1-3 3-5 4h-1 0c-5 5-9 11-13 16-3 6-5 11-8 17 0 1 0 5-1 5h0c0-1-1-1-1 0s0 4-1 6h0v-2l-2 1c2 6 4 12 5 18 0 3 0 8 1 10l2 4v1c-1-2-2-5-3-7l-4-9c-4-13-7-27-9-40-1-10-1-21-1-31v-36-66c0-13 0-27 1-41 1-12 2-24 5-36z" class="C"></path><path d="M68 293v-2h1c0 3 1 7 0 11l-1-9z" class="G"></path><path d="M64 332c0-5 1-9 1-13 1 4 1 9 1 14-1-1-1-1-2-1z" class="v"></path><path d="M67 375h1l1 1-3 6c-1 0-1 0-2-1v-2h1 0 0c1 0 2-3 2-4z" class="M"></path><path d="M74 289l1 3h0c0 2-1 3-1 4 0 3 0 6-1 9v-2h0l-1 1v-13h1s0-1 1-2z" class="G"></path><path d="M74 289l1 3h0c0 2-1 3-1 4-1-2-1-3-1-5 0 0 0-1 1-2z" class="m"></path><path d="M64 332c1 0 1 0 2 1v1s1 2 2 2l-1 5v1 2h-1v-3l-1 1v12 7l-1-29z" class="E"></path><path d="M66 334s1 2 2 2l-1 5v1c-2-1-1-6-1-8z" class="k"></path><path d="M68 263l1 28h-1v2l-2-27h1c1-1 1-2 1-3z" class="V"></path><path d="M69 239h1l1 2-1 2c0 2-1 6-1 8l-1 12c0 1 0 2-1 3h-1c0-9 1-17 3-26v-1z" class="T"></path><path d="M69 239h1l1 2-1 2v-2l-1-1v-1z" class="M"></path><path d="M72 304l1-1h0v2c0 3-1 7-1 10s1 7 0 10v2h0c-1 0-1 0-1-1-1 0-1 0-2 1h0v1-10h0v-1h0c2-3 0-5 1-8 0-2 2-3 2-5z" class="S"></path><path d="M84 359c1 0 1 1 1 1-3 3-15 16-15 20h0v1l-2 2c1-4 2-8 4-12 3-5 7-8 12-12z" class="T"></path><path d="M71 241l1-1v7 2c-1 3-1 6-1 9 0 11 1 22 4 33v1l-1-3c0-2-2-6-3-9 0-4-1-9-2-13v-10c0-2 1-4 0-6 0-2 1-6 1-8l1-2z" class="M"></path><path d="M71 241l1-1v7 2c-1 3-1 6-1 9v-7h-1l-1 6c0-2 1-4 0-6 0-2 1-6 1-8l1-2z" class="Y"></path><path d="M75 291c3 9 4 18 10 25l6 6c1 2 4 4 5 7h-1v-1l-1 1 6 9c1 2 2 4 5 5h2c-1 1 0 1-1 1l-4 2-1-1c0 1-1 2-1 2l-1 1c-1 1-1 2-1 3l-1 1h-1v-2h-1-2v1h-1v-1c-1 0-1 1-1 1-2 2-3 4-5 5-1 1-2 1-2 2-3 2-6 4-9 7-2 3-4 5-5 8l-1 3-1-1h-1c0 1-1 4-2 4h0 0-1l1-18v-7-12l1-1v3h1v-2-1l1-5h0c0-3 0-5 1-8v-1h0c1-1 1-1 2-1 0 1 0 1 1 1h0v-2c1-3 0-7 0-10s1-7 1-10c1-3 1-6 1-9 0-1 1-2 1-4h0v-1z" class="C"></path><path d="M69 371c0 1 0 1 1 2l-1 3-1-1h-1l2-4z" class="W"></path><path d="M90 336l1 3v2c0 3-1 6-3 9l-2 3c0-1 0-2 1-3 2-5 3-9 2-14h0c1 1 1 2 1 4v4 1-2-1c1 0 1-2 0-2v-4z" class="D"></path><path d="M80 355c0 2-1 2-3 4-1 1-2 3-3 4s-1 1-1 2v1c0-1 1-2 1-2l1 1c-2 3-4 5-5 8-1-1-1-1-1-2 2-6 6-13 11-16z" class="Q"></path><path d="M69 328v-1h0c1-1 1-1 2-1 0 1 0 1 1 1h0c0 1 1 1 1 2s-3 2-1 4h2v1c-1 2-2 3-3 4 1 1 2 1 2 1-1 2-3 5-4 7 0-2-1-3-1-4l-1-1 1-5h0c0-3 0-5 1-8z" class="M"></path><path d="M80 355c4-3 6-6 7-10l1-1c0-4 0-7-1-11-1-3-3-7-4-10-1-1-2-4-2-5h1c2 6 5 12 7 18 1 5 0 9-2 14-1 1-1 2-1 3l2-3c2-1 3-2 3-4v1 1c-1 1 0 2 0 2v1c-2 2-3 4-5 5-1 1-2 1-2 2-3 2-6 4-9 7l-1-1s-1 1-1 2v-1c0-1 0-1 1-2s2-3 3-4c2-2 3-2 3-4z" class="a"></path><path d="M84 355h1l-2 2c-1 0-1 1-2 1 1 0 3-1 4-2h0 1c-1 1-2 1-2 2-3 2-6 4-9 7l-1-1c3-3 6-6 10-9z" class="T"></path><path d="M88 350c2-1 3-2 3-4v1 1c-1 1 0 2 0 2v1c-2 2-3 4-5 5h-1 0c-1 1-3 2-4 2 1 0 1-1 2-1l2-2h-1l2-2 2-3z" class="S"></path><path d="M65 361v-7-12l1-1v3h1v-2-1l1 1c0 1 1 2 1 4h0 2v1h0c-2 2-3 3-3 5 2-1 3-4 6-3h0c-1 1-2 2-2 3 1 1 2 1 3 1v1c-2 0-5 0-6 2-1 1-1 2-1 4 0 3 0 7-1 10 0 3-1 6-2 9h0-1l1-18z" class="q"></path><path d="M75 291c3 9 4 18 10 25l6 6c1 2 4 4 5 7h-1v-1l-1 1 6 9c1 2 2 4 5 5h2c-1 1 0 1-1 1l-4 2-1-1c0 1-1 2-1 2l-1 1c-1 1-1 2-1 3l-1 1h-1v-2h-1-2v1h-1v-1c-1 0-1 1-1 1v-1s-1-1 0-2v-1-1c0 2-1 3-3 4 2-3 3-6 3-9v-2l-1-3c1-5-1-8-3-12-1-3-2-5-3-7-2-3-3-5-4-7-3-6-4-12-5-18h0v-1z" class="L"></path><path d="M94 338c1-4-1-7-2-10v5 1l-1-1c-1-4-2-8-4-13 2 3 5 6 7 9l6 9c1 2 2 4 5 5h2c-1 1 0 1-1 1l-4 2-1-1c0-1-1-1-2-2l-1-1c-1-1-3-2-4-4z" class="D"></path><path d="M94 338c1 2 3 3 4 4l1 1c1 1 2 1 2 2s-1 2-1 2l-1 1c-1 1-1 2-1 3l-1 1h-1v-2h-1-2v1h-1v-1c-1 0-1 1-1 1v-1s-1-1 0-2v-1-1c0 2-1 3-3 4 2-3 3-6 3-9v-2h1c1 1 1 1 2 1v-2z" class="N"></path><path d="M91 350c1-2 1-6 3-8 0 2-1 4-1 6v2 1h-1v-1c-1 0-1 1-1 1v-1z" class="B"></path><path d="M94 338c1 2 3 3 4 4v2l-2 2c0 1-2 2-3 2 0-2 1-4 1-6v-1h0-2-1v-2h1c1 1 1 1 2 1v-2z" class="G"></path><path d="M98 342l1 1c1 1 2 1 2 2s-1 2-1 2l-1 1c-1 1-1 2-1 3l-1 1h-1v-2h-1-2v-2c1 0 3-1 3-2l2-2v-2z" class="H"></path><path d="M98 342l1 1c1 1 2 1 2 2s-1 2-1 2l-1 1-1-1v-3-2z" class="f"></path><path d="M71 258c0-3 0-6 1-9 0 8 2 13 7 20v4h0v1l1 1c0 2 1 3 2 5 1 0 1 1 1 1h-1-1l-1 1c5 2 8 7 12 10h1c1 2 6 4 8 4 1 0 1 0 1-1h3l1-1c-1 0-2 0-2-2 1 1 2 2 4 1h1c1-1 2-1 3-1h1 0v-2h0c2 2 4 3 7 3l-1 1c1 0 3 1 4 2h-1c1 1 2 1 4 1h2 0-2v1h1c0 1 1 4 0 5v1 2c0 2-1 4-1 6l-1 1v2h0 1v1h1c-1 1-4 1-5 1 1 1 1 1 3 1l-2 1-1 1c0 3 0 4-2 6h1v1l-1 1c0 1 0 1-1 2h0c-1 0-1 0-1 1l-1-1c-3 1-5 5-7 7l-1 2h-1-1-2c-2-1-3-1-5-2v1l-6-9 1-1v1h1c-1-3-4-5-5-7l-6-6c-6-7-7-16-10-25-3-11-4-22-4-33z" class="c"></path><path d="M120 317h2c1 1 1 1 3 1l-2 1-1 1c0-1-1-1-1-2-1 0-1-1-1-1z" class="D"></path><path d="M106 294c1 1 3 0 5 0h2v1l-6 1c-1 0-1 0-2-1l1-1zm-28-19c-2-2-5-3-7-5 3 0 5 2 8 3h0v1 1h-1z" class="G"></path><path d="M79 274l1 1c0 2 1 3 2 5 1 0 1 1 1 1h-1-1c-1-2-2-3-3-6h1v-1z" class="V"></path><path d="M111 294c3-1 5-1 8 0 1 0 3 1 4 2h-1c-3-1-6-2-9-1v-1h-2z" class="F"></path><path d="M120 306c1 0 1 0 2 1h0c-2 3-4 3-7 4h-1v-1h1v-1h0-4v-1c3 0 6 0 9-2z" class="E"></path><path d="M120 326h1v1l-1 1c0 1 0 1-1 2h0c-1 0-1 0-1 1l-1-1c-3 1-5 5-7 7l-1-1c1-2 6-6 8-7l3-3z" class="G"></path><path d="M112 292h1 0v-2h0c2 2 4 3 7 3l-1 1c-3-1-5-1-8 0-2 0-4 1-5 0-1 0-2 0-2-2 1 1 2 2 4 1h1c1-1 2-1 3-1z" class="n"></path><path d="M85 316s0-1 1-1c0 0 0 1 1 1s2 0 2 1 1 1 1 1c1 0 1 1 2 1h0 1l1 1h0c-1 0-1 1-2 1h-1v1h0l-6-6z" class="k"></path><path d="M105 307c5-2 9-1 13-4 2-1 3-3 5-4v1c-3 4-7 7-12 7-1 0-1 0-1 1h-1c-1 0-3-1-4-1z" class="t"></path><path d="M90 304c1 1 3 2 4 3-1 0-1 0 0 1 0 1 0 2 1 2 1 2 3 3 4 4 2 2 4 4 7 5 2 1 3 2 4 4h-1l-8-6c-1 0-1-1-2-1l-1-1-2-2c-2-3-5-5-6-9z" class="K"></path><path d="M98 326h1c0-1 1-1 1-2 1 1 3 2 3 3 2 3 2 7 5 9h1l1 1-1 2h-1l-3-5c-1-1-2-3-3-4-2-1-4-2-5-4h1z" class="N"></path><path d="M107 296c-2 1-5 1-8 1-6 0-10-7-14-11-3-1-5-3-8-4-1 0-2-1-2-2h0c2 0 3 1 5 2h0c5 2 8 7 12 10h1c1 2 6 4 8 4 1 0 1 0 1-1h3c1 1 1 1 2 1z" class="j"></path><path d="M106 314c3 1 6 1 10 1l1 1v1c2 1 2 3 3 5l-1 1h-3c-2-2-6-3-8-5l-3-3h0 0l1-1z" class="g"></path><path d="M91 322h0v-1h1c2 2 4 4 6 5h-1c1 2 3 3 5 4 1 1 2 3 3 4l3 5h-1-2c-2-1-3-1-5-2v1l-6-9 1-1v1h1c-1-3-4-5-5-7z" class="O"></path><path d="M94 329l1-1v1h1l6 7h0-1c-1 0-1 1-1 1v1l-6-9z" class="d"></path><defs><linearGradient id="U" x1="92.144" y1="307.051" x2="106.748" y2="297.391" xlink:href="#B"><stop offset="0" stop-color="#cc0d12"></stop><stop offset="1" stop-color="#892323"></stop></linearGradient></defs><path fill="url(#U)" d="M90 304c-5-5-10-12-11-20 2 2 3 5 5 8 4 8 13 13 21 15 1 0 3 1 4 1h2v1h4 0v1h-1v1c-4 0-7 0-10-1v1c1 0 2 0 3 1h-1-1-1c1 1 1 2 2 2l-1 1h0-2c-1-1-2-1-2-2-1 0-2-1-3-2-1 0-2-1-3-1s-1-1-1-2c-1-1-1-1 0-1-1-1-3-2-4-3z"></path><path d="M94 307c3 2 8 5 11 8h0-2c-1-1-2-1-2-2-1 0-2-1-3-2-1 0-2-1-3-1s-1-1-1-2c-1-1-1-1 0-1z" class="J"></path><path d="M126 297h2 0-2v1h1c0 1 1 4 0 5v1 2c0 2-1 4-1 6l-1 1v2h0 1v1h1c-1 1-4 1-5 1h-2l-2-1-1 1v-1l-1-1c-4 0-7 0-10-1-1 0-1-1-2-2h1 1 1c-1-1-2-1-3-1v-1c3 1 6 1 10 1h1c3-1 5-1 7-4h0c-1-1-1-1-2-1 2-1 3-4 4-7l2-2z" class="E"></path><path d="M117 314c1-1 2-1 2-1l1 1c1 1 3 1 3 2h-3v1l-2-1-1 1v-1l-1-1h1v-1z" class="p"></path><path d="M107 312h6v1l-2 1h6v1h-1c-4 0-7 0-10-1-1 0-1-1-2-2h1 1 1z" class="c"></path><path d="M107 312h6v1l-2 1-5-1h0v1c-1 0-1-1-2-2h1 1 1z" class="v"></path><path d="M122 307h1c-1 1-1 2-2 3-2 2-5 2-8 2h-6c-1-1-2-1-3-1v-1c3 1 6 1 10 1h1c3-1 5-1 7-4z" class="D"></path><path d="M126 297h2 0-2v1h1c0 1 1 4 0 5v1 2c0 2-1 4-1 6l-1-3v1h0c-1 2-2 3-3 4l-1-1v-3c1-1 1-2 2-3h-1 0c-1-1-1-1-2-1 2-1 3-4 4-7l2-2z" class="R"></path><path d="M127 306l-1-1v-2l1 1v2z" class="Z"></path><path d="M122 307v-1l1-1c1 0 1 0 2-1v1l1 1h-1c0 1 1 2 0 3h0-1 0v-3l-1 1h-1 0z" class="Y"></path><path d="M70 380c4-4 6-5 11-6 3 1 6 2 8 5 0 1 1 2 1 4v2l1-1 1 1 3 6c-1 2 0 4 1 6s3 4 5 5l4 1h1 2c1 1 2 0 2 2l1 1v3 2l1 1-9 9c-2 1-3 3-5 4h-1 0c-5 5-9 11-13 16-3 6-5 11-8 17 0 1 0 5-1 5h0c0-1-1-1-1 0s0 4-1 6h0v-2l-2 1-2-14-1-9c-1-5 0-11 0-16l-2-25c-1-4-1-7-1-10-1-4 1-8 3-11l2-2v-1z" class="C"></path><path d="M93 421l2-1v1c-1 1-3 3-2 4l-3 3c0-2 2-5 3-7z" class="R"></path><path d="M68 445c0-2 0-3 1-4v1c1 3 1 7 1 10h-1v2l-1-9z" class="W"></path><path d="M80 419l4-4-2 4c0 1-1 2-1 3v2l-1 3h0c0-1-1-1-2-1h0c1-1 1-3 1-5 1 0 1-1 1-2z" class="L"></path><path d="M81 416h0v-2c0-2 1-5 2-7 1-1 3-1 4-2-1 3-4 6-5 9 0 1 0 2-1 3v-1z" class="Y"></path><path d="M102 410h-2v-1c-1-1-1-2-1-4h-1l1-1 1 1h4v1c-2 1-2 3-2 4z" class="n"></path><path d="M69 454v-2h1l3 15-2 1-2-14z" class="S"></path><path d="M93 421c0-2 0-4 1-5 1 0 0 4 0 5 1-2 2-4 3-5h2 0 0c0 1-1 3-2 4-1 2-2 4-4 5-1-1 1-3 2-4v-1l-2 1z" class="K"></path><path d="M68 429v-5-1l1 1v18-1c-1 1-1 2-1 4-1-5 0-11 0-16z" class="Q"></path><path d="M67 399l2 25-1-1v1 5l-2-25c1-2 0-3 1-5z" class="F"></path><path d="M75 429l-3-12h1v1s1 0 1 1h0 1v-3c0 2 0 5 1 7v-3h1c0 1 1 2 1 4v1 1h0 0-2c-1 1-1 2-1 3z" class="d"></path><path d="M75 416c0 2 0 5 1 7 0 1 0 1-1 2-1-1-2-6-2-7 0 0 1 0 1 1h0 1v-3z" class="B"></path><path d="M82 441h2c-3 6-5 11-8 17 0 1 0 5-1 5h0c0-1-1-1-1 0l2-20c1 0 1-1 1-1 0 6-2 11-2 17l7-18z" class="V"></path><path d="M78 426c1 0 2 0 2 1l-3 15s0 1-1 1c0-4 0-9-1-14 0-1 0-2 1-3h2z" class="h"></path><path d="M75 416v-5h0l1 1h1v-1h1l3 5v1c0 1-1 1-1 2s0 2-1 2c0 2 0 4-1 5h0v-1-1c0-2-1-3-1-4h-1v3c-1-2-1-5-1-7z" class="E"></path><path d="M97 420h2v1c0 1-1 2-1 3-1 0-1 1-1 1-5 5-9 11-13 16h-2c3-5 5-9 8-13l3-3c2-1 3-3 4-5z" class="h"></path><path d="M106 403h2c1 1 2 0 2 2l1 1v3 2l1 1-9 9c-2 1-3 3-5 4h-1 0s0-1 1-1c0-1 1-2 1-3v-1h-2c1-1 2-3 2-4h0l3-6c0-1 0-3 2-4v-1h3c1 1 1 0 2 1h1c0-1-1-2-1-2-1-1-2 0-4-1h1z" class="I"></path><path d="M104 405h3c1 1 1 0 2 1 1 0 1 1 1 1l-1 1h-2v-2h-1-2v-1z" class="P"></path><path d="M103 417h1l6-6c-1 3-4 4-6 7 0 1-1 2-2 2l-4 4c0-1 1-2 1-3v-1h-2c1-1 2-3 2-4 1 1 2 1 2 1l1 1 1-1z" class="D"></path><path d="M97 420c1-1 2-3 2-4 1 1 2 1 2 1l1 1-3 3v-1h-2z" class="W"></path><path d="M104 406h2 1v2c-2 3-3 6-4 9l-1 1-1-1s-1 0-2-1h0l3-6c0-1 0-3 2-4z" class="N"></path><path d="M70 380c4-4 6-5 11-6 3 1 6 2 8 5 0 1 1 2 1 4v2c0 1 1 3 1 4 0 2-2 5-2 7l2 1v2c-1 3-5 5-8 5-2 1-4 0-6-1-3-2-4-5-5-8 0-3 0-6 2-8 0-1 0-1 1-1 1 1 0 3 1 4 2-3 3-5 6-7-1 4-4 10-3 14 0 1-1 2 0 3 0 1 1 1 2 1s2-2 3-3c2-5 4-13 3-18-1-2-2-3-3-4h-1c-1-1-3-1-5-1-2 1-5 4-6 7-1 0-1 0 0 0 0 1 0 1-1 2-2 2-4 7-4 10h0v5c-1 2 0 3-1 5-1-4-1-7-1-10-1-4 1-8 3-11l2-2v-1z" class="h"></path><path d="M65 394h0c0 2 0 3 1 4v-1c0-1 0-2 1-3v5c-1 2 0 3-1 5-1-4-1-7-1-10z" class="S"></path><path d="M171 114c16-9 33-17 51-22 8-2 15-4 23-5 18-4 36-8 54-8 7-1 13 0 20 0 21 2 42 5 63 11 12 4 23 8 34 13 6 2 12 5 17 9 3 2 6 4 8 6l6 5c1 0 2 1 3 2h0c-1 0-1 0-1 1h-4v1c-10-2-17-1-25 5h-1 0l2-2h0c-1 0-2 1-2 1l-1-1c-4 3-8 6-9 12-1 2-1 5-1 7 0 5 0 9 1 13 0 1 1 4 1 5h-1v2c-4-6-9-11-13-16l-12-11-8-7c-1-1-3-3-5-4-1-1-3-2-5-4h-1l-5-3-3-3c-1-1-2-2-4-3-1 0-1-1-2-1v-1c-1-1-2-2-4-3-1 0-1 1-2 0l-3-1c-6-2-11-4-16-9l1 1c0 1 1 3 3 4h1c-2 0-4-2-6-2l-1-1c-2 2-4 5-6 6-2 0-2-2-3-3s-2-1-3-2c-5-1-9-1-14-1h-2l-6 6-4-6h-1c-1 1-2 3-3 3-2 2-4 2-6 3l-2 1-6 2c-1 0-5 2-6 2-7 4-13 8-19 13h0c0 1-1 1-1 1l-1 1v-1c0-1 2-2 2-3v-1l-8 6-2 1-2 2c-1-1-1-1-1-2l-2 2h0l-6 5v-1c-2 1-2 2-4 2h-1 0v-1h1-1c-1-1-1 0-1-1-2 0-2 0-3-1l-2-1h-2v-2l1-1v-1c1-2 3-4 4-7h0 1v-1-1c1 0 2-1 3-1v-1c0-1 1-2 3-3h0c1 0 2-1 2-2h0c1 0 2-1 2-2l-2-1v-2c-1 1-2 1-2 1-1 0-2-1-2-1l-16 6h-1c-1 0-2 1-2 1-3 1-6 1-9 1h-3l-1-1h-2v1l-3-1v-2h-2c-1-1-1-1-2-1v-1c-2 0-7 1-9 0l2-1z" class="m"></path><path d="M355 107c0-1-1-1-1-2h0 1l1 1-1 1z" class="K"></path><path d="M397 140h1l1 1c0 1 0 1-1 2l-1-3z" class="C"></path><path d="M206 102l1 2-2 1c0-1-1-1-1-2l2-1z" class="M"></path><path d="M413 130c1-1 3-1 4-2v2c-1 0-2 1-4 1v-1z" class="P"></path><path d="M387 138l-3-1c-1 0-1-1-1-2v-1l1 1c1 1 2 1 4 1v1l-1 1z" class="e"></path><path d="M252 86c1 0 4 0 6-1v2l-5 1-1-2z" class="Q"></path><path d="M206 102l5-2 2 2-6 2-1-2z" class="W"></path><path d="M355 105c2 0 2 0 3-1v6c-1 0-1-1-2-1l-1-2 1-1-1-1z" class="t"></path><path d="M400 130h0c1 1 1 1 2 3v6h-1c-1-1 0-5-1-7v-2z" class="R"></path><path d="M395 135v1 1h0c-3 1-5 1-8 1l1-1c2 0 4 0 6-1l1-1z" class="f"></path><path d="M211 100l7-2s1 0 1 1l-6 3-2-2z" class="M"></path><path d="M224 95c0 1 1 1 1 2l-6 2c0-1-1-1-1-1 1-1 4-2 6-3z" class="S"></path><path d="M258 85l9-1c-1 0-1 1-2 1l3 1h-6c-1 0-2 0-4 1v-2z" class="N"></path><path d="M267 84h6 3v1l-8 1-3-1c1 0 1-1 2-1z" class="W"></path><path d="M398 143l2 2-1 1-3-3v-1c-1 0-3-1-4-1h-2l1-1c1-1 4 0 6 0l1 3z" class="E"></path><path d="M413 131c-1 0-3 0-4 1h0c-2 0-3 1-4 2v1h-1c0-2 0-2-1-4v-1h2v2c2-2 6-2 8-2v1z" class="Z"></path><path d="M384 117c1 3 5 4 7 7l1 2v1c-1-1-2-2-3-2s-1-1-2-1l-4-6 1-1z" class="e"></path><path d="M384 117l-1-2h0c2 0 3 2 4 3 0 1 2 2 3 2l1 1s-2-1-3-1l1 1c1 1 2 1 2 3-2-3-6-4-7-7z" class="I"></path><path d="M402 140c0-2 1-3 2-4h1c1 0 1 0 2-1l1 1c0 1-1 2-2 3 0 1 1 3 0 5v-4l-1-1-3 1z" class="a"></path><path d="M193 108l11-5c0 1 1 1 1 2l-6 2c-1 1-3 2-4 2h0c0-1-1-1-2-1z" class="o"></path><path d="M328 87c3-2 11-1 15 0l1 1h-1-6c-2-1-4 0-5 0-1-1 0-1-1-1h-3z" class="E"></path><path d="M354 86l13 3-1 2-15-4c1-1 2-1 3-1z" class="W"></path><path d="M402 154c-4-2-8-8-10-12 3 2 8 5 9 8v1h1v-1 4z" class="F"></path><path d="M395 135l-6-9 7 6c1 0 2 1 2 2v2c-1 1-2 1-3 1h0v-1-1z" class="e"></path><path d="M402 140l3-1 1 1v4 4-2h-3-1v4 1c-1-4-1-7 0-11zm-209-32c1 0 2 0 2 1h0l-15 8c-1-1-1-1-2-1v-1l15-7z" class="M"></path><path d="M419 122c2-2 4-5 8-5 1-1 4 0 5 0-2 1-5 2-6 4 1 0 4-1 6-1-2 1-3 2-5 2-1 1-3 0-4 1v-1h0-4z" class="N"></path><path d="M370 103h1-1c0-1-1-1-1-1v-1l9 3c3 2 6 4 10 5v1c-3 0-7-2-10-3-1 0-2-1-4-1h0c-1 0-3-2-4-3z" class="F"></path><path d="M418 130c9-6 20-6 31-4h-4v1c-10-2-17-1-25 5h-1 0l2-2h0c-1 0-2 1-2 1l-1-1z" class="L"></path><path d="M390 120c3 0 7 1 10 1-1 0-2 0-3 1h0l7 7c-2-1-4-1-6-2s-5-3-7-6l-1-1z" class="V"></path><path d="M413 108l17 7v-1h5v1c-3 1-8 0-10 1s-5 2-7 2l6-3-12-6 1-1z" class="T"></path><defs><linearGradient id="V" x1="378.367" y1="140.495" x2="378.601" y2="131.582" xlink:href="#B"><stop offset="0" stop-color="#cb0d0b"></stop><stop offset="1" stop-color="#a41b2a"></stop></linearGradient></defs><path fill="url(#V)" d="M372 125c2 3 4 6 7 9 2 2 4 4 6 7-1 0-1 0-1 1l-8-7c-1-1-3-3-5-4h1 0v-2l1 1 1-1-2-2v-2z"></path><path d="M273 84c4-1 9-1 13-1h23v1l-32 1h-1v-1h-3z" class="Q"></path><path d="M309 83c10 0 21 0 31 1l14 2c-1 0-2 0-3 1-4-1-8-1-12-2h-11l-19-1v-1z" class="L"></path><path d="M367 89c16 5 32 11 46 19l-1 1-18-9c-10-3-19-6-28-9l1-2z" class="q"></path><path d="M384 142c0-1 0-1 1-1s1 1 2 2c2 2 6 3 8 5 3 2 4 6 7 8h0v-2-4-4h1 3v2l3 19v2c-4-6-9-11-13-16l-12-11z" class="r"></path><path d="M358 104l1-1c0 1 1 3 1 4 2 6 5 11 8 16l4 1v1 2l2 2-1 1-1-1v2h0-1c-1-1-3-2-5-4h-1l-5-3-3-3c-1-1-2-2-4-3-1 0-1-1-2-1v-1c-1-1-2-2-4-3 0 0 0-1 1-2h0v-1h-1v-1l1-1h0c0-1 2-1 3-2l2 3 2 1 1-1c1 0 1 1 2 1v-6z" class="T"></path><path d="M368 123l4 1v1 2l2 2-1 1-1-1-4-6z" class="P"></path><path d="M362 119l3 6s1 1 1 2h-1l-5-3-3-3 1-1 1 1 1-1c1 0 1-1 2-1z" class="G"></path><path d="M362 119l3 6-1-1c-2-1-3-2-5-3l1-1c1 0 1-1 2-1z" class="Y"></path><path d="M351 106l2 3 2 1 1-1c1 0 1 1 2 1l1 2 3 7c-1 0-1 1-2 1l-1 1-1-1-1 1c-1-1-2-2-4-3-1 0-1-1-2-1v-1c-1-1-2-2-4-3 0 0 0-1 1-2h0v-1h-1v-1l1-1h0c0-1 2-1 3-2z" class="c"></path><path d="M349 112c1-1 2-1 3-1l2 2-1 1c-1 0-3 0-4-2z" class="j"></path><path d="M351 106l2 3s-1 0-1 1l-1-1-3 2v-1h-1v-1l1-1h0c0-1 2-1 3-2z" class="e"></path><path d="M356 109c1 0 1 1 2 1l1 2-2 1 1 2h0c-1 0-1 0-2-1s-2-3-4-4c0-1 1-1 1-1l2 1 1-1z" class="E"></path><path d="M356 109c1 0 1 1 2 1l1 2-2 1-2-3 1-1z" class="i"></path><path d="M348 111v2h1c0-1-1-1 0-1 1 2 3 2 4 2 2 1 3 2 4 4h-1c1 1 1 2 2 2l-1 1c-1-1-2-2-4-3-1 0-1-1-2-1v-1c-1-1-2-2-4-3 0 0 0-1 1-2z" class="p"></path><path d="M359 112l3 7c-1 0-1 1-2 1l-1 1-1-1c-1 0-1-1-2-2h1c-1-2-2-3-4-4l1-1 2 2v-1c1 1 1 1 2 1h0l-1-2 2-1z" class="a"></path><path d="M357 118l3 2-1 1-1-1c-1 0-1-1-2-2h1z" class="K"></path><path d="M302 90c1-1 2-1 3 0 3 1 5 5 8 6l3-3c-2 4-3 7-5 11 0 1 1 2 1 2-5-1-9-1-14-1h-2l-1-2c-1-2-1-4-2-6l-1-3c2-1 2-3 5-3 1-1 2-1 4-1h1z" class="c"></path><path d="M298 103c1 0 3 0 4-1 0 0 0-1 1-2 0-1 0-1 1-1 1 1 2 1 4 1h0v1h-2v1c1 0 2 1 3 1h1l-1 1c-1-1-2 0-2 0-3-1-7 0-9-1z" class="Q"></path><path d="M297 104v-4l1-1v4c2 1 6 0 9 1 0 0 1-1 2 0l1-1 1 1c0 1 1 2 1 2-5-1-9-1-14-1v-1h-1z" class="d"></path><path d="M302 90c1-1 2-1 3 0 3 1 5 5 8 6l3-3c-2 4-3 7-5 11l-1-1h-1c-1 0-2-1-3-1v-1h2v-1h1c0-4-2-7-5-9v-1h0-2z" class="L"></path><path d="M301 90h1 2 0v1c-2 2-4 7-6 8l-1 1v4h1v1h-2l-1-2c-1-2-1-4-2-6l-1-3c2-1 2-3 5-3 1-1 2-1 4-1z" class="M"></path><path d="M297 96c0 3-1 4-1 7l1 1h0 1v1h-2l-1-2v-1c0-1 0-3 1-4 0-1 0-1 1-2z" class="U"></path><path d="M292 94c2-1 2-3 5-3 1-1 2-1 4-1l-1 1c-1 2-2 3-3 4v1h0c-1 1-1 1-1 2-1 1-1 3-1 4v1c-1-2-1-4-2-6l-1-3z" class="K"></path><path d="M300 91c-1 2-2 3-3 4-1-1 0-2 0-3l3-1z" class="J"></path><path d="M293 97c1-1 1-2 1-3 0 0 1 0 1-1 2 1 1 2 2 3h0c-1 1-1 1-1 2-1 1-1 3-1 4v1c-1-2-1-4-2-6z" class="N"></path><path d="M328 87h3c1 0 0 0 1 1 1 0 3-1 5 0h6v1c-3 0-6 0-9 1v1c2 0 4-1 6 0-2 0-5 0-6 1h0 2v1c-1 0-2 1-4 1-2 1-3 3-5 4v1h1l1 1h1-2l-2 2v1l1 1c0 1 1 3 3 4h1c-2 0-4-2-6-2l-1-1c-2 2-4 5-6 6-2 0-2-2-3-3s-2-1-3-2c0 0-1-1-1-2 2-4 3-7 5-11 3-3 7-5 12-6z" class="r"></path><path d="M322 94l1-1c0 2-1 3-1 4 1-1 1-2 3-3v1h-1c-2 2-4 6-5 9 0-4 2-8 3-10z" class="b"></path><path d="M332 88c1 0 3-1 5 0h6v1c-3 0-6 0-9 1v1c2 0 4-1 6 0-2 0-5 0-6 1h0c-3 1-5 2-6 3-2 0-2 1-4 0h1v-1c-2 1-2 2-3 3 0-1 1-2 1-4l-1 1h-1c-1 0-1 1-2 2h0v-1l3-3c3-2 6-3 10-4z" class="D"></path><path d="M325 95l3-3c2-1 4-2 6-2v1c2 0 4-1 6 0-2 0-5 0-6 1h0c-3 1-5 2-6 3-2 0-2 1-4 0h1z" class="d"></path><path d="M334 92c4 0 6 0 10 1v1c1 2 2 3 3 5 1 0 1 2 1 2v3c0 1 0 1 2 2h1c-1 1-3 1-3 2h0l-1 1v1h1v1h0c-1 1-1 2-1 2-1 0-1 1-2 0l-3-1c-6-2-11-4-16-9v-1l2-2h2-1l-1-1h-1v-1c2-1 3-3 5-4 2 0 3-1 4-1v-1h-2 0z" class="N"></path><path d="M342 108v-1c1-1 2-2 3-2v2l-2 1h-1z" class="D"></path><path d="M348 104c0 1 0 1 2 2-2 0-4 1-5 1v-2l3-1z" class="O"></path><path d="M337 98c3 0 4 1 6 3v1c-1 0-2-2-3-2h0c-2 0-2-1-3-2z" class="K"></path><path d="M350 106h1c-1 1-3 1-3 2-1 0-2 0-3 1h-1l-1-1 2-1c1 0 3-1 5-1z" class="E"></path><path d="M336 93h4c1 0 2 1 2 1 1 1 2 3 3 4 0 0 1 1 1 2-3-2-5-4-9-5-1-1-3 0-5-1 2 0 3-1 4-1z" class="D"></path><path d="M337 98h0c1 1 1 2 3 2h0c0 1 1 2 1 3s0 4 1 5h1l1 1h1c1-1 2-1 3-1h0l-1 1v1h1v1h0c-1 1-1 2-1 2-1 0-1 1-2 0v-1c-1 0-2-1-4-1l1-1h0c-2-2-3-4-4-6s-2-3-4-5c0-1 2-1 3-1z" class="I"></path><path d="M342 110h0 2 3 1v1h0c-1 1-1 2-1 2-1 0-1 1-2 0v-1c-1 0-2-1-4-1l1-1h0z" class="V"></path><path d="M328 100h2-1l-1-1h-1v-1c2-1 3-3 5-4 2 1 4 0 5 1h1c0 1 1 2 2 2s2 1 3 2h-1s-1-1-2-1h0c-1-1-2-1-2-1l-1 1c-1 0-3 0-3 1 2 2 3 3 4 5h0-1l-5-3c-1-1-3-1-4-1z" class="T"></path><path d="M328 100c1 0 3 0 4 1l5 3h1 0c1 2 2 4 4 6h0l-1 1c2 0 3 1 4 1v1l-3-1c-6-2-11-4-16-9v-1l2-2z" class="S"></path><path d="M328 100c1 0 3 0 4 1l5 3c-2 1-5-2-7-2-1-1-2-1-4-1 5 5 9 8 15 10 2 0 3 1 4 1v1l-3-1c-6-2-11-4-16-9v-1l2-2z" class="m"></path><path d="M334 90c3-1 6-1 9-1 8 2 16 6 22 11 2 1 3 2 5 3 1 1 3 3 4 3h0c2 1 3 2 4 2 1 1 3 1 4 2s2 2 2 3h1c2 0 4-1 6-2s5 0 8 1c2 1 4 1 6 3-2 0-6-2-8-3h-4-2c1 1 3 1 4 2 3 0 6 1 8 3h0l-3-1-1 1s-1 0-2-1v1h1c1 0 1 0 2 1h-1c6 3 13 6 20 4h4 0v1c-2 1-5 1-7 2h0c-2 0-3 0-4-1-2 0-3 0-6 1-2-1-4-3-6-4-3 0-7-1-10-1-1 0-3-1-3-2-1-1-2-3-4-3h0l1 2-1 1c-1-1-8-7-8-7h-1s-1-1-2-1c-5-5-10-10-17-13-2-1-5-3-7-3-2-1-3-1-4-1-2-1-3-2-4-2-2-1-4 0-6 0v-1z" class="M"></path><path d="M399 118l-9-3c4 0 7 1 10 1l-1 1s-1 0-2-1v1h1c1 0 1 0 2 1h-1z" class="D"></path><path d="M258 87c2-1 3-1 4-1l4 1h0c-5 2-9 3-14 6-4 2-7 4-10 5-2 1-5 2-6 3 0 1 0 1-1 2h-1v2c-1 1-3 2-4 3-3 1-6 2-7 4-1 1-2 1-2 1-1 0-2-1-2-1l-16 6h-1c-1 0-2 1-2 1-3 1-6 1-9 1h-3l-1-1h-2v1l-3-1v-2h-2l15-8c1 0 3-1 4-2l6-2 2-1 6-2 6-3 6-2c0-1-1-1-1-2l7-2c7-3 14-5 21-7l1 2 5-1z" class="C"></path><path d="M222 107h2 2c1 0 1 0 2 1l-2 1c-2 1-5 0-7 0l3-2z" class="G"></path><path d="M224 95l7-2h0l3 1-9 3c0-1-1-1-1-2z" class="M"></path><path d="M182 117c2 0 4 1 6 1s5 0 7-1v1c-2 0-5 1-8 1h-2v1l-3-1v-2z" class="N"></path><path d="M222 107c5-3 10-4 14-6 0 1 0 1-1 2h-1v2c-1 1-3 2-4 3h-2c-1-1-1-1-2-1h-2-2z" class="Q"></path><path d="M231 93c7-3 14-5 21-7l1 2-19 6-3-1h0z" class="T"></path><path d="M276 85h1 0l1 1c5 0 10 2 13 6l1 2 1 3c1 2 1 4 2 6l1 2-6 6-4-6h-1c-1 1-2 3-3 3-2 2-4 2-6 3l-2 1-6 2c-1 0-5 2-6 2-7 4-13 8-19 13h0c0 1-1 1-1 1l-1 1v-1c0-1 2-2 2-3v-1l-8 6-2 1-2 2c-1-1-1-1-1-2l-2 2h0l-6 5v-1c-2 1-2 2-4 2h-1 0v-1h1-1c-1-1-1 0-1-1-2 0-2 0-3-1l-2-1h-2v-2l1-1v-1c1-2 3-4 4-7h0 1v-1-1c1 0 2-1 3-1v-1c0-1 1-2 3-3h0c1 0 2-1 2-2h0c1 0 2-1 2-2l-2-1v-2c1-2 4-3 7-4 1-1 3-2 4-3v-2h1c1-1 1-1 1-2 1-1 4-2 6-3 3-1 6-3 10-5 5-3 9-4 14-6h0l-4-1h6l8-1z" class="C"></path><path d="M251 103c1 0 1 0 2 1v1h-2v-2z" class="b"></path><path d="M226 115h1c-1 1-1 1 0 2-2 1-2 0-4 0h0c1 0 2-1 2-2h1z" class="K"></path><path d="M229 112v2l-2 3c-1-1-1-1 0-2h-1l3-3z" class="Y"></path><path d="M238 124c1-2 2-3 3-4h2 0l-2 4h-3z" class="i"></path><path d="M229 112l3-3c2 0 2 0 3 1-2 1-4 3-6 4v-2z" class="a"></path><path d="M248 118c2-3 4-7 7-9v1l-1 2c-1 1-1 1-1 2-1 1-3 2-3 4h0c-1 1 0 0-1 0v-1s-1 0-1 1z" class="E"></path><path d="M235 127v3h0l-1 1c1 0 1 1 1 1l-2 1-2 2c-1-1-1-1-1-2 2-2 3-3 5-6z" class="S"></path><path d="M250 118c1-1 2-2 3-2h1l-7 7-2-1c1-1 2-3 3-4 0-1 1-1 1-1v1c1 0 0 1 1 0z" class="V"></path><path d="M218 136h0c3-1 4-2 6-4 0 2-1 5-2 7-2 1-2 2-4 2h-1 0v-1h1c0-1 1-1 1-1l2-2h-2c-1 0-1 0-1-1z" class="j"></path><path d="M213 136h5c0 1 0 1 1 1h2l-2 2s-1 0-1 1h-1c-1-1-1 0-1-1-2 0-2 0-3-1l-2-1h0c1-1 2-1 2-1z" class="b"></path><path d="M213 136h5c0 1 0 1 1 1-2 1-4 1-6 1l-2-1h0c1-1 2-1 2-1z" class="f"></path><path d="M260 101v1c0 1 0 1 1 2l-1 1c-1 1-1 1 0 2l2 2h-1v1c1 0 1 0 1 1h-1 0l-1-1c-2 1-3 2-4 2h-1l2-2h1l1-1h0c0-1-1-2-1-3h-1c-1-2 2-2 2-3 1 0 1-1 1-2z" class="E"></path><path d="M255 112h1c1 0 2-1 4-2l1 1-2 2h1l-6 3h-1c-1 0-2 1-3 2h0c0-2 2-3 3-4 0-1 0-1 1-2h1z" class="f"></path><path d="M266 93l1 1h0c1 1 2 1 3 1l-2 2-5 3c-1 0-2 1-3 2v-1c-1 0-1-1-1-2 1-1 2-1 3-2l2-1v-1c0-1 1-1 2-2z" class="G"></path><path d="M262 97c1 0 1 0 2-1v1c0 1 0 1-1 1l-1 1 1 1c-1 0-2 1-3 2v-1c-1 0-1-1-1-2 1-1 2-1 3-2z" class="d"></path><path d="M243 120c3-5 5-11 7-17h1v2c-1 3-2 7-3 10-1 2-3 4-4 6v-1h-1 0z" class="N"></path><path d="M268 97l1 1v1h-1c-1 1-2 7-5 7-1 0-2-1-2-2-1-1-1-1-1-2 1-1 2-2 3-2l5-3z" class="T"></path><path d="M218 122h1c1 0 1-1 1-1l1-1h0c-1 3-3 5-4 7s-5 6-5 8v1h1s-1 0-2 1h0-2v-2l1-1v-1c1-2 3-4 4-7h0 1v-1-1c1 0 2-1 3-1v-1z" class="g"></path><path d="M218 123c0 2-3 5-5 7-1 2-2 3-3 4v-1c1-2 3-4 4-7h0 1v-1-1c1 0 2-1 3-1z" class="C"></path><path d="M243 120h1v1c0 1 0 1 1 1l2 1-4 3-8 6s0-1-1-1l1-1h0v-3c0-1 2-2 3-3h3l2-4z" class="q"></path><path d="M238 124h3l-6 6v-3c0-1 2-2 3-3z" class="R"></path><path d="M269 99h1 1 3l5 1c1 0 3 1 4 2 0 2-1 2-2 3 0 1-2 2-2 2-1 1-3 2-4 2 0 1 0 1 1 2l-2 1-6 2c-1 0-5 2-6 2-7 4-13 8-19 13h0c0 1-1 1-1 1l-1 1v-1c0-1 2-2 2-3v-1l4-3 7-7 6-3h-1l2-2h0 1c0-1 0-1-1-1v-1h1l-2-2c-1-1-1-1 0-2l1-1c0 1 1 2 2 2 3 0 4-6 5-7h1z" class="D"></path><path d="M262 109l4 1-1 1h0c-1 2-3 1-5 2h-1l2-2h0 1c0-1 0-1-1-1v-1h1z" class="V"></path><path d="M273 110l2-1c0 1 0 1 1 2l-2 1-6 2-1-1c-1 0 0 0 0-1l6-2z" class="S"></path><path d="M273 110l2-1c0 1 0 1 1 2l-2 1c0-1-1-1-1-1v-1h0z" class="M"></path><path d="M276 102h0 1c1-1 2-1 3-1l1 1c-4 4-11 7-16 9h0l1-1 3-5c0 2-1 3-2 4h0c1 0 1 0 2-1h1l1-1h0c1-1 3-4 5-5z" class="d"></path><path d="M271 99h3 0c1 1 2 1 3 1 0 1-1 2-1 2-2 1-4 4-5 5h0l-1 1h-1c-1 1-1 1-2 1h0c1-1 2-2 2-4s3-4 4-6h-2z" class="U"></path><path d="M261 114c2-1 4-1 6-2 0 1-1 1 0 1l1 1c-1 0-5 2-6 2-7 4-13 8-19 13h0c0 1-1 1-1 1l-1 1v-1c0-1 2-2 2-3 6-5 11-10 18-13z" class="G"></path><path d="M261 114c2-1 4-1 6-2 0 1-1 1 0 1l1 1c-1 0-5 2-6 2h-2s-1 1-2 1c1-1 2-1 3-2v-1z" class="d"></path><path d="M269 99h1 1 2c-1 2-4 4-4 6l-3 5-4-1-2-2c-1-1-1-1 0-2l1-1c0 1 1 2 2 2 3 0 4-6 5-7h1z" class="D"></path><path d="M276 85h1 0l1 1c5 0 10 2 13 6l1 2 1 3c1 2 1 4 2 6l1 2-6 6-4-6h-1c-1 1-2 3-3 3-2 2-4 2-6 3-1-1-1-1-1-2 1 0 3-1 4-2 0 0 2-1 2-2 1-1 2-1 2-3-1-1-3-2-4-2l-5-1h-3-1-1v-1l-1-1 2-2c-1 0-2 0-3-1h0l-1-1h0v-1c-9 1-16 5-23 10-3 2-6 5-8 7v1c-1-1-1-1-3-1l-3 3-3 3h-1l-2-1v-2c1-2 4-3 7-4 1-1 3-2 4-3v-2h1c1-1 1-1 1-2 1-1 4-2 6-3 3-1 6-3 10-5 5-3 9-4 14-6h0l-4-1h6l8-1z" class="h"></path><path d="M275 109c1 0 3-1 4-2 0 2-1 2-2 2h2 1l2-1c-2 2-4 2-6 3-1-1-1-1-1-2z" class="W"></path><path d="M276 85h1 0l1 1h-5-2l-1 1c-1 0-3 1-4 0h0l-4-1h6l8-1z" class="k"></path><path d="M236 101c1-1 4-2 6-3-1 1-1 2-1 2-1 2-5 4-7 5v-2h1c1-1 1-1 1-2z" class="V"></path><path d="M273 92h1s1 1 2 1h2v1c1 1 0 2 1 3h-1c-1 0-1 1-2 1 0-1 1-1 1-1-1-2-3-1-4-1s-1-1-1-1c2-1 4-1 6 0-1-1-3-2-5-3z" class="M"></path><path d="M266 92h7c2 1 4 2 5 3-2-1-4-1-6 0h-2c-1 0-2 0-3-1h0l-1-1h0v-1z" class="B"></path><path d="M270 95h2s0 1 1 1 3-1 4 1c0 0-1 0-1 1h-1 1 2s1 1 1 2l-5-1h-3-1-1v-1l-1-1 2-2z" class="U"></path><path d="M270 99c1-2 3-1 5-1h1 2s1 1 1 2l-5-1h-3-1z" class="o"></path><path d="M270 87l1-1h2v1c2 0 5 0 7 1h0c5 3 6 6 8 11-6-5-11-9-18-11h0v-1z" class="Z"></path><path d="M203 118l16-6s1 1 2 1c0 0 1 0 2-1v2l2 1c0 1-1 2-2 2h0c0 1-1 2-2 2h0c-2 1-3 2-3 3v1c-1 0-2 1-3 1v1 1h-1 0c-1 3-3 5-4 7v1l-1 1v2h2l2 1c1 1 1 1 3 1 0 1 0 0 1 1h1-1v1l-6 1h-1-1c-1 2-1 4-2 6v-2l-2 1v-2-3l-1-1h0-2c-1 1-1 2-1 3v7l-1 9h-1v-3c-1 3-1 7-3 9 0 1 0 1-1 0-1 2-1 4-2 6-1 3-10 18-10 20l-1 3h-1v1h-2c0 1-1 1-2 1v1-2-1l-1-3c0 1-1 0-1 0-4-2-6-1-9 0-2 0-6 0-7-1v2l-1 1c2 1 5 3 8 5l6 6v2l1 8c0 2 0 4 1 6 1 5-1 10-1 15l-1-2c-1 1-2 3-4 5h0c-1 1-1 2-1 3-1 1-2 3-3 4-1 0-1 1-1 2l-1-1v-6l-2 1c-1 0-2 0-3-1-2-1-4-1-6-1v1l7 6c1 1 2 3 3 5l-1 1-2-3-1 1h0v1h-1v1 1l-1-1h-1c-2-1-4-2-7-2-1 0-3 0-4 1h1c0 1 1 1 2 2l1 1v1c-4 0-6 0-9-1h-4c-1 0-1 0-2-1h0c-1 0-1 1-2 1 0 2-5 4-6 6-2 1-3 2-5 3 0 1 0 2-1 3s-2 4-4 4h-3c-2 0-3 0-4 1l-3 1-3 2s2 1 2 2l12 8c1 1 1 1 1 3 1 1 1 2 2 3s1 1 2 1l1 1h0s0 1-1 0c-3 0-5-1-7-3h0v2h0-1c-1 0-2 0-3 1h-1c-2 1-3 0-4-1 0 2 1 2 2 2l-1 1h-3c0 1 0 1-1 1-2 0-7-2-8-4h-1c-4-3-7-8-12-10l1-1h1 1s0-1-1-1c-1-2-2-3-2-5l-1-1v-1h0v-4c-5-7-7-12-7-20v-2-7l-1 1-1-2h-1c0-1 1-2 1-3s1-3 1-5v-1-1l-1-1c2-5 4-11 7-16 4-9 9-18 14-26 1-1 1-1 1-2l4-5 3-4 3-4c0-1 1-2 2-3 0-1 0-1 1-1v-1l3-3c0-1 1-1 1-2l9-9c3-3 5-7 8-9l3-3c2-1 2-2 4-3 1 0 2-1 2-2h1l2-2c1-1 2-2 4-3 1 0 2-1 3-2l2-2h0l3-1c0-1 2-2 3-3 2-1 3-2 5-3 2-2 4-3 5-4 1 0 2-1 3-1v-1h1c0 1-1 2-1 2h0 1 2c2 1 7 0 9 0v1c1 0 1 0 2 1h2v2l3 1v-1h2l1 1h3c3 0 6 0 9-1 0 0 1-1 2-1h1z" class="h"></path><path d="M89 264c-2-1-2-1-3-2l1-1c1 0 2 1 2 2v1z" class="g"></path><path d="M94 274c2-1 4 1 6 1-1 1-1 2-1 2-2-1-4-2-5-3z" class="D"></path><path d="M109 293c-2-2-2-4-2-6l1-1 1 1c0 2 2 3 3 5-1 0-2 0-3 1z" class="Z"></path><path d="M71 231c1 0 1 1 2 1 0 3-1 6-1 8l-1 1-1-2h-1c0-1 1-2 1-3s1-3 1-5z" class="W"></path><path d="M79 269l1 1c3 3 3 4 4 8l-2-1c-1 0-1-1-1-2h-1l-1-1v-1h0v-4z" class="H"></path><path d="M79 269l1 1c0 1 1 2 1 3h-2v-4z" class="l"></path><path d="M112 285l4 3c1 1 1 2 2 3s1 1 2 1l1 1h0s0 1-1 0c-3 0-5-1-7-3h0v2h0-1c-1-2-3-3-3-5h0l2 2h0 1 0c2 0 3 2 4 2-1-1-1-1-1-3 0-1-1-1-2-2-1 0-1-1-1-1z" class="d"></path><path d="M100 275l3 2 12 8c1 1 1 1 1 3l-4-3-13-8s0-1 1-2z" class="k"></path><path d="M91 264c1 1 2 1 3 1h1 1c1 2 2 2 3 2h0l1 1c1 1 2 2 4 2l7 1c-2 0-3 0-4 1-2-1-5-1-7-1-5-1-9-2-13-4h0 1 2c1 1 1 1 2 1s2 1 3 1h1c1 1 1 1 2 1h1 2 1l-2-1-6-3c-1-1-2-1-3-2h0z" class="Q"></path><path d="M82 249c0-1 1-1 1-1 2-1 3-2 4-3 2-1 2-1 4 0h0c0 1-1 2-1 4l1 1h1 0c2 0 3-2 4-2h0c0 4-3 5-5 8h0c0-2 1-2 2-3l-3-3-1-1c1-1 0-1 0-2h1v-2h-1c-1 0-3 2-4 3v1h0c1 0 2 1 3 1v2c-1 0-1 0-2 1 0-1 1-1 1-2-1-1-4-2-5-2z" class="d"></path><path d="M93 255c1 1 1 1 1 2v1h0l1 1h1c-1 1-1 3-1 4l1 1-1 1h-1c-1 0-2 0-3-1h-2 0v-1c0-1-1-2-2-2h0l1-3c1 0 1 0 2-1 0 0 0-1 1-1h0v1c1-1 2-1 2-2z" class="f"></path><path d="M88 258c1 1 1 2 2 3h-3l1-3z" class="e"></path><path d="M93 255c1 1 1 1 1 2v1h0l-2 1-2 2c-1-1-1-2-2-3 1 0 1 0 2-1 0 0 0-1 1-1h0v1c1-1 2-1 2-2z" class="G"></path><path d="M93 255c1 1 1 1 1 2v1h0l-2 1c0-1-1-1-1-2 1-1 2-1 2-2z" class="V"></path><path d="M95 259h1c-1 1-1 3-1 4l1 1-1 1h-1c-1 0-2 0-3-1h-2l1-2 5-3z" class="E"></path><path d="M155 125c0 2-3 3-4 5s-2 3-2 5h-1s-1 0-1 1v-1c-1 0-2 1-2 2l-2 2c-1 1-1 2-2 2-1-1 0-2 0-3l-1-1v-1l15-11z" class="I"></path><path d="M126 149l14-13v1c-1 2 0 3 0 5l-3 4c-3 0-6 1-8 3h-3 0zm-26 134l-2-2h1c1 0 2 1 2 2 1 2 1 3 1 5l-2 3-5 1-3-3-2-2 1-1 2-2c1 0 1 0 2 1 1-1 1-2 3-1l1-1v1l1-1z" class="D"></path><path d="M95 285c1-1 1-2 3-1l1-1v1l1-1c0 1 1 2 1 3l-2 2h-2c-1-1-2-2-2-3z" class="W"></path><path d="M70 228c2-5 4-11 7-16 4-9 9-18 14-26 1 1-6 13-7 15-5 10-9 20-11 31-1 0-1-1-2-1v-1-1l-1-1z" class="k"></path><path d="M92 272c-1-1-2-1-3-1-5-3-8-6-10-11 3 2 5 5 8 7 4 2 8 3 13 4 2 0 5 0 7 1l-3 1-3 2s2 1 2 2l-3-2c-2 0-4-2-6-1h0l-2-2z" class="D"></path><path d="M100 271c2 0 5 0 7 1l-3 1h-7c1-1 2-1 2-1h1 2c-1 0-1 0-2-1h0z" class="B"></path><path d="M92 272l5 1h7l-3 2s2 1 2 2l-3-2c-2 0-4-2-6-1h0l-2-2z" class="M"></path><path d="M126 149h0 3c2-2 5-3 8-3-3 4-7 8-12 11h-1c-1 1-2 1-4 1-1 0-3 1-4 1 2-3 4-5 7-8l3-2z" class="k"></path><path d="M129 151h1c-1 2-6 4-6 6-1 1-2 1-4 1 3-3 6-5 9-7z" class="l"></path><path d="M126 149h0 3c2-2 5-3 8-3-3 4-7 8-12 11h-1c0-2 5-4 6-6h-1s1 0 1-1c1 0 2-1 3-2l-7 3-1 1-1-1h-1l3-2z" class="P"></path><path d="M103 175c1 3 3 5 5 7h0c-3 0-5 1-7 3h-2l-2 2 1 1-2 4c-1 2-1 3-3 4l-1 1h-3l8-13c1-3 3-6 6-9z" class="B"></path><path d="M97 187l1 1-2 4c-1 2-1 3-3 4l-1 1v-1c0-2 4-7 5-9z" class="Q"></path><path d="M84 278v1l3-3 1-2h1l1 1c0 1 1 4 0 5s-2 2-3 2l-1 1 5 1v1l-1 1h1l-1 1 2 2 3 3 5-1 2-3c0 1 1 2 1 3l1 1h0c0 2 1 2 2 2l-1 1h-3c0 1 0 1-1 1-2 0-7-2-8-4h-1c-4-3-7-8-12-10l1-1h1 1s0-1-1-1c-1-2-2-3-2-5h1c0 1 0 2 1 2l2 1z" class="I"></path><path d="M102 295c-4-1-7-2-10-5-2-1-3-3-5-4l1-1h1c0 1 1 1 1 2h0l2 2 3 3 5-1 2-3c0 1 1 2 1 3l1 1h0c0 2 1 2 2 2l-1 1h-3z" class="L"></path><path d="M81 217c0 2 0 3 1 5h0 2v1h0c2 2 3 3 4 6 1 2 1 6-1 8-1 2-3 3-4 5-1 1-2 3-2 5v1l1 1c1 0 4 1 5 2 0 1-1 1-1 2h-4c-2-1-4-2-6-4-2-5 1-15 2-20l3-12z" class="C"></path><path d="M82 227l2 1c0 1-1 2-1 2 0 2 1 4 1 6l1-1c-1 2-2 3-3 5 1-3 1-7 1-10l-1-3z" class="X"></path><path d="M82 222h0 2v1h0c0 1 0 2 1 2 1 3 1 7 0 10h0l-1 1c0-2-1-4-1-6 0 0 1-1 1-2l-2-1v-5z" class="e"></path><defs><linearGradient id="W" x1="97.882" y1="232.095" x2="79.527" y2="239.487" xlink:href="#B"><stop offset="0" stop-color="#161414"></stop><stop offset="1" stop-color="#361f1f"></stop></linearGradient></defs><path fill="url(#W)" d="M82 211h1v1c0 1 1 3 1 4 2 2 3 4 4 5l2 2c1 1 2 1 3 1s1 0 1 1 2 2 3 3l1 1h0l-1 2 2 1v3c0 2 0 4-1 5h0c0 3-2 5-2 8-1 0-2 2-4 2h0-1l-1-1c0-2 1-3 1-4h0c-2-1-2-1-4 0-1 1-2 2-4 3 0 0-1 0-1 1l-1-1v-1c0-2 1-4 2-5 1-2 3-3 4-5 2-2 2-6 1-8-1-3-2-4-4-6h0v-1h-2 0c-1-2-1-3-1-5v-2c1-1 1-3 1-4z"></path><path d="M92 246l2-2v-1c0-1 2-2 2-3 0 2-1 5-3 7l-1-1h0z" class="t"></path><path d="M91 245l1 1h0l1 1c0 1-1 2-1 3h-1l-1-1c0-2 1-3 1-4h0z" class="Y"></path><path d="M97 228l1 1h0l-1 2s-1 1-2 1h0l-1-2c1-1 1-1 2-1h1v-1z" class="M"></path><path d="M94 234h1c1 2-1 5-2 7h0-1 0c1-1 1-3 1-4 1-1 0-2 1-3z" class="H"></path><path d="M90 223c1 1 2 1 3 1s1 0 1 1 2 2 3 3v1h-1c-1 0-1 0-2 1v-1s0-1-1-1v-2c-1-1-1-1-2-1l-2-2h1z" class="J"></path><path d="M90 228c1 3 2 8 0 11-1 2-5 3-6 4s-2 3-3 4c0-2 1-4 2-5 1-2 3-3 4-5 2-2 2-6 1-8l2-1z" class="E"></path><path d="M82 211h1v1c0 1 1 3 1 4 2 2 3 4 4 5l2 2h-1c-2-2-4-3-6-4 2 3 6 6 7 9l-2 1c-1-3-2-4-4-6h0v-1h-2 0c-1-2-1-3-1-5v-2c1-1 1-3 1-4z" class="Q"></path><path d="M82 211h1v1c0 1 1 3 1 4v1c-1 0-2-1-3-2 1-1 1-3 1-4z" class="V"></path><path d="M97 187l2-2h2c-1 1-2 2-2 3l1 1h0c-1 1-1 1-1 2-1 3-1 6 0 9l1 1c1 2 5 7 8 7h1c-1 1-2 2-3 2v-1c-2 1-3 1-4 3-1 1-1 2-1 3 0 0-1 0-1-1v-1l-2-2s-2 1-2 2v1h-1-1c-1 1-2 1-2 2s1 1 2 1h0c-1 1-2 2-2 3s1 3 1 4c-1 0-2 0-3-1l-2-2c-1-1-2-3-4-5 0-1-1-3-1-4v-1h-1c0-2 1-3 1-4l1-1c0-1 1-2 2-3l3-6h3l1-1c2-1 2-2 3-4l2-4-1-1z" class="C"></path><path d="M97 206c0 2 1 2 1 4-1 0-3 0-4-1l3-3z" class="R"></path><path d="M97 187l2-2h2c-1 1-2 2-2 3l1 1h0c-1 1-1 1-1 2-1 3-1 6 0 9l1 1-2 2c0 1-1 3 0 4 0 1 1 1 1 2 0 2 1 3 1 4l-2-2v-1c0-2-1-2-1-4h0c0-2 1-3 1-4 0-2-1-4-1-6v-3s-1 0-1-1l2-4-1-1z" class="p"></path><path d="M98 203c0-2 0-2 1-3l1 1-2 2z" class="g"></path><path d="M82 211c0-2 1-3 1-4l1-1c0-1 1-2 2-3 2 2 0 7 1 10 0 2 1 4 2 6-3-2-5-4-6-7v-1h-1z" class="L"></path><path d="M98 203l2-2c1 2 5 7 8 7h1c-1 1-2 2-3 2v-1c-2 1-3 1-4 3-1 1-1 2-1 3 0 0-1 0-1-1v-1c0-1-1-2-1-4 0-1-1-1-1-2-1-1 0-3 0-4z" class="X"></path><path d="M110 208c3 0 4 0 7 1v1h-3v1h1c1 1 1 1 2 1v1c2 3 2 5 2 8 1 3 1 6 3 9 0 0 0 1 1 1l2-1h0 0v2c1-1 1-1 2-1 1 1 2 1 3 2 3 2 8 2 11 2 1 0 2 1 3 1h0 2c3 0 6 2 8 3h1 2v2c-2-1-4-1-6-1v1l7 6c1 1 2 3 3 5l-1 1-2-3-1 1h0v1h-1v1 1l-1-1h-1c-2-1-4-2-7-2-1 0-3 0-4 1h1c0 1 1 1 2 2l1 1v1c-4 0-6 0-9-1h-4c-1 0-1 0-2-1h0c-1 0-1 1-2 1 0 2-5 4-6 6-2 1-3 2-5 3 0 1 0 2-1 3s-2 4-4 4h-3l-7-1c-2 0-3-1-4-2l-1-1h0c-1 0-2 0-3-2h-1l1-1-1-1c0-1 0-3 1-4h-1l-1-1h0v-1c0-1 0-1-1-2 0 1-1 1-2 2v-1c2-3 5-4 5-8h0c0-3 2-5 2-8h0c1-1 1-3 1-5v-3l-2-1 1-2h0l-1-1c-1-1-3-2-3-3s0-1-1-1c0-1-1-3-1-4s1-2 2-3h0c-1 0-2 0-2-1s1-1 2-2h1 1v-1c0-1 2-2 2-2l2 2v1c0 1 1 1 1 1 0-1 0-2 1-3 1-2 2-2 4-3v1c1 0 2-1 3-2h1z" class="m"></path><path d="M94 257l3-2-1 4h-1l-1-1h0v-1z" class="X"></path><path d="M123 231l2-1h0 0v2h0c1 1 2 1 3 2v1h-1c-2-1-3-2-4-4z" class="R"></path><path d="M100 241v-1h1l5 10-3-3-2-2c-1-1-1-2-1-4z" class="f"></path><path d="M131 239h1v1c-1 0 0 1-1 2-3 2-7 4-11 4v-1c3 0 4-1 6-2s4-2 5-4z" class="E"></path><path d="M126 243v-1c-2 0-3 0-5-1h4c0-2-1-1-2-1l-1-1h3c1 1 4 0 6 0-1 2-3 3-5 4z" class="O"></path><path d="M113 238c4 4 6 8 7 14 0 2-1 4-2 6l-1-1c2-2 2-4 1-7s-2-6-4-8c1 0 1 1 2 1v1-1l-1-1c0-1-1-2-2-3v-1z" class="S"></path><path d="M98 240c1 2 1 3 0 5v1c0 2 0 4-1 7v2l-3 2c0-1 0-1-1-2 0 1-1 1-2 2v-1c2-3 5-4 5-8h0c0-3 2-5 2-8z" class="X"></path><path d="M93 255h0l3-3h1v1 2l-3 2c0-1 0-1-1-2z" class="E"></path><path d="M99 235v1c0 1 0 2 1 3v2c0 2 0 3 1 4l2 2h-1c0 2 1 3 2 5h0c1 0 2 1 2 1 1 0 2-1 3 1h0-5c-1-2-3-3-4-5 0-1-1-1-1-3h0-1v-1c1-2 1-3 0-5h0c1-1 1-3 1-5z" class="H"></path><path d="M125 239s1-1 1-2c-1-1-3-1-5-2-3-1-4-5-5-8 1 1 2 3 3 4 2 3 4 4 8 5h0v-1h1v-1c-1-1-2-1-3-2h0c1-1 1-1 2-1 1 1 2 1 3 2 3 2 8 2 11 2 1 0 2 1 3 1h0 2c3 0 6 2 8 3h-10c-4 0-8 1-12 1v-1h-1c-2 0-5 1-6 0z" class="F"></path><defs><linearGradient id="X" x1="133.005" y1="237.289" x2="134.519" y2="232.884" xlink:href="#B"><stop offset="0" stop-color="#2c1818"></stop><stop offset="1" stop-color="#191a1a"></stop></linearGradient></defs><path fill="url(#X)" d="M127 231c1 1 2 1 3 2 3 2 8 2 11 2 1 0 2 1 3 1h0 2c-5 1-9 1-13 1-2 0-4 0-6-1h0v-1h1v-1c-1-1-2-1-3-2h0c1-1 1-1 2-1z"></path><path d="M124 257c2 0 4-2 6-2 0 2-5 4-6 6-2 1-3 2-5 3 0 1 0 2-1 3s-2 4-4 4h-3l-7-1c-2 0-3-1-4-2l-1-1h0c-1 0-2 0-3-2h-1l1-1c0-1 0-2 1-3v1c1 1 3 1 4 2 5 0 10-2 15-4 1 0 2 0 3-1h1c1-1 2-1 3-2h1z" class="d"></path><path d="M96 264c0-1 0-2 1-3v1c1 1 3 1 4 2h0 3v1h-4-4-1l1-1z" class="L"></path><defs><linearGradient id="Y" x1="106.059" y1="266.136" x2="105.87" y2="264.374" xlink:href="#B"><stop offset="0" stop-color="#131513"></stop><stop offset="1" stop-color="#23191b"></stop></linearGradient></defs><path fill="url(#Y)" d="M114 262c1 0 2 1 2 1 1 0 2-2 3-1-1 1-2 2-4 2-1 0-2 1-2 2h-3l-7 1c-1 0-2 0-3 1l-1-1h0c-1 0-2 0-3-2h4c5 1 9 0 14-3z"></path><path d="M124 257c2 0 4-2 6-2 0 2-5 4-6 6-2 1-3 2-5 3s-4 1-6 2c0-1 1-2 2-2 2 0 3-1 4-2-1-1-2 1-3 1 0 0-1-1-2-1 2 0 5-2 5-3h1c1-1 2-1 3-2h1z" class="I"></path><path d="M113 266c2-1 4-1 6-2 0 1 0 2-1 3s-2 4-4 4h-3l-7-1c-2 0-3-1-4-2 1-1 2-1 3-1l7-1h3z" class="J"></path><path d="M103 267l7-1v1c0 1 2 1 3 1h-3c-2 0-5 1-7-1z" class="Q"></path><path d="M113 266c2-1 4-1 6-2 0 1 0 2-1 3-2-1-3 1-5 1-1 0-3 0-3-1v-1h3z" class="F"></path><path d="M110 208c3 0 4 0 7 1v1h-3v1h1c1 1 1 1 2 1v1 4 4l-1 1-7 1c-2 0-4-2-6-4-2 1-3 1-3 3h-1c0-2 1-3 1-6 0 0 0-1 1-1 0-1 0-2 1-3 1-2 2-2 4-3v1c1 0 2-1 3-2h1z" class="P"></path><path d="M109 208h1c1 1 1 1 1 3h0-6l1-1c1 0 2-1 3-2z" class="f"></path><path d="M115 211c1 1 1 1 2 1v1 4c0 1 0 2-1 3-1-3 0-6-2-8h1v-1z" class="e"></path><path d="M110 208c3 0 4 0 7 1v1h-3v1h1v1h-1l-3-1h0c0-2 0-2-1-3z" class="l"></path><path d="M103 219l-1-1c0-2 2-4 3-6 0 0 0 1 1 1 1 1 2 2 4 3 1 0 3 1 4 2 0 1 1 3 2 4l-7 1c-2 0-4-2-6-4z" class="N"></path><path d="M98 211l2 2v1c0 1 1 1 1 1-1 0-1 1-1 1 0 3-1 4-1 6 1 1 1 3 2 4s4 3 6 4c2 2 3 4 5 6 0 1 1 2 1 2v1c1 1 2 2 2 3l1 1v1-1c-1 0-1-1-2-1-4-3-7-4-11-5l-2 1v2h-1v1-2c-1-1-1-2-1-3v-1-3l-2-1 1-2h0l-1-1c-1-1-3-2-3-3s0-1-1-1c0-1-1-3-1-4s1-2 2-3h0c-1 0-2 0-2-1s1-1 2-2h1 1v-1c0-1 2-2 2-2z" class="q"></path><path d="M98 211l2 2v1c0 1 1 1 1 1-1 0-1 1-1 1-2-1-4-2-6-2h1 1v-1c0-1 2-2 2-2z" class="n"></path><path d="M99 232c2 2 3 3 4 5l-2 1v2h-1v1-2c-1-1-1-2-1-3v-1-3z" class="R"></path><path d="M99 236h1c1 1 1 1 1 2l-1 1c-1-1-1-2-1-3z" class="g"></path><path d="M100 239l1-1h0v2h-1v1-2z" class="a"></path><defs><linearGradient id="Z" x1="137.58" y1="237.825" x2="146.144" y2="255.142" xlink:href="#B"><stop offset="0" stop-color="#0f1111"></stop><stop offset="1" stop-color="#251819"></stop></linearGradient></defs><path fill="url(#Z)" d="M154 239h1 2v2c-2-1-4-1-6-1v1l7 6c1 1 2 3 3 5l-1 1-2-3-1 1h0v1h-1v1 1l-1-1h-1c-2-1-4-2-7-2-1 0-3 0-4 1h1c0 1 1 1 2 2l1 1v1c-4 0-6 0-9-1h-4c-1 0-1 0-2-1h0c-1 0-1 1-2 1-2 0-4 2-6 2 2-2 5-3 5-6v-1-1s0-1 1-1v-1h0l1-1s0-1 1-1h0v-1h0-2l1-2c1-1 0-2 1-2 4 0 8-1 12-1h10z"></path><path d="M148 246h3 0c-1 1-2 1-3 1s-1 1-2 1h0l-1 1v-1h1l-1-1c1 0 2-1 3-1z" class="B"></path><path d="M155 249c-1 0-1-1-2-1v-1h2l3 3-1 1-2-2z" class="u"></path><path d="M158 247c1 1 2 3 3 5l-1 1-2-3-3-3 2 1 1-1z" class="K"></path><path d="M155 249l2 2h0v1h-1v1 1l-1-1-2-2v-1l2-1z" class="a"></path><path d="M155 249l2 2h0l-1 1c-1-1-1-2-3-2l2-1z" class="p"></path><path d="M129 251c0 1 0 2 1 3 1 0 5-1 6-2 1 2 1 2 2 3h-4c-1 0-1 0-2-1h0c-1 0-1 1-2 1-2 0-4 2-6 2 2-2 5-3 5-6z" class="F"></path><path d="M136 252c3 0 5-1 7 0h1c0 1 1 1 2 2l1 1v1c-4 0-6 0-9-1-1-1-1-1-2-3z" class="W"></path><path d="M126 177h1c1 1 1 2 1 2 0 1 2 1 2 2l1-1 1 1c1 1 3 3 4 3h1l1 1v2c1 1 1 1 2 1 1-1 1-1 1-2l1-1v2l1 1h1 1v-2h1 0c0 1 0 1 1 2h0 1c0 1 0 2 1 2l1 2c2 4 6 6 9 8 2 0 3 0 5 2h1c1 2 1 3 2 4h1l1 1h1 1v-2h1v2l1 8c0 2 0 4 1 6 1 5-1 10-1 15l-1-2c-1 1-2 3-4 5h0c-1 1-1 2-1 3-1 1-2 3-3 4-1 0-1 1-1 2l-1-1v-6l-2 1c-1 0-2 0-3-1v-2h-2-1c-2-1-5-3-8-3h-2 0c-1 0-2-1-3-1-3 0-8 0-11-2-1-1-2-1-3-2-1 0-1 0-2 1v-2h0 0l-2 1c-1 0-1-1-1-1-2-3-2-6-3-9 0-3 0-5-2-8v-1c-1 0-1 0-2-1h-1v-1h3v-1c-3-1-4-1-7-1h-1-1c-3 0-7-5-8-7l-1-1c-1-3-1-6 0-9 0-1 0-1 1-2h0l-1-1c0-1 1-2 2-3 2-2 4-3 7-3v1l-1 1h1 3 7c1-1 2-1 4-1h0l1-1h1-1v-1c2-1 2-2 3-4z" class="C"></path><path d="M140 188c1-1 1-1 1-2l1-1v2 5c-1-2 0-2 0-4h-2 0z" class="H"></path><path d="M161 235c1 1 1 3 1 4h0v2l-2 1 1-1c0-2-1-2-1-3-1-1 0-2 0-3h1z" class="G"></path><path d="M161 241v-1-1h1 0v2l-2 1 1-1z" class="N"></path><path d="M152 221c1 0 0-1 1-1l1 1v-1c1 1 3 4 3 5 1 1 1 2 1 2h-1-1c0-2-3-4-4-6z" class="M"></path><path d="M157 225c2 3 4 7 4 10h-1c-1 0-1-1-2-2v-1c1-2-1-4-2-5h1 1s0-1-1-2z" class="N"></path><path d="M129 205l1-1v3l1 1h-2c-1 1-3 2-5 2l-1-1c1-2 3-3 5-4h1z" class="E"></path><path d="M129 205l1-1v3l1 1h-2v-3z" class="B"></path><path d="M128 205c2-2 5-6 8-7l-2 7v2l-1-1v2h-2l-1-1v-3l-1 1h-1z" class="Z"></path><path d="M130 207c1-2 1-3 2-4l1 1v2 2h-2l-1-1z" class="u"></path><path d="M142 187l1 1h1 1c0 1 1 2 0 3-1 3 0 6 0 9 0 2 0 3 1 4l-2 2c-1-5-1-10-2-14v-5zm-5 1c1 2 1 2 1 4-1 0-1 1-2 1-3 0-6 1-9 2-1 1-2 1-2 1h-2c0 1 0 1-1 1 0-1 0-2 1-2l2-2h0 2c1-2 3-2 4-3l2-1c1 0 3 0 4-1z" class="U"></path><path d="M147 233h1c2-1 4-3 6-5-2-3-4-5-5-8l1-1 6 8c-4 4-8 8-13 8h-2c-3 0-8 0-11-2v-1c1 0 1 1 2 1h1c2 1 3 1 5 1l-3-2c-1 0-2-1-3-1l-1-1h0c3 1 6 3 9 3h-1l1-2 2 1 2 1h3z" class="d"></path><path d="M142 232l2 1h3c-2 1-5 1-7 0h-1l1-2 2 1z" class="J"></path><path d="M126 177h1c1 1 1 2 1 2 0 1 2 1 2 2l1-1 1 1c1 1 3 3 4 3h1l1 1v2c1 1 1 1 2 1h0 0c-1 0-1 1-2 0v1c0 1 1 2 0 3 0 1-1 1-2 1 1 0 1-1 2-1 0-2 0-2-1-4-1 1-3 1-4 1l-2 1h-1-2-1-2c-1 0-2 1-3 1h0v-1-2l-1-1v-1-2l1-1h0l1-1h1-1v-1c2-1 2-2 3-4z" class="a"></path><path d="M123 182v1l1 1c-1 1-1 2-2 2h-1v-2l1-1h0l1-1z" class="B"></path><path d="M121 187h2l1 1 1 1v1c-1 0-2 1-3 1h0v-1-2l-1-1z" class="f"></path><path d="M132 185c1 1 1 1 2 1 0 1 0 0 1 1 0 0 1 0 1 1h1c-1 1-3 1-4 1l-2 1h-1-2-1c2-1 3-1 4-3l1-2z" class="B"></path><path d="M126 177h1c1 1 1 2 1 2 0 1 2 1 2 2l1-1 1 1c1 1 3 3 4 3h1l1 1v2c1 1 1 1 2 1h0 0c-1 0-1 1-2 0v1c0 1 1 2 0 3 0 1-1 1-2 1 1 0 1-1 2-1 0-2 0-2-1-4h-1c0-1-1-1-1-1-1-1-1 0-1-1-1 0-1 0-2-1-1 0-2-2-3-2-2 0-4 0-5 1l-1-1v-1h1-1v-1c2-1 2-2 3-4z" class="D"></path><path d="M131 180l1 1c1 1 3 3 4 3h1l1 1v2c-2-2-5-4-8-5v-1l1-1z" class="K"></path><path d="M126 177h1c1 1 1 2 1 2 0 1 2 1 2 2v1c-2-1-4-1-6 0h-1v-1c2-1 2-2 3-4z" class="j"></path><path d="M136 198l4-3c-1 4-2 7-2 10-1 3-1 7-1 10 0 5 1 13 5 17l-2-1-1 2h1c-3 0-6-2-9-3h0l1 1c1 0 2 1 3 1l3 2c-2 0-3 0-5-1h-1c-1 0-1-1-2-1v1c-1-1-2-1-3-2h0v-2c1-5 1-10-1-14 0-1-1-2-1-3l-1-1v-1c2 0 4-1 5-2h2 2v-2l1 1v-2l2-7z" class="N"></path><path d="M134 207v-2c0 4 0 9 1 13v1h-2-2v-2h1v-4-1h1 0c1-1 1-2 1-3v-2z" class="f"></path><path d="M132 212l1 1v6h-2v-2h1v-4-1z" class="j"></path><path d="M137 215c0 5 1 13 5 17l-2-1c-1 0-3-1-4-2h0l1-1c-1-2-1-5-2-7v-2-1l1 3c0 2 1 4 2 6v-1c0-1 0-2-1-3h0v-1-2c0-1 0-1-1-1 1-1 1-3 1-4z" class="E"></path><path d="M133 206l1 1v2c0 1 0 2-1 3h0-1v1 4h-1v-4l-3 1s-1 0-2 1c0-1-1-2-1-3l-1-1v-1c2 0 4-1 5-2h2 2v-2z" class="g"></path><path d="M125 212l4-1 1 1c-1 0-1 1-2 1l-1 1h1s-1 0-2 1c0-1-1-2-1-3z" class="f"></path><path d="M133 206l1 1v2c-1 0-4 1-5 2l-4 1-1-1v-1c2 0 4-1 5-2h2 2v-2z" class="c"></path><path d="M133 219h2v2c1 2 1 5 2 7l-1 1h0c1 1 3 2 4 2l-1 2h1c-3 0-6-2-9-3v-1-4h2v-1c-1 0-1 0-2-1v-4h2z" class="K"></path><path d="M136 229l-2-3h1c-1-1-1-2-2-3l2-2c1 2 1 5 2 7l-1 1z" class="P"></path><path d="M128 214l3-1v4 2 4c1 1 1 1 2 1v1h-2v4 1h0l1 1c1 0 2 1 3 1l3 2c-2 0-3 0-5-1h-1c-1 0-1-1-2-1v1c-1-1-2-1-3-2h0v-2c1-5 1-10-1-14 1-1 2-1 2-1z" class="G"></path><path d="M101 185c2-2 4-3 7-3v1l-1 1h1 3 7c1-1 2-1 4-1l-1 1v2 1l1 1v2 1h0c1 0 2-1 3-1h2 1 2 1c-1 1-3 1-4 3h-2 0l-2 2c-1 0-1 1-1 2 1 0 1 0 1-1h2c-3 2-5 4-7 5h-1c0 1 0 1-1 1v1s1 0 2 1h2c1 1 1 5 3 5l1 1v1l1 1c0 1 1 2 1 3 2 4 2 9 1 14v2h0c-1 0-1 0-2 1v-2h0 0l-2 1c-1 0-1-1-1-1-2-3-2-6-3-9 0-3 0-5-2-8v-1c-1 0-1 0-2-1h-1v-1h3v-1c-3-1-4-1-7-1h-1-1c-3 0-7-5-8-7l-1-1c-1-3-1-6 0-9 0-1 0-1 1-2h0l-1-1c0-1 1-2 2-3z" class="B"></path><path d="M103 200c1 0 3 1 4 1v3 1h-1c-2-2-3-3-3-5z" class="Q"></path><path d="M101 185c2-2 4-3 7-3v1l-1 1c-3 0-4 1-6 3l-1 2h0l-1-1c0-1 1-2 2-3z" class="e"></path><path d="M115 204h3 2c1 1 1 5 3 5l1 1v1h0c-2 0-3-2-4-2-1-1-3-2-4-3l1-1v-1h-2z" class="p"></path><path d="M107 201h1v1h2l-1 1c1 1 5 1 6 1h2v1l-1 1c-3 0-6 0-9-1v-1-3zm-5-3c0-2-1-5 0-8 0-2 2-3 4-4h0c-1 1-1 1-1 2h-1l1 1c0 1 0 2 1 2-1 1-2 2-2 4-1 1-1 2-2 3z" class="V"></path><path d="M109 189h3l-4 2c-3 2-3 4-4 8l4 1v1h-1c-1 0-3-1-4-1l-1-1v-1c1-1 1-2 2-3 0-2 1-3 2-4s2-1 3-2z" class="P"></path><path d="M106 185c2 1 3 1 5 1h3v2c-1 0-1 1-2 1h0-3c-1 1-2 1-3 2-1 0-1-1-1-2l-1-1h1c0-1 0-1 1-2h0v-1z" class="E"></path><path d="M105 189l2-2h1l1 1v1c-1 1-2 1-3 2-1 0-1-1-1-2z" class="F"></path><path d="M111 184h7c1-1 2-1 4-1l-1 1h-1c0 1 0 2-1 3s-3 1-4 1h-1v-2h-3c-2 0-3 0-5-1h3 0l-1-1h3z" class="H"></path><path d="M120 184c0 1 0 2-1 3s-3 1-4 1h-1v-2h-3c1-1 3-1 5-1 1 0 3 0 4-1z" class="e"></path><path d="M117 209c2 1 6 5 7 7 1 4 0 7-1 10 0 2 0 3-1 4-2-3-2-6-3-9 0-3 0-5-2-8v-1c-1 0-1 0-2-1h-1v-1h3v-1z" class="E"></path><path d="M121 215l1-1c0 1 1 1 1 2v2c0 1 0 1-1 1h-1v-4z" class="J"></path><path d="M117 209c2 1 6 5 7 7h-1c0-1-1-1-1-2l-1 1-1-1c-1-1-2-2-3-2s-1 0-2-1h-1v-1h3v-1z" class="f"></path><path d="M108 201v-1l-4-1c1-4 1-6 4-8l1 1 1 1c1 1 2 1 2 2s-1 1-1 1c-1 1-1 1-1 2 1 0 1 1 1 1 3 0 7-3 9-5h0c1 0 2 0 2-1h3 0l-2 2c-1 0-1 1-1 2 1 0 1 0 1-1h2c-3 2-5 4-7 5h-1c0 1 0 1-1 1v1s1 0 2 1h-3c-1 0-5 0-6-1l1-1h-2v-1z" class="d"></path><path d="M110 202c1 0 3 0 4-1 1 0 3-2 5-2l-2 1 1 1h-1c0 1 0 1-1 1v1s1 0 2 1h-3c-1 0-5 0-6-1l1-1z" class="O"></path><path d="M120 184h1v2 1l1 1v2 1h0c1 0 2-1 3-1h2 1 2 1c-1 1-3 1-4 3h-2-3c0 1-1 1-2 1h0c-2 2-6 5-9 5 0 0 0-1-1-1 0-1 0-1 1-2 0 0 1 0 1-1s-1-1-2-2l-1-1-1-1 4-2h0c1 0 1-1 2-1h1c1 0 3 0 4-1s1-2 1-3z" class="Q"></path><path d="M120 184h1v2 1l1 1v2 1h0-1v-2-1s-1-1-2-1c1-1 1-2 1-3z" class="j"></path><path d="M125 190h2 1 2 1c-1 1-3 1-4 3-3-1-6 0-9 0l1-1 2-1h1c1 0 2-1 3-1z" class="K"></path><path d="M112 195c1 0 4-1 5-1h0c-1 1-3 1-4 3h1c2-1 3-1 4-2s1-1 2-1c-2 2-6 5-9 5 0 0 0-1-1-1 0-1 0-1 1-2 0 0 1 0 1-1z" class="R"></path><path d="M145 188v-2h1 0c0 1 0 1 1 2h0 1c0 1 0 2 1 2l1 2c2 4 6 6 9 8 2 0 3 0 5 2h1c1 2 1 3 2 4h1l1 1h1 1v-2h1v2l1 8c0 2 0 4 1 6 1 5-1 10-1 15l-1-2c-1 1-2 3-4 5h0c-1 1-1 2-1 3-1 1-2 3-3 4-1 0-1 1-1 2l-1-1v-6-2h0c0-1 0-3-1-4 0-3-2-7-4-10 0-1-2-4-3-5v1l-1-1c-1 0 0 1-1 1l-3-2c-1-5-3-9-5-13l2-2c-1-1-1-2-1-4 0-3-1-6 0-9 1-1 0-2 0-3z" class="r"></path><path d="M163 236h0c1 4 0 7 1 10 1-3 2-9 4-10 1 1 0 2 0 3h0c-1 1-1 2-1 3-1 1-2 3-3 4-1 0-1 1-1 2l-1-1v-6-2 1h1v-4z" class="J"></path><path d="M147 206h0c1 1 1 2 2 2 3 3 6 5 8 9l2 4h-2c-1-1-2-1-2-3h0c0-2-4-6-6-7-1-2-2-3-2-5z" class="K"></path><path d="M170 207h1v-2h1v2l-1 1v3c-1 1-2 3-2 4s-1 3-2 4h-1c1-1 1-2 1-3l1-1 1-4h0c-1 0-2-1-3-1h0c2 0 2-2 3-3h1z" class="U"></path><path d="M169 207h1v4h-1 0c-1 0-2-1-3-1h0c2 0 2-2 3-3z" class="T"></path><path d="M159 200c2 0 3 0 5 2h1c1 2 1 3 2 4h1l1 1c-1 1-1 3-3 3h0s-1-1-1-2c-1-3-3-5-6-8z" class="R"></path><path d="M166 206h0c-2-1-2-1-2-3l1-1c1 2 1 3 2 4l-1-1v1z" class="Z"></path><path d="M166 206v-1l1 1h1l1 1c-1 1-1 3-3 3h0s-1-1-1-2h1v-2z" class="a"></path><path d="M169 215c0-1 1-3 2-4v-3l1-1 1 8c0 2 0 4 1 6-1 0-1-1-2-2v-4c-2 2 0 7 0 10 0 1-1 1-1 2-1 0-1-1-1-2v-5-1c0-1 0-3-1-4z" class="M"></path><path d="M154 220c-1-2-3-2-5-4h1l5 2c0 2 1 2 2 3h2c2 5 4 10 4 15v4h-1v-1h0c0-1 0-3-1-4 0-3-2-7-4-10 0-1-2-4-3-5z" class="k"></path><path d="M144 206l2-2c0 1 0 2 1 2 0 2 1 3 2 5 2 1 6 5 6 7h0l-5-2h-1c2 2 4 2 5 4v1l-1-1c-1 0 0 1-1 1l-3-2c-1-5-3-9-5-13z" class="q"></path><path d="M145 200c0-3-1-6 0-9 2 8 5 13 11 19-3-1-4-1-6-3l-1 1c-1 0-1-1-2-2h0c-1 0-1-1-1-2-1-1-1-2-1-4z" class="t"></path><path d="M145 200c1 3 3 5 5 7h0l-1 1c-1 0-1-1-2-2h0c-1 0-1-1-1-2-1-1-1-2-1-4z" class="l"></path><path d="M203 118l16-6s1 1 2 1c0 0 1 0 2-1v2l2 1c0 1-1 2-2 2h0c0 1-1 2-2 2h0c-2 1-3 2-3 3v1c-1 0-2 1-3 1v1 1h-1 0c-1 3-3 5-4 7v1l-1 1v2h2l2 1c1 1 1 1 3 1 0 1 0 0 1 1h1-1v1l-6 1h-1-1c-1 2-1 4-2 6v-2l-2 1v-2-3l-1-1h0-2c-1 1-1 2-1 3v7l-1 9h-1v-3c-1 3-1 7-3 9 0 1 0 1-1 0-1 2-1 4-2 6-1 3-10 18-10 20l-1 3h-1v1h-2c0 1-1 1-2 1v1-2-1l-1-3c0 1-1 0-1 0-4-2-6-1-9 0-2 0-6 0-7-1v2l-1 1c2 1 5 3 8 5l6 6h-1v2h-1-1l-1-1h-1c-1-1-1-2-2-4h-1c-2-2-3-2-5-2-3-2-7-4-9-8l-1-2c-1 0-1-1-1-2h-1 0c-1-1-1-1-1-2h0-1v2h-1-1l-1-1v-2l-1 1c0 1 0 1-1 2-1 0-1 0-2-1v-2l-1-1h-1c-1 0-3-2-4-3l-1-1-1 1c0-1-2-1-2-2 0 0 0-1-1-2h-1c-1 2-1 3-3 4v1h1-1l-1 1h0c-2 0-3 0-4 1h-7-3-1l1-1v-1h0c-2-2-4-4-5-7l1-2 1-1 1-1c0-1 1-2 2-3l6-8 2-1c1 0 3-1 4-1 2 0 3 0 4-1h1c5-3 9-7 12-11l3-4c0-2-1-3 0-5l1 1c0 1-1 2 0 3 1 0 1-1 2-2l2-2c0-1 1-2 2-2v1c0-1 1-1 1-1h1c0-2 1-3 2-5s4-3 4-5c1-1 1-1 0-2-1 0-3 1-4 2h-1 0c0-1 2-2 3-3 2-1 3-2 5-3 2-2 4-3 5-4 1 0 2-1 3-1v-1h1c0 1-1 2-1 2h0 1 2c2 1 7 0 9 0v1c1 0 1 0 2 1h2v2l3 1v-1h2l1 1h3c3 0 6 0 9-1 0 0 1-1 2-1h1z" class="c"></path><path d="M147 172l2-1 1 1s0 1 1 1c-2 0-3 1-4 1v-2z" class="q"></path><path d="M191 147c1 1 1 3 1 4h-1c0 1 0 2-1 3v-5l1-2z" class="L"></path><path d="M187 137c1 2 3 3 3 6-1 0-1-1-2-1v-1c0-1-1-1-2-2 0-1 0-1 1-2z" class="X"></path><path d="M186 176h0l-3 3c-2 1-4 1-6 1h-1v-1h6 0c2-1 3-2 4-3z" class="t"></path><path d="M155 142c1-2 1-3 3-4h0c1 1 0 3 0 4v1l-3-1z" class="G"></path><path d="M159 180h0v3 2l-2-1-1-1v-1c1-1 2-1 3-2z" class="Z"></path><path d="M154 169c2-1 4-3 7-3h0l-5 5v-2h0-2z" class="q"></path><path d="M165 148s0-1 1-1h0 1 1c-1 2-1 5-3 7v-1h-1v-1h0c1-1 1-2 1-4z" class="N"></path><path d="M164 152v1h1v1l-6 6-1-1c2-1 4-4 6-7z" class="L"></path><path d="M150 161h4l4-2 1 1c-1 0-1 1-2 1-3 1-5 1-7 2h-3v-1c1 0 2-1 3-1z" class="P"></path><path d="M180 126c2 1 5 2 7 4h0-1-1c2 1 2 1 3 2h0c-3-2-6-3-9-4l1-2z" class="U"></path><path d="M138 175v-3-1c1 1 1 1 1 2 1 1 1 3 1 4 1 1 2 3 2 4l-1-1-1 1h0l-2-6z" class="G"></path><path d="M190 149c-1-4-1-5-4-7-1-1-2-1-3-2 2 0 3 1 5 2 1 0 1 1 2 1 0 1 1 3 1 4l-1 2z" class="N"></path><path d="M125 157l-1 1h1c-3 1-8 3-11 2l2-1c1 0 3-1 4-1 2 0 3 0 4-1h1z" class="V"></path><path d="M162 168h0 1v13c-1-1-1-2-2-3 0-3-1-8 1-10z" class="F"></path><path d="M185 164c1 0 1 0 2 1-1 0-1 0-1 1h1-1c-1 1-3 2-4 2-1-1-2-1-2-2s4-2 5-2z" class="o"></path><path d="M153 191s-1-1-1-2h4c1 0 2 1 3 2v2l-1 1c-1-1-3-3-4-3h-1z" class="a"></path><path d="M140 181l1-1 1 1c2 2 3 3 4 5h-1v2h-1-1l-1-1v-2l-1-2-1-2z" class="S"></path><path d="M180 157c-1-2-2-3-2-5-1-3-1-6 0-8 1-1 2-2 2-3v3c-2 5-1 9 1 13h-1z" class="X"></path><path d="M170 153l2 2c0 2 1 4 3 5 1 0 7 1 7 2h-1c-2 0-6 0-8-2-2-1-3-5-3-7z" class="Q"></path><path d="M147 172c1-3 1-5 3-6h6c-3 1-5 2-6 6l-1-1-2 1z" class="L"></path><path d="M175 192l1-1h-1c0-2 0-3 1-4h0c2 2 3 4 4 7 1 0 1 0 2 1h-1v1h-2c0 1-1 1-2 1v1-2-1l-1-3c0 1-1 0-1 0z" class="R"></path><path d="M176 192h1c1 1 2 2 3 2s1 0 2 1h-1v1h-2c0 1-1 1-2 1v1-2-1l-1-3z" class="I"></path><path d="M177 195c1 0 2 1 3 1 0 0 1 0 1-1v1h-2c0 1-1 1-2 1v1-2-1z" class="H"></path><path d="M153 191h1c1 0 3 2 4 3 2 1 5 3 8 5l6 6h-1v2h-1-1l-1-1c-1-1-1-3-2-4-1-2-3-4-5-5-3-2-5-4-8-6z" class="G"></path><path d="M129 161l-2-1v-1s1-1 1-2h3c0-1 0-1 1-1v1c1 2 2 4 4 5 0 1 1 2 2 3h0-1-4l1-1h0l-5-3z" class="e"></path><path d="M169 135c4-1 10-3 13-2 1 1 4 2 5 4-1 1-1 1-1 2-1 0-3-1-4-1s-3 1-4 2v-1l-1-1c1-1 4-2 5-3-2-1-4 0-6 0l-5 2 2-2h0-4z" class="a"></path><path d="M172 151l2 2 4 4h1 1 1c1 1 2 3 2 4l-1 1c0-1-6-2-7-2-2-1-3-3-3-5l-2-2c0-1 0 0 1-1 0 1 1 2 1 2 1 0 1 0 1 1h2 0v-1c-1 0-2 0-3-1v-2z" class="B"></path><path d="M172 155c2 0 4 1 5 3 1 0 1 0 1 1-1 0-2 0-3 1-2-1-3-3-3-5z" class="K"></path><path d="M154 169h2 0v2c-4 4-8 8-8 14 0 2 1 3 1 5-1 0-1-1-1-2h-1 0c-1-1-1-1-1-2h1v-12c1 0 2-1 4-1 1-1 2-2 3-4z" class="h"></path><path d="M177 138l1 1v1c-2 2-3 6-3 10 1 3 2 5 4 7h-1l-4-4-2-2-1-1c0-1-1-1 0-2 0-4 3-7 6-10z" class="l"></path><path d="M171 150c1 0 2-1 3-1v4l-2-2-1-1z" class="X"></path><path d="M140 137l1 1c0 1-1 2 0 3 1 0 1-1 2-2l1 1v1l-2 2-2 2c-2 5-6 8-10 11-1 0-2 1-3 1l-2 1h-1l1-1c5-3 9-7 12-11l3-4c0-2-1-3 0-5z" class="N"></path><path d="M145 137c0-1 1-2 2-2v1c0-1 1-1 1-1h1c-1 3-2 5-2 8h0v1c1 1 1 2 1 3h1l-4 2h0c-2 1-3 3-5 4 0-2 2-5 3-8 0-1 0-3 1-4h0v-1l-1-1 2-2z" class="E"></path><path d="M146 144l1-1h0c0 2 1 3 0 4l-1-1v-2z" class="J"></path><path d="M145 137c0-1 1-2 2-2v1c0-1 1-1 1-1h1c-1 3-2 5-2 8l-1 1c-1-1 0-3 0-5h-1c1-1 1-1 0-2z" class="P"></path><path d="M169 135h4 0l-2 2 5-2h0c-1 1-1 1-2 1-2 1-4 3-6 4 0 0 1 0 1 1 0 0 0 3-1 4v-4h-1c-1 0-2 1-3 2-1 3-2 5-4 7h-1l-4 4h0c2-4 2-7 4-11 3-4 7-6 10-8z" class="N"></path><path d="M171 137l5-2h0c-1 1-1 1-2 1-2 1-4 3-6 4 0 0 1 0 1 1 0 0 0 3-1 4v-4h-1c-1 0-2 1-3 2-1 3-2 5-4 7h-1c1-2 2-5 3-7 2-2 6-4 9-6z" class="O"></path><defs><linearGradient id="a" x1="191.171" y1="163.019" x2="183.893" y2="162.111" xlink:href="#B"><stop offset="0" stop-color="#d30702"></stop><stop offset="1" stop-color="#ae191f"></stop></linearGradient></defs><path fill="url(#a)" d="M190 154c1-1 1-2 1-3h1c1 7 1 14-3 20-4 5-12 7-19 8 5-2 10-4 14-8 1-1 3-3 3-4l1-1h-1 0-1c0-1 0-1 1-1-1-1-1-1-2-1 4-3 4-6 5-10z"></path><path d="M128 168v-1c2-1 3-1 4-1l-1 1 1 1c1 1 2 2 2 3 1 1 2 4 4 5v-1l2 6h0l1 2 1 2-1 1c0 1 0 1-1 2-1 0-1 0-2-1v-2l-1-1h-1c-1 0-3-2-4-3 0-5-1-9-4-13z" class="a"></path><path d="M138 185h2c0-1 0-1 1-1v-1l1 2-1 1c0 1 0 1-1 2-1 0-1 0-2-1v-2z" class="R"></path><path d="M128 168l1-1c2 1 2 3 4 4 2 4 3 9 4 13h-1c-1 0-3-2-4-3 0-5-1-9-4-13z" class="I"></path><defs><linearGradient id="b" x1="197.87" y1="147.603" x2="185.404" y2="147.614" xlink:href="#B"><stop offset="0" stop-color="#d00a08"></stop><stop offset="1" stop-color="#9e212c"></stop></linearGradient></defs><path fill="url(#b)" d="M187 130a19.81 19.81 0 0 1 11 11c2 5 1 11 1 16-1 3-1 7-3 9 0 1 0 1-1 0l1-8c0-10-1-18-8-26h0c-1-1-1-1-3-2h1 1 0z"></path><path d="M129 161l5 3h0l-1 1h4 1 0l4 4-10-3c-1 0-2 0-4 1v1c3 4 4 8 4 13l-1-1-1 1c0-1-2-1-2-2 0 0 0-1-1-2h-1c0-2-1-4 0-7-3-2-6-4-9-3h-1c-4 0-7 2-10 4 0-1 1-2 2-3 2 0 4-2 5-2 3-1 6 0 9-2l1-1c2-1 3-1 5-1l1-1z" class="L"></path><path d="M126 170c2 3 3 7 5 10l-1 1c0-1-2-1-2-2 0 0 0-1-1-2h-1c0-2-1-4 0-7z" class="J"></path><path d="M129 161l5 3h0l-1 1c-4-1-7-1-11-1l1-1c2-1 3-1 5-1l1-1z" class="P"></path><path d="M150 125c0-1 2-2 3-3 2-1 3-2 5-3 2-2 4-3 5-4 1 0 2-1 3-1v-1h1c0 1-1 2-1 2h0 1c0 1 0 1 1 2h0c-4 3-9 6-12 9h6c6-1 12-2 18 0l-1 2c-5-2-9-2-14-1s-11 3-14 7c0 2-1 4 0 6 0 1 1 2 2 3l2-1 3 1c-1 1-2 3-3 4l-2 2-4-2h-1c0-1 0-2-1-3v-1h0c0-3 1-5 2-8 0-2 1-3 2-5s4-3 4-5c1-1 1-1 0-2-1 0-3 1-4 2h-1 0z" class="T"></path><path d="M155 142l3 1c-1 1-2 3-3 4v-2h1v-1l-1 1-2-2 2-1z" class="L"></path><path d="M162 126c6-1 12-2 18 0l-1 2c-5-2-9-2-14-1 1-1 1-1 2-1h1 5 0-11z" class="N"></path><path d="M160 150c2-2 3-4 4-7 1-1 2-2 3-2h1v4 2h-1-1 0c-1 0-1 1-1 1 0 2 0 3-1 4h0c-2 3-4 6-6 7l-4 2h-4s1 0 1-1c-2 0-5 1-7 1l-3-3c-1-2-1-3-1-5 2-1 3-3 5-4h0l4-2 4 2 2-2c1-1 2-3 3-4v-1l1 1c-2 4-2 7-4 11h0l4-4h1z" class="B"></path><path d="M143 154l-1-1c2 0 4 0 5 1 1 0 1 0 1 1v3h-1c-1 0-1 1-2 1-1-1-1-1-1-2 0 0-1-1-2-1h0v-2h1z" class="F"></path><path d="M143 154c1 0 2 1 3 2h0c-1 1-1 1-2 1 0 0-1-1-2-1h0v-2h1z" class="c"></path><path d="M155 154h0l4-4h1l-2 3c-1 2-2 2-2 4-3 2-7 3-11 4l3-3c4-1 4-2 7-4z" class="V"></path><path d="M160 150c2-2 3-4 4-7 1-1 2-2 3-2h1v4 2h-1-1 0c-1 0-1 1-1 1-2 4-4 6-9 9 0-2 1-2 2-4l2-3z" class="S"></path><path d="M106 171c3-2 6-4 10-4h1c3-1 6 1 9 3-1 3 0 5 0 7-1 2-1 3-3 4v1h1-1l-1 1h0c-2 0-3 0-4 1h-7-3-1l1-1v-1h0c-2-2-4-4-5-7l1-2 1-1 1-1z" class="C"></path><path d="M104 173l1-1c2 2 4 4 7 6 1 1 2 1 3 2l4-2h1c1-1 1-2 1-4l1-1c0-1 0 0 0 0 1 1 2 1 2 3s-1 4-2 5h1v1h1-1l-1 1h0c-2 0-3 0-4 1h-7-3-1l1-1v-1h0c-2-2-4-4-5-7l1-2z" class="X"></path><path d="M115 180l4-2c0 1-1 2-2 3h-1l-1-1z" class="u"></path><path d="M104 173l1-1c2 2 4 4 7 6l-1 1h-1c-2-1-5-4-6-6z" class="G"></path><path d="M111 184c2-1 3-1 4-1l1-1h1c2 0 3-1 5-1h1v1h1-1l-1 1h0c-2 0-3 0-4 1h-7z" class="g"></path><path d="M203 118l16-6s1 1 2 1c0 0 1 0 2-1v2l2 1c0 1-1 2-2 2h0c0 1-1 2-2 2h0c-2 1-3 2-3 3v1c-1 0-2 1-3 1v1 1h-1 0c-1 3-3 5-4 7v1l-1 1v2h2l2 1c1 1 1 1 3 1 0 1 0 0 1 1h1-1v1l-6 1h-1-1c-1 2-1 4-2 6v-2l-2 1v-2-3l-1-1h0-2c-1 1-1 2-1 3v7l-1 9h-1v-3c0-5 1-11-1-16a19.81 19.81 0 0 0-11-11c-2-2-5-3-7-4-6-2-12-1-18 0h-6c3-3 8-6 12-9h0c-1-1-1-1-1-2h2c2 1 7 0 9 0v1c1 0 1 0 2 1h2v2l3 1v-1h2l1 1h3c3 0 6 0 9-1 0 0 1-1 2-1h1z" class="c"></path><path d="M194 128l1-1 3 1 1 1h-1c-1 0-3 0-4-1z" class="H"></path><path d="M217 140h1-1v1l-6 1h-1-1c0-1 1-1 1-1 3 0 4-1 7-1z" class="E"></path><path d="M209 135v-1-4h1c0-1 0-2 1-2 1-1 2-2 3-2-1 3-3 5-4 7v1l-1 1z" class="Z"></path><path d="M201 151c-1-5 0-9-1-14h0l1-1c1 1 0 2 1 3h0 2s1 0 1-1v-1c0-1 0-4 1-5h0l1 14-2 1v-2-3l-1-1h0-2c-1 1-1 2-1 3v7z" class="t"></path><path d="M169 115c2 1 7 0 9 0v1c1 0 1 0 2 1h2v2c-1 0-2 0-4-1 0 1 0 1 1 2-1-1-2-1-3-1-3-2-5-2-8-2h0c-1-1-1-1-1-2h2z" class="G"></path><path d="M223 114l2 1c0 1-1 2-2 2h0c0 1-1 2-2 2h0c-2 1-3 2-3 3v1c-1 0-2 1-3 1-4 2-8 4-12 5l4-3 1-2c-1 0-1-1-1-2 3 0 6-1 8-2 0-1 4-2 5-3 1 0 1-1 2-2l1-1z" class="f"></path><path d="M215 120v1l-8 5 1-2c-1 0-1-1-1-2 3 0 6-1 8-2z" class="I"></path><path d="M179 120c-1-1-1-1-1-2 2 1 3 1 4 1l3 1 4 2h2 8c1 1 2 1 3 1l-3 1v1l-1 1h-1-4 0c0 1 2 1 2 1l-1 1c-2-1-4-1-6-3 0 0-1 0-2-1h0-1l-6-2c-1 0-2-1-3-1 1-2 2 0 3-1h0z" class="B"></path><path d="M179 120c-1-1-1-1-1-2 2 1 3 1 4 1l3 1 4 2v1h-1l-9-3h0z" class="W"></path><path d="M189 122h2 8c1 1 2 1 3 1l-3 1v1l-1 1h-1c-1 0-2 0-3-1-1 0-2-1-3-1 0 0-2-1-3-1h1v-1z" class="F"></path><path d="M189 122h2l3 1v1h-3s-2-1-3-1h1v-1z" class="M"></path><path d="M194 124h5v1l-1 1h-1c-1 0-2 0-3-1-1 0-2-1-3-1h3z" class="P"></path><path d="M203 118l16-6s1 1 2 1c0 0 1 0 2-1v2l-1 1c-1 1-1 2-2 2-1 1-5 2-5 3-2 1-5 2-8 2-1-1-4 0-5 1-1 0-2 0-3-1h-8-2l-4-2v-1h2l1 1h3c3 0 6 0 9-1 0 0 1-1 2-1h1z" class="Z"></path><path d="M203 118l16-6s1 1 2 1c0 0 1 0 2-1v2l-1 1c-1 1-1 2-2 2h-1v-1h0l1-1 1-1h-1c-5 1-10 3-16 4l-1 1c-1-1-1 0 0-1z" class="q"></path><path d="M199 122c4-2 8-3 13-4 2-1 5-2 8-3l-1 1h0v1h1c-1 1-5 2-5 3-2 1-5 2-8 2-1-1-4 0-5 1-1 0-2 0-3-1z" class="U"></path><path d="M467 132l2-1-2-2h0c1 1 2 1 3 2l1 1c1 0 1 1 2 2l2 2c2 1 3 3 5 4 1 1 12 12 13 14 2 1 3 2 4 4l2 2c0 1 1 1 2 2 0 1 1 2 2 3 0 0 1 0 1 1l2 3 2 2 4 6c1 2 3 4 4 6 0 0 0 1 1 1 0 0 0 1 1 2s1 2 1 3c0 2 3 6 5 8l4 10c2 5 4 10 5 15 0 1 1 4 1 5l2 17c0 1-1 5-1 6h1v6 8c-1 5-2 9-2 13l-2 11 1 1v3h0-1v1c-1 3-2 6-3 10l-1 1c0 2-2 5-3 7l-2 5h-1-1s-1 1-1 2c-1 1-3 2-4 3 0 1 0 1-1 2v1c-3 2-5 4-7 7-1 1-2 2-3 4-1 1-1 2-2 4h3l-3 3v1l1 1h0v1h1l-1 1-1 1 1 1c2 0 2-1 3 0l2 2h1l2 3v1c4 0 5 2 9 4l2 2h-1v1c5 5 11 12 14 18l2 5c-2-1-3-5-5-7s-5-4-8-3c-1 0-3 0-4 1l-2 1c-1 1-2 3-2 4l-1 2v4 1l-1 2c0-1 0-1-1-2-1 0-3 1-3 3s1 3 1 5c0 1-2 4-3 5-1 0-2 1-3 1l-1-1c-2 1-5 2-7 2h-4c-2 1-2 1-3 1s-1-1-1-1v1l1 1h1l4 1v1h-1c0 1 1 2 1 2 0 1-1 1 0 2l-1 1c-1 0-1-1-2-1-2-1-3 0-5 0-1 1-1 2-2 2v-1c-1 2-2 4-2 6s0 5-1 7h0l1 1v1c-2 0-4-1-5 0 0 1 0 1-1 1-2 5-7 6-11 8l-10 6c0 1 0 1 1 2 3-1 6-3 9-5l1 1-1 1v1l-1 2v2h1c-1 1-4 3-4 3-5 3-9 6-14 6l-1-2c-2 0-3 0-5-2-1-1-3-4-3-6-1-5 3-9 7-11 1 0 2-1 3-1h-1c-2-1-7 2-8 4-4 3-5 6-5 11-1-1 0-1-1-1h0v-2l-3-201c0-2 0-4 1-5l1 5c0-3 1-6 1-9v-4-7s1-3 1-4v-1l-1 1h0-1l1-6h1v-2h1 0s1-1 1-2c1 0 1 1 2 1h0c2-1 2-4 2-5 2-5 6-6 10-8 0-1 1-1 2-2 4-3 5-7 5-12h1c0 2 0 4-1 6 0 0 0 1 1 2v1l2-2v3h1c0-3 0-4 2-7l1 1 1-2s1-2 2-2c0-1 1-2 1-3 1 0 1-1 2-1 0 0 0 1 1 1v2-1c1 0 2-1 4-1h0l1-2h1 3v-1c0-1 0-3-1-4 0-1 1 0 0-1 1-2 2-3 3-3 1-1 1-2 1-2 2-1 4-1 7 0 1 0 6 3 7 3 0 0 0-1 1-1v-1c-1-1-2-3-4-4-1 0-3-2-5-3h-2-1c0-2-3-3-4-4-1 0-1 0-2 1-1 0-4-3-6-3-3-3-5-6-7-10l1-1v-1c-1-1-2-3-2-4v-2h0l1-1c0-2-1-3-2-4z" class="C"></path><path d="M480 314l1 1c1 1 2 0 2 2h-4v-1c0-1 1-2 1-2z" class="f"></path><path d="M482 319c1 0 2-1 3-1 1 1 1 2 1 3-1 1-1 1-2 1 0-1 0-1-1-2v1l-1-2z" class="a"></path><path d="M467 307c2 1 3 3 4 5l-1-1c-1 0-2-1-3-3h0-1 0l1-1z" class="F"></path><path d="M497 335h2c1 0 1-1 2 0h1v1h-3 0c-1 1-1 0-2 0v-1z" class="X"></path><path d="M437 319h2v4 1h-1l-1-5z" class="T"></path><path d="M504 311c2-1 2-1 5 0-2 1-3 2-5 3v-3z" class="F"></path><path d="M522 293l1-2c1 1 1 1 1 2l-1 2h0l-1 1c-1 0-1-1-2-1h0l2-2z" class="o"></path><path d="M520 295l2-1 1 1-1 1c-1 0-1-1-2-1h0z" class="M"></path><path d="M504 325v3l1 1c0 1-3 3-4 4 1-3 1-5 3-8zm-2-13l2-1v3l-4 3h-2l1-1 2-2c1 0 1 0 1-2z" class="V"></path><path d="M440 428h1c2 0 4-1 5 0l-9 3v-1h0c1-1 2-1 3-2h0z" class="U"></path><path d="M501 313l1-1c0 2 0 2-1 2l-2 2-3 2-1-1s1 0 1-1c1 0 2-1 3-1l-1-1v-1h3z" class="I"></path><path d="M482 295c3-1 5-2 8-2h3c1 0 1 1 2 1-4 0-9 0-12 2l-1-1z" class="L"></path><path d="M505 306l5-2c0 1-1 2-2 3l-3 2s-1-1-2-1l1-1h-1c0-1 1-1 2-1z" class="e"></path><path d="M511 324h1c-2 2-4 4-7 5l-1-1v-3s1 0 1-1l1 1v2h0l5-3z" class="N"></path><path d="M499 336h0 3c-1 1-1 2-1 3l-1 1-2-1-2-1c0-1 0-1 1-2 1 0 1 1 2 0z" class="j"></path><path d="M497 336c1 0 1 1 2 0v2l-1 1-2-1c0-1 0-1 1-2z" class="v"></path><path d="M490 330l1-1 6 6v1c-1 1-1 1-1 2l-2-1c1 0 1-1 1-1v-2h-1c-1-2-2-3-4-4z" class="e"></path><path d="M498 314l1 1c-1 0-2 1-3 1-3 1-6 2-9 2h0v-1c4 0 7-2 11-3z" class="E"></path><path d="M445 446c-1-1-1-3-2-4 1-2 2-4 4-5l1 1c-1 1-3 2-2 4 0 1 2 2 2 3-1 1-1 1-2 1h-1z" class="h"></path><path d="M508 307v1h1v1 1 1c-3-1-3-1-5 0l-2 1-1 1-1-1c2-1 3-2 4-2 0 0 0-1 1-1l3-2z" class="M"></path><path d="M437 385l1 1c1 1 2 1 2 2-1 1-2 3-3 4v3 9 2-6-15z" class="T"></path><path d="M494 308v-1s-3-1-4-1c-2-2-5-4-7-6h1c4 4 8 6 14 6-1 0-2 1-2 1h0v1h1c-1 1-2 1-3 0h0z" class="n"></path><path d="M447 295h1c-1 2-2 4-2 6 1 2 2 2 3 3h0v1h-1-1-1c-1-1-2-3-2-4 0-2 2-5 3-6z" class="h"></path><path d="M498 306h7c-1 0-2 0-2 1h1l-1 1-4 1c-2 0-5 0-7-1h2 0c1 1 2 1 3 0h-1v-1h0s1-1 2-1z" class="Y"></path><path d="M459 303v-1c-1-1-2-1-3-2h0l2-1c1-1 1 0 3 1l1 2 3 2c-2 0-3-1-5-1h-1z" class="V"></path><path d="M461 300l1 2h0c-1 0-1 0-2-1l1-1z" class="F"></path><path d="M439 323c1 4 1 8 1 12 0 1-1 2-2 3v-14h1v-1z" class="G"></path><path d="M483 321l1 1c-1 1-1 2-1 3-1 2-1 2-2 3 0-1-1-1-1-2 0-2 0-6 1-7h1l1 2z" class="X"></path><path d="M487 314v-2h1c4 1 8 0 12 0l1 1h-3l-8 1c-1 1-2 1-3 1v-1z" class="E"></path><path d="M437 319v-2h0 1c2 2 6 3 9 2l1 1h1l-1 1c-1 0-3 0-4 1h-1c-1-1-3-2-4-3h-2z" class="h"></path><path d="M496 318l3-2-1 1h2l-9 7c-1 0-2 1-3 2v-2-1h1l7-5z" class="a"></path><path d="M522 293c1-3 3-10 5-11s3-3 5-4h1c-1 3-3 4-5 5l-1 1-3 9c0-1 0-1-1-2l-1 2z" class="L"></path><path d="M435 299c2 2 1 4 2 7v1-1l1-1 1 1c0 2 0 2 1 3 1 2 3 3 5 4 1 0 1 0 2 1h-1c-3-1-6-2-8-4-3-3-3-7-3-11z" class="AX"></path><path d="M437 409l1 3c0 2-1 4 0 5h1v11h1 0c-1 1-2 1-3 2h0v-2-19z" class="Q"></path><path d="M437 428s0-1 1-1l1 1h1c-1 1-2 1-3 2h0v-2z" class="N"></path><path d="M487 318c3 0 6-1 9-2 0 1-1 1-1 1l1 1-7 5h-1c-1-1-1-3-1-4v-1z" class="P"></path><path d="M487 319c1 0 2 1 3 1v1h-2c0 1 1 2 1 2h-1c-1-1-1-3-1-4z" class="Z"></path><path d="M466 308c-4-3-9-2-14-2 2-1 4-3 6-3h1 1c2 0 3 1 5 1l2 3-1 1z" class="Q"></path><path d="M481 311c1-1 1-2 3-3l1 1v1l1 1v3h1v1h-1v1c-1 1-2 1-3 1 0-2-1-1-2-2l1-1c-1-1 0-1-1-3z" class="H"></path><path d="M486 314l-1 2h0l-2-3c0-1 0-2 1-3h1l1 1v3z" class="f"></path><path d="M490 330c2 1 3 2 4 4h1v2s0 1-1 1l-8-3h0l2-2 2-2z" class="c"></path><path d="M446 314h1c-1-1-1-1-2-1-2-1-4-2-5-4-1-1-1-1-1-3l5 5h1c1 0 1 1 2 1h1l3 1c2 1 5 1 8 1-5 1-9 2-13 0z" class="E"></path><path d="M461 448l5-5v2h1c-1 1-4 3-4 3-5 3-9 6-14 6l-1-2 3 1 1-1c-2-1-3-1-4-2-2-1-2-2-3-4h1c0 1 0 1 1 2 2 2 5 3 7 3 3 0 5-1 7-3z" class="G"></path><path d="M462 285h1v5c1 3 4 6 6 9 1 0 2 1 2 2v1l-6-6v1c1 1 2 2 3 4v1c-1-2-2-4-3-5-2-2-5-2-6-3l1-1c1 0 3 1 5 2-2-2-3-6-5-7v-1c0-1 1 0 2-2z" class="a"></path><path d="M485 331c1 0 2-1 2-2l-1-1-2-6c0 1 1 1 1 2 1 1 2 3 4 4l24-18c-4 5-10 8-15 12-3 2-5 4-7 7l-1 1-2 2v-1-1h0c-1 1-2 1-3 1h0z" class="G"></path><path d="M437 404h2c1 1 2 3 2 4 1 0 1 1 1 2 1 0 1 1 1 1 0 1 1 1 1 2v1 1l-3 1c0-2 0-3-1-4l-1 5h-1c-1-1 0-3 0-5l-1-3v-3-2z" class="q"></path><path d="M438 412v2h1 0v-2h1l-1 5h-1c-1-1 0-3 0-5z" class="F"></path><path d="M510 304c4-2 7-5 10-9 1 0 1 1 2 1-3 6-7 10-13 14v-1-1h-1v-1c1-1 2-2 2-3z" class="T"></path><path d="M457 441c0 1 0 1 1 2 3-1 6-3 9-5l1 1-1 1v1l-1 2-5 5v-2h1-4c-3 0-5-1-7-1 2-1 4-2 5-3l-1-1h2z" class="V"></path><path d="M467 440v1l-1 2-5 5v-2h1-4c4-1 6-3 9-6z" class="D"></path><path d="M471 301l6 8c1 2 2 3 3 5 0 0-1 1-1 2v1c-6-3-8-7-10-13h0 1c0 1 1 2 3 3 0-1-2-4-2-5v-1zm-4 13h0c-2-3-8-3-11-5 3-1 6-2 10-1h1 0c1 2 2 3 3 3l1 1c1 1 2 1 3 2 0 1 1 1 2 2-1 0-1-1-2-1v4 1l-1-1c-2 0-4-3-6-5z" class="N"></path><path d="M474 314c0 1 1 1 2 2-1 0-1-1-2-1v4 1l-1-1v-1c-1-1-2-1-3-2v-1c1 0 2 0 3 1 0-1 0-1 1-2z" class="d"></path><path d="M485 305c1 1 3 2 5 3h0 2c2 1 5 1 7 1l4-1c1 0 2 1 2 1-1 0-1 1-1 1-1 0-2 1-4 2-4 0-8 1-12 0h-1v2h-1v-3l-1-1v-1l-1-1c-1 0-1-1-1-1 0-1 1-1 2-2z" class="b"></path><path d="M503 308c1 0 2 1 2 1-1 0-1 1-1 1-1-1-5 0-6 1-1-1-1-1-2-1 1-1 2-1 3-1l4-1z" class="d"></path><path d="M490 308h2c2 1 5 1 7 1-1 0-2 0-3 1 1 0 1 0 2 1h-9c1-1 1-2 1-3z" class="G"></path><path d="M485 305c1 1 3 2 5 3h0c0 1 0 2-1 3-1-1-3-1-4-2l-1-1c-1 0-1-1-1-1 0-1 1-1 2-2z" class="X"></path><path d="M439 323v-4c1 1 3 2 4 3h1c1-1 3-1 4-1l1-1h0 5c-2 1-5 3-5 5v1c-1 1-3 2-4 3l-5 6c0-4 0-8-1-12z" class="C"></path><path d="M449 320h0 5c-2 1-5 3-5 5v1c-1 1-3 2-4 3 1-2 2-5 4-7v-1h-1l1-1z" class="G"></path><path d="M514 321l-8 3c6-4 13-7 16-13 1-2 2-5 3-8l7-15 1 1v3h0-1v1c-1 3-2 6-3 10l-1 1c0 2-2 5-3 7l-2 5h-1-1s-1 1-1 2c-1 1-3 2-4 3 0 1 0 1-1 2-1 0-1 0-2 1h0-1-1s2-2 3-2v-1z" class="j"></path><path d="M528 297h1 0c0-2 1-2 2-3 0-1 0-2 1-3v-1h1v2h0-1v1c-1 3-2 6-3 10-1-2 1-4-1-6z" class="v"></path><path d="M528 297c2 2 0 4 1 6l-1 1c0 2-2 5-3 7h-2l5-14z" class="P"></path><path d="M523 311h2l-2 5h-1-1s-1 1-1 2c-1 1-3 2-4 3 0 1 0 1-1 2-1 0-1 0-2 1h0-1-1s2-2 3-2v-1c1-1 2-1 3-3h2c2-2 3-5 4-7z" class="K"></path><path d="M447 319c4-1 8-3 12-4 2 0 5-1 8-1 2 2 4 5 6 5l1 1v-1-4c1 0 1 1 2 1 1 1 2 2 2 3l-1 9c0 2 0 5-1 6l-1 1v-1c1-1 1-2 1-4h-1c0-3-2-7-4-9-3-3-7-3-11-3-2 1-4 1-6 2h-5 0-1l-1-1z" class="U"></path><defs><linearGradient id="c" x1="468.341" y1="435.33" x2="463.878" y2="429.996" xlink:href="#B"><stop offset="0" stop-color="#ca070b"></stop><stop offset="1" stop-color="#a71d24"></stop></linearGradient></defs><path fill="url(#c)" d="M477 420l1-1v1c0 1 1 1 1 1l1 1c1 1 2 2 3 2l1 1v1c-2 0-4-1-5 0 0 1 0 1-1 1-2 5-7 6-11 8l-10 6h-2c-1-1-3-1-4-2 3-1 7-2 9-3l1-1c-1 0-2 0-3-1h1l3-1c2 0 3-1 5-2 6-3 8-5 10-11z"></path><path d="M478 420c0 1 1 1 1 1l1 1c1 1 2 2 3 2l1 1v1c-2 0-4-1-5 0 0 1 0 1-1 1v-7z" class="a"></path><path d="M452 282h1 2l1-1v1c0 1 0 1-1 1s-2 1-3 2h-1l1 1c2-1 4-2 6-1h1 2 1c-1 2-2 1-2 2v1c2 1 3 5 5 7-2-1-4-2-5-2l-1 1c-2-1-6-1-8-2l-2 1s0-1-1-1l-2-1h-3v-1c2-4 6-6 9-8z" class="C"></path><path d="M451 291c3 0 6 1 9 2l-1 1c-2-1-6-1-8-2h1l-1-1z" class="F"></path><path d="M458 285h1 2c-3 2-5 2-8 2-2 0-4 1-6 2h0v-1c1-1 3-2 5-2 2-1 4-2 6-1z" class="Z"></path><path d="M452 282h1 2l1-1v1c0 1 0 1-1 1s-2 1-3 2h-1l1 1c-2 0-4 1-5 2v1h0-1l-1-1v2c2 0 5 0 6 1l1 1h-1l-2 1s0-1-1-1l-2-1h-3v-1c2-4 6-6 9-8z" class="D"></path><path d="M446 291h5l1 1h-1l-2 1s0-1-1-1l-2-1z" class="U"></path><path d="M441 408c1 1 2 1 2 2h1c0 1 0 1 1 2 0 2 1 3 2 5h1s1 0 2 1h-1 1c0 1 1 1 1 1h1c0 1 0 1 1 2h0l-1 1c-1 0-2 0-3-1v2c1 0 2 1 3 1s2 1 3 1v1c-3 1-5 1-7 1s-5 0-7 1h-1-1v-11l1-5c1 1 1 2 1 4l3-1v-1-1c0-1-1-1-1-2 0 0 0-1-1-1 0-1 0-2-1-2z" class="C"></path><path d="M441 408c1 1 2 1 2 2h1c0 1 0 1 1 2 0 2 1 3 2 5h1s1 0 2 1h-1 1c0 1 1 1 1 1h1c0 1 0 1 1 2h0l-1 1c-1 0-2 0-3-1h-2c1 0 1 1 1 2 2 2 4 1 5 3h0c-3 0-4-1-6-3 0 0-1-1-1-2h-5v-5l3-1v-1-1c0-1-1-1-1-2 0 0 0-1-1-1 0-1 0-2-1-2z" class="U"></path><defs><linearGradient id="d" x1="473.555" y1="407.828" x2="452.205" y2="420.238" xlink:href="#B"><stop offset="0" stop-color="#cf070b"></stop><stop offset="1" stop-color="#a31f29"></stop></linearGradient></defs><path fill="url(#d)" d="M458 397c1 0 2 0 3 1h2l5 3c0-1 0 0 1-1h1v1l1-1c1 2 2 3 4 4l1 3c1 1 2 3 2 4v3 5l-1 1c-1 0-1 0-2 1-1 3-3 5-6 6-6 2-11 1-17 1-2 0-4 1-6 0-1-1-3 0-5 0 2-1 5-1 7-1s4 0 7-1v-1c-1 0-2-1-3-1s-2-1-3-1v-2c1 1 2 1 3 1h2c1 0 4 2 6 1h1v-1h0 2l1-1h1v1l-1 2v1h1c1 0 3 0 4-1 2-1 3-4 4-6 0-3 0-6-3-8-1-1-2-1-4-2-1-1-1-2-2-3 0-1 1 0 2-1-1-1-2-1-3-1-1-1-2-1-2-2h0-5-3c2-1 3-1 5-3v1l-1 1h3 0 1v-1c0-1-2-1-3-2z"></path><path d="M475 404l1 3c0 1 0 1-1 2l-2-4 2-1z" class="a"></path><path d="M468 401c0-1 0 0 1-1h1v1l1-1c1 2 2 3 4 4l-2 1c-2-1-3-3-5-4z" class="E"></path><path d="M464 421h1v1l-1 2v1h1c-2 0-4 0-5-1h-2c-1 0-3-1-4-2 1 0 4 2 6 1h1v-1h0 2l1-1z" class="f"></path><path d="M464 421h1v1l-1 2-1-1v-1l1-1z" class="e"></path><path d="M476 407c1 1 2 3 2 4v3 5l-1 1c-1 0-1 0-2 1 2-5 2-8 0-12 1-1 1-1 1-2z" class="R"></path><path d="M456 401h5 0c0 1 1 1 2 2 1 0 2 0 3 1-1 1-2 0-2 1 1 1 1 2 2 3 2 1 3 1 4 2 3 2 3 5 3 8-1 2-2 5-4 6-1 1-3 1-4 1h-1v-1l1-2v-1h-1l-1 1h-2v-2-1l-2-1v-3h0c-1-1-2-3-2-3-1-1-1-1-2-1-1-1-3-2-4-3h-1v-1c1-2 2-3 3-4l3-2z" class="C"></path><path d="M459 415h1c1 2 1 2 3 3 1 1 2 1 2 3v1-1h-1l-1 1h-2v-2-1l-2-1v-3h0z" class="g"></path><path d="M463 418c1 1 2 1 2 3v1-1h-1-1c-1-1-1-1-1-2l1-1z" class="f"></path><defs><linearGradient id="e" x1="467.209" y1="306.3" x2="480.48" y2="280.693" xlink:href="#B"><stop offset="0" stop-color="#0d1111"></stop><stop offset="1" stop-color="#211818"></stop></linearGradient></defs><path fill="url(#e)" d="M463 281l1-1h1c2 1 5 4 7 5-1 0-1 1-1 1-1 1-2 1-2 2v1c3 0 6-1 9-2 5-1 10-1 15-4l1 1c-3 3-6 5-8 8l6-1 1 1-3 1c-3 0-5 1-8 2l1 1-2 1c-1 0-1 0-1 1 2 2 3 5 5 7-1 1-2 1-2 2 0 0 0 1 1 1-2 1-2 2-3 3 1 2 0 2 1 3l-1 1-1-1c-1-2-2-3-3-5l-6-8c0-1-1-2-2-2-2-3-5-6-6-9v-5-4z"></path><path d="M477 291h1v2h2c-1 1-3 1-4 1v-1l1-2z" class="R"></path><path d="M477 291c-2 0-3 2-5 2v-1l1-1c2-1 4-2 6-1l-1 1h-1z" class="B"></path><path d="M465 280c2 1 5 4 7 5-1 0-1 1-1 1h-1c-2-1-5-4-6-6h1z" class="Q"></path><path d="M479 290c1-1 2-1 4-1 0 1-2 3-3 4h0-2v-2l1-1z" class="p"></path><path d="M479 305c-1-2 0-5-1-8-1 0-2-1-4-1l-1-1c2 0 4 1 6 0h3l1 1-2 1c-1 0-1 0-1 1h-1c-1 2 0 5 0 7z" class="n"></path><path d="M479 305c0-2-1-5 0-7h1c2 2 3 5 5 7-1 1-2 1-2 2 0 0 0 1 1 1-2 1-2 2-3 3-1-2-2-3-2-6z" class="p"></path><path d="M460 318c4 0 8 0 11 3 2 2 4 6 4 9h1c0 2 0 3-1 4v1l1-1v1l1 1c2-1 3-2 5-3 1 0 2-1 3-2h0c1 0 2 0 3-1h0v1 1l-2 2h0s0 1-1 1c-2 2-5 5-7 6l-1 1h-1s0-1 1-1h-1l-2 1c-2 1-3 2-3 4v1h0v1h-1l1 1c-1 1-2 0-3 0-2-1-4-3-5-5 0 0-1 0-1-1v-1h-3 0c-3-1-5-3-6-6v-1h0c0-2-1-2-2-4l3-3v-1c-1-1-1 0-2-1 0-1 0-1-1-2l-2 2v-1c0-2 3-4 5-5s4-1 6-2z" class="O"></path><path d="M454 320c2-1 4-1 6-2-1 1-1 2-1 2v1l-2 1h1 6 0v1c0 2 0 3-1 4 0 1-1 2-2 2 0-1-1-1-1-2-3 0-4 0-6 1v-1c-1-1-1 0-2-1 0-1 0-1-1-2l-2 2v-1c0-2 3-4 5-5z" class="U"></path><path d="M454 320c2-1 4-1 6-2-1 1-1 2-1 2v1l-1-1c-3 0-5 3-7 4l-2 2v-1c0-2 3-4 5-5z" class="H"></path><path d="M464 322c3 0 6 1 7 3 2 2 2 4 3 6-1 2-3 4-3 6-1 0-1 0-1-1 0 1 0 2-1 3-1 0-2 1-3 1s-1 0-2-1v-3h0c-1 0-1 1-1 1h0c-1 0-1-1-1-1h0c0-2 0-3 1-5-1 0-2-1-3 0v-1l1-1c1 0 2-1 2-2 1-1 1-2 1-4v-1z" class="o"></path><path d="M464 323c1 1 1 2 1 3 0 2-2 4-2 5-1 0-2-1-3 0v-1l1-1c1 0 2-1 2-2 1-1 1-2 1-4z" class="k"></path><path d="M464 336l1-1c2 0 4-3 5-4v5c0 1 0 2-1 3-1 0-2 1-3 1s-1 0-2-1v-3z" class="c"></path><path d="M454 328c2-1 3-1 6-1 0 1 1 1 1 2l-1 1v1c1-1 2 0 3 0-1 2-1 3-1 5h0s0 1 1 1h0s0-1 1-1h0v3c1 1 1 1 2 1s2-1 3-1c1-1 1-2 1-3 0 1 0 1 1 1v1c2 1 5-1 6-2 2-1 3-2 5-3 1 0 2-1 3-2h0c1 0 2 0 3-1h0v1 1l-2 2h0s0 1-1 1c-2 2-5 5-7 6l-1 1h-1s0-1 1-1h-1l-2 1c-2 1-3 2-3 4v1h0v1h-1l1 1c-1 1-2 0-3 0-2-1-4-3-5-5 0 0-1 0-1-1v-1h-3 0c-3-1-5-3-6-6v-1h0c0-2-1-2-2-4l3-3z" class="F"></path><path d="M458 338l3 1h0c0-2-1-2-1-4 0-1 1-1 1-2v1c1 2 1 4 2 6 1 0 1 0 1 1h-1-2c-4-1-6-2-8-5l2-1 2 2s1 0 1 1z" class="k"></path><path d="M454 328c2-1 3-1 6-1 0 1 1 1 1 2l-1 1v1c0 1 0 1-1 1-1 2-1 3 0 5l-1 1c0-1-1-1-1-1l-2-2-2 1v-1h0c0-2-1-2-2-4l3-3z" class="B"></path><path d="M455 335c-1-2 0-3 1-4 1-2 3-2 5-2l-1 1v1c0 1 0 1-1 1-1 2-1 3 0 5l-1 1c0-1-1-1-1-1l-2-2z" class="N"></path><path d="M457 337v-1-2c0-1 1-1 2-2h0c-1 2-1 3 0 5l-1 1c0-1-1-1-1-1z" class="R"></path><path d="M463 344v-1h1c2-2 5-2 8-3s7-3 10-5c-1 2-3 4-4 6h0l-1 1h-1s0-1 1-1h-1l-2 1c-2 1-3 2-3 4v1h0v1h-1l1 1c-1 1-2 0-3 0-2-1-4-3-5-5z" class="O"></path><path d="M470 348h-2l-3-3 1-1h1c1 2 2 3 4 3v1h-1z" class="F"></path><path d="M467 344c2 0 4-3 7-2-2 1-3 2-3 4v1h0c-2 0-3-1-4-3z" class="E"></path><path d="M458 369l3-1c1 0 1 0 1 1 0 0-1 0 0 1-1 1-2 4-2 6 0 1 0 2-1 2l1 1 2 3c-2 0-3-1-5-1 0 1-1 1-1 1 4 1 7 3 10 6h0c-7-1-16-1-21 3-2 1-3 2-4 3h1s1-1 2-1h2 5v-1c-1-1-6 1-6 0 1-1 3-1 5-1 3 0 5 0 8 2v1c1 1 3 2 3 4-1-1-2-1-3-1 1 1 3 1 3 2v1h-1 0-3l1-1v-1c-2 2-3 2-5 3h3l-3 2c-1 1-2 2-3 4v1h1c1 1 3 2 4 3 1 0 1 0 2 1 0 0 1 2 2 3h0v3l2 1v1 2h0v1h-1c-2 1-5-1-6-1h-2l1-1h0c-1-1-1-1-1-2h-1s-1 0-1-1h-1 1c-1-1-2-1-2-1h-1c-1-2-2-3-2-5-1-1-1-1-1-2h-1c0-1-1-1-2-2 0-1-1-3-2-4h-2v-9-3c1-1 2-3 3-4 2-2 4-4 5-6s2-3 2-5l7-7 1 1s0 1 1 1c0-1 1-2 2-3z" class="h"></path><path d="M450 411l1-1c1 1 1 3 2 4 0 1 0 2 1 3 0 0 0 1-1 1l-1-1c-1-2-1-4-2-6z" class="K"></path><path d="M458 394c1 1 3 2 3 4-1-1-2-1-3-1l-5 1 2-1c2-1 2-2 3-3z" class="l"></path><path d="M450 407h0v1h1c1 1 3 2 4 3 0 1-1 1 0 2-1 1-1 1-2 1-1-1-1-3-2-4l-1 1v-4z" class="E"></path><path d="M455 371s0 1 1 1c-1 4-1 7-3 11l-6 3c2-1 3-2 4-4 3-3 2-7 4-11z" class="O"></path><path d="M450 407c-2-1-3-3-4-4-1-2-4-3-5-4 3 0 6 1 9 2h3 3l-3 2c-1 1-2 2-3 4h0z" class="Q"></path><path d="M450 401h3 3l-3 2h-2 0-1v-1h0v-1z" class="E"></path><path d="M458 369l3-1c1 0 1 0 1 1 0 0-1 0 0 1-1 1-2 4-2 6 0 1 0 2-1 2l1 1 2 3c-2 0-3-1-5-1 0 1-1 1-1 1l-2 1h-1c2-4 2-7 3-11 0-1 1-2 2-3z" class="o"></path><path d="M455 411c1 0 1 0 2 1 0 0 1 2 2 3h0v3l2 1v1 2h0v1h-1c-2 1-5-1-6-1h-2l1-1h0c-1-1-1-1-1-2h-1s-1 0-1-1h-1 1 1l1-1 1 1c1 0 1-1 1-1-1-1-1-2-1-3 1 0 1 0 2-1-1-1 0-1 0-2z" class="e"></path><path d="M455 417h1c2 0 2 1 2 2 1 0 1 1 2 2h0l1-1v2h0v1h-1c-2-2-3-4-5-6z" class="u"></path><path d="M452 417l1 1c1 0 1-1 1-1h1 0c2 2 3 4 5 6-2 1-5-1-6-1h-2l1-1h0c-1-1-1-1-1-2h-1s-1 0-1-1h-1 1 1l1-1z" class="N"></path><defs><linearGradient id="f" x1="429.26" y1="276.599" x2="484.578" y2="262.584" xlink:href="#B"><stop offset="0" stop-color="#df0000"></stop><stop offset="1" stop-color="#a51a1e"></stop></linearGradient></defs><path fill="url(#f)" d="M435 230c1 4-1 9 1 13 1 0 2 0 2-1 1 0 2-1 2-2v-1c1-1 3-2 4-3l-1 14 3-6c1-1 2-2 4-3l2-3c2-1 3-2 6-2 1 0 2 0 3 1 5-1 10 1 16 1 1-1 2-1 3 0l1 1c-5 1-9 1-14 0-2 0-4-1-6 0h-1c-3 2-7 5-9 8l-3 3c0 1 1 1 1 2l5-1c0 1 1 1 1 2-4 2-8 4-11 7h1l1 1 1 1-1 2c0 1-1 2-1 3v1h0v3h0c3-1 5-2 7-4v3 1h2v1c1 0 1 0 2-1h1l-1 2 2 1h-1c1 1 3 0 3 1l-1 1-7 6c-3 2-7 4-9 8v1h3l2 1c1 0 1 1 1 1-3 1-7 3-8 6-1 2-1 3 0 5 1 3 4 6 7 8h-1c-1 0-1-1-2-1h-1l-5-5-1-1-1 1v1-1c-1-3 0-5-2-7v-5-12-8-15c0-6 0-11-1-16 0-3 1-6 1-9v-4z"></path><path d="M448 250c0 1 1 1 1 2-1 1-2 1-3 1 1-1 1-2 2-3z" class="R"></path><path d="M458 236c1 0 2 0 3 1-4 1-7 3-11 4l2-3c2-1 3-2 6-2z" class="K"></path><path d="M446 261l1 1-1 2c0 1-1 2-1 3v1h0-1c-2 1-4 3-6 4 1-2 3-3 4-5s2-4 4-6z" class="Y"></path><path d="M450 280c-1 2-2 3-4 4-1 2-3 4-4 6l-1 1-1 1v-1c-1-1 0-2 1-3 2-4 5-6 9-8z" class="m"></path><path d="M449 252l5-1c0 1 1 1 1 2-4 2-8 4-11 7-1 1-2 1-3 1h0c1-3 3-5 5-8 1 0 2 0 3-1z" class="K"></path><path d="M435 230c1 4-1 9 1 13l1 1c0 3 0 4 1 7v1h0c1 1 0 1 0 2-1 1-2 2-2 3l-1 25v-8-15c0-6 0-11-1-16 0-3 1-6 1-9v-4z" class="M"></path><path d="M454 272c1 0 1 0 2-1h1l-1 2 2 1h-1c1 1 3 0 3 1l-1 1-7 6c-3 2-7 4-9 8v1h-2l1-1c1-2 3-4 4-6 2-1 3-2 4-4 1-1 2-1 3-2l-1-1v-3l2-1v-1z" class="L"></path><path d="M454 272c1 0 1 0 2-1h1l-1 2 2 1h-1c1 1 3 0 3 1l-1 1v-1h-2-3-1l-1-1 2-1v-1z" class="j"></path><path d="M454 273h1c0 1 0 1-1 2h-1l-1-1 2-1z" class="v"></path><path d="M486 334l8 3 2 1 2 1 2 1 1-1h2 3l-3 3v1l1 1h0v1h1l-1 1-1 1 1 1c2 0 2-1 3 0l2 2h1l2 3v1h-3l1 1c-1 1-2 1-3 1 0 0-1 0-2 1h0c-5 0-10 0-15 2h0-2c-3 1-6 2-9 4-3 0-5 1-8 1l-1 1c-1 1-2 2-2 3h-1v-1h-2c-2 0-2 0-3 2v1c-1-1 0-1 0-1 0-1 0-1-1-1l-3 1c-1 1-2 2-2 3-1 0-1-1-1-1l-1-1c1-1 3-2 4-3-1-1-3-2-4-2h-1l1-1h0v-1-1h1l1-1v-2h1c0-1 1-1 2-1l2-2 3-1 2-1v-1h-1l1-1c1-1 2-1 3-1h3v-1c-1 0-2 0-3-1h-1c1 0 2 1 3 0l-1-1h1v-1h0v-1c0-2 1-3 3-4l2-1h1c-1 0-1 1-1 1h1l1-1c2-1 5-4 7-6 1 0 1-1 1-1z" class="T"></path><path d="M475 350l2 1h1l-1 1h1l-1 1h0c-2 1-4 2-6 2s-3-1-5-1v-1h-1l1-1c1-1 2-1 3-1h3l3-1z" class="q"></path><path d="M466 353h-1l1-1c1-1 2-1 3-1s2 1 3 1c-2 1-4 1-6 1z" class="t"></path><path d="M475 350l2 1h1l-1 1h-5c-1 0-2-1-3-1h3l3-1z" class="K"></path><path d="M489 350h2s0-1 1-1 3 1 5 1l1 1v1h0 1c1 0 1 1 2 1h0l-2 1h0-10c-3 0-5 1-7 1 0-1 1-2 3-2 1-2 1-2 4-3z" class="X"></path><path d="M489 350h0l1 2-5 1c1-2 1-2 4-3z" class="E"></path><path d="M494 351c1 0 2 0 4 1h0 1c1 0 1 1 2 1h0l-2 1h0c-1-1-4-1-5-1 0-1-1-1 0-2z" class="Z"></path><path d="M489 350h2s0-1 1-1 3 1 5 1l1 1v1c-2-1-3-1-4-1l-5-1h0z" class="Q"></path><path d="M477 358c3 0 10-1 12 0 1 0 1 0 1 1h-2c-3 1-6 2-9 4-3 0-5 1-8 1h-1 0l2-1-2-2h-1 2v-1h-1c2 0 3-1 5-1v-1h2z" class="B"></path><path d="M475 358h2c0 1 0 1 1 1 1 1 1 0 2 1-1 1-2 1-4 2l-4 1-2-2h-1 2v-1h-1c2 0 3-1 5-1v-1z" class="Y"></path><path d="M474 361c2-1 4-1 6-1-1 1-2 1-4 2 0-1-1-1-2-1z" class="e"></path><path d="M471 360h2l1 1h0c1 0 2 0 2 1l-4 1-2-2h-1 2v-1z" class="E"></path><path d="M471 360h2l1 1h-4-1 2v-1z" class="t"></path><path d="M503 347l1 1c2 0 2-1 3 0l2 2h1l2 3v1h-3 0-5-5l2-1h0c-1 0-1-1-2-1h-1 0v-1l-1-1h4l-2-1 1-1 1-1h1 1 0z" class="X"></path><path d="M503 347l1 1c1 1 1 2 2 3h-1 0c-1-1-1-1-2-1 0-1 0-2-1-3h1 0z" class="H"></path><path d="M501 347h1c1 1 1 2 1 3h-2l-2-1 1-1 1-1z" class="g"></path><path d="M504 354c-1 0-1-1-1-1-1 0-1 0-1-1h0c1 0 2 0 3 1 0 0 1-1 2-1h0l2 2h-5z" class="l"></path><path d="M509 350h1l2 3v1h-3 0l-2-2 2-2z" class="D"></path><path d="M466 354c2 0 3 1 5 1v1l-1 1c2 1 4-1 5 1v1c-2 0-3 1-5 1h1v1h-2l-2-1v1 1l-2-1c-1 0-2 0-3-1h-4l-1-1c0-1 1-1 2-1l2-2 3-1 2-1z" class="N"></path><path d="M465 361h2v1l-2-1z" class="W"></path><path d="M461 356l3-1 2 1c-2 1-4 1-6 2h-1 0l2-2z" class="D"></path><path d="M466 354c2 0 3 1 5 1v1h-5l-2-1 2-1z" class="I"></path><path d="M467 360h0c-2 0-2 0-3-1h0 4c1-1 1-1 2-1 2 0 3 0 5 1-2 0-3 1-5 1h1v1h-2l-2-1h0z" class="G"></path><path d="M467 360l1-1 2 1h1v1h-2l-2-1h0z" class="O"></path><path d="M457 359l1 1h4c1 1 2 1 3 1l2 1v-1-1l2 1h1l2 2-2 1h0 1l-1 1c-1 1-2 2-2 3h-1v-1h-2c-2 0-2 0-3 2v1c-1-1 0-1 0-1 0-1 0-1-1-1l-3 1c-1 1-2 2-2 3-1 0-1-1-1-1l-1-1c1-1 3-2 4-3-1-1-3-2-4-2h-1l1-1h0v-1-1h1l1-1v-2h1z" class="l"></path><path d="M462 360c1 1 2 1 3 1l2 1v1c-1 1-2 1-4 2v-1s1-1 1-2c-1 0-1 0-2-1h-1l1-1z" class="Y"></path><path d="M456 361h0c3 1 4 0 6 2h0c-1 1-1 1-2 0-1 0-4 0-5 1h-1 0v-1-1h1l1-1z" class="U"></path><path d="M455 364c1-1 2-1 4 0v1 1l-1 1h0c-1-1-3-2-4-2h-1l1-1h1z" class="p"></path><path d="M467 360l2 1h1l2 2-2 1h0 1l-1 1c-1 1-2 2-2 3h-1v-1h-2c-2 0-2 0-3 2v1c-1-1 0-1 0-1 0-1 0-1-1-1l-3 1c-1 1-2 2-2 3-1 0-1-1-1-1l-1-1c1-1 3-2 4-3h0 1l2-1v1h1c1-1 1-1 1-2 2-1 3-1 4-2v-1-1-1z" class="G"></path><path d="M470 364l-2 1h-1v-1c1-1 1-1 3-1v1h0z" class="L"></path><path d="M458 367h1l2-1v1c-1 0-2 1-3 2s-2 2-2 3c-1 0-1-1-1-1l-1-1c1-1 3-2 4-3h0z" class="g"></path><path d="M486 334l8 3 2 1 2 1 2 1 1-1h2 3l-3 3v1l1 1h0v1h1l-1 1-1 1h0-1-1l-1 1-1 1 2 1h-4c-2 0-4-1-5-1s-1 1-1 1h-2c-3 1-3 1-4 3-2 0-3 1-3 2l-1-1 1-1c-1 0-2-1-3-1l-1-1h-1l-2-1-3 1v-1c-1 0-2 0-3-1h-1c1 0 2 1 3 0l-1-1h1v-1h0v-1c0-2 1-3 3-4l2-1h1c-1 0-1 1-1 1h1l1-1c2-1 5-4 7-6 1 0 1-1 1-1z" class="C"></path><path d="M471 347c1 1 2 2 4 2v1l-3 1v-1c-1 0-2 0-3-1h-1c1 0 2 1 3 0l-1-1h1v-1h0z" class="j"></path><path d="M475 349c2 0 4 1 6 1v1l-2 1-1-1h-1l-2-1v-1z" class="f"></path><path d="M486 334l8 3 2 1 2 1 2 1c-1 0-2 0-2 1-3-3-9-5-13-6 1 0 1-1 1-1z" class="G"></path><path d="M503 339h3l-3 3v1l1 1h0v1h1l-1 1-1 1h0-1-1l-1 1-1 1 2 1h-4c-2 0-4-1-5-1s-1 1-1 1h-2c-3 1-3 1-4 3-2 0-3 1-3 2l-1-1 1-1c-1 0-2-1-3-1l2-1v-1c-1-2-3-2-4-4h1c2 1 3 2 5 3h2v-1l-2-1v-2-1l3 4h1c1-1 2-1 2-2v-1h2c3-1 4-2 7-4 0-1 1-1 2-1l1-1h2z" class="U"></path><path d="M497 346c3-2 4-3 6-4v1l1 1h0v1h1l-1 1-1 1h0-1-1c-1 0-2-1-2-1h-2z" class="B"></path><path d="M504 344v1h1l-1 1-1 1h0l-2-2 3-1z" class="X"></path><path d="M492 349c2-2 4-1 5-3h2s1 1 2 1l-1 1-1 1 2 1h-4c-2 0-4-1-5-1z" class="u"></path><path d="M499 349h-2l-1-1h1 3l-1 1z" class="Z"></path><path d="M449 326l2-2c1 1 1 1 1 2 1 1 1 0 2 1v1l-3 3c1 2 2 2 2 4h0v1c1 3 3 5 6 6h0 3v1c0 1 1 1 1 1 1 2 3 4 5 5h1c1 1 2 1 3 1v1h-3c-1 0-2 0-3 1l-1 1h1v1l-2 1-3 1-2 2c-1 0-2 0-2 1h-1v2l-1 1h-1v1 1h0l-1 1h1c1 0 3 1 4 2-1 1-3 2-4 3l-7 7c0 2-1 3-2 5s-3 4-5 6c0-1-1-1-2-2l-1-1v-14-6-5-3l1-18v-1c1-1 2-2 2-3l5-6c1-1 3-2 4-3z" class="r"></path><path d="M449 341v-6l2 2c0 2-1 3-2 4z" class="n"></path><path d="M451 331c1 2 2 2 2 4h0v1h-2v1l-2-2 1-2 1-2z" class="P"></path><path d="M450 333c1 1 1 2 1 3v1l-2-2 1-2z" class="t"></path><path d="M438 339h0c1 3 0 7 1 10l3-3c1 0 2-1 3-2h1 0 0c-2 2-5 4-6 7h1c-1 2-3 6-4 6l1-18z" class="h"></path><path d="M451 336h2c1 3 3 5 6 6h0l1 1v1c-2 1-4 2-7 4h-1l-1 1h-1l-3 2c-3 3-5 5-6 9-1 1-2 3-3 5h0-1v-5-3c1 0 3-4 4-6 1-1 2-2 3-2 1-1 2-1 3-2l2-6c1-1 2-2 2-4v-1z" class="r"></path><path d="M460 343v1c-2 1-4 2-7 4h-1l-1 1h-1c-2-1-2-1-4-1-1 1-3 2-4 3-2 3-4 6-5 9v-3c1 0 3-4 4-6 1-1 2-2 3-2 1-1 2-1 3-2v1c3 0 6-2 8-3 0-1 1-2 2-2h3z" class="f"></path><path d="M451 336h2c1 3 3 5 6 6h0l1 1h-3c-1 0-2 1-2 2-2 1-5 3-8 3v-1l2-6c1-1 2-2 2-4v-1z" class="I"></path><path d="M459 342h3v1c0 1 1 1 1 1 1 2 3 4 5 5h1c1 1 2 1 3 1v1h-3c-1 0-2 0-3 1l-1 1h1v1l-2 1-3 1-2 2c-1 0-2 0-2 1h-1c0-1 0-1-1-2l-5 2-2-1h0l-2-1-2 1c-1 1-2 2-3 2 1-4 3-6 6-9l3-2h1l1-1h1c3-2 5-3 7-4v-1l-1-1z" class="D"></path><path d="M460 354v2h1l-2 2c-1 0-2 0-2 1h-1c0-1 0-1-1-2 2-1 4-2 5-3z" class="L"></path><path d="M450 349h1v1c0 1 0 1-1 2l1 1c2-1 4 0 7 0v1c-1 1-3 0-4 0s-2 1-3 1h-3l-2 2-2 1c-1 1-2 2-3 2 1-4 3-6 6-9l3-2z" class="a"></path><path d="M447 354h4v1h-3l-1-1z" class="X"></path><path d="M444 358v-1c0-1 1-1 1-2h1c0-1 0-1 1-1l1 1-2 2-2 1z" class="e"></path><path d="M450 349h1v1c0 1 0 1-1 2l1 1c-1 0-2 0-3 1-1-1-1-2-1-3h0l3-2z" class="Y"></path><path d="M459 342h3v1c0 1 1 1 1 1 1 2 3 4 5 5h1c1 1 2 1 3 1v1h-3c-1 0-2 0-3 1l-1 1h1v1l-2 1-3 1h-1v-2c1-1 2-1 2-2l-4-3h-1-3s-1 0-2-1h0 1c3-2 5-3 7-4v-1l-1-1z" class="S"></path><path d="M459 342h3v1c0 1-1 1-1 2 0 0 1 1 0 1 0 0-1 1-1 2l1 1v1c-1-1-2-1-3-1h-1-3s-1 0-2-1h0 1c3-2 5-3 7-4v-1l-1-1z" class="B"></path><path d="M446 357l2 1h0l2 1 5-2c1 1 1 1 1 2v2l-1 1h-1v1 1h0l-1 1h1c1 0 3 1 4 2-1 1-3 2-4 3l-7 7c0 2-1 3-2 5s-3 4-5 6c0-1-1-1-2-2l-1-1v-14-6h1 0c1-2 2-4 3-5 1 0 2-1 3-2l2-1z" class="C"></path><path d="M438 381c1 0 2-1 4-1l2 2c0 1-2 3-3 4-1 0-2-1-3-1v-2c0-1 1-1 0-2z" class="AA"></path><path d="M437 371h0c1 1 2 1 3 2v1c0 1 1 1 2 1h0c-1 2-3 4-4 6 1 1 0 1 0 2v2 1l-1-1v-14z" class="S"></path><path d="M446 357l2 1h0l2 1 5-2c1 1 1 1 1 2v2l-1 1h-1v1 1h0l-1 1-3-3c-1 2-2 3-3 4v1h-2v-2h-1l-3 3c1 1 1 2 1 3l-2 2c-1-1-2-1-3-2h0v-6h1 0c1-2 2-4 3-5 1 0 2-1 3-2l2-1z" class="N"></path><path d="M448 358h0c-1 1 0 1-1 2 0 1-2 2-2 2l-1-1v-1l1-1h1c1-1 1-1 2-1z" class="Z"></path><path d="M442 362h1l1-1 1 1-5 4v-2c0-1 1-2 2-2z" class="X"></path><path d="M438 365l2-1v2 1h0v1h0l-3 3h0v-6h1 0z" class="U"></path><path d="M438 365l2-1v2 1c-1 0-1 0-2 1l-1-1 1-2h0z" class="H"></path><path d="M446 357l2 1c-1 0-1 0-2 1h-1l-1 1v1l-1 1h-1c-1 0-2 1-2 2l-2 1c1-2 2-4 3-5 1 0 2-1 3-2l2-1z" class="Y"></path><path d="M444 360v1l-1 1h-1l2-2z" class="v"></path><path d="M450 359l5-2c1 1 1 1 1 2v2l-1 1h-1v1 1h0l-1 1-3-3c-1 2-2 3-3 4h-1c0-2 1-3 1-5 1-1 2-2 3-2z" class="o"></path><path d="M459 251h7c2 1 5 1 8 2-1 2-2 1-4 2h-1l1 1h1c1 0 2 1 3 2h-1c1 1 1 1 2 1 2 1 3 3 6 3h0c2 2 4 5 5 7 1 1 3 5 4 5 2 1 4 1 5 1v-2c-1-1-2-1-3-2l1-1 3 2h0 4 1c0 1 1 1 1 1 1 1 2 0 3 0l8-1-1 2c-3 3-6 4-9 6l-9 4-1-1c-5 3-10 3-15 4-3 1-6 2-9 2v-1c0-1 1-1 2-2 0 0 0-1 1-1-2-1-5-4-7-5h-1l-1 1v4h-1-1-2-1c-2-1-4 0-6 1l-1-1h1c1-1 2-2 3-2s1 0 1-1v-1l-1 1h-2-1l7-6 1-1c0-1-2 0-3-1h1l-2-1 1-2h-1c-1 1-1 1-2 1v-1h-2v-1-3c-2 2-4 3-7 4h0v-3h0v-1c0-1 1-2 1-3l1-2-1-1-1-1h-1c3-3 7-5 11-7l4-2z" class="C"></path><path d="M471 267c1 1 1 1 2 1 0 1 1 1 2 2h-1-2-2c1 0 2 0 2-1 0 0-1 0-1-1v-1z" class="K"></path><path d="M470 258h1c3 3 8 7 10 11-4-3-8-7-13-9l2-2zm-19 4v-1c0-1 1-1 2-1 4-2 6-3 10-1 3 1 5 3 8 5 1 0 2 1 2 2h0l-2-1v1 1c-3-1-5-2-6-4 0 0-1 0-1-1s0-1-1-2-3-1-4-1c-3 1-6 1-8 3z" class="F"></path><path d="M465 263h1c2 0 4 1 5 2h0v1 1c-3-1-5-2-6-4z" class="b"></path><path d="M449 263c-1-1-1-1 0-2 1-2 4-4 6-5v-1c6-3 9 3 14 0l1 1h1c1 0 2 1 3 2h-1l-2-1h-1c0 1 0 1 1 1h-1l-2 2-5-2v1c-4-2-6-1-10 1-1 0-2 0-2 1v1c-1 0-1 1-2 1h0z" class="K"></path><path d="M463 258h-1v-1c3 0 5 0 8 1l-2 2-5-2z" class="Q"></path><path d="M459 251h7c2 1 5 1 8 2-1 2-2 1-4 2h-1c-5 3-8-3-14 0v1c-2 1-5 3-6 5-1 1-1 1 0 2-1 0-2 1-3 1l1-2-1-1-1-1h-1c3-3 7-5 11-7l4-2z" class="o"></path><path d="M493 270l3 2h0 4 1c0 1 1 1 1 1 1 1 2 0 3 0l8-1-1 2c-3 3-6 4-9 6l-9 4-1-1c-5 3-10 3-15 4-3 1-6 2-9 2v-1c0-1 1-1 2-2 0 0 0-1 1-1 4-1 8-1 12-3l-1-5v-1-1h2c1 0 1 0 1 1 1 1 2 3 3 3h1c0-1 1-1 1-1 1 0 1 0 2 1s1 1 2 1l1-1v-1h-2c-1 0-2-1-2-2h0l3 1h1l-1-2v-2c-1-1-2-1-3-2l1-1z" class="T"></path><path d="M483 277v-1-1h2c1 0 1 0 1 1 1 1 2 3 3 3h1c0 1 1 1 0 2 0 1-1 1-1 1-1 0-2 0-3-1s-2-2-2-4h0-1z" class="D"></path><path d="M493 283l13-7c2-1 4-2 6-2-3 3-6 4-9 6l-9 4-1-1z" class="B"></path><path d="M451 262c2-2 5-2 8-3 1 0 3 0 4 1s1 1 1 2 1 1 1 1c1 2 3 3 6 4v1c0 1 1 1 1 1 0 1-1 1-2 1h2c-3 2-5 3-8 6h0c0 1-1 2-1 2l2 2h-1l-1 1v4h-1-1-2-1c-2-1-4 0-6 1l-1-1h1c1-1 2-2 3-2s1 0 1-1v-1l-1 1h-2-1l7-6 1-1c0-1-2 0-3-1h1l-2-1 1-2h-1c-1 1-1 1-2 1v-1h-2v-1-3c-2 2-4 3-7 4h0v-3h0v-1c0-1 1-2 1-3 1 0 2-1 3-1h0c1 0 1-1 2-1z" class="m"></path><path d="M460 269l2 1 1 1c-1 0-2 1-3 1l-2 2-2-1 1-2h-1c1-1 2-1 3-1l1-1z" class="B"></path><path d="M457 271c1 1 2 0 3 1l-2 2-2-1 1-2z" class="Q"></path><path d="M456 268l4-1h0v2l-1 1c-1 0-2 0-3 1s-1 1-2 1v-1h-2v-1h1l3-2z" class="V"></path><path d="M464 262c0 1 1 1 1 1 1 2 3 3 6 4v1c-1 0-3-1-4-1-3-2-7-3-11-1h0v-1c1 0 2-1 3-1l5-2z" class="Q"></path><path d="M470 270h2c-3 2-5 3-8 6h0c0 1-1 2-1 2l2 2h-1l-1 1v4h-1-1-2-1c1-1 2-1 2-2l1-5c2-4 6-6 9-8z" class="E"></path><path d="M463 278l2 2h-1l-1 1v-3z" class="D"></path><path d="M451 262c2-2 5-2 8-3 1 0 3 0 4 1s1 1 1 2l-5 2c-1 0-2 1-3 1v1h0s0 1-1 1c0 0-1 0-2 1h3l-3 2h-1v-3c-2 2-4 3-7 4h0v-3h0v-1c0-1 1-2 1-3 1 0 2-1 3-1h0c1 0 1-1 2-1z" class="B"></path><path d="M463 260c1 1 1 1 1 2l-5 2v-1c-1 0-1 0-2-1 2 0 3-1 5-1 0 0 1 0 1-1z" class="a"></path><path d="M457 262c1 1 1 1 2 1v1c-1 0-2 1-3 1v1h0s0 1-1 1c0 0-1 0-2 1h3l-3 2h-1v-3h0c0-1 1-1 3-2 0 0 1 0 1-1h0-2c1-1 2-2 3-2z" class="g"></path><path d="M506 211h1c1 1 1 3 2 4 1 0 3 0 4-1l3 3c-1 1-2 1-3 2 1 1 2 0 3 1l-2 2v1l1 1c-2 1-4 2-5 3h1 2 0l-1 2 1 2-1 1c-1 0-2-1-3 0-3 1-4 3-6 4-3 2-5 3-8 4-3 3-5 5-6 9-1 3 0 7 2 9 3 3 7 5 11 6v4h-1v1l1 1-1 1v1h-1-4 0l-3-2-1 1c1 1 2 1 3 2v2c-1 0-3 0-5-1-1 0-3-4-4-5-1-2-3-5-5-7h0c-3 0-4-2-6-3-1 0-1 0-2-1h1c-1-1-2-2-3-2h-1l-1-1h1c2-1 3 0 4-2-3-1-6-1-8-2h-7l-4 2c0-1-1-1-1-2l-5 1c0-1-1-1-1-2l3-3c2-3 6-6 9-8h1c2-1 4 0 6 0 5 1 9 1 14 0l-1-1c-1-1-2-1-3 0l5-2c-1 0-2 1-3 0 1-2 3-3 4-5h0l5-5c1-1 2-3 2-3l1-1v-1c2 1 4 3 7 2 1 0 2-1 3-2 2 0 3-2 4-4v-2c1-1 1-2 1-4z" class="c"></path><path d="M460 245h2c-1 1-2 1-3 2l-1-1 2-1z" class="H"></path><path d="M458 249h4 0v1 1h4-7l-1-1v-1z" class="n"></path><path d="M477 254h1l7 4h0c-2 0-3-1-5-1v1s-1 0-1 1c0 0-1-1-1-2-1-1-1-1-2-1 1-1 1-1 1-2z" class="G"></path><path d="M476 241l2 3h0c-1 1 0 6 0 7v1 2h-1 0v-1-1c1-2 0-3 0-6l-2-2c0-1 0-1-1-2 1 0 1 0 2-1z" class="O"></path><path d="M476 241v-1c2 0 4 3 5 3h6 0c-2 2-4 2-6 2-1 0-1 0-2-1h0-1 0l-2-3z" class="u"></path><path d="M493 238h0c0 2-2 4-3 6l3-3c1 0 2 0 2-1-3 3-5 5-6 9l-1 2h0v-1c-1-4 2-9 5-12z" class="L"></path><path d="M482 236h0c3-1 6-4 7-6s2-4 3-5c-1 4-1 7-5 10-1 1-5 4-6 4l-1-1c-1-1-2-1-3 0l5-2z" class="e"></path><path d="M451 247c1 1 5 2 7 2v1l1 1-4 2c0-1-1-1-1-2l-5 1c0-1-1-1-1-2l3-3z" class="Z"></path><path d="M454 251c1-1 2-1 4-1l1 1-4 2c0-1-1-1-1-2z" class="t"></path><path d="M474 253l2 1h1 0c0 1 0 1-1 2 1 0 1 0 2 1 0 1 1 2 1 2l2 3c-3 0-4-2-6-3-1 0-1 0-2-1h1c-1-1-2-2-3-2h-1l-1-1h1c2-1 3 0 4-2z" class="B"></path><path d="M474 253l2 1h1 0c0 1 0 1-1 2l-2-1h-4c2-1 3 0 4-2z" class="F"></path><path d="M476 254h1 0c0 1 0 1-1 2l-2-1c1 0 1-1 2-1z" class="N"></path><path d="M489 249c-1 3 0 7 2 9 3 3 7 5 11 6v4h-1-2 0v-1c-3-2-6-5-9-8h-2l-1-1h1 0c1-2 0-5 0-7h0l1-2z" class="X"></path><path d="M487 264c0-1 0-1 1-2 4 1 7 5 11 6h2v1l1 1-1 1v1h-1-4 0l-3-2c-2-1-3-2-4-3l-2-3z" class="G"></path><path d="M489 267c2 0 4 1 5 2 2 0 4 1 6 1v1c-1 1-2 1-4 1h0l-3-2c-2-1-3-2-4-3z" class="P"></path><path d="M480 258c2 2 5 4 7 6l2 3c1 1 2 2 4 3l-1 1c1 1 2 1 3 2v2c-1 0-3 0-5-1-1 0-3-4-4-5-1-2-3-5-5-7h0l-2-3c0-1 1-1 1-1z" class="S"></path><path d="M513 214l3 3c-1 1-2 1-3 2 1 1 2 0 3 1l-2 2v1l1 1c-2 1-4 2-5 3h1 2 0l-1 2 1 2-1 1c-1 0-2-1-3 0-3 1-4 3-6 4-3 2-5 3-8 4 0 1-1 1-2 1l-3 3c1-2 3-4 3-6h0c2-2 4-6 6-8 2-1 5-1 7-4 4-3 1-6 1-10l2-1c1 0 3 0 4-1z" class="T"></path><path d="M512 354c4 0 5 2 9 4l2 2h-1v1c5 5 11 12 14 18l2 5c-2-1-3-5-5-7s-5-4-8-3c-1 0-3 0-4 1l-2 1c-1 1-2 3-2 4l-1 2v4 1l-1 2c0-1 0-1-1-2-1 0-3 1-3 3s1 3 1 5c0 1-2 4-3 5-1 0-2 1-3 1l-1-1c-2 1-5 2-7 2h-4c-2 1-2 1-3 1s-1-1-1-1v1l1 1h1l4 1v1h-1c0 1 1 2 1 2 0 1-1 1 0 2l-1 1c-1 0-1-1-2-1-2-1-3 0-5 0-1 1-1 2-2 2v-1c-1 2-2 4-2 6s0 5-1 7h0c-1 0-2-1-3-2l-1-1s-1 0-1-1v-1-5-3c0-1-1-3-2-4l-1-3c-2-1-3-2-4-4l-1 1v-1h-1c-1 1-1 0-1 1l-5-3h-2c0-2-2-3-3-4v-1c-3-2-5-2-8-2-2 0-4 0-5 1 0 1 5-1 6 0v1h-5-2c-1 0-2 1-2 1h-1c1-1 2-2 4-3 5-4 14-4 21-3h0c-3-3-6-5-10-6 0 0 1 0 1-1 2 0 3 1 5 1l-2-3-1-1c1 0 1-1 1-2 0-2 1-5 2-6v-1c1-2 1-2 3-2h2v1h1c0-1 1-2 2-3l1-1c3 0 5-1 8-1 3-2 6-3 9-4h2 0c5-2 10-2 15-2h0c1-1 2-1 2-1 1 0 2 0 3-1l-1-1h3z" class="C"></path><path d="M478 411c1 1 2 2 2 3l-1 1-1-1v-3z" class="B"></path><path d="M502 386l4-2c0 1 0 2-1 2l1 1h-2-2v-1z" class="Z"></path><path d="M483 402c-2-1-4-1-5-2v-1h1c2 0 3 0 5 1h-3-1c1 0 1 1 1 1h1 1v1h0z" class="U"></path><path d="M497 391c1-3 3-4 5-5v1h2c-2 2-5 3-7 4z" class="R"></path><path d="M506 384c3-2 3-4 5-7l-2 7s-1 1-2 1l-1 2-1-1c1 0 1-1 1-2z" class="a"></path><path d="M458 393c1 0 4 1 5 2v1c0 1 0 1 1 1l-1 1h-2c0-2-2-3-3-4v-1z" class="u"></path><path d="M516 378c0-2 0-5 2-7l1 1v3 1c-1 1-2 3-2 4l-1-2z" class="a"></path><path d="M463 395c3 2 6 3 8 5l-1 1v-1h-1c-1 1-1 0-1 1l-5-3 1-1c-1 0-1 0-1-1v-1z" class="Q"></path><path d="M511 383h0c1-2 1-7 2-9 0 2 1 3 1 5v1h2v-2l1 2-1 2c-1 0-1 0-2-1v2h-3 0z" class="p"></path><path d="M506 387l-1 2-1 2h-3c-2 2-5 4-5 6-1-1-2-1-2-2l3-4c2-1 5-2 7-4h2z" class="j"></path><path d="M493 407l2-1c0 1 1 2 1 2 0 1-1 1 0 2l-1 1c-1 0-1-1-2-1-2-1-3 0-5 0-1 1-1 2-2 2v-1l-6-3 6-1c2 1 7 2 8 2v-2h-1z" class="l"></path><path d="M483 373l1 1c2 0 5-2 7-3 1 1 1 1 0 2l-1 2v1h-2v1c1 1 2 1 3 2-3 0-6-1-10-3 1-1 1-2 2-3z" class="G"></path><path d="M490 376h1c1-1 2-1 2-2l3-3 1 1c0 2-1 3-2 5 3-3 5-4 8-5 1 1 0 1 0 2v1 1l-4 2c-2 1-5 1-7 1h-1c-1-1-2-1-3-2v-1h2z" class="L"></path><path d="M484 400c1 0 2 1 3 0h0c-2-2-4-2-6-2h0l-1-1c1 0 2-1 3-1 5 1 9 5 14 4l1 1h0c-2 0-4 0-7-1h-3c2 1 3 2 6 2-2 1-2 1-3 1s-1-1-1-1v1l1 1h1l4 1v1h-1l-2 1c-4-1-7-3-10-5h0v-1h-1-1s0-1-1-1h1 3z" class="d"></path><path d="M511 383h0 3v-2c1 1 1 1 2 1v4 1l-1 2c0-1 0-1-1-2-1 0-3 1-3 3s1 3 1 5c0 1-2 4-3 5-1 0-2 1-3 1l-1-1c-2 1-5 2-7 2h-4c-3 0-4-1-6-2h3c3 1 5 1 7 1h0l-1-1-1-3c0-2 3-4 5-6h3l1-2 1-2 1-2c1 0 2-1 2-1l2-1z" class="c"></path><path d="M511 383h0 3v-2c1 1 1 1 2 1v4 1l-1 2c0-1 0-1-1-2v-1-1h1c-1-1-1-1-2-1s-4 1-5 2l-1-1c1 0 2-1 2-1l2-1z" class="f"></path><path d="M513 384h2c1 1 1 2 1 3l-1 2c0-1 0-1-1-2v-1-1h1c-1-1-1-1-2-1z" class="B"></path><path d="M507 385l1 1c-1 2-1 2-1 4 1 1 0 3 0 3l-2 3v-4-1h-1l1-2 1-2 1-2z" class="F"></path><path d="M505 389h1v3l1 1-2 3v-4-1h-1l1-2z" class="G"></path><path d="M514 385v1 1c-1 0-3 1-3 3s1 3 1 5c0 1-2 4-3 5-1 0-2 1-3 1l-1-1 2-1c4-2 2-6 3-10 0-2 2-3 4-4z" class="E"></path><path d="M504 391h1v1 4c-1 2-3 4-6 5h-1l-1-1-1-3c0-2 3-4 5-6h3z" class="L"></path><path d="M504 391h1v1h-1c-1 0-2 3-3 4h-1c0-1 1-2 1-3h1c-1-1-1-1-1-2h3z" class="d"></path><path d="M512 354c4 0 5 2 9 4l2 2h-1v1c-3-1-6-3-9-2-2 0-3 1-4 2-2 1-4 1-5 2s-3 2-4 2c-1-1-1-1-1-2l1-1c-2-1-5 1-7 2-3 1-5 2-8 2-2 0-3 1-5 1h-1c1-2 3-3 4-4h0-2-2c3-2 6-3 9-4h2 0c5-2 10-2 15-2h0c1-1 2-1 2-1 1 0 2 0 3-1l-1-1h3z" class="S"></path><path d="M505 357h0c-5 3-11 4-17 4-1 1-3 1-4 1 2-1 5-2 6-3 5-2 10-2 15-2z" class="D"></path><path d="M503 375c1 0 2-1 2-2 1-2 0-4 1-6 1 0 2-1 3-1 1 2 0 5 0 6-1 3-3 5-4 8-6 1-10 3-16 6h0l-12 2h0l-2-2h-1c-1 0-2-2-2-2v-1h-1v-1c1-1 1-2 2-2h1c0-2-1-2-1-3h3c3-1 4-4 7-5h1l-1 1c-1 1-1 2-2 3 4 2 7 3 10 3h1c2 0 5 0 7-1l4-2v-1z" class="T"></path><path d="M486 381c1 1 2 1 3 2v2 1h0l-2-1c0-1-1-2-2-2l1-1v-1z" class="g"></path><path d="M485 383l-3-1c-1 1-2 1-3 1s-3-1-4-1l1-1 6 1c-1 0-1-1-2-1l-4-2 10 2v1l-1 1z" class="n"></path><path d="M471 364c3 0 5-1 8-1h2 2 0c-1 1-3 2-4 4h1 0c-1 1-2 1-3 1v1l1 1h1 3l-3 3-3 3c-1 0-2 0-3 1 0 1 1 1 1 3h-1c-1 0-1 1-2 2v1h1v1s1 2 2 2h1l2 2h0-1c-3 1-5 0-7-2l-1 1-2-2c-1-2-3-6-6-7v1l-1-1c1 0 1-1 1-2 0-2 1-5 2-6v-1c1-2 1-2 3-2h2v1h1c0-1 1-2 2-3l1-1z" class="AA"></path><path d="M481 363h2 0c-1 1-3 2-4 4h1 0c-1 1-2 1-3 1v1l1 1h-4l-1-1c1-3 6-4 8-6z" class="P"></path><path d="M462 370v-1c1-2 1-2 3-2h2v1h1v7h0c-2 4-1 7 0 10l1 1-1 1-2-2c-1-2-3-6-6-7v1l-1-1c1 0 1-1 1-2 0-2 1-5 2-6z" class="H"></path><path d="M460 376c2 1 3 3 5 3 1 2 1 4 2 5l-1 1c-1-2-3-6-6-7v1l-1-1c1 0 1-1 1-2z" class="Q"></path><path d="M467 368h1v7h0l-1-1c-1 0-2 0-4-1-1 0-1-1-1-3l3-2h2z" class="V"></path><path d="M467 132l2-1-2-2h0c1 1 2 1 3 2l1 1c1 0 1 1 2 2l2 2c2 1 3 3 5 4 1 1 12 12 13 14 2 1 3 2 4 4l2 2c0 1 1 1 2 2 0 1 1 2 2 3 0 0 1 0 1 1l2 3 2 2 4 6c1 2 3 4 4 6 0 0 0 1 1 1 0 0 0 1 1 2s1 2 1 3c0 2 3 6 5 8l4 10c2 5 4 10 5 15 0 1 1 4 1 5l2 17c0 1-1 5-1 6-1 6-2 13-6 18-2 2-4 3-5 5s-1 3-2 5c-1 0-2-1-3-2v-1l-1-1c-1 1-1 2-1 3-1 2 0 3 1 4l3 2c-3 0-8 0-10 2 0 1 0 2-1 3-1 0-3-1-5-2 1-2 2-4 2-5h0c-2 1-3 2-3 4 0 3 2 5 4 7-2 1-4 2-6 2l-1 1 1 1c-1 0-5-1-7-2-1 0-1-1-2-1h-3l3-1-1-1-6 1c2-3 5-5 8-8l9-4c3-2 6-3 9-6l1-2-8 1c-1 0-2 1-3 0 0 0-1 0-1-1v-1l1-1-1-1v-1h1v-4c-4-1-8-3-11-6-2-2-3-6-2-9 1-4 3-6 6-9 3-1 5-2 8-4 2-1 3-3 6-4 1-1 2 0 3 0l1-1-1-2 1-2h0-2-1c1-1 3-2 5-3 0 0 1 0 2-1 0 0 1 0 1-1 1 0 2-1 3-1v-2h1 1c0-1 1-2 1-2 0-1 0-1 1-1v-1s1-1 1-2c1-6-5-15-9-20 0-2-2-4-3-6s-2-4-4-5c-2-2-3-4-4-7-2-2-4-5-6-7-1-1-2-3-4-4-1 0-3-2-5-3h-2-1c0-2-3-3-4-4-1 0-1 0-2 1-1 0-4-3-6-3-3-3-5-6-7-10l1-1v-1c-1-1-2-3-2-4v-2h0l1-1c0-2-1-3-2-4z" class="r"></path><path d="M512 246l1-1c1 1 2 1 2 2l1 2v1c-2 0-2-2-3-2l-1-2z" class="K"></path><path d="M492 291c0-1 3 0 3-1 2-1 2-3 4-4h0v2h0c-1 2 0 3 0 5l-6-1-1-1z" class="O"></path><path d="M499 288v5c1 1 2 1 3 1l-1 1 1 1c-1 0-5-1-7-2-1 0-1-1-2-1h-3l3-1 6 1c0-2-1-3 0-5h0z" class="F"></path><path d="M518 262c2-2 3-3 4-6h1l1 2h2l1 1c-3 1-4 3-7 4h-1l-1-1z" class="H"></path><defs><linearGradient id="g" x1="513.027" y1="271.507" x2="510.856" y2="266.901" xlink:href="#B"><stop offset="0" stop-color="#131413"></stop><stop offset="1" stop-color="#25151a"></stop></linearGradient></defs><path fill="url(#g)" d="M522 265l4-3 2-2c-4 6-8 10-15 12l-8 1c-1 0-2 1-3 0 0 0-1 0-1-1v-1l1-1-1-1v-1h1c2 0 5 0 7-1l2-1 4-2 4-1h1 2c1 0 1-1 2-1-1 1-2 2-2 3z"></path><defs><linearGradient id="h" x1="524.485" y1="262.608" x2="501.404" y2="270.096" xlink:href="#B"><stop offset="0" stop-color="#d50309"></stop><stop offset="1" stop-color="#9e1f25"></stop></linearGradient></defs><path fill="url(#h)" d="M520 263h2c1 0 1-1 2-1-1 1-2 2-2 3-6 4-13 5-20 5l-1-1v-1h1c2 0 5 0 7-1l2-1 4-2 4-1h1z"></path><path d="M468 137l3 2c3 2 7 5 10 7 3 4 7 7 10 11 2 2 5 4 6 7-4-3-7-7-11-10l-1 1v1l6 5h-2-1c0-2-3-3-4-4-1 0-1 0-2 1-1 0-4-3-6-3-3-3-5-6-7-10l1-1v-1c-1-1-2-3-2-4v-2z" class="O"></path><path d="M485 156c-2-2-5-7-6-10 3 2 5 5 7 8l-1 1v1z" class="M"></path><path d="M468 137l3 2v2c1 1 1 2 1 3 1 1 1 2 1 3h-2c0-1-1-2-1-3v-1c-1-1-2-3-2-4v-2z" class="D"></path><path d="M469 145l1-1c0 1 1 2 1 3 2 4 5 6 9 8 2 0 3 1 4 2-1 0-1 0-2 1-1 0-4-3-6-3-3-3-5-6-7-10z" class="U"></path><path d="M522 219h1c0-1 1-2 1-2 0-1 0-1 1-1v-1c0 1 1 1 1 2 2 3 3 6 4 10 2 7 4 14 1 21-1 2-2 3-4 4s-6 1-8 0v-1c1-1 2-2 3-2s3 0 3-1c1 0 1-1 1-1 0-4-2-6-5-8l-2-3c0-2-1-3-1-5-1-3 1-5 2-7l2-2v-1-2z" class="c"></path><path d="M522 222h0c0 2 1 2 0 4h0v-1h-1c0-1 0-1-1-1l2-2z" class="D"></path><path d="M521 235v-1h2l1 5v1c-2-2-2-3-3-5z" class="l"></path><path d="M522 219h1c0-1 1-2 1-2 0-1 0-1 1-1v-1c0 1 1 1 1 2-1 1-1 3-3 4 0 1 0 1-1 1h0v-1-2z" class="E"></path><path d="M522 225v1c-1 3 0 5 1 8h-2v1c-1-1-2-3-1-5v-1c0-1 1-2 2-4z" class="X"></path><path d="M515 224s1 0 2-1c0 0 1 0 1-1 1 0 2-1 3-1v-2h1v2 1l-2 2c-1 2-3 4-2 7 0 2 1 3 1 5l2 3c3 2 5 4 5 8 0 0 0 1-1 1 0 1-2 1-3 1s-2 1-3 2c1-1 1-2 2-2v-1h0v-1c-2-1-4-2-5-2l1 3-1 1-1-2c0-1-1-1-2-2l-1 1 1 2c-1 1-1 2-1 3s1 2 1 2c1 0 2 0 2 1s-1 3 0 4h1l1 1c0 1-1 2 0 3h1l1 1-4 1-4 2-2 1c-2 1-5 1-7 1v-4c-4-1-8-3-11-6-2-2-3-6-2-9 1-4 3-6 6-9 3-1 5-2 8-4 2-1 3-3 6-4 1-1 2 0 3 0l1-1-1-2 1-2h0-2-1c1-1 3-2 5-3z" class="D"></path><path d="M502 252c2-3 3-5 4-9h1v4c0 1-1 1-1 2l-3 4-1-1zm2-11l1 1s1 0 0 1c0 3-1 5-4 7h-1l4-9z" class="X"></path><path d="M502 252l1 1c1 0 1 1 2 1h0c1 1 3 2 3 4h0-1c-2-3-4-3-7-4-1 0-3 0-4-1h1c2-1 3-1 4 0l1-1z" class="Z"></path><defs><linearGradient id="i" x1="505.628" y1="264.793" x2="504.59" y2="267.388" xlink:href="#B"><stop offset="0" stop-color="#45181b"></stop><stop offset="1" stop-color="#311d1c"></stop></linearGradient></defs><path fill="url(#i)" d="M502 264c2 0 5 1 6 1 0 0 1 1 1 2-2 1-5 1-7 1v-4z"></path><path d="M508 243v-1h2c0 1 1 4 2 4l1 2c-1 1-1 2-1 3s1 2 1 2h0v3h0c-1-1-1-1-2-3 0-3-2-7-3-10z" class="G"></path><path d="M504 237h3 1v1c1 1 3 3 3 4h1 0l2 2 2-2h0v-1l1 1h0v2c-1 0-1 0-2-1v4c0-1-1-1-2-2l-1 1c-1 0-2-3-2-4h-2v1c-1-2-4-4-4-6z" class="n"></path><path d="M504 237c2 0 2 0 3 1s1 1 1 2l1 1 1 1h-2v1c-1-2-4-4-4-6z" class="e"></path><path d="M508 237c1-1 2-1 3-2 0 0 1 0 1-1 1 0 2 0 2-1h2v1l-1 1h0c1 1 2 2 2 3h0-2 0-1v2l2 1v1l-2 2-2-2h0-1c0-1-2-3-3-4v-1z" class="f"></path><path d="M512 242c0-1-1-2-1-3v-1h1l2 2 2 1v1l-2 2-2-2z" class="B"></path><path d="M513 253c1 0 2 0 2 1s-1 3 0 4h1l1 1c0 1-1 2 0 3h1l1 1-4 1-4 2v-4-1-8c1 2 1 2 2 3h0v-3h0z" class="X"></path><path d="M511 262h2 1l1 2-4 2v-4z" class="K"></path><path d="M513 253c1 0 2 0 2 1s-1 3 0 4h1c0 1 0 2-1 2-1-1-1-1-1-2h-1c0 1 0 3-1 3h-1v-8c1 2 1 2 2 3h0v-3h0z" class="f"></path><path d="M522 221v1l-2 2c-1 2-3 4-2 7 0 2 1 3 1 5l2 3v1c1 1 1 2 2 3-4-1-6-2-8-5h0 2 0c0-1-1-2-2-3h0l1-1v-1c0-2 0-4 1-6 0-2 3-4 5-6z" class="Q"></path><path d="M521 240c-1 0-3-1-4-2v-4l2 2 2 3v1z" class="V"></path><path d="M514 240v-2h1c2 3 4 4 8 5-1-1-1-2-2-3v-1c3 2 5 4 5 8 0 0 0 1-1 1 0 1-2 1-3 1s-2 1-3 2c1-1 1-2 2-2v-1h0v-1c-2-1-4-2-5-2l1 3-1 1-1-2v-4c1 1 1 1 2 1v-2h0l-1-1v1h0v-1l-2-1z" class="F"></path><path d="M514 240v-2h1c2 3 4 4 8 5 0 0 0 1 1 1 0 2 0 2-1 3-1 0-1-1-2-1v-1c1 1 1 1 2 1v-1s-1-1-2-1l-4-2-1-1v1h0v-1l-2-1z" class="H"></path><path d="M485 167c2-1 4-1 7 0 1 0 6 3 7 3 0 0 0-1 1-1v-1c2 2 4 5 6 7 1 3 2 5 4 7 2 1 3 3 4 5s3 4 3 6c4 5 10 14 9 20 0 1-1 2-1 2v1c-1 0-1 0-1 1 0 0-1 1-1 2h-1-1v2c-1 0-2 1-3 1 0 1-1 1-1 1-1 1-2 1-2 1l-1-1v-1l2-2c-1-1-2 0-3-1 1-1 2-1 3-2l-3-3c-1 1-3 1-4 1-1-1-1-3-2-4h-1c0 2 0 3-1 4v2c-1 2-2 4-4 4-1 1-2 2-3 2-3 1-5-1-7-2v1l-1 1s-1 2-2 3l-5 5h0c-1 2-3 3-4 5 1 1 2 0 3 0l-5 2c-6 0-11-2-16-1-1-1-2-1-3-1-3 0-4 1-6 2l-2 3c-2 1-3 2-4 3l-3 6 1-14c-1 1-3 2-4 3v1c0 1-1 2-2 2 0 1-1 1-2 1-2-4 0-9-1-13v-7s1-3 1-4v-1l-1 1h0-1l1-6h1v-2h1 0s1-1 1-2c1 0 1 1 2 1h0c2-1 2-4 2-5 2-5 6-6 10-8 0-1 1-1 2-2 4-3 5-7 5-12h1c0 2 0 4-1 6 0 0 0 1 1 2v1l2-2v3h1c0-3 0-4 2-7l1 1 1-2s1-2 2-2c0-1 1-2 1-3 1 0 1-1 2-1 0 0 0 1 1 1v2-1c1 0 2-1 4-1h0l1-2h1 3v-1c0-1 0-3-1-4 0-1 1 0 0-1 1-2 2-3 3-3 1-1 1-2 1-2z" class="C"></path><path d="M462 193h1v1h1l-1 7-1-2h-1l1-6z" class="N"></path><path d="M465 186l1 1-2 7h-1v-1c0-3 0-4 2-7z" class="U"></path><path d="M471 188c3 0 4 0 6 1l3 3c1 1 2 2 2 3-1 1-2-1-4-1-2-1-4-2-6-1h-2l-1-1 2-4z" class="L"></path><path d="M450 226c1-3 3-4 6-5-1 1-2 3-1 5 0 1 2 2 3 3l3 3h1v1 1c-2-1-4-1-5-2-2-1-4-2-6-4 0 0 0-1-1-1v-1z" class="n"></path><path d="M461 232h0c-4-1-7-3-9-5v-1h1c1 0 3 3 3 3h2l3 3z" class="I"></path><path d="M466 230h0c3-4 4-11 4-15v-7c0-3-1-7-1-10-1-1-1-3 0-4h1l3 9c1 1 2 1 4 2h-1-2-1l1 1c3 1 5 3 7 4-2 0-6-1-8 0l-2 8v3l-1 1c0 2 0 4-1 5v2c-1 1-2 1-3 1z" class="N"></path><path d="M467 185s1-2 2-2c0-1 1-2 1-3 1 0 1-1 2-1 0 0 0 1 1 1v2-1c1 0 2-1 4-1v1h1c1 1 1 2 1 3h0l2-1h1v1c0 1 1 2 2 3 1 0 1 1 2 1v-1l1 1v1c-1 0-1 0-2 1 2 1 3 3 4 5l-1 1-2-3c-1 1-2 1-3 1l-1 1c0-1-1-2-2-3l-3-3c-2-1-3-1-6-1l1-1h2l-1-1v-1h-1c-1 1-1 1-2 1h-2l-1-1z" class="K"></path><path d="M476 187l1-1c1 0 1 1 1 1s1 1 2 1l-2 1-2-2z" class="g"></path><path d="M481 183h1v1c0 1 1 2 2 3v1 1h-2v-1c-1-1-1-1-1-2h-2l-1-1 1-1 2-1z" class="p"></path><path d="M481 183l1 1v1h-2l-1-1 2-1z" class="X"></path><path d="M473 186l2-2h1v1l-1-1-1 1v1c1 1 1 1 2 1l2 2 2-1 1 1h0l1 2h-1l-1 1-3-3c-2-1-3-1-6-1l1-1h2l-1-1z" class="B"></path><path d="M480 188l1 1h0l1 2h-1c-1-1-2-2-3-2l2-1z" class="X"></path><path d="M481 189h3l1 1h0c2 1 3 3 4 5l-1 1-2-3c-1 1-2 1-3 1l-1 1c0-1-1-2-2-3l1-1h1l-1-2z" class="B"></path><path d="M481 191h1c2 0 3 1 4 2-1 1-2 1-3 1l-1 1c0-1-1-2-2-3l1-1z" class="N"></path><path d="M467 185s1-2 2-2c0-1 1-2 1-3 1 0 1-1 2-1 0 0 0 1 1 1v2-1c1 0 2-1 4-1v1h1c1 1 1 2 1 3h0l-1 1-1 1-1-1v-1h-1l-2 2v-1h-1c-1 1-1 1-2 1h-2l-1-1z" class="f"></path><path d="M477 181h1c1 1 1 2 1 3h0l-1 1-1 1-1-1v-1h-1l-2 2v-1h-1c-1 1-1 1-2 1 1-1 3-2 4-2l3-3z" class="D"></path><path d="M454 216h1c1 0 2-1 3-1l1-1v1l-3 6c-3 1-5 2-6 5v1c1 0 1 1 1 1 2 2 4 3 6 4 1 1 3 1 5 2h2l1 1v1c-1 0-3 0-4-1-1 0-2 0-3 1-3 0-4 1-6 2l-2 3c-2 1-3 2-4 3l-3 6 1-14c0-2 0-4 1-5 1-5 4-9 8-13 0-1 0-1 1-2z" class="W"></path><path d="M448 232h0c0 1 1 1 1 1 1 1 0 5 0 6v1l3-2-2 3c-2 1-3 2-4 3-1-2-1-6 0-8v5c1-1 1-2 1-3 0-2 0-4 1-5v-1z" class="J"></path><path d="M453 218c0 3-4 7-5 10-1 2-2 5-2 8-1 2-1 6 0 8l-3 6 1-14c0-2 0-4 1-5 1-5 4-9 8-13z" class="k"></path><path d="M450 226v1c1 0 1 1 1 1 2 2 4 3 6 4 1 1 3 1 5 2h2l1 1v1c-1 0-3 0-4-1-1 0-2 0-3 1-3 0-4 1-6 2l-3 2v-1c0-1 1-5 0-6 0 0-1 0-1-1h0c0-2 1-4 2-6z" class="C"></path><path d="M459 183h1c0 2 0 4-1 6 0 0 0 1 1 2v1l2-2v3l-1 6h1l1 2c0 1-1 4-1 5h-1c-1 1-2 2-2 3-2 3-3 5-5 7-1 1-1 1-1 2-4 4-7 8-8 13-1 1-1 3-1 5-1 1-3 2-4 3v1c0 1-1 2-2 2 0 1-1 1-2 1-2-4 0-9-1-13v-7s1-3 1-4v-1l-1 1h0-1l1-6h1v-2h1 0s1-1 1-2c1 0 1 1 2 1h0c2-1 2-4 2-5 2-5 6-6 10-8 0-1 1-1 2-2 4-3 5-7 5-12z" class="AA"></path><path d="M437 225h1c0 2 1 4 0 6 0-1 0 0-1-1v-5z" class="n"></path><path d="M461 199h1l1 2c0 1-1 4-1 5h-1c-1 1-2 2-2 3v-1-1h0c1-1 2-2 2-3l1-1c-2 0-2 2-3 3s-1 2-2 2h-1c3-3 4-6 5-9z" class="L"></path><path d="M436 213v-2h1 0c2 3 2 6 2 8l-1 1-1-1h-1v-1l-1 1h0-1l1-6h1z" class="k"></path><path d="M435 213h1l1 6h-1v-1l-1 1h0-1l1-6z" class="o"></path><path d="M460 192l2-2v3l-1 6c-1 3-2 6-5 9l-1 1h0v-1c1-1 1-2 1-4h0l-1 2-3 4v1c-1-1 0-3-1-4h-1c1-1 1-2 2-3l3-6h1 1 0l2-4 1-2z" class="D"></path><path d="M452 204l1-1 1 1c-1 2-2 4-2 5v1 1c-1-1 0-3-1-4h-1c1-1 1-2 2-3z" class="F"></path><path d="M455 198h1 1l-3 6-1-1-1 1 3-6z" class="Q"></path><path d="M459 183h1c0 2 0 4-1 6 0 0 0 1 1 2v1l-1 2-2 4h0-1-1l-3 6c-1 1-1 2-2 3-2 1-4 2-5 4l-1 1v-2h-1c-1 0-1 1-3 0 2-1 2-4 2-5 2-5 6-6 10-8 0-1 1-1 2-2 4-3 5-7 5-12z" class="o"></path><path d="M459 189s0 1 1 2v1l-1 2-1-2 1-3z" class="R"></path><path d="M455 196s2-3 3-4l1 2-2 4h0-1-1v-2z" class="Z"></path><path d="M455 196v2l-3 6c-1 1-1 2-2 3-2 1-4 2-5 4l-1-1c2-3 5-3 6-7h-1c3-2 4-4 6-7z" class="s"></path><path d="M486 193l2 3 1-1c3 1 5 3 7 4l1 1c1 1 3 0 4 0l1 1c1-1 2-1 3-1v1c-1 1-2 2-3 2h-1-1c1 1 2 1 3 2h-3v1h2 0c-1 0-2 0-3 1h1c-1 1-2 1-4 1h-2c1 1 2 2 3 2h0l-1 1c2 0 3 0 5 1h0v1h0 1c0 2 1 1 2 2h1v2c-1 2-2 4-4 4-1 1-2 2-3 2-3 1-5-1-7-2v1l-1 1s-1 2-2 3l-5 5h0c-1 2-3 3-4 5 1 1 2 0 3 0l-5 2c-6 0-11-2-16-1-1-1-2-1-3-1 1-1 2-1 3-1 1 1 3 1 4 1v-1l-1-1h-2v-1-1h2c1 0 1 0 1-1l1-1c1 0 2 0 3-1v-2c1-1 1-3 1-5l1-1v-3l2-8c2-1 6 0 8 0-2-1-4-3-7-4l-1-1h1 2 1s1 0 1 1c1 0 2 1 2 2h1l2 2c2-4 4-7 5-11-1-1-2-2-4-3 1-1 1-1 1-2h-2c1 0 2 0 3-1z" class="I"></path><path d="M481 217s1-1 1-2 1-1 1-2h1c0 2-1 3-1 5-1 1-1 2-1 3-1-1-1-3-1-4z" class="g"></path><path d="M497 203h3c1 1 2 1 3 2h-3c-3 1-5 1-8 1 1-1 3 0 4-1 0-1-1-1-2-1 1 0 2 0 3-1z" class="Z"></path><path d="M481 217c0 1 0 3 1 4 0-1 0-2 1-3h0v7c0 1 1 3 0 5 0-3-1-4-2-6-1-1 0-5 0-7z" class="Y"></path><path d="M488 199c1 0 1 1 1 1h0c1 2 0 2 1 3l4 1c1 0 2 0 2 1-1 1-3 0-4 1-3 0-5 2-7 3-1 1-1 1-2 1 2-4 4-7 5-11z" class="E"></path><path d="M483 218c1-1 1-3 2-4l1 1 1-1v1h0c0 2-1 2 0 4h0c1 2 2 3 3 4 0 0-1 2-2 3l-5 5v-1h0c1-2 0-4 0-5v-7z" class="V"></path><path d="M486 215l1-1v1h0c0 2-1 2 0 4h0c1 2 2 3 3 4 0 0-1 2-2 3v-1l-1-1-1-9z" class="K"></path><path d="M486 193l2 3 1-1c3 1 5 3 7 4l1 1c1 1 3 0 4 0l1 1c1-1 2-1 3-1v1c-1 1-2 2-3 2h-1-1-3c-1 1-2 1-3 1l-4-1c-1-1 0-1-1-3h0s0-1-1-1c-1-1-2-2-4-3 1-1 1-1 1-2h-2c1 0 2 0 3-1z" class="G"></path><path d="M489 200l8 3c-1 1-2 1-3 1l-4-1c-1-1 0-1-1-3z" class="I"></path><path d="M497 200c1 1 3 0 4 0l1 1c1-1 2-1 3-1v1c-1 1-2 2-3 2h-1v-1c-1 0-2 0-3-1h-2l1-1z" class="j"></path><path d="M488 196l1-1c3 1 5 3 7 4l1 1-1 1-8-5z" class="C"></path><path d="M485 214v-1c3-3 6-4 9-5 1 1 2 2 3 2h0l-1 1c2 0 3 0 5 1h0v1h0 1c0 2 1 1 2 2h1v2c-1 2-2 4-4 4-1 1-2 2-3 2-3 1-5-1-7-2v1l-1 1c-1-1-2-2-3-4h0c-1-2 0-2 0-4h0v-1l-1 1-1-1z" class="U"></path><path d="M501 213h1c0 2 1 1 2 2h1v2c-1 2-2 4-4 4h0c0-1 0-2 1-3 0-1 1-2 1-2v-1c-2 0-4 1-5 2l-1-1c1-1 3-1 4-3z" class="v"></path><path d="M490 217c0 1 0 1 1 1 3-2 6-4 10-5h0c-1 2-3 2-4 3l1 1c-2 0-6 2-7 3v1 1l-2-2h0c0-1 1-2 1-3z" class="l"></path><path d="M496 211c2 0 3 0 5 1h0v1c-4 1-7 3-10 5-1 0-1 0-1-1l1-2c1-2 3-3 5-4z" class="B"></path><path d="M485 214v-1c3-3 6-4 9-5 1 1 2 2 3 2h0l-1 1c-2 1-4 2-5 4l-1 2c0 1-1 2-1 3h0l2 2-1 1c-1-1-2-2-3-4h0c-1-2 0-2 0-4h0v-1l-1 1-1-1z" class="E"></path><path d="M471 218l2-8c1 2 1 2 3 3h0l3-1v1c1 0 1-1 2-1v1h0l-1 1c-2 4-1 9-1 13 1 1 1 3 1 4-1 1-2 2-3 2-4 2-9 2-13 1h-2v-1-1h2c1 0 1 0 1-1l1-1c1 0 2 0 3-1v-2c1-1 1-3 1-5l1-1v-3z" class="G"></path><path d="M469 229h1l1 1h0c1-1 1-1 2-1h1c-3 3-6 4-10 4h-2v-1h2c1 0 1 0 1-1l1-1c1 0 2 0 3-1z" class="P"></path><path d="M474 224h0c1-2 1-2 1-4 0 2 1 5 1 7v1c0 1-1 1-2 1h-1c-1 0-1 0-2 1h0l-1-1h-1v-2c1-1 1-3 1-5l1 3h2l1-1z" class="K"></path><path d="M469 227l2-1 1 1-2 2h-1v-2z" class="B"></path><path d="M474 224c0 1 1 2 1 3l-1 1h-1v-3l1-1z" class="v"></path><path d="M471 218l2-8c1 2 1 2 3 3h0l3-1v1c1 0 1-1 2-1v1h0-1c-3 2-4 4-5 7 0 2 0 2-1 4h0l-1 1h-2l-1-3 1-1v-3z" class="H"></path><path d="M471 218c1 0 1-1 1-1h2c-1 2-2 3-3 4v-3z" class="K"></path><path d="M471 218l2-8c1 2 1 2 3 3h0 1l-3 4h-2s0 1-1 1z" class="a"></path><path d="M485 167c2-1 4-1 7 0 1 0 6 3 7 3 0 0 0-1 1-1v-1c2 2 4 5 6 7 1 3 2 5 4 7 2 1 3 3 4 5s3 4 3 6c4 5 10 14 9 20 0 1-1 2-1 2v1c-1 0-1 0-1 1 0 0-1 1-1 2h-1-1v2c-1 0-2 1-3 1 0 1-1 1-1 1-1 1-2 1-2 1l-1-1v-1l2-2c-1-1-2 0-3-1 1-1 2-1 3-2l-3-3c-1 1-3 1-4 1-1-1-1-3-2-4h-1c0 2 0 3-1 4v2-2h-1c-1-1-2 0-2-2h-1 0v-1h0c-2-1-3-1-5-1l1-1h0c-1 0-2-1-3-2h2c2 0 3 0 4-1h-1c1-1 2-1 3-1h0-2v-1h3c-1-1-2-1-3-2h1 1c1 0 2-1 3-2v-1c-1 0-2 0-3 1l-1-1c-1 0-3 1-4 0l-1-1c-2-1-4-3-7-4-1-2-2-4-4-5 1-1 1-1 2-1v-1l-1-1v1c-1 0-1-1-2-1-1-1-2-2-2-3v-1h-1l-2 1h0c0-1 0-2-1-3h-1v-1h0l1-2h1 3v-1c0-1 0-3-1-4 0-1 1 0 0-1 1-2 2-3 3-3 1-1 1-2 1-2z" class="C"></path><path d="M505 201h1c0 1 0 2-1 2 0 1-1 1-2 2-1-1-2-1-3-2h1 1c1 0 2-1 3-2z" class="H"></path><path d="M496 177h1l6-3-3 4-1 2-1-1h-1v-1l-1-1z" class="V"></path><path d="M504 187c-1-1-2-1-4-1-1-1-2-2-2-3h1 0c5 2 8 2 10 8h0l-1-1c-1-1-2-2-4-3z" class="Q"></path><path d="M512 192h1c1 1 2 1 2 3h0c2 3 5 7 6 11h-1c-1-2-2-5-4-7s-3-4-4-7z" class="N"></path><path d="M481 172c1-2 2-3 3-3 0 2-1 3 1 6l2 4h-2v-1c0-1-1-1-1-1h-2 0c0-1 0-3-1-4 0-1 1 0 0-1z" class="g"></path><path d="M481 173h2l1 4h-2 0c0-1 0-3-1-4z" class="P"></path><path d="M499 180l1-2c1 1 1 1 1 2-1 0-2 1-3 1v1h1v1h-1c0 1 1 2 2 3 2 0 3 0 4 1h0-1-3-4c-2 1-5-2-6-2-1-1-1-1-2-1v-1h3l8-3z" class="B"></path><path d="M496 187h4 3 1c1 1 2 3 2 4v1l-1 1c0 3 2 5 1 8h-1v-1c-1 0-2 0-3 1l-1-1c1-1 2-1 2-3 0-1-2-3-3-4l1-1-5-5z" class="e"></path><path d="M496 187h4c1 1 2 2 3 4l1 1c1 2 1 2 1 4-1-1-3-4-4-4l-5-5z" class="N"></path><path d="M484 177s1 0 1 1v1h2 2c1-1 5-2 7-2l1 1v1h1l1 1c-2 1-5 2-8 3h-3v1c1 0 1 0 2 1-1 0-3 2-3 3l-1-1v1c-1 0-1-1-2-1-1-1-2-2-2-3v-1h-1l-2 1h0c0-1 0-2-1-3h-1v-1h0l1-2h1 3v-1h0 2z" class="W"></path><path d="M478 181l7 1c-1 1-2 1-3 2v-1h-1l-2 1h0c0-1 0-2-1-3z" class="P"></path><path d="M484 177s1 0 1 1v1h2 2c-1 1-2 1-2 2-4-1-7-2-10-1l1-2h1 3v-1h0 2z" class="Y"></path><path d="M484 177s1 0 1 1h-1c-1 0-1 0-2-1h2z" class="K"></path><path d="M489 179c1-1 5-2 7-2l1 1v1c-2 1-4 1-6 1-1 1-3 1-4 1 0-1 1-1 2-2z" class="R"></path><path d="M497 178v1c-2 1-4 1-6 1l1-1c2 0 3-1 5-1z" class="P"></path><path d="M485 182h1c1 0 3 1 5 1h-3v1c1 0 1 0 2 1-1 0-3 2-3 3l-1-1v1c-1 0-1-1-2-1-1-1-2-2-2-3 1-1 2-1 3-2z" class="B"></path><path d="M486 182c1 0 3 1 5 1h-3v1c1 0 1 0 2 1-1 0-3 2-3 3l-1-1v-1-1c0-2 1-2 0-3z" class="X"></path><path d="M510 182c2 1 3 3 4 5s3 4 3 6c4 5 10 14 9 20 0 1-1 2-1 2v1c-1 0-1 0-1 1 0 0-1 1-1 2h-1-1v2c-1 0-2 1-3 1 0 1-1 1-1 1-1 1-2 1-2 1l-1-1c3 0 4-4 7-6h0c2-1 2-2 2-4-1-2-2-4-3-7h1c-1-4-4-8-6-11h0c0-2-1-2-2-3h-1c-2-2-2-6-3-9l1-1z" class="f"></path><path d="M510 182c2 1 3 3 4 5s3 4 3 6v1 1l2 2c1 2 2 5 3 7h1v1c0 1 1 1 1 2v1h-1v-1l-3-6c-1-2-3-5-4-6h-1 0c0-2-1-2-2-3h-1c-2-2-2-6-3-9l1-1z" class="e"></path><path d="M490 185c1 0 4 3 6 2l5 5-1 1c1 1 3 3 3 4 0 2-1 2-2 3-1 0-3 1-4 0l-1-1c-2-1-4-3-7-4-1-2-2-4-4-5 1-1 1-1 2-1v-1c0-1 2-3 3-3z" class="l"></path><path d="M495 192c-2-1-2-4-2-6l3 3c0 1 0 2-1 2v1z" class="G"></path><path d="M496 189l4 4c1 1 3 3 3 4 0 2-1 2-2 3-1 0-3 1-4 0l-1-1c1-1 1-2 1-2 0-2-2-4-2-5v-1c1 0 1-1 1-2z" class="U"></path><path d="M500 207h1c3-1 6-3 7-6 2-3 2-6 2-9 0 1 1 1 1 2v6c0 3 2 5 3 7v1l1-1h1c-1 1-1 2-2 2l-1-1h-1v1l1 1s0 1-1 1h-1c0 1 1 2 0 3h2c-1 1-3 1-4 1-1-1-1-3-2-4h-1c0 2 0 3-1 4v2-2h-1c-1-1-2 0-2-2h-1 0v-1h0c-2-1-3-1-5-1l1-1h0c-1 0-2-1-3-2h2c2 0 3 0 4-1z" class="a"></path><path d="M499 210v-1h2c1-1 1 0 1-1 1 0 2 0 3 1v3h-1l-2-2h-3z" class="Y"></path><path d="M494 208h2c2 1 2 1 3 2h3l2 2v1h-2 0-1 0v-1h0c-2-1-3-1-5-1l1-1h0c-1 0-2-1-3-2z" class="f"></path><path d="M506 211l1-1h1v1c0-1 1-2 1-2l2 2c0 1 1 2 0 3h2c-1 1-3 1-4 1-1-1-1-3-2-4h-1z" class="R"></path><path d="M145 285v2h5c4 0 7 1 10 2l-1 1h-3 0c0 1-1 1-2 1l1 1h3c3 1 6 3 8 6 0 2 1 2 0 4-2 5-7 9-12 11h-1v1c5-1 10-4 14-7v-1h1c0 2-1 3-2 4v1l1-1c0-1 1-1 2-2h0l3-6v81 58c1 23 3 45 9 67 4 13 8 26 15 37l9 15-1 1c3 4 6 9 9 12 4 4 9 7 13 12l16 11 1 1c1 1 2 1 2 3 0 1 1 2 2 3-1 0-1 1-2 2h0 0c1 2 2 4 2 6 1 2 1 4 1 6l-1 2c-4-3-8-6-11-10l1-2h-1-1c-1 0-2-1-3-1l-2-1h-1l-1-1c0-1-1-2-2-3h-2c-1 1-2 1-2 1 1 0 4 1 5 2h0l-1 1c-1 0-1-1-2-1h-2l-1 1c-1 0-3-1-5-1-5-1-9-3-13-5-2-1-4-1-7-1h-7l-4 1h-2c1 0 1-1 1-1h-2-1v-1h-6v1h-1v1c-1 0-2 1-2 1-3-2-6-3-9-6h1 1c1-1 1-1 1-2-5-1-9-4-13-8h-1c0 1 0 2 1 2l-1 1h-1c0-2 0-2-1-4-2 0-5-3-6-4-3-3-4-7-6-11-1 0-1 0-1-1l-3-4 1 5h-1l-3-3h0-1l-2 1c-3-3-7-6-11-10-2-2-4-5-6-7l-1-1h-2l4 6h-1c-2-2-3-4-5-7-6-8-12-18-18-27-1 0-1-2-1-3h0l1 1c1 1 2 2 3 2h0l-4-7-1-2-1 1-1-1c-1-3-3-6-4-8 0-1 0-2-1-3l-2-4c-1-2-1-7-1-10-1-6-3-12-5-18l2-1v2h0c1-2 1-5 1-6s1-1 1 0h0c1 0 1-4 1-5 3-6 5-11 8-17 4-5 8-11 13-16h0 1c2-1 3-3 5-4l9-9-1-1v-2-3l-1-1c0-2-1-1-2-2h-2-1l-4-1c-2-1-4-3-5-5s-2-4-1-6l-3-6-1-1-1 1v-2c0-2-1-3-1-4-2-3-5-4-8-5-5 1-7 2-11 6h0c0-4 12-17 15-20 0 0 0-1-1-1h0v-1c0-1 1-1 2-2 2-1 3-3 5-5 0 0 0-1 1-1v1h1v-1h2 1v2h1l1-1c0-1 0-2 1-3l1-1s1-1 1-2l1 1 4-2c1 0 0 0 1-1h-2c-3-1-4-3-5-5v-1c2 1 3 1 5 2h2 1 1l1-2c2-2 4-6 7-7l1 1c0-1 0-1 1-1h0c1-1 1-1 1-2l1-1v-1h-1c2-2 2-3 2-6l1-1 2-1c-2 0-2 0-3-1 1 0 4 0 5-1h-1v-1h-1 0v-2l1-1c0-2 1-4 1-6v-2-1c1-1 0-4 0-5h-1v-1h2 0 1c1-2 4-3 6-3 1 0 2-1 2-1v-1c1 0 1-1 2-1v-1c1 1 1 1 2 1l1-1c1-2 1-3 3-5z" class="m"></path><path d="M210 582h1l1 1-1 1-1-1v-1z" class="k"></path><path d="M120 330h2c1 1 1 2 1 3h0l-3-3z" class="I"></path><path d="M204 592c0-1-1-2 0-3s1-1 2-1c0 1-1 1 0 3-1 0-1 0-2 1h0z" class="H"></path><path d="M118 331c0-1 0-1 1-1h0 1l3 3-2-1h0l-1 1-2-2z" class="F"></path><path d="M164 427c1 1 2 1 3 2 0 0 1 0 1 1h0v1l-6-2 2-2zm-3-122h-1c-2 1-2 1-4 0v-1l3-1h1c1 1 1 1 1 2z" class="W"></path><path d="M173 585c2 0 3 0 5 1h0c-2 1-5 1-7 0l2-1z" class="p"></path><path d="M166 592l6 1-1 1s-1 0-1 1c-2-1-3-1-5-1 1-1 1-1 1-2z" class="S"></path><path d="M146 303c3 0 4 2 6 3-2 0-5-1-8 0 0-1-1-1-1-1 1-1 1-1 3-1v-1z" class="F"></path><path d="M208 589h1 2c2 0 3 1 5 2l-1 1-2 1-5-4z" class="P"></path><path d="M133 307s1-1 2-1h0c0 1 0 1 1 2l-6 5v-1l1-2 2-3z" class="F"></path><path d="M161 580c4 2 7 4 12 5l-2 1c-4-1-7-3-11-5l1-1z" class="X"></path><path d="M155 319c3 0 5 1 7 1h0-4l1 1c-1 1 0 3 0 4-1 0-1 0-2-1h0c0-1-1-2-1-3-1-1-2-1-3-2h2z" class="d"></path><path d="M123 333h2v-2c1 1 1 2 2 4 1 0 2 1 3 2h-1-2c-2-1-3-2-4-4z" class="a"></path><path d="M160 448c0-2-1-3-2-4 1 0 3-1 3-1 1-2 1-2 1-4-1 0-2-1-2-2h0c1 0 2 2 3 3 0 3-1 6-2 8h-1z" class="M"></path><path d="M159 303c1-1 2-2 2-3s-1-1-1-2h0c0-2-2-3-3-4v-1c2 1 4 3 5 5 0 1 1 3 0 5 0 1-1 1-1 2 0-1 0-1-1-2h-1z" class="q"></path><path d="M172 593c5 0 10-1 15-2-5 3-12 4-17 4 0-1 1-1 1-1l1-1z" class="Q"></path><path d="M202 572h0c0 2 0 4 1 6h1l1 1v-1h0l1-2c0 3-1 5-3 8-2-4-2-9-1-12z" class="S"></path><path d="M126 315c1-2 2-5 3-6h1l1 1-1 2v1c-1 1-2 3-3 3h-1v-1z" class="e"></path><path d="M232 592c3 1 4 1 5 3 1 1 2 1 3 3h-1l1 2c1 1 2 3 2 5h-1c0-4-2-5-5-8-1-1-2-3-4-5z" class="d"></path><path d="M129 328l1 1v1h1v5 1l1 1-1 1h-1l-1 2-2-3h2 1c-1-1-2-2-3-2 1-1 2-1 2-2v-5z" class="P"></path><path d="M130 329v1h1v5h-1c-1-2-1-4 0-6z" class="j"></path><path d="M162 320c3-1 5-1 7-2h0v3 1h-1v-2l-3 1c-2 1-5 0-6 0l-1-1h4 0z" class="L"></path><path d="M168 320l1-1v2 1h-1v-2z" class="U"></path><path d="M242 596l1 1c1 1 2 1 2 3 0 1 1 2 2 3-1 0-1 1-2 2h0c-1-3-4-5-5-7h1c0-1 0-1 1-2h0z" class="T"></path><path d="M167 307v-1h1c0 2-1 3-2 4v1l1-1c0-1 1-1 2-2-1 3-3 4-5 5-3 2-9 4-12 3 0-1 5-2 6-2l6-3c1-1 3-3 3-4z" class="W"></path><path d="M226 585l16 11h0c-1 1-1 1-1 2h-1 0c-1-2-2-2-3-3-1-2-2-2-5-3-2-2-4-4-7-6 0-1 0-1 1-1z" class="r"></path><path d="M146 437l5-1c1 1 0 2 0 2 1 1 4 1 5 2-1 0-3 1-5 1h-3l-2-2 1-1h-1v-1h0z" class="L"></path><path d="M216 591c1 1 3 2 4 4 2 1 4 5 6 5-1 0-3 1-4 0h-2l-4-3v-1c1 1 1 1 2 1 0 1 0 1 1 1h0 1v-1l-3-2v-1c0-1-1-2-2-2l1-1z" class="H"></path><path d="M139 308c0-1 1-1 1-2 2-3 4-5 7-6h0 2c1-1 2 0 4 0l-1 1c-2 1-4 1-6 2h0v1c-2 0-2 0-3 1 0 0 1 0 1 1-1 0-2 0-3 1l-1 1h-1z" class="Q"></path><path d="M149 573c1-1 2-1 3-1l2 1-2 1s-1 0-1 1c-1 4 0 6 2 9h-1c0 1 0 2 1 2l-1 1h-1c0-2 0-2-1-4-2-2-2-5-2-8h0l1-2z" class="W"></path><path d="M149 573c1-1 2-1 3-1l2 1-2 1s-1 0-1 1c-1-1 0-1-1-2l-2 2 1-2z" class="L"></path><path d="M140 444c3 3 7 7 11 7 3 0 5 0 7-2 1 0 1-1 2-1h1l-1 1c-1 1-1 1-3 2h0v1h3l-1 1h0 0c-5 0-10-1-15-4l-2-1-2-3h0v-1z" class="Q"></path><path d="M167 416h1c1-1 1-3 0-5 0-4 0-7 1-11l1 31-1 1-1-1v-1h0c0-1-1-1-1-1v-4-9z" class="o"></path><path d="M133 307c0-1 1-2 2-3l5-5c5-5 9-8 16-9 0 1-1 1-2 1l1 1h3c-6 0-11 1-15 6-2 1-4 3-5 5s-1 3-2 5c-1-1-1-1-1-2h0c-1 0-2 1-2 1z" class="V"></path><path d="M165 573c0 1 1 2 1 3l-1 1-2-2v1c0 1 2 2 3 3h3c2 1 3 3 5 4l-1 1c-5-2-9-3-12-7v-2h-1c1-1 1-1 2 0h1 0v-1c1 0 1-1 2-1z" class="E"></path><path d="M125 318l1 1c0 3 0 6-1 9l-1 1c-1 0-2 0-4-1l1-1v-1h-1c2-2 2-3 2-6l1-1 2-1z" class="g"></path><path d="M123 325v-3l1-1v3c0 2-1 3 0 4h-1v-3h0z" class="H"></path><path d="M123 325h0v3h1v1c-1 0-2 0-4-1l1-1c1 0 1-2 2-2z" class="J"></path><path d="M206 588c1 0 2 0 2 1l5 4 2-1c1 0 2 1 2 2v1l3 2v1h-1 0c-1 0-1 0-1-1-1 0-1 0-2-1v1l-3-2c-1-1-3-1-4-2-2 0-2-2-3-2-1-2 0-2 0-3z" class="R"></path><path d="M215 592c1 0 2 1 2 2v1l-4-2 2-1z" class="B"></path><path d="M206 588c1 0 2 0 2 1h0c0 1 0 2 1 3h1c2 1 4 3 6 4v1l-3-2c-1-1-3-1-4-2-2 0-2-2-3-2-1-2 0-2 0-3z" class="O"></path><path d="M198 573c0 1 0 2-1 3 0 2-1 4-3 6-1 1-2 1-4 2h0-1c-2 1-5 3-8 3v-1h0c2 0 6-1 7-3h-2v-1h1 1c2-1 4-3 6-4l1-2v-1-1c1 0 2-1 3-1z" class="u"></path><path d="M189 584c1-2 2-3 4-3l1 1c-1 1-2 1-4 2h0-1z" class="B"></path><path d="M198 573c0 1 0 2-1 3s-3 3-5 4l2-2 1-2v-1-1c1 0 2-1 3-1z" class="E"></path><path d="M142 440c2 1 4 2 6 2h0 0v-1h3v1l5 2c-4 2-7 3-10 2-2-1-3-2-5-4v-1l1-1z" class="V"></path><path d="M142 440c2 1 4 2 6 2h0v1c-2 1-3 0-4-1h-3v-1l1-1z" class="F"></path><path d="M210 599c1-1 2-1 1-3v-1h2l3 2 4 3h2c1 1 3 0 4 0 2 1 2 3 4 5h-1l-1-1c0-1-1-2-2-3h-2c-1 1-2 1-2 1-1 1-2 1-3 1-3-2-6-3-9-4z" class="U"></path><path d="M131 316l1 1c1 1 2 0 3 0 0 2-1 3-1 4s-1 2-1 3c-1 1-1 2-1 3l-1 3h-1v-1l-1-1v-5-5c0-1 1-2 2-2z" class="N"></path><path d="M129 323c0 1 1 3 2 3 0 1 0 1 1 1l-1 3h-1v-1l-1-1v-5z" class="E"></path><path d="M204 592c1-1 1-1 2-1s1 2 3 2c1 1 3 1 4 2h-2v1c1 2 0 2-1 3l-8-3v-1h0-2c-1 0-1-1-1-2 1 0 1 0 2 1h0c1-1 2-1 2-1h1v-1h0z" class="k"></path><path d="M204 592c1-1 1-1 2-1s1 2 3 2l-1 1c-2-1-3-1-4-2z" class="p"></path><path d="M208 594l1-1c1 1 3 1 4 2h-2v1c1 2 0 2-1 3l-8-3v-1l1 1h1c1 1 5 1 6 1 0-1-1-2-2-3z" class="F"></path><path d="M175 597c-1 0-1 0-1-1h0c8-1 16-4 25-3 0 1 0 2 1 2h2 0v1c-2 0-4-1-6-1-5-1-11 1-15 2h-6z" class="L"></path><path d="M156 567c2 0 4 1 5 2l4 4c-1 0-1 1-2 1v1h0-1c-1-1-1-1-2 0v5h1l-1 1c-3-2-4-6-8-7l2-1-2-1 3-2v-1-1l1-1z" class="N"></path><path d="M161 569l4 4c-1 0-1 1-2 1v1h0c0-2-2-3-2-6z" class="V"></path><path d="M152 572l3-2c1 2 2 3 3 4v1c-1 1-1 1-2 0-1 0-2-1-2-2l-2-1z" class="D"></path><path d="M144 318c3 0 6 0 9 1 1 1 2 1 3 2h0-1l-2-1c-3-1-8-1-10-1-2 1-2 2-3 3s-2 1-2 1c-2 2-4 4-4 7-1 3 1 6 2 8h1v1h0l-5-2-1-1v-1-5l1-3c0-1 0-2 1-3 0-1 1-2 1-3 3-2 6-3 10-3z" class="C"></path><path d="M165 321l3-1v2h1l-1 16c0-1 0-1-2-2 0-1 0-1-1-2-1-2-1-5-2-7-2 0-3 0-4-2 0-1-1-3 0-4 1 0 4 1 6 0z" class="F"></path><path d="M165 321l3-1v2c-1 4-1 8-1 12h-1v-8-3c0-1-1-1-1-1v-1z" class="R"></path><path d="M159 321c1 0 4 1 6 0v1 7 5c-1-2-1-5-2-7-2 0-3 0-4-2 0-1-1-3 0-4z" class="C"></path><path d="M181 597c4-1 10-3 15-2 2 0 4 1 6 1l8 3c3 1 6 2 9 4 1 0 2 0 3-1 1 0 4 1 5 2h0l-1 1c-1 0-1-1-2-1h-2l-1 1c-1 0-3-1-5-1-5-1-9-3-13-5-2-1-4-1-7-1h-7l-4 1h-2c1 0 1-1 1-1h-2-1v-1z" class="D"></path><path d="M184 598c1-1 3-1 4-1l1 1-4 1h-2c1 0 1-1 1-1z" class="X"></path><path d="M188 597c5-1 8-1 12 0h-3l-1 1h-7l-1-1z" class="E"></path><path d="M196 598l1-1h3l13 4c2 1 4 2 6 2h0c1 0 2 0 3-1 1 0 4 1 5 2h0l-1 1c-1 0-1-1-2-1h-2l-1 1c-1 0-3-1-5-1-5-1-9-3-13-5-2-1-4-1-7-1z" class="f"></path><path d="M145 285v2h5c4 0 7 1 10 2l-1 1h-3 0c-7 1-11 4-16 9l-5 5c-1 1-2 2-2 3l-2 3-1-1 7-9h0-2c3-3 6-5 7-10 1-2 1-3 3-5z" class="C"></path><path d="M145 285v2 1c-2 3-3 7-6 10-1 1-1 2-2 2h0-2c3-3 6-5 7-10 1-2 1-3 3-5z" class="g"></path><path d="M140 308h9l1 1-1 1c-3 0-7 1-9 3 3 1 5 1 8 3h3c1 1 3 2 4 3h-2c-3-1-6-1-9-1-4 0-7 1-10 3 0-1 1-2 1-4-1 0-2 1-3 0l-1-1 8-8h1z" class="L"></path><path d="M148 316h3c1 1 3 2 4 3h-2c-3-1-6-1-9-1 1-1 2 0 3-1h0 1c1 1 1 1 2 0-1-1-1-1-2-1h0z" class="W"></path><path d="M139 290c1 1 1 1 2 1l1-1c-1 5-4 7-7 10h2 0l-7 9h-1c-1 1-2 4-3 6h-1 0v-2l1-1c0-2 1-4 1-6v-2-1c1-1 0-4 0-5h-1v-1h2 0 1c1-2 4-3 6-3 1 0 2-1 2-1v-1c1 0 1-1 2-1v-1z" class="m"></path><path d="M139 290c1 1 1 1 2 1-1 1-2 2-4 2v-1c1 0 1-1 2-1v-1z" class="B"></path><path d="M135 300h2 0l-7 9h-1l3-5c0-1 0-1 1-2v-2h0c1 0 1 1 2 1v-1z" class="V"></path><path d="M130 428l1-3v-1c4 5 10 7 15 9 1 0 2 1 3 2h-3v1 1h0v1h1l-1 1 2 2v1h0 0c-2 0-4-1-6-2l-1 1-2-1v1l-3-3v-3c-2-1-4-3-6-4v-1l-2-2 1-1 1 1z" class="M"></path><path d="M128 428l1-1 1 1c1 3 6 5 9 7 3 1 5 2 7 4l2 2v1h0 0c-2 0-4-1-6-2l-1 1-2-1v1l-3-3v-3c-2-1-4-3-6-4v-1l-2-2z" class="D"></path><path d="M136 435l6 5-1 1-2-1v1l-3-3v-3z" class="d"></path><path d="M202 572c-1-1 0-5 0-6-1-1-1-3-1-4-1-4-3-8-4-12 3 3 4 7 7 10v1c3 4 6 9 9 12 4 4 9 7 13 12-1 0-1 0-1 1-2-1-4-3-5-4-3-3-6-5-9-7l-1 1v3l-1-1h-1c-2 0 0-4-2-5v3l-1 2h0v1l-1-1h-1c-1-2-1-4-1-6h0z" class="h"></path><defs><linearGradient id="j" x1="167.175" y1="338.013" x2="143.098" y2="340.709" xlink:href="#B"><stop offset="0" stop-color="#d90000"></stop><stop offset="1" stop-color="#a51e26"></stop></linearGradient></defs><path fill="url(#j)" d="M143 319c2 0 7 0 10 1l2 1h1 0c0 1 1 2 1 3h0c1 1 1 1 2 1 1 2 2 2 4 2 1 2 1 5 2 7 1 1 1 1 1 2 2 1 2 1 2 2l1 12v17c-1-1-1-2-2-3l-4-8s-1 1-1 2l-1-2-3 3c-1 0-3 0-4-1l-2-1c-2 2-5 2-7 3 0-1 1-1 2-2h0v-2c-1 0-1-1-2-1v-1h-1v-1c1 0 2 0 3-1h0-3v-1l3-4c-1-1-2-2-3-2s-4 2-5 3v-1c1-1 2-3 2-4h-1l-1-1h1l1-1-1-1-2-1v-1h-1-1c-1-2-3-5-2-8 0-3 2-5 4-7 0 0 1 0 2-1s1-2 3-3z"></path><path d="M159 347h3v1l1-1h1l1 1c0 1 0 1-1 1h-2c-1 0-2 0-3-1v-1z" class="r"></path><path d="M140 322c1-1 1-2 3-3 2 2 4 2 5 2h1v1c-2 0-4 0-5 2h-1l1-1v-1h-1c-1 0-2 1-3 0z" class="R"></path><path d="M143 319c2 0 7 0 10 1l2 1h1 0c0 1 1 2 1 3h0c-3-1-5-1-8-2v-1h-1c-1 0-3 0-5-2z" class="a"></path><path d="M140 325h0c2 2 3 3 4 5h0c2-2 2-2 4-3 2 0 3 1 5 2 1 1 2 3 2 6 0 1-1 2-1 3-1 1-3 3-4 3h-1c0 1-1 0-2 0h0 1c1-2 4-4 4-6s-1-3-2-4-2-1-3-1c-1 1-2 2-3 2l-4-7z" class="B"></path><path d="M158 345c1 1 1 1 1 2v1c1 1 2 1 3 1l1 1c2 1 4 3 4 6l1 2 1-8v17c-1-1-1-2-2-3l-4-8c-1-2-1-2-1-3l-4-2h-1c0-1 0-2 1-2v-4z" class="Q"></path><path d="M158 351c2-1 3-1 5-1 3 4 3 8 5 13l-1 1-4-8c-1-2-1-2-1-3l-4-2z" class="T"></path><path d="M150 341c1 0 3-2 4-3h0s1 0 1 1c1 1 1 3 2 4l1 2v4c-1 0-1 1-1 2l-2-1c-1 0-2-1-2-1-2-1-3-3-5-5-1 0-1 0-1-1 0 0 1 0 2-1h0l1-1z" class="X"></path><path d="M149 342c2 0 2 0 3 1v1l-3-1v-1h0z" class="e"></path><path d="M155 346l3 3c-1 0-1 1-1 2l-2-1-1-2c0-1 0-1 1-2z" class="f"></path><path d="M150 341c1 0 3-2 4-3h0s1 0 1 1c1 1 1 3 2 4l1 2v4l-3-3h-1l-2-3c-1-1-1-1-3-1l1-1z" class="R"></path><path d="M152 343v-1c1 0 2 0 3 1 0 1 1 1 0 2l-1 1-2-3z" class="I"></path><path d="M155 343h2l1 2v4l-3-3h-1l1-1c1-1 0-1 0-2z" class="P"></path><path d="M138 323c1 1 1 2 2 3v-1l4 7c1 0 2-1 3-2 1 0 2 0 3 1s2 2 2 4-3 4-4 6h-1 0 0v-1h-2-5l-2-1v-1h-1-1c-1-2-3-5-2-8 0-3 2-5 4-7z" class="D"></path><path d="M144 332c1 0 2-1 3-2 1 0 2 0 3 1s2 2 2 4-3 4-4 6h-1 0 0v-1h-2c2 0 3-1 4-2s1-3 1-4c-1-1-2-2-3-2v1l3 2c-2 0-4 0-5-2h0-1 0v-1z" class="F"></path><path d="M138 323c1 1 1 2 2 3v-1l4 7v1h0c1 2 0 4-1 6-2-3-4-5-6-8l1 7h-1-1c-1-2-3-5-2-8 0-3 2-5 4-7z" class="M"></path><defs><linearGradient id="k" x1="139.767" y1="349.791" x2="160.198" y2="350.315" xlink:href="#B"><stop offset="0" stop-color="#141414"></stop><stop offset="1" stop-color="#341b1c"></stop></linearGradient></defs><path fill="url(#k)" d="M140 340h5 2v1h0c1 0 2 1 2 0h1l-1 1h0c-1 1-2 1-2 1 0 1 0 1 1 1 2 2 3 4 5 5 0 0 1 1 2 1l2 1h1l4 2c0 1 0 1 1 3 0 0-1 1-1 2l-1-2-3 3c-1 0-3 0-4-1l-2-1c-2 2-5 2-7 3 0-1 1-1 2-2h0v-2c-1 0-1-1-2-1v-1h-1v-1c1 0 2 0 3-1h0-3v-1l3-4c-1-1-2-2-3-2s-4 2-5 3v-1c1-1 2-3 2-4h-1l-1-1h1l1-1-1-1z"></path><path d="M140 340h5 2v1c-2 1-5 2-7 1h0l1-1-1-1z" class="Q"></path><path d="M162 353c0 1 0 1 1 3 0 0-1 1-1 2l-1-2v-1h-4c1-2 3-1 5-2z" class="E"></path><path d="M145 354c2 1 5 3 7 3-2 2-5 2-7 3 0-1 1-1 2-2h0v-2c-1 0-1-1-2-1v-1z" class="G"></path><path d="M155 350l2 1h1l4 2c-2 1-4 0-5 2h-1c-1 0-1-1-2-1-2-1-3 0-4-1 1-1 2-1 3-1 1-1 1-1 1-2h1z" class="n"></path><path d="M163 356l4 8c1 1 1 2 2 3v1c1 3 1 7 0 10v6 16c-1 4-1 7-1 11 1 2 1 4 0 5h-1v9 4c-1-1-2-1-3-2l-2 2-3-1h-7c-4 1-11 1-15-1s-5-5-6-9c-1 1-1 2-1 3 0-2-1-3-1-5h-1c0-1 0-3 1-4h1c1-5 3-7 7-11l3-2c0-1 0-1-1-2 3-1 5-2 7-3 1-1 3-1 5-2 1-1 3-2 4-2s2-1 3-1c-3-2-9-1-13-1h-4v-1c3-2 5-3 8-5h0l-1-1h-2v-1-1h-1c-1 0-1 0-1-1l1-1c1-1 1-2 1-3v-1c0-1-1-2-1-3l-1-1v-1h2 0l1-1v-1h0 4 1v-1h1 1c-1 0-2-1-3-1h0c0-3 1-4 3-6 1 1 3 1 4 1l3-3 1 2c0-1 1-2 1-2z" class="h"></path><path d="M129 412h1c0 2 0 4 1 6-1 1-1 2-1 3 0-2-1-3-1-5h-1c0-1 0-3 1-4z" class="R"></path><path d="M151 392c0 1 1 1 0 2h-2 0c-1 1-2 2-4 2v1h1v1c-3 0-4 0-6 1 0-1 0-1-1-2 3-1 5-2 7-3 1-1 3-1 5-2z" class="l"></path><path d="M151 370c1 2 2 5 2 7 1 1 1 3 1 5 2 2 4 3 5 5-1-1-5-3-6-3v-1h0c-2-2-1-6-2-8 0-2-1-3-1-5h1z" class="O"></path><path d="M158 389c3 2 6 3 8 6-1-1-2-2-4-2h-8s-1 0-2 1h-1c1-1 0-1 0-2 1-1 3-2 4-2s2-1 3-1z" class="n"></path><path d="M155 390h3l1 1c-1 1-2 1-4 1l-1 1s-1 0-2 1h-1c1-1 0-1 0-2 1-1 3-2 4-2z" class="S"></path><path d="M152 401h2 1 0-1c-1-1-1-1-1-2h1c3 0 6 1 9 0h4c-3 1-5 3-7 4h0-1l-3 3-1-1v-1c1 0 1-1 1-1v-1c-2 0-3 0-4-1z" class="V"></path><path d="M152 401h2 1 0-1c-1-1-1-1-1-2h1 1c1 2 3 2 5 2l-1 1h-3c-2 0-3 0-4-1z" class="o"></path><path d="M152 428c0-1-1-1-1-1l-1-1 4-4c2-1 3 0 5 0l-2 3v1h2v1l-1 1h1-7z" class="F"></path><path d="M162 420l-1-1h-1s-1-1-2-1c1-1 1-1 2-1v-1c1 0 1-1 1-2 1 0 1 0 1-1 1-2 1-3 2-5h1v3l1 1c-1 0-1 1-1 2v6l-1 1h0c-1-1-1-1-2-1z" class="M"></path><path d="M166 412l1 4v9 4c-1-1-2-1-3-2l-2 2-3-1h-1l1-1v-1h-2v-1l2-3c1-1 2-1 3-2 1 0 1 0 2 1h0l1-1v-6c0-1 0-2 1-2z" class="C"></path><path d="M159 426l5 1-2 2-3-1h-1l1-1v-1zm-13-58c1 0 2 0 3 2h1c0 2 1 3 1 5 1 2 0 6 2 8h0v1s-1-1-2-1v-1s-4 2-5 3c0 0-1 0-1 1l-1 1h-1l2 1h-4v-1l8-5h0l-1-1h-2v-1-1h-1c-1 0-1 0-1-1l1-1c1-1 1-2 1-3v-1c0-1-1-2-1-3l-1-1v-1h2z" class="T"></path><path d="M146 373v3 3 1l2 1h-2v-1-1h-1c-1 0-1 0-1-1l1-1c1-1 1-2 1-3v-1z" class="L"></path><path d="M159 367l1 1c1-2 1-2 1-4 0 0 1 1 2 1 1 1 1 2 1 3 1 0 1 1 1 1 0 1 0 1-1 2v2c1 1 1 1 2 1v2h-1c0 2 2 5 3 6v1c-1-1-1-3-3-3-1 0-2 1-3 1l2 4c-2-2-3-4-5-7s-5-7-8-10v2h-1-1c-1-2-2-2-3-2h0l1-1v-1h0 4 1 2 0c1 0 2 1 2 2h1c0-1 1-1 2-1z" class="C"></path><path d="M152 366h2 0c1 0 2 1 2 2h-2c-1 0-2-1-3-1h-1c0 1 0 1 1 1v2h-1-1c-1-2-2-2-3-2h0l1-1v-1h0 4 1z" class="K"></path><path d="M163 356l4 8c1 1 1 2 2 3v1c1 3 1 7 0 10v6-1h-1v-1c-1-1-3-4-3-6h1v-2c-1 0-1 0-2-1v-2c1-1 1-1 1-2 0 0 0-1-1-1 0-1 0-2-1-3-1 0-2-1-2-1 0 2 0 2-1 4l-1-1c-1 0-2 0-2 1h-1c0-1-1-2-2-2h0-2v-1h1 1c-1 0-2-1-3-1h0c0-3 1-4 3-6 1 1 3 1 4 1l3-3 1 2c0-1 1-2 1-2z" class="U"></path><path d="M154 365c1-1 1-2 3-2 1 2 1 3 2 4-1 0-2 0-2 1h-1c0-1-1-2-2-2h0-2v-1h1 1z" class="B"></path><path d="M166 376l3-3v5 6-1h-1v-1c-1-1-3-4-3-6h1z" class="W"></path><path d="M163 356l4 8c1 1 1 2 2 3v1c-2-1-3-4-5-5l-3-3c0-1 0-2 1-2 0-1 1-2 1-2z" class="Y"></path><path d="M143 425h-1-4c-2-1-4-4-4-7-1-2-1-6 1-7 2-2 4-2 6-4 1-1 2-3 3-5l-3 1h0v-1c3-2 8-1 11-1 1 1 2 1 4 1v1s0 1-1 1v1l1 1 3-3h1 0c-1 1-2 3-3 4l-1 1c0 2-1 4-1 6v2l-1 1-6 5h0l-3 1v1 1h-2z" class="C"></path><path d="M143 420l1-1 1-1h2v1l-1 1v1c1 1 1 1 2 1l-3 1v1 1h-2 1l1-1c-2-1-4-1-5-2v-1h1c1 0 1 0 2-1z" class="l"></path><path d="M143 420l-1-1-2 1c0-1 1-2 1-3 2-3 5-4 9-5 0 1-1 2-2 3v1h1l-2 3v-1h-2l-1 1-1 1z" class="X"></path><path d="M148 416c-1 1-2 1-2 1l-1-1c1-1 2-1 3-2v1 1z" class="c"></path><path d="M150 411h0c-1 0-1 0-1 1-2 0-4 1-5 2h0l-1-1h1c1-2 5-4 8-4h1c1-1 2-1 4-2l-1 1c0 2-1 4-1 6v2l-1 1-6 5h0c-1 0-1 0-2-1v-1l1-1 2-3h-1v-1c1-1 2-2 2-3v-1z" class="K"></path><path d="M154 411h1c0 1-1 2-2 3l-1-2 2-1z" class="B"></path><path d="M150 411c2 0 2-1 4-1v1l-2 1-3 4h-1v-1c1-1 2-2 2-3v-1z" class="l"></path><path d="M152 412l1 2c0 1-1 2-2 3h-1l-2 2v3h0c-1 0-1 0-2-1v-1l1-1 2-3 3-4z" class="O"></path><path d="M110 337c2-2 4-6 7-7l1 1 2 2 1-1h0l2 1h0c1 2 2 3 4 4l2 3 1-2h1l1-1 5 2h0v-1h1v1l2 1 1 1-1 1h-1l1 1h1c0 1-1 3-2 4v1c1-1 4-3 5-3s2 1 3 2l-3 4v1h3 0c-1 1-2 1-3 1v1h1v1c1 0 1 1 2 1v2h0c-1 1-2 1-2 2 2-1 5-1 7-3l2 1c-2 2-3 3-3 6h0c1 0 2 1 3 1h-1-1v1h-1-4 0v1l-1 1h0-2v1l1 1c0 1 1 2 1 3v1c0 1 0 2-1 3l-1 1c0 1 0 1 1 1h1v1 1h2l1 1h0l-8 5v1h4c4 0 10-1 13 1-1 0-2 1-3 1s-3 1-4 2c-2 1-4 1-5 2-2 1-4 2-7 3 1 1 1 1 1 2l-3 2c-4 4-6 6-7 11h-1c-1 1-1 3-1 4h1c0 2 1 3 1 5v1c0 1 1 2 1 2v1l-1 3-1-1-1 1v-1l1-1-1-1h-6c0-1 1-1 1-2h0c0-1-1-1-1-1v-1c0 1 0 1-1 1 0-3-1-6-4-8 0-1-1-1-2-2l6-3h-3l-6 3-1-1v-2-3l-1-1c0-2-1-1-2-2h-2-1l-4-1c-2-1-4-3-5-5s-2-4-1-6l-3-6-1-1-1 1v-2c0-2-1-3-1-4-2-3-5-4-8-5-5 1-7 2-11 6h0c0-4 12-17 15-20 0 0 0-1-1-1h0v-1c0-1 1-1 2-2 2-1 3-3 5-5 0 0 0-1 1-1v1h1v-1h2 1v2h1l1-1c0-1 0-2 1-3l1-1s1-1 1-2l1 1 4-2c1 0 0 0 1-1h-2c-3-1-4-3-5-5v-1c2 1 3 1 5 2h2 1 1l1-2z" class="C"></path><path d="M118 408c3-1 4-1 7 0v1h-2c-2-1-3 0-5-1z" class="Q"></path><path d="M113 384h1 1c2 0 3-1 4-2l-1 3h1v1l1 1h-2c-2-1-4-2-5-3z" class="u"></path><path d="M139 397c1 1 1 1 1 2l-3 2h-1c-1 1-3 3-5 3 2-3 6-5 8-7z" class="v"></path><path d="M98 373c-1-1-1-2-2-3h0c-1-1-2-4-1-5 0-1 0-1 1-1l1 3 1-1v7zm13 33c1 0 2 1 3 1h-2v1 2h0c1-1 1-1 1-2 2 0 2 1 3 1 1-1 2-1 2-1 2 1 3 0 5 1l-2 1v-1h-3l-6 3-1-1v-2-3z" class="a"></path><path d="M131 404c2 0 4-2 5-3h1c-4 4-6 6-7 11h-1c0-3 1-6 2-8z" class="Y"></path><path d="M111 399c3 0 6 0 9-1 1-1 4-2 5-1h1v1c-3 0-6 1-8 2l-2 2c-1-1-1-1-2-1l1-1h-8 4v-1z" class="F"></path><path d="M121 409v1c-1 3 0 8 1 11 0 1 0 1-1 1 0-3-1-6-4-8 0-1-1-1-2-2l6-3z" class="e"></path><path d="M96 383h1c0-1 0-2-1-3 0-1-1-2-1-2 0-2-1-3-1-4v-1h0c2 4 3 9 6 12 1 1 5 2 5 2v1 1l-2 1c-1-1-2-3-4-3 0-2-2-2-4-3l1-1z" class="g"></path><path d="M128 416h1c0 2 1 3 1 5v1c0 1 1 2 1 2v1l-1 3-1-1-1 1v-1l1-1-1-1h-6c0-1 1-1 1-2l4-1 1-6z" class="e"></path><path d="M130 422c0 1 1 2 1 2v1l-1 3-1-1-1 1v-1l1-1c1-2 1-2 1-4z" class="P"></path><path d="M92 378c0-1 0-2 1-3s0-2 1-3v1h0v1c0 1 1 2 1 4 0 0 1 1 1 2 1 1 1 2 1 3h-1l-1 1h-2 0v1h-1l-1-1-1 1v-2c1 0 1-1 1-1 1-1 1-3 1-4z" class="R"></path><path d="M94 381v-1c1 0 1 1 1 1 0 1 0 1 1 2l-1 1h-2c1-1 1-2 1-3z" class="u"></path><path d="M92 378c0 1 1 2 2 3 0 1 0 2-1 3h0v1h-1l-1-1-1 1v-2c1 0 1-1 1-1 1-1 1-3 1-4z" class="Y"></path><path d="M119 402c2-1 5-3 8-3 1 0 1 0 2 1v1c-5 1-10 4-15 6-1 0-2-1-3-1l-1-1c0-2-1-1-2-2 3 0 5 0 8-1l2-2 1 2z" class="L"></path><path d="M116 402l2-2 1 2c-3 1-5 3-9 3 0-2-1-1-2-2 3 0 5 0 8-1z" class="J"></path><path d="M99 387c2 0 3 2 4 3l2-1v-1-1c4 2 5 5 8 7h1c-1 1-1 1-2 1l-1-1c-1 2-1 4-1 5h1v1h-4c-2 0-3-1-4-2-4-3-3-7-4-11z" class="L"></path><path d="M105 387c4 2 5 5 8 7h1c-1 1-1 1-2 1l-1-1c-1 0-1 0-1-1h0v-1l-1 2c-2 0-2-3-4-3v3h-1c0-1 0-3-1-4h0l2-1v-1-1z" class="f"></path><path d="M93 384h2c2 1 4 1 4 3 1 4 0 8 4 11 1 1 2 2 4 2h8l-1 1c1 0 1 0 2 1-3 1-5 1-8 1h-2-1l-4-1c-2-1-4-3-5-5s-2-4-1-6l-3-6h1v-1h0z" class="D"></path><path d="M114 401c1 0 1 0 2 1-3 1-5 1-8 1h-2l-1-1h1c2 0 5 0 8-1z" class="G"></path><path d="M93 385c1 1 4 3 4 5h0c0 4-2 6 2 9 2 1 4 2 6 2 1 0 1 0 1 1h-1l1 1h-1l-4-1c-2-1-4-3-5-5s-2-4-1-6l-3-6h1z" class="j"></path><path d="M93 385c1 1 4 3 4 5l-2-2v2 1l-3-6h1z" class="X"></path><defs><linearGradient id="l" x1="122.485" y1="384.341" x2="98.678" y2="367.147" xlink:href="#B"><stop offset="0" stop-color="#d0050c"></stop><stop offset="1" stop-color="#a22225"></stop></linearGradient></defs><path fill="url(#l)" d="M98 366c1 1 2 1 2 2 1 1 1 3 1 4v2c0 1 1 1 1 1l1-1v-1c1-1 1-1 3-1h2 0 2l1-1c2 1 2 3 3 4h1c1-1 1-3 0-4v-1h1l1 1c2 1 3 3 6 2h0c2 0 2 0 3 1l1 1 1 1c2 1 3 0 5 1h1v1l-2 2c-1 1-1 2-2 2l-2 1h-3 0c-1-1-1-1-2-1 0 0-1 1-2 1 0 1-1 2-2 2h-1l1-3c-1 1-2 2-4 2h-1-1c-1-2-8-3-10-4-1-1-2-1-2-2-2-1-2-4-3-5v-7z"></path><path d="M103 373c1-1 1-1 3-1h2 0 2l1 4-5-3h-3z" class="J"></path><path d="M110 372l1-1c2 1 2 3 3 4h1c1 1 2 1 2 2s-1 1-1 2h-5 0c-1 0-1 0-2-1h0c1 0 1 0 2-1v-1l-1-4z" class="S"></path><path d="M115 375c1-1 1-3 0-4v-1h1l1 1c2 1 3 3 6 2h0c2 0 2 0 3 1l1 1 1 1c2 1 3 0 5 1h1v1l-2 2c-1 1-1 2-2 2l-2 1h-3 0c-1-1-1-1-2-1 0 0-1 1-2 1 0 1-1 2-2 2h-1l1-3 9-3h0v-1h-5c-2 1-3 1-5 1h-2c0-1 1-1 1-2s-1-1-2-2z" class="d"></path><path d="M117 377l2-1h0v2l-1 1h-2c0-1 1-1 1-2z" class="L"></path><path d="M115 375c1-1 1-3 0-4v-1h1l1 1v2c1 1 2 1 2 3l-2 1c0-1-1-1-2-2z" class="U"></path><path d="M123 373c2 0 2 0 3 1l1 1 1 1-1 1c-2 0-3 0-4-1s-1-1 0-3h0z" class="T"></path><path d="M128 379h2c1-1 1-1 3-1-1 1-2 1-3 2-2 0-4 1-5 2h-2s-1 1-2 1c0 1-1 2-2 2h-1l1-3 9-3z" class="R"></path><path d="M137 363l1 2v1l2 2h6 0-2v1l1 1c0 1 1 2 1 3v1c0 1 0 2-1 3l-1 1c0 1 0 1 1 1-3 2-4 5-6 7-1 1-2 1-3 1h-1c0 1-1 1-1 1-2 1-4 1-6 0l-8-1-1-1v-1c1 0 2-1 2-2 1 0 2-1 2-1 1 0 1 0 2 1h0 3l2-1c1 0 1-1 2-2l2-2v-1h-1c-2-1-3 0-5-1l-1-1 1-1h0v-1c0-1 1-1 2-2h0l2-1s1 0 1-1v-2l-3-2h0c1 0 3-1 5-1 1-1 1-1 2-1z" class="AA"></path><path d="M127 375l1-1c2 1 4 1 6 3h-1c-2-1-3 0-5-1l-1-1z" class="n"></path><path d="M137 363l1 2-2-1-2 2h0 0c-1 1 0 2 0 3h-1v-2l-3-2h0c1 0 3-1 5-1 1-1 1-1 2-1z" class="q"></path><path d="M128 388v-1c2 0 5-4 6-5v5h1c0 1-1 1-1 1-2 1-4 1-6 0z" class="S"></path><path d="M140 368h6 0-2v1l1 1c0 1 1 2 1 3v1c0 1 0 2-1 3l-1 1c0 1 0 1 1 1-3 2-4 5-6 7-1 1-2 1-3 1 1-2 3-4 4-6v-1c0-3-2-3-1-6h0c1-3 1-4 1-6z" class="E"></path><path d="M145 377l-1 1c0 1 0 1 1 1-3 2-4 5-6 7h0c0-1 0-1 1-2v-2c1-2 1-3 3-4l2-1zm-5-9h6 0-2v1l1 1c0 1 1 2 1 3v1h-2c0-1-2-2-3-3v-1h0l-1 1 2 2v1h-3c1-3 1-4 1-6z" class="N"></path><defs><linearGradient id="m" x1="108.126" y1="353.485" x2="106.315" y2="364.653" xlink:href="#B"><stop offset="0" stop-color="#cb090f"></stop><stop offset="1" stop-color="#a61e27"></stop></linearGradient></defs><path fill="url(#m)" d="M110 337c2-2 4-6 7-7l1 1 2 2 1-1h0l2 1h0c1 2 2 3 4 4l2 3 1-2h1l1-1 5 2h0v-1h1v1l2 1 1 1-1 1h-1l1 1h1c0 1-1 3-2 4v1c1-1 4-3 5-3s2 1 3 2l-3 4v1h3 0c-1 1-2 1-3 1v1h1v1c1 0 1 1 2 1v2h0c-1 1-2 1-2 2 2-1 5-1 7-3l2 1c-2 2-3 3-3 6h0c1 0 2 1 3 1h-1-1v1h-1-4 0v1l-1 1h-6l-2-2v-1l-1-2c-1 0-1 0-2 1-2 0-4 1-5 1h0l3 2v2c0 1-1 1-1 1l-2 1h0c-1 1-2 1-2 2v1h0l-1 1-1-1c-1-1-1-1-3-1 1 0 1-1 1-1l-1-2h0l-3-3c-1-1-2-1-3-2-2 0-3-2-4-2-2 0-5-1-7-2 1 2 2 3 1 5-1 0-3-2-4-3-3-3-7-3-10-6-3 1-5 2-8 3 0 0 0-1-1-1h0v-1c0-1 1-1 2-2 2-1 3-3 5-5 0 0 0-1 1-1v1h1v-1h2 1v2h1l1-1c0-1 0-2 1-3l1-1s1-1 1-2l1 1 4-2c1 0 0 0 1-1h-2c-3-1-4-3-5-5v-1c2 1 3 1 5 2h2 1 1l1-2z"></path><path d="M104 352h2c0 1 1 1 1 1l-2 1-1-1v-1z" class="K"></path><path d="M126 364h3l1 1h0l3 2v2c0 1-1 1-1 1-2-1-4-2-6-4v-2z" class="R"></path><path d="M120 367c1-1 2-1 4-1 1 1 4 3 6 4v1h0c-1 1-2 1-2 2v1h0l-1 1-1-1c-1-1-1-1-3-1 1 0 1-1 1-1l-1-2h0l-3-3z" class="D"></path><path d="M123 370c1 0 1 0 2 1 2 1 3 0 5 0-1 1-2 1-2 2v1h0l-1 1-1-1c-1-1-1-1-3-1 1 0 1-1 1-1l-1-2z" class="S"></path><path d="M114 358c1 1 5 1 6 0l-1-1h0c3 0 5 0 7 1 3 0 5 1 7 1l-1 1h-4c-2-1-4 0-5-1h-1v2c-1 0-1 0-1 1l-5-1c-1 0-3 0-5-1s-5-2-7-2h-1v-1h3c2 0 5 1 8 1z" class="D"></path><path d="M116 361c2-1 4-1 6-2v2c-1 0-1 0-1 1l-5-1z" class="X"></path><path d="M93 350h2 1v2h1l1-1 1-1 1 1v1c-1 1-1 1-3 1 0 1 0 1 1 2h-2 0-1-3c-1 1-2 1-3 2h0c1 0 1 0 2-1 1 1 1 1 2 1l-8 3s0-1-1-1h0v-1c0-1 1-1 2-2 2-1 3-3 5-5 0 0 0-1 1-1v1h1v-1z" class="D"></path><path d="M84 359c2-1 4-3 6-4s4-1 6 0h-1-3c-1 1-2 1-3 2h0c1 0 1 0 2-1 1 1 1 1 2 1l-8 3s0-1-1-1h0z" class="L"></path><path d="M102 346l4-2v1 1c1-1 2-1 3-1v1h2l1 1c2 0 2 1 3 1 0 1 1 2 1 3v-1l-6 3h-3s-1 0-1-1h-2v1l1 1c-2 0-6 0-7 1h0-1-1 2c-1-1-1-1-1-2 2 0 2 0 3-1v-1l-1-1-1 1c0-1 0-2 1-3l1-1s1-1 1-2l1 1z" class="Y"></path><path d="M109 346h2c-1 1-2 1-4 1 1 0 1 0 2-1z" class="D"></path><path d="M106 346c1-1 2-1 3-1v1c-1 1-1 1-2 1l-2 1-1-1v-1h1 1z" class="c"></path><path d="M102 346l4-2v1 1h-1-1v1l1 1h1 1c-1 1-2 1-3 2l-2-2-1-1 1-1h0z" class="u"></path><path d="M102 346l1 1v1h-1l-1-1 1-1z" class="e"></path><path d="M101 345l1 1h0l-1 1 1 1 2 2h-2c-1 1 0 2-2 1l-1-1-1 1c0-1 0-2 1-3l1-1s1-1 1-2z" class="a"></path><path d="M100 347v4l-1-1-1 1c0-1 0-2 1-3l1-1z" class="I"></path><path d="M112 347c2 0 2 1 3 1 0 1 1 2 1 3v-1l-6 3h-3s-1 0-1-1h-2v-1h1c2-1 3-2 4-3h1s1-1 2 0h0v-1z" class="E"></path><path d="M106 352c2 0 5-1 7-2h0 3l-6 3h-3s-1 0-1-1z" class="J"></path><path d="M152 357l2 1c-2 2-3 3-3 6h0c1 0 2 1 3 1h-1-1v1h-1-4 0v1l-1 1h-6l-2-2v-1l-1-2c-1 0-1 0-2 1-2 0-4 1-5 1l-1-1h-3l-1-1c-1-1-3-1-4-1 0-1 0-1 1-1v-2h1c1 1 3 0 5 1h4l1-1c4 0 8 1 12 1 2-1 5-1 7-3z" class="f"></path><path d="M133 362h2v-1h1c1 1 1 0 1 0 2 1 4 1 6 1h-6v1c-1 0-1 0-2 1l-2-2z" class="a"></path><path d="M122 361h4c1 1 4 1 5 1 1-1 1-1 2 0h-1v1l1-1 2 2c-2 0-4 1-5 1l-1-1h-3l-1-1c-1-1-3-1-4-1 0-1 0-1 1-1z" class="Z"></path><path d="M125 363s1-1 2-1h3l1 2h-2-3l-1-1zm12 0v-1h6c2 1 5 1 8 2 1 0 2 1 3 1h-1-1v1h-1-4 0v1l-1 1h-6l-2-2v-1l-1-2z" class="Y"></path><path d="M141 364c3 0 7 0 10 1l1 1h-1-4-4v-1l-2-1z" class="E"></path><path d="M137 363c2 1 3 1 4 1l2 1v1h4 0v1l-1 1h-6l-2-2v-1l-1-2z" class="t"></path><path d="M137 363c2 1 3 1 4 1l2 1v1c-2 0-2 0-4-1l-1 1v-1l-1-2zm2-15c1-1 4-3 5-3s2 1 3 2l-3 4v1h3 0c-1 1-2 1-3 1v1h1v1c1 0 1 1 2 1v2h0c-1 1-2 1-2 2-4 0-8-1-12-1-2 0-4-1-7-1-2-1-4-1-7-1h0l1 1c-1 1-5 1-6 0h3l1-1c-1-1-1-2-2-3-2 0-4 0-6-1l6-3v1h2l1 1c4-1 8-1 12-1h3c2-1 2-2 2-4 1-1 1-1 3-2v2 1z" class="M"></path><path d="M137 355c2 0 4 0 7 1h0v1c-2 0-6 0-8-1l1-1z" class="t"></path><path d="M116 351h2l1 1h-1v1l4 1h0c-1 1-3 1-4 1-1-1-1-1-2-1-2 0-4 0-6-1l6-3v1z" class="G"></path><path d="M139 348c1-1 4-3 5-3s2 1 3 2l-3 4v1h3 0c-1 1-2 1-3 1v1h1v1h0v2 1l-1-1v-1h0c-3-1-5-1-7-1v-1h4 0l3 1h0c-1 0-1-1-1-1h-1l-1-1h0c-1 0-2 1-3 1-1-1 0-1-1-1h-2-1 0c-1-1-1-1-3-2h3c2-1 2-2 2-4 1-1 1-1 3-2v2 1z" class="L"></path><path d="M144 352l-2 1c-1 0-1 0-2-1v-1c1 0 2-1 3-1l1 1v1z" class="M"></path><path d="M131 351h3c2 1 3 0 4 0h1l1 1c-1 0-1 1-1 1h-5 0c-1-1-1-1-3-2z" class="K"></path><path d="M136 347c1-1 1-1 3-2v2 1c-1 2-1 2-3 2v1h2c-1 0-2 1-4 0 2-1 2-2 2-4z" class="N"></path><path d="M110 337c2-2 4-6 7-7l1 1 2 2 1-1h0l2 1h0c1 2 2 3 4 4l2 3 1-2h1l1-1 5 2h0v-1h1v1l2 1 1 1-1 1h-1l1 1h1c0 1-1 3-2 4v-2c-2 1-2 1-3 2 0 2 0 3-2 4h-3c-4 0-8 0-12 1l-1-1h-2c0-1-1-2-1-3-1 0-1-1-3-1l-1-1h-2v-1c-1 0-2 0-3 1v-1-1c1 0 0 0 1-1h-2c-3-1-4-3-5-5v-1c2 1 3 1 5 2h2 1 1l1-2z" class="C"></path><path d="M115 344v-2c2-1 3-1 4-2l1 1-3 2c0 1-1 2-1 2h-1v-1z" class="G"></path><path d="M115 344s0-1 1-1h1c0 1-1 2-1 2h-1v-1z" class="d"></path><path d="M132 337l5 2h0v-1h1v1l2 1 1 1-1 1h-1c-3-1-5-3-8-4l1-1z" class="W"></path><path d="M120 333l1-1h0l2 1h0c1 2 2 3 4 4l2 3c1 0 2 1 3 2h-1l-9-6v-1c-1-1-1-1-1-2h-1z" class="N"></path><path d="M121 333c0 1 0 1 1 2v1c-5 0-8 1-12 4v1c-2-1-2-1-3-2h1 1 1c3-2 6-4 10-5l1-1zm14 14v-1c0-1-1-1-1-2l1-2h1l4 1h1c0 1-1 3-2 4v-2c-2 1-2 1-3 2h-1z" class="V"></path><path d="M135 347v-1c0-1-1-1-1-2l1-2h1c0 2 1 2 1 3h-1-1v2z" class="O"></path><path d="M110 337c2-2 4-6 7-7l1 1 2 2h1l-1 1c-4 1-7 3-10 5h-1l1-2z" class="c"></path><path d="M112 343c1 1 2 2 3 2h1c2 1 3 2 5 3l4-5-2 5c1 1 2 1 2 2-1 1-5 0-7 1h-2c0-1-1-2-1-3-1 0-1-1-3-1l-1-1h-2v-1h0 1s1 0 1 1h1 3l-1-1c-1 0-2 0-2-1v-1z" class="o"></path><path d="M100 337c2 1 3 1 5 2h2c1 1 1 1 3 2l2 2v1c0 1 1 1 2 1l1 1h-3-1c0-1-1-1-1-1h-1 0c-1 0-2 0-3 1v-1-1c1 0 0 0 1-1h-2c-3-1-4-3-5-5v-1z" class="L"></path><path d="M160 452h0c2 0 4-2 5-4 1-3 2-5 1-8-1-4-5-6-9-8 0 0-1 0-1-1 5 2 10 4 13 9 1 1 1 4 1 6 1 7 1 14 2 22l5 38c1 5 3 11 5 16 0 1 2 4 2 5l8 15c5 9 9 18 7 29l-1 2c-1 0-2 1-3 1v1 1l-1 2c-2 1-4 3-6 4h-1-1v1c-3 1-6 2-10 1h-3l1-1c-2-1-3-3-5-4h-3c-1-1-3-2-3-3v-1l2 2 1-1c0-1-1-2-1-3l-4-4c-1-1-3-2-5-2l-1 1v1 1l-3 2c-1 0-2 0-3 1l-1 2h0c0 3 0 6 2 8-2 0-5-3-6-4-3-3-4-7-6-11-1 0-1 0-1-1v-4-9c1-2 2-5 4-7v-1c1-1 2-2 3-4h1 1v-1h-2l-1-1 1-1v-1c2-3 3-7 6-9h-2 0l-1 1s-1 0-1-1c1-1 1-1 1-2v-1l8-23h0l3-7c1 0 2-2 2-2-1-1-1-1-2 0v-2l1-1c1 0 1-1 2-1v-2c-3 2-6 3-8 4v-1h-1l-1 1h0 0-2l-1-1h0c2-1 3-1 4-1s2-1 2-1v-1c-5 2-11 3-16 1h4c4 0 9-1 13-4 2-3 3-4 4-8-1 0-1-1-1-2h0c-2-1 0-1-2-2h-1 0c-1-1-2-1-2-3 0-1 0-2-1-3l3-3c1-1 2-1 3-1h2c0-1 1-2 1-3 1-2 4-4 3-6h0-1-2l-2-1h0l1-1z" class="C"></path><path d="M183 556c1 0 2 1 3 1-2 1-4 0-6 0v-1h3z" class="V"></path><path d="M160 532v2h3c-1 0-2 1-3 2h0l-1-1v-1l1-2z" class="U"></path><path d="M171 533h1c1 1 2 2 2 3h-1l-2-2v-1z" class="g"></path><path d="M191 574l2-2v2h2v1l-3 1s-1-1-1-2z" class="G"></path><path d="M188 576l3-2c0 1 1 2 1 2l-2 2h-1l-1-2z" class="N"></path><path d="M159 534v1l1 1h0c-2 1-3 2-5 2l4-4z" class="L"></path><path d="M144 576c-1-3-3-5-4-8l2 2c1 0 1 1 2 2v3 1z" class="R"></path><path d="M141 546c1-1 2-2 3-4h1c0 2-2 5-3 6l-1-1v-1z" class="f"></path><path d="M179 553l4-4h1 1c-2 2-3 4-5 5h-1v-1z" class="F"></path><path d="M147 550c0 1 1 2 1 3h1 1v-2c0 2 0 3 1 5-1 1-2 1-3 1h0c-1-2-1-5-1-7z" class="L"></path><path d="M151 556l2 4h-3c-1-1-2-2-2-3h0c1 0 2 0 3-1z" class="V"></path><path d="M150 567c2-1 3-1 5-2 1 1 1 1 1 2l-1 1v1h-2v-1h-2l-1-1z" class="e"></path><path d="M153 568c1-1 1-1 2-1v1 1h-2v-1z" class="l"></path><path d="M160 532l3-3h1v1 2c0 1-1 1-1 2h-3v-2z" class="T"></path><path d="M188 576l1 2h1c-3 1-6 3-10 2l1-1c3 0 5-1 7-3z" class="E"></path><path d="M175 534c1-1 2-1 3 0 0 0 1-1 2-1 1 1 3 2 4 3l1 1c-2 0-2 0-3-1-1 0-2-1-2-1-1 0-2 1-2 2l-2-2v-1h-1z" class="H"></path><path d="M178 546c1 0 1 1 2 1h1 2c-2 0-3 0-5 1s-4 3-6 3v-2c2-1 3-2 5-2 1 0 1-1 1-1z" class="Q"></path><path d="M165 524h1l2-1c-1 3 0 6-2 7v1c0-2 1-3 1-5h0c-1 1-2 3-3 4v-1h-1l2-5z" class="h"></path><path d="M154 529l1 1c0 1 0 2-1 4h0v1 1c1-1 1-2 2-2h0c-2 3-4 6-5 9v1h0v-2-1l-2 1c3-4 4-8 5-13z" class="L"></path><path d="M175 540v-1c1-1 1-1 1-2v-2l2 2 6 3v2c-3-1-6-2-9-2z" class="N"></path><path d="M170 534l1-1v1l2 2h1 1v-2h1v1 2c0 1 0 1-1 2v1c-1-1-1-1-2-1-2-1-4-3-4-5h1z" class="Y"></path><path d="M170 534l1-1v1l2 2h-1c-1-1-2-1-2-2z" class="H"></path><path d="M165 549c-1 1-2 2-2 3l-1 1h0c-2-4 0-9 0-12-1-2-1-3-3-4h1 1c3 1 2 8 2 10s-1 3 0 4c1-1 1-2 2-2h0z" class="E"></path><path d="M186 535l2 7h0l-4-2-6-3c0-1 1-2 2-2 0 0 1 1 2 1 1 1 1 1 3 1l-1-1c1 0 1 0 2-1z" class="D"></path><path d="M165 549l2-2c2-2 4-3 6-4 1 0 2 0 2-1 2 0 4 1 6 1-2 1-2 1-3 2v1s0 1-1 1c-1-1-1-1-2-1-4 0-7 1-9 3h-1 0z" class="N"></path><path d="M141 547l1 1c-4 6-4 13-4 20-1 0-1 0-1-1v-4-9c1-2 2-5 4-7z" class="G"></path><path d="M146 569c1-1 3-2 4-2l1 1h2v1h2v1l-3 2c-1 0-2 0-3 1l-1 2h0v-1h0-1-1c0-1 1-2 1-2 0-1 1-1 1-2-1 0-1 0-2-1z" class="n"></path><path d="M153 569h2v1l-3 2c-1 0-2 0-3 1 0-1 0-1-1-1v-1c1-2 3-2 5-2z" class="j"></path><path d="M180 558c3 1 8 2 9 5 1 2 2 5 2 7-3-4-6-8-11-9 0-1-1-2-1-2l1-1z" class="G"></path><path d="M170 518l2 4h0c1 0 1-1 1-2h1c0 3-1 5-2 7 0 1 0 3-1 4h-2 0v-10h0c0-1 1-2 1-3z" class="F"></path><path d="M146 569c1 1 1 1 2 1 0 1-1 1-1 2 0 0-1 1-1 2h1 1 0v1c0 3 0 6 2 8-2 0-5-3-6-4v-1-2-1-3c1 0 1-1 2-3z" class="O"></path><path d="M172 551v1l2 1h0c2-2 6-4 9-5-2 2-5 5-8 5v1h2s1 0 1-1h1v1h1v1l3 1h-3v1h-1c0 1 0 1 1 1l-1 1s1 1 1 2h-5c1-1 3-1 4-1-2-3-4-4-5-6-1-1-3-2-4-3l2-2v2z" class="E"></path><defs><linearGradient id="n" x1="147.827" y1="543.643" x2="155.585" y2="559.587" xlink:href="#B"><stop offset="0" stop-color="#cb0c10"></stop><stop offset="1" stop-color="#a51e26"></stop></linearGradient></defs><path fill="url(#n)" d="M149 542l2-1v1 2 4c1 2 3 4 4 5l7 7c-5-1-6-2-9-6-1-1-2-3-3-3v2h-1-1c0-1-1-2-1-3 0-3 1-5 2-8z"></path><path d="M169 576c-3-3-4-8-8-10-1 0-3 0-4-1h0c2-1 5 0 7 1s4 4 4 6c1 1 1 2 1 3 2 3 8 7 11 8h5l1-1v1c-3 1-6 2-10 1h-3l1-1c-2-1-3-3-5-4v-3z" class="e"></path><path d="M169 576l2 2c1 2 3 3 4 5h-1c-2-1-3-3-5-4v-3z" class="m"></path><path d="M180 580c-1-1-2-1-3-2-3-3-4-7-4-11 0-1 1-1 1-2h0l1-1s0 1 1 1c2 1 4 4 5 7 1 2 1 3 0 5h-1c-2-2-3-4-5-6 1 3 2 5 6 8l-1 1z" class="l"></path><defs><linearGradient id="o" x1="197.587" y1="549.137" x2="182.783" y2="560.673" xlink:href="#B"><stop offset="0" stop-color="#d60505"></stop><stop offset="1" stop-color="#b0171c"></stop></linearGradient></defs><path fill="url(#o)" d="M183 528l1-1 8 15c5 9 9 18 7 29l-1 2c-1 0-2 1-3 1h-2v-2c1-1 2-2 3-4 1-4 1-8-1-12-3-5-7-7-12-9h-2-1c-1 0-1-1-2-1v-1c1-1 1-1 3-2h1 1l1-1v-2l4 2h0l-2-7-3-7z"></path><path d="M182 543h1v1c0 1-1 2-1 2-1 0 0 0-1 1h-1c1-2 1-3 2-4z" class="M"></path><path d="M181 543h1c-1 1-1 2-2 4-1 0-1-1-2-1v-1c1-1 1-1 3-2z" class="F"></path><path d="M183 528l1-1 8 15h-1c-1-1-3-5-4-7 0 2 1 4 2 5v2c-1 1-1 1-1 0h0l-2-7-3-7z" class="q"></path><path d="M160 452h0c2 0 4-2 5-4 1-3 2-5 1-8-1-4-5-6-9-8 0 0-1 0-1-1 5 2 10 4 13 9 1 1 1 4 1 6 1 7 1 14 2 22l5 38c1 5 3 11 5 16 0 1 2 4 2 5l-1 1h-1l-5-14c-1 2-1 3-2 4l-1 2h-1c0 1 0 2-1 2h0l-2-4c0 1-1 2-1 3h0l-1 2-2 1h-1c-3 3-5 7-9 10h0c-1 0-1 1-2 2v-1-1h0c1-2 1-3 1-4l-1-1c2-3 2-6 4-9 0-2 2-4 3-5 1-3 2-5 3-8v-1c0-2 0-5 1-7h0v-1h-1v-4c-3 4-4 8-6 12s-5 8-4 12v1c-3 3-1 7-4 10h-2 0l-1 1s-1 0-1-1c1-1 1-1 1-2v-1l8-23h0l3-7c1 0 2-2 2-2-1-1-1-1-2 0v-2l1-1c1 0 1-1 2-1v-2c-3 2-6 3-8 4v-1h-1l-1 1h0 0-2l-1-1h0c2-1 3-1 4-1s2-1 2-1v-1c-5 2-11 3-16 1h4c4 0 9-1 13-4 2-3 3-4 4-8-1 0-1-1-1-2h0c-2-1 0-1-2-2h-1 0c-1-1-2-1-2-3 0-1 0-2-1-3l3-3c1-1 2-1 3-1h2c0-1 1-2 1-3 1-2 4-4 3-6h0-1-2l-2-1h0l1-1z" class="r"></path><path d="M158 475l3-3c1 1 0 2 1 2l-3 3c-1 0-1-1-1-2h0z" class="g"></path><path d="M168 501v1h1c-1 3-2 4-5 5v-1c2-1 3-3 4-5z" class="k"></path><path d="M161 488h0l4-4h1c0 1-2 3-3 5-1 1-2 3-3 5-1-1-1-1-2 0v-2l1-1c1 0 1-1 2-1v-2z" class="H"></path><path d="M170 486v-1h1c1 5 2 13-2 17h0-1v-1c2-5 2-10 2-15z" class="B"></path><path d="M160 479c2 0 6-2 8-3-2 1-3 2-4 3-1 2-2 3-3 4-1 2-4 5-7 5v-1l-1 1h0-1-1v1h-3 1c1-1 3-1 4-2s2-2 3-2h0l1-1c1-1 1-2 1-2l1-1c0-1 1-1 1-2z" class="t"></path><path d="M162 474c1-2 3-4 5-5-1 2-1 3-3 5s-3 3-4 5c0 1-1 1-1 2l-1 1s0 1-1 2l-1 1h0c-1 0-2 1-3 2s-3 1-4 2h-1 3v-1h1 1 0l1-1v1h0c-5 2-11 3-16 1h4c4 0 9-1 13-4 2-3 3-4 4-8l3-3z" class="P"></path><path d="M177 506c1 5 3 11 5 16 0 1 2 4 2 5l-1 1h-1l-5-14c-1 2-1 3-2 4l-1 2h-1c0 1 0 2-1 2h0l-2-4c1-3 3-6 3-9h0c0-1 0-2 1-2v3c1 2 0 3 0 4-1 2-1 3-1 4h1l1-1v-2c0-1 1-1 1-1h2c0 2 1 5 2 7v-1-1c0-1-1-2-1-2 0-1 0-1-1-2v-1-2c-1 0-1-1-1-1v-1-4z" class="L"></path><path d="M173 509v3c-1 3-1 6 0 8 0 1 0 2-1 2h0l-2-4c1-3 3-6 3-9z" class="D"></path><path d="M168 488l1 1h0c1-1 1-2 1-3 0 5 0 10-2 15-1 2-2 4-4 5 0-2 0-5 1-7h0v-1h-1v-4-1c1-1 2-5 4-6v1z" class="N"></path><path d="M164 493c1-1 2-5 4-6v1c0 2 1 4 0 5 0 1-1 2-2 3h0c-1-1-1-2-2-2v-1z" class="J"></path><path d="M112 412l6-3h3l-6 3c1 1 2 1 2 2 3 2 4 5 4 8 1 0 1 0 1-1v1s1 0 1 1h0c0 1-1 1-1 2h6l1 1-1 1v1l2 2v1c2 1 4 3 6 4v3l3 3 1 3v1h0l2 3 2 1c5 3 10 4 15 4h0l2 1h2 1 0c1 2-2 4-3 6 0 1-1 2-1 3h-2c-1 0-2 0-3 1l-3 3c1 1 1 2 1 3 0 2 1 2 2 3h0 1c2 1 0 1 2 2h0c0 1 0 2 1 2-1 4-2 5-4 8-4 3-9 4-13 4h-4c5 2 11 1 16-1v1s-1 1-2 1-2 0-4 1h0l1 1h2 0 0l1-1h1v1c2-1 5-2 8-4v2c-1 0-1 1-2 1l-1 1v2c1-1 1-1 2 0 0 0-1 2-2 2l-3 7h0l-8 23v1c0 1 0 1-1 2 0 1 1 1 1 1l1-1h0 2c-3 2-4 6-6 9v1l-1 1 1 1h2v1h-1-1c-1 2-2 3-3 4v1c-2 2-3 5-4 7v9 4l-3-4 1 5h-1l-3-3h0-1l-2 1c-3-3-7-6-11-10-2-2-4-5-6-7l-1-1h-2l4 6h-1c-2-2-3-4-5-7-6-8-12-18-18-27-1 0-1-2-1-3h0l1 1c1 1 2 2 3 2h0l-4-7-1-2-1 1-1-1c-1-3-3-6-4-8 0-1 0-2-1-3l-2-4c-1-2-1-7-1-10-1-6-3-12-5-18l2-1v2h0c1-2 1-5 1-6s1-1 1 0h0c1 0 1-4 1-5 3-6 5-11 8-17 4-5 8-11 13-16h0 1c2-1 3-3 5-4l9-9z" class="c"></path><path d="M142 450h1v1c0 1 0 2-1 3v-4z" class="E"></path><path d="M143 451l1 4h-1l-1-1c1-1 1-2 1-3z" class="p"></path><path d="M144 455l1 2v1l-2-1v-2h1z" class="Z"></path><path d="M140 448h2l2 1-1 1h-1l-2-2z" class="R"></path><path d="M130 451h6v1h-4v-1h-2z" class="B"></path><path d="M97 438h1v1c-1 1-3 2-4 3 1-2 2-3 3-4z" class="g"></path><path d="M112 417c1 1 1 1 1 2 0 2 0 4-1 6 0-1 0-1-1-1 0-3 1-5 1-7zm-32 51l1 1h0 1c-1 1-2 4-3 5h-1c0-2 1-4 2-6z" class="f"></path><path d="M104 433v2h0l-6 3h-1c2-2 4-4 7-5z" class="u"></path><path d="M91 471c-1-1-1-1-2-1-2-1-3-1-5 0l-2 2 3-5 6 2c-1 1-1 1 0 2z" class="Q"></path><path d="M140 453h2v1c-1 1-3 1-5 1s-3 1-5 1l-2 1v-1c3-2 7-2 10-3z" class="R"></path><path d="M123 435l1-1c1 2 1 3 1 5h1c0 1 0 1-1 2-1-1-3-2-5-3l1-1c-1 0-1-1-2-1l1-1h1 0c1 1 1 2 3 3 0-1-1-2-1-3z" class="n"></path><path d="M155 464c-1-1-4 0-5 0 2-1 5-3 7-3 1-1 1 0 2-1h2c0 1-1 2-1 3h-2c-1 0-2 0-3 1z" class="R"></path><path d="M111 424c1 0 1 0 1 1-1 3-3 5-5 7l-3 3v-2c3-3 6-5 7-9z" class="j"></path><path d="M134 459c2 0 3-1 4-1 1 1 2 3 3 4 0 1 0 2-1 3l-1-2h-1c-1 0-2-2-2-3-1-1-1-1-2-1z" class="V"></path><path d="M95 459s0-1 1-1c5-4 8-6 14-5-1 0-1 0-2 1l-2 1c-1-1-4 0-6 1s-4 3-5 4v-1z" class="j"></path><path d="M161 454h2c-3 4-8 4-13 6h0c-1-1-1-1-3-1v-1h0 3 1l3-1c3-1 6-1 7-3z" class="p"></path><path d="M142 478h-8c1-1 3-1 4-2 3-2 5-5 9-6l1-1 1 1-3 1c-2 1-3 3-5 3l1 1c1 1 2 1 3 1-2 0-4 0-5 1h-2 2c0 1 1 1 2 1z" class="P"></path><path d="M116 439c2 0 4 1 6 2-1 0-1 1-1 1-2-1-3-1-5-2h0c-5 0-10 1-14 2 0-1 1-2 2-3 2 1 4 0 6 0h6z" class="V"></path><path d="M110 453c3 1 5 1 8 1-1 1-1 2-2 3l-1 1c-2 0-4 0-5 1v1l-2 2c-1-3 0-4 1-7l-1-1c1-1 1-1 2-1z" class="J"></path><path d="M113 455l2 1 1 1-1 1c-2 0-4 0-5 1v-2l1-1 2-1z" class="g"></path><path d="M113 455l2 1c-1 1-2 1-3 1s-1 0-1-1l2-1z" class="u"></path><path d="M110 453c3 1 5 1 8 1-1 1-1 2-2 3l-1-1-2-1c-1 0-2-1-4 0h0l-1-1c1-1 1-1 2-1z" class="Q"></path><path d="M143 457l2 1v-1c2 0 4 1 6 1h-1-3 0v1c2 0 2 0 3 1h0s-2 1-3 1c-2 2-2 5-4 7-1 1-3 3-5 4 2-6 6-8 5-15z" class="R"></path><path d="M124 452h0c2-1 2-1 4-1v1c1 0 1 0 2-1h2v1c-1 0-2 0-3 1v1l-1-1-3 2c-1 0-2 1-2 2-2 1-4 3-5 4l-1-1h0v-1l1-1h-1-2l1-1c1-1 1-2 2-3l6-2z" class="H"></path><path d="M124 452v1h0c-3 2-5 5-7 7v-1l1-1h-1-2l1-1c1-1 1-2 2-3l6-2z" class="d"></path><path d="M128 425l1 1-1 1v1l2 2v1h-1l1 1h-1 0c-1-1-1-2-2-3v-1c-1 0-1 0-1 1s2 3 1 5c0 1-1 3-1 4v1h-1c0-2 0-3-1-5l-1 1-1-6v-1s0-1 1-1c0-1 1-1 2-1s2 0 3-1z" class="Z"></path><path d="M123 427c0-1 1-1 2-1 0 2-1 2-3 3v-1s0-1 1-1z" class="B"></path><path d="M124 434v-3h1l2 3c0 1-1 3-1 4v1h-1c0-2 0-3-1-5z" class="K"></path><path d="M122 441c1 0 2 2 4 2 0-2 1-6 3-7h0l1 1c0 2 0 5-1 8v5l-1 1c-1-1-2-4-3-6-1-1-3-2-4-3h0s0-1 1-1z" class="R"></path><path d="M148 469c2 0 3-1 4-2 1 1 1 2 1 3 0 2 1 2 2 3h0l-1 1c-2 0-4 1-5 1h-1v1l-2 1-1-1c-1 0-2 0-3-1l-1-1c2 0 3-2 5-3l3-1-1-1z" class="D"></path><path d="M148 469c2 0 3-1 4-2 1 1 1 2 1 3 0 2 1 2 2 3h0l-1 1c-2 0-4 1-5 1 0-1 2-3 2-4-1-1-1-1-2-1l-1-1z" class="L"></path><path d="M85 472l1-1c1 0 1 0 2 1-1 1-1 3-1 4-1 4-3 6-6 9h0l-1-1h-1v1h-1v-2c1-3 4-6 6-9l1-2z" class="K"></path><path d="M85 472l1-1c1 0 1 0 2 1-1 1-1 3-1 4-1-1-1-1-1-2v-2h-1z" class="H"></path><path d="M84 474l1-1h0c-2 4-5 7-6 11v1h-1v-2c1-3 4-6 6-9z" class="Z"></path><path d="M95 459v1 1 1c1 2 2 4 3 5l-2 1-1 1c-2-2-4-3-6-4s-4 0-5 1-2 2-2 3h-1 0l-1-1c3-5 6-4 11-6 1-1 2-2 4-3z" class="E"></path><path d="M96 468c-2-2-4-3-6-5 2 0 3-1 4-2h1v1c1 2 2 4 3 5l-2 1z" class="P"></path><path d="M130 432l-1-1h1c2 1 4 3 6 4v3l3 3 1 3v1h0l2 3h-2c-4-4-9-10-11-16h1z" class="g"></path><path d="M136 438l3 3 1 3v1h0l-5-6 1-1z" class="r"></path><path d="M130 432l-1-1h1c2 1 4 3 6 4v3l-1 1c-2-3-3-5-5-7z" class="W"></path><path d="M114 419v-1c1 1 2 2 2 3l1-1h0c1 2 2 3 2 5v2 1l-3 1c-1 1-2 1-3 1l-6 3v-1c2-2 4-4 5-7 1-2 1-4 1-6h1z" class="m"></path><path d="M114 419v-1c1 1 2 2 2 3 1 1 1 2 1 3 0 2-2 3-3 3h0v-1c1-2 0-5 0-7z" class="Y"></path><path d="M113 414h-1v-1h1c1 0 2 1 4 1h0c3 2 4 5 4 8 1 0 1 0 1-1v1s1 0 1 1h0c0 1-1 1-1 2h6c-1 1-2 1-3 1s-2 0-2 1c-1 0-2 0-3 1h-1v-1-2c0-2-1-3-2-5h0l-1 1c0-1-1-2-2-3v1h-1c0-1 0-1-1-2 1-1 1-1 1-3z" class="I"></path><path d="M119 418c1 2 1 5 1 8l-1 1v-2c0-2-1-3-2-5l2 2h0v-4z" class="a"></path><path d="M113 414h-1v-1h1c1 0 2 1 4 1l2 4v4h0l-2-2h0l-1 1c0-1-1-2-2-3v1h-1c0-1 0-1-1-2 1-1 1-1 1-3z" class="X"></path><path d="M113 414c1 2 3 4 4 6l-1 1c0-1-1-2-2-3v1h-1c0-1 0-1-1-2 1-1 1-1 1-3z" class="b"></path><path d="M144 449c5 3 10 4 15 4h0l2 1c-1 2-4 2-7 3l-3 1c-2 0-4-1-6-1l-1-2-1-4v-1l1-1z" class="C"></path><path d="M74 463c0-1 1-1 1 0h0v3c0 6 0 12 1 18l2-1v2h1v-1h1l1 1h0 1c-1 2-3 5-3 8h0 0l-2-3c0 1 0 2 1 3-1 1-1 1-1 2 1 0 1 1 0 1-1-2-1-7-1-10-1-6-3-12-5-18l2-1v2h0c1-2 1-5 1-6z" class="L"></path><path d="M74 463c0-1 1-1 1 0h0v3-1h0c-2 2 0 5-1 7-1-1-1-2-1-3h0c1-2 1-5 1-6z" class="Q"></path><path d="M78 483v2h1v-1h1l1 1-3 5c-1-2-1-4-2-6l2-1z" class="I"></path><path d="M155 473h1c2 1 0 1 2 2h0c0 1 0 2 1 2-1 4-2 5-4 8l-1-1c-1-1-2-1-3-2v1c-2-1-3 0-4-1h3v-1c-2-2-6-2-8-3-1 0-2 0-2-1h-2 2c1-1 3-1 5-1l1 1 2-1v-1h1c1 0 3-1 5-1l1-1z" class="f"></path><path d="M155 473h1c2 1 0 1 2 2h0c-1 1-4 2-5 1h-1l2-2h0l1-1z" class="Q"></path><path d="M154 474h0l-2 2c-2 0-4 1-6 1l2-1v-1h1c1 0 3-1 5-1z" class="O"></path><path d="M108 454l1 1c-1 3-2 4-1 7v1c-2-1-2 0-3 1l-3 1v1l2 1v1l-1-1-1 1c-1 0-2 0-4-1-1-1-2-3-3-5v-1-1c1-1 3-3 5-4s5-2 6-1l2-1z" class="c"></path><path d="M108 454l1 1c-1 3-2 4-1 7v1c-2-1-2 0-3 1 0-2-1-5-1-7l2-2 2-1z" class="M"></path><path d="M95 462c2-1 4-2 6-2 1 0 1 1 2 2 0 1-1 1-1 2h0v1 1l2 1v1l-1-1-1 1c-1 0-2 0-4-1-1-1-2-3-3-5z" class="d"></path><path d="M119 428h1c1-1 2-1 3-1-1 0-1 1-1 1v1l1 6c0 1 1 2 1 3-2-1-2-2-3-3h0-1l-1 1c1 0 1 1 2 1l-1 1-2-1c-2 0-5 0-7 1h3l2 1h-6c-2 0-4 1-6 0-1 1-2 2-2 3-6 2-13 9-16 14-1 1-1 4-2 5s-2 3-4 3c2-3 3-6 5-10s5-8 9-12c1-1 3-2 4-3v-1l6-3h0l3-3v1l6-3c1 0 2 0 3-1l3-1z" class="S"></path><path d="M121 435h0 0-1l-1 1c1 0 1 1 2 1l-1 1-2-1h-3s-1-1-2-1c2 0 5-2 6-1h2z" class="Y"></path><path d="M113 436c1 0 2 1 2 1h3c-2 0-5 0-7 1h3l2 1h-6c-2 0-4 1-6 0l9-3z" class="P"></path><path d="M113 430v1l-2 2 1 1c-5 1-9 3-14 5v-1l6-3h0l3-3v1l6-3z" class="Y"></path><path d="M119 428h1c1-1 2-1 3-1-1 0-1 1-1 1v1l1 6c0 1 1 2 1 3-2-1-2-2-3-3h0l-1-3h-1l-7 2-1-1 2-2v-1c1 0 2 0 3-1l3-1z" class="D"></path><path d="M119 428h1c1-1 2-1 3-1-1 0-1 1-1 1-1 1-4 1-4 1-1 1-1 2-2 2s-2 0-2 1h1 4l-7 2-1-1 2-2v-1c1 0 2 0 3-1l3-1z" class="P"></path><path d="M117 460l1 1c1-1 3-3 5-4 0-1 1-2 2-2l3-2 1 1s0 1-1 2c-1 2-11 8-11 9l1 1c-5 4-11 8-16 12h1v1c1 0 3 0 4 1-1 0-3-1-4 0v1h2v1c-1 1-1 1-2 1h-2c-3 2-8 3-11 4h0l-1-1c-2 1-4 2-5 3v-1h-1c-1 1-3 5-4 5 0-3 2-6 3-8h-1c3-3 5-5 6-9 0-1 0-3 1-4 1 1 1 3 3 3h0c1-2 0-3 0-4-1-1-1-1 0-2h4 0l1-1 2-1c2 1 3 1 4 1l1-1 1 1v-1l-2-1v-1l3-1c1-1 1-2 3-1v-1l2-2v-1c1-1 3-1 5-1h2 1l-1 1v1h0z" class="AA"></path><path d="M108 462l2-2h0c0 1 1 2 1 3v1c-1 0-2 0-3-1v-1z" class="t"></path><path d="M98 467c2 1 3 1 4 1l1-1 1 1c-2 1-3 2-4 2l-5-1h0l1-1 2-1z" class="Y"></path><path d="M117 460l1 1c1-1 3-3 5-4 0-1 1-2 2-2l3-2 1 1s0 1-1 2c-1 2-11 8-11 9l1 1c-5 4-11 8-16 12h1v1c1 0 3 0 4 1-1 0-3-1-4 0v1h0-4c-2 0-5 0-7-1 3-1 6-2 9-4 2-1 5-3 7-5 2-1 5-3 7-5 1-2 1-4 2-5v-1z" class="a"></path><path d="M99 481c-2-1-3-1-5-1 3-1 6-2 9-1 1 0 3 0 4 1-1 0-3-1-4 0v1h0-4z" class="S"></path><defs><linearGradient id="p" x1="89.485" y1="485.667" x2="88.238" y2="482.109" xlink:href="#B"><stop offset="0" stop-color="#0e1310"></stop><stop offset="1" stop-color="#1c1416"></stop></linearGradient></defs><path fill="url(#p)" d="M82 485c3-2 7-4 10-5 2 1 5 1 7 1h4 0 2v1c-1 1-1 1-2 1h-2c-3 2-8 3-11 4h0l-1-1c-2 1-4 2-5 3v-1h-1c-1 1-3 5-4 5 0-3 2-6 3-8z"></path><path d="M103 481h2v1c-1 1-1 1-2 1h-2c-3 2-8 3-11 4h0l-1-1c-2 1-4 2-5 3v-1h-1c3-4 11-6 16-6h5 0l-1-1h0z" class="Q"></path><path d="M118 466c5-4 11-5 16-7 1 0 1 0 2 1 0 1 1 3 2 3h1l1 2-1 3h-1c0-2 0-2-2-3 0 0-1-1-2 0 0 0-1 1-2 1-3-1-5-2-8 0 0 0-1 0-1 1 0 2 1 4 2 6 4 5 7 7 13 7 1 0 2 0 2 1 2 0 4 0 6 1h1c1 1 2 0 4 1v-1c1 1 2 1 3 2l1 1c-4 3-9 4-13 4h-4c5 2 11 1 16-1v1s-1 1-2 1-2 0-4 1h0l1 1h2 0 0l1-1h1v1l-5 2v-1h0c-1 1-2 1-2 3h0c1-1 2-1 3-2v1c0 1-1 1-1 1h-1c-1 1-3 1-4 1h-4c-1 0-2 0-3-1l-6-1-1 3v3c-1 1-1 1-2 1s-1-1-2-1c-1-5-2-7-6-10v-1l1-1-2-2h2l4-1h-5c-2 0-3-1-5-1l1-1c1 0 1 0 2 1v-1l-1-1-6-3h-3c-1-1-3-1-4-1v-1h-1c5-4 11-8 16-12z" class="T"></path><path d="M103 478c2 0 6 0 8 1l-1 1h-3c-1-1-3-1-4-1v-1z" class="B"></path><path d="M111 479c3 1 7 2 9 5h-1c-1 0-2-1-3-1l-6-3 1-1z" class="J"></path><path d="M124 486c7 4 14 7 23 6 0 0 0 1 1 1h0c-1 1-2 1-2 3h0c1-1 2-1 3-2v1c0 1-1 1-1 1h-1c-1 1-3 1-4 1h-4c-1 0-2 0-3-1l-6-1-1 3v3c-1 1-1 1-2 1s-1-1-2-1c-1-5-2-7-6-10v-1l1-1-2-2h2l4-1z" class="B"></path><path d="M137 495h3c1 0 3 0 3 1v1h-4c-1 0-2 0-3-1 0 0 0-1 1-1z" class="C"></path><path d="M118 487h2c1 3 7 5 7 8v1l-3-2c-1-2-3-3-5-4l1-1-2-2z" class="a"></path><path d="M137 495c-2 0-6-1-8-3v-1c3 1 9 1 11 3v1h-3z" class="E"></path><path d="M119 491v-1c2 1 4 2 5 4l3 2c0 1 0 2 1 2h1v3c-1 1-1 1-2 1s-1-1-2-1c-1-5-2-7-6-10z" class="I"></path><path d="M123 467c0 2 1 4 2 6 4 5 7 7 13 7 1 0 2 0 2 1 2 0 4 0 6 1h1c1 1 2 0 4 1v-1c1 1 2 1 3 2l1 1c-4 3-9 4-13 4h-4c-3 0-7-2-10-3-3-2-5-4-8-6-1-1-3-3-5-4-1 0-1 0-1-1h1c1-2 2-2 2-4v-2c1-1 2-1 3-2 1 0 1 0 2 1l1-1z" class="a"></path><path d="M135 485l-2-1h0c-1 0-2 0-2-1h1c2 0 3 0 3 1v1z" class="t"></path><path d="M115 475c1-2 2-2 2-4h0c0 2 0 3 1 4v1h-1l-2-1z" class="Q"></path><path d="M140 481c2 0 4 0 6 1 0 1 0 1-1 1h-1c-2-1-4-1-6-1h-2 0c1-1 2 0 3-1h-3 4z" class="E"></path><path d="M123 467c0 2 1 4 2 6v1c-2-1-3 0-4-1s-2-1-3-2h-1 0v-2c1-1 2-1 3-2 1 0 1 0 2 1l1-1z" class="j"></path><path d="M120 467l2 1v2h-4l-1-1c1-1 2-1 3-2zm-5 8l2 1c1 1 4 2 6 3 4 3 7 6 12 8l2 1 3 1h2-4c-3 0-7-2-10-3-3-2-5-4-8-6-1-1-3-3-5-4-1 0-1 0-1-1h1z" class="J"></path><path d="M146 482h1c1 1 2 0 4 1-1 1-2 1-3 1h2l1 1c0 1 0 1-1 1-2 1-6 2-8 2-1-1-2-1-3-1 0-1-1-1-2-1s-1-1-2-1v-1c0-1-1-1-3-1 2 0 4 0 6-1 2 0 4 0 6 1h1c1 0 1 0 1-1z" class="K"></path><path d="M145 485c-3 1-5 1-7 0v-1h4l2 1h1z" class="F"></path><path d="M146 482h1c1 1 2 0 4 1-1 1-2 1-3 1-1 1-2 1-3 1h-1l-2-1h3v-1c1 0 1 0 1-1z" class="l"></path><path d="M107 480h3l6 3 1 1v1c-1-1-1-1-2-1l-1 1c2 0 3 1 5 1h5l-4 1h-2l2 2-1 1v1c4 3 5 5 6 10 1 0 1 1 2 1s1 0 2-1v-3l1-3 6 1c1 1 2 1 3 1h4c1 0 3 0 4-1h1s1 0 1-1v-1c-1 1-2 1-3 2h0c0-2 1-2 2-3h0v1l5-2c2-1 5-2 8-4v2c-1 0-1 1-2 1l-1 1v2c1-1 1-1 2 0 0 0-1 2-2 2l-3 7h0l-8 23v1c0 1 0 1-1 2 0 1 1 1 1 1l1-1h0 2c-3 2-4 6-6 9v1l-1 1 1 1h2v1h-1-1c-1 2-2 3-3 4v1c-2 2-3 5-4 7v9 4l-3-4 1 5h-1l-3-3h0-1l-2 1c-3-3-7-6-11-10-2-2-4-5-6-7l-1-1h-2l4 6h-1c-2-2-3-4-5-7-6-8-12-18-18-27-1 0-1-2-1-3h0l1 1c1 1 2 2 3 2h0l-4-7-1-2-1 1-1-1c-1-3-3-6-4-8 0-1 0-2-1-3l-2-4c1 0 1-1 0-1 0-1 0-1 1-2-1-1-1-2-1-3l2 3h0 0c1 0 3-4 4-5h1v1c1-1 3-2 5-3l1 1h0c3-1 8-2 11-4h2c1 0 1 0 2-1v-1h-2v-1c1-1 3 0 4 0z" class="C"></path><path d="M113 547c1 0 2 0 3 1h0-3l-1-1h1z" class="F"></path><path d="M108 548l-1-1h1c2 1 3 0 5 0h-1l1 1c-1 0-1 0-2 1l-1-1h-2z" class="V"></path><path d="M97 521l2 1c-1 1-1 1-1 3v1 1l-1-1v-5z" class="e"></path><path d="M107 525c1 0 2 0 3 1v1h1 0 0v1c-1 0-1 1-1 1-1-1-2-2-3-4z" class="Q"></path><path d="M114 514l1 1c-1 2-2 3-5 4 1 0 1-1 1-1l-2-1c2-1 3-1 5-3z" class="W"></path><path d="M99 522l1-1h3c-2 2-4 3-5 5v-1c0-2 0-2 1-3z" class="X"></path><path d="M137 563c-1-1-1-3-1-4s-1-1-2-2v-1-1h3v-1 9z" class="B"></path><path d="M134 525c1 2 3 4 3 6l-1 1c-1-2-2-3-4-4v-1c1-1 1-2 2-2z" class="j"></path><path d="M116 523h1 0l-1 1c-2 1-2 0-3 1l1 1 2-1c-2 1-4 1-6 1-1-1-2-1-3-1h0 1c2-1 2-1 4-1 1 0 2-1 2-1h2z" class="K"></path><path d="M83 488h1v1c-1 2-1 2-1 4l1 3-1-1h-1c-1 0-2 0-2-1l-1-1h0c1 0 3-4 4-5z" class="G"></path><path d="M80 494h1v-2h1l1 1 1 3-1-1h-1c-1 0-2 0-2-1z" class="L"></path><path d="M125 532c1-1 2-1 4-2 2 1 4 1 6 3h0l-2 2v-1c-1-1-2-1-3-1l-1 1h-1v-1c-1-1-2 0-3-1z" class="V"></path><path d="M93 510l3 1c0 1 1 1 1 1 1 1 0 1 1 1l2 3c-1 0-1 0-2 1-2-1-6-4-6-6l1-1z" class="N"></path><path d="M118 540c-1-1-2-2-3-2-2 0-4 0-5 1l-3 2 2-2c0-2 2-3 3-4h0 5c1 2 1 3 2 4l-1 1z" class="L"></path><path d="M136 524l3 6c1 3 0 5-2 8h0v-2c-1-1-1-2-1-2v-2l1-1c0-2-2-4-3-6l2-1z" class="B"></path><path d="M137 547h2 0c-1 1-3 2-5 2-4 1-8 1-11 1-2 0-3 0-5-1l2-2 1 1c5 0 11 0 16-1z" class="S"></path><path d="M125 532c1 1 2 0 3 1v1c0 3-1 6-2 9-2 1-4 1-6 2l-2-5 1-1c1 2 1 3 2 4 2-2 4-4 4-7v-3-1z" class="G"></path><path d="M126 558s-2-4-3-5c2 0 3 0 5 1l1 1c1 2 3 6 2 9h0v1h0-1c-2-2-3-5-4-7z" class="I"></path><path d="M128 554l1 1-1 1c0-1-1-1-1-2h1z" class="H"></path><path d="M126 558h1l4 6v1h0-1c-2-2-3-5-4-7z" class="n"></path><path d="M130 533c1 0 2 0 3 1v1c0 2-1 5-3 6-3 3-7 4-10 6v-2c2-1 4-1 6-2 1-3 2-6 2-9h1l1-1z" class="K"></path><path d="M129 534l1-1v1c1 0 1 0 1 1h1v1c-1 1-2 2-2 3h-1v-3s1 0 0-1v-1h0z" class="Z"></path><path d="M103 514c1 0 1 0 2-1 0 2 0 2 1 4h1 2l2 1s0 1-1 1h0c-2 1-5 0-6 1l-1 1h-3l-1 1-2-1c1-1 1-2 1-4 1-1 1-1 2-1h2 1 0l-1-2h1z" class="U"></path><path d="M103 514c1 0 1 0 2-1 0 2 0 2 1 4l-4-1h1 0l-1-2h1z" class="R"></path><path d="M133 535l2-2 1 1s0 1 1 2c0 1-1 2-1 3v2c-2 2-4 4-7 5-1 0-3 1-4 1s-3 0-4 1l-1-1h0c3-2 7-3 10-6 2-1 3-4 3-6z" class="L"></path><path d="M77 496c1 0 1-1 0-1 0-1 0-1 1-2-1-1-1-2-1-3l2 3h0l1 1c0 1 1 1 2 1h1l1 1c0 5 0 10 2 15l-1 1-1-1c-1-3-3-6-4-8 0-1 0-2-1-3l-2-4z" class="AA"></path><path d="M87 517h0l1 1c1 1 2 2 3 2h0l12 17c2 3 5 5 6 8v1c-3-1-4-2-6-4l-1-3c-1-1-2-2-3-4l-6-9c-1-2-3-4-4-6h-1c-1 0-1-2-1-3z" class="G"></path><path d="M102 539c-1-3-3-6-5-9v-1c2 3 3 6 6 8h0c2 3 5 5 6 8v1c-3-1-4-2-6-4l-1-3z" class="L"></path><path d="M120 515h0c1 2 1 1 3 2v2h0l1-1v-1-7h0c0 1 1 2 0 3h2v3h1v2l-3 1c-1 3-3 7-7 8-2 1-4 1-6 1v-1h0 0-1v-1c2 0 4 0 6-1l-2 1-1-1c1-1 1 0 3-1l1-1h0-1-2c0-2 2-2 3-4l1-1 1-1v-1-1h1z" class="D"></path><path d="M126 513v3h1v2l-3 1v-6h2z" class="J"></path><path d="M116 525c2 0 3-2 4-3h1c-1 2-3 3-5 5h-5 0-1v-1c2 0 4 0 6-1z" class="V"></path><path d="M116 523c0-1 5-4 5-4l1-1v2 1 1h-1-1c-1 1-2 3-4 3l-2 1-1-1c1-1 1 0 3-1l1-1h0-1z" class="P"></path><path d="M140 526v1c1 2 1 4 1 6 1-1 3-2 4-4 1-1 1-2 2-3v1c0 1 0 1-1 2 0 1 1 1 1 1l1-1h0 2c-3 2-4 6-6 9v1l-1 1 1 1h2v1h-1-1c-1 2-2 3-3 4l-2 1h0-2c-5 1-11 1-16 1 1-1 3-1 4-1s3-1 4-1c3-1 5-3 7-5v-2c0-1 1-2 1-3v2h0c2-3 3-5 2-8h1v-4z" class="I"></path><path d="M140 544s-1-1 0-2c0-1 1-3 2-3 0-1 1-1 1-2l1 1v1l-1 1 1 1c-2 0-3 1-4 2v1z" class="n"></path><path d="M144 541h2v1h-1-1c-1 2-2 3-3 4l-2 1h0-2l3-2v-1-1c1-1 2-2 4-2z" class="E"></path><path d="M140 526v1c1 2 1 4 1 6 1-1 3-2 4-4 1-1 1-2 2-3v1c0 1 0 1-1 2 0 1 1 1 1 1l1-1h0 2c-3 2-4 6-6 9l-1-1c1-2 2-3 2-5l-8 9h-1v-2c0-1 1-2 1-3v2h0c2-3 3-5 2-8h1v-4z" class="N"></path><path d="M129 498l1-3 6 1c1 1 2 1 3 1v1c1 2-1 2-1 4l1 1c-1 2-1 5-1 7 2 4 4 9 9 10h0l1 1h-1c-2 0-4-1-5-3-1-1-2-2-2-3-1-1-1-2-2-3-1 4 1 10 2 14v4h-1l-3-6-1-10c-1 0-1 0-2 1s-3 2-4 4c0 2-1 6-2 7-2 2-5 3-7 5 0 1-1 2-3 2-1 0-3 0-4-1l-3-3s0-1 1-1c2 0 4 0 6-1 4-1 6-5 7-8l3-1v-2h-1v-3h-2c1-1 0-2 0-3 1-3 0-6-1-8 1 0 1 0 2 1v-2h0c1 0 1 1 2 1s1 0 2-1v-3z" class="M"></path><path d="M127 515l1 1 2-2h1 1c-1 2-3 3-4 4h-1v-2-1z" class="R"></path><path d="M134 503l1-3c1-1 3-1 4-2 1 2-1 2-1 4l-2 7c0 1 0 2-1 3 0 1-3 2-3 2h-1v-1c1-4 3-6 3-10z" class="D"></path><path d="M129 498l1-3 6 1c1 1 2 1 3 1v1c-1 1-3 1-4 2l-1 3c0 4-2 6-3 10v1h-1l-2 2-1-1v1h-1v-3h-2c1-1 0-2 0-3 1-3 0-6-1-8 1 0 1 0 2 1v-2h0c1 0 1 1 2 1s1 0 2-1v-3z" class="j"></path><path d="M129 509l1-2v-1c1 2 1 4 0 6h-1v-3z" class="Y"></path><path d="M127 505c1 0 2 1 3 1v1l-1 2h0-1v-1h0c0-1-1-2-1-3z" class="a"></path><path d="M128 508v1h1 0v3h1 0l1 1v1h-1l-2 2-1-1c1-2 1-5 1-7z" class="g"></path><path d="M125 501c1 0 1 1 2 1v3c0 1 1 2 1 3h0c0 2 0 5-1 7v1h-1v-3h-2c1-1 0-2 0-3 1-3 0-6-1-8 1 0 1 0 2 1v-2h0z" class="V"></path><path d="M125 501c1 0 1 1 2 1v3c0 1 1 2 1 3h0c-1-1-1-1-1-2h0c0 2-1 4-1 5 0-3 0-6-1-10h0z" class="B"></path><path d="M126 511c0-1 1-3 1-5h0c0 1 0 1 1 2h0 0c0 2 0 5-1 7v1h-1v-3-2z" class="k"></path><path d="M129 498l1-3 6 1c1 1 2 1 3 1v1c-1 1-3 1-4 2l-1 3c0 1-1 2-1 3s-1 2-1 2v-1c-1-2-3-3-4-4h1v-2-3z" class="Q"></path><path d="M153 492c2-1 5-2 8-4v2c-1 0-1 1-2 1l-1 1v2c1-1 1-1 2 0 0 0-1 2-2 2l-3 7h0l-8 23c-1 1-1 2-2 3-1 2-3 3-4 4 0-2 0-4-1-6v-1c-1-4-3-10-2-14 1 1 1 2 2 3 0 1 1 2 2 3 1 2 3 3 5 3h1l-1-1h0c-5-1-7-6-9-10 0-2 0-5 1-7l-1-1c0-2 2-2 1-4v-1h4c1 0 3 0 4-1h1s1 0 1-1v-1c-1 1-2 1-3 2h0c0-2 1-2 2-3h0v1l5-2z" class="C"></path><path d="M156 497l2-1h0l-3 7h0v-1c-2 0-2 1-3 1s-2 1-2 1h-1 0-2c1-1 2-1 3-1 0-1 1-1 1-2h-1c3 0 4-2 6-4z" class="O"></path><path d="M158 492v2c1-1 1-1 2 0 0 0-1 2-2 2h0l-2 1c-2 2-3 4-6 4-1 0-3-1-4-2l-1-1h-4c2 0 4 1 5 0h1c4-1 7-3 11-6z" class="I"></path><path d="M158 494c1-1 1-1 2 0 0 0-1 2-2 2h0l-2 1h-1c1-2 2-2 3-3z" class="K"></path><path d="M155 497h1c-2 2-3 4-6 4-1 0-3-1-4-2 3 0 6-1 9-2z" class="e"></path><path d="M153 492c2-1 5-2 8-4v2c-1 0-1 1-2 1l-1 1c-4 3-7 5-11 6h-1c-1 1-3 0-5 0-1 1-1 4-2 5l-1-1c0-2 2-2 1-4v-1h4c1 0 3 0 4-1h1s1 0 1-1v-1c-1 1-2 1-3 2h0c0-2 1-2 2-3h0v1l5-2z" class="n"></path><defs><linearGradient id="q" x1="96.787" y1="483.299" x2="112.068" y2="509.528" xlink:href="#B"><stop offset="0" stop-color="#db0000"></stop><stop offset="1" stop-color="#ac1e23"></stop></linearGradient></defs><path fill="url(#q)" d="M107 480h3l6 3 1 1v1c-1-1-1-1-2-1l-1 1c2 0 3 1 5 1h5l-4 1h-2l2 2-1 1v1c4 3 5 5 6 10h0v2c-1-1-1-1-2-1 1 2 2 5 1 8h0v7 1l-1 1h0v-2c-2-1-2 0-3-2h0-1l-2-1c-1 0-1 1-2 1l-1-1c-2 2-3 2-5 3h-2-1c-1-2-1-2-1-4-1 1-1 1-2 1h-1l1 2h0-1-2l-2-3c-1 0 0 0-1-1 0 0-1 0-1-1l-3-1c-1 0-1 0-1-1-1-1-2-2-2-4-1-1 0-1 1-3-1-2-3-1-5-2 1-1 1-2 2-2v-1c-1-1-2-2-3-2v-1c-1-1-1-1 0-2 0-3 3-4 5-5h0c3-1 8-2 11-4h2c1 0 1 0 2-1v-1h-2v-1c1-1 3 0 4 0z"></path><path d="M114 485c2 0 3 1 5 1h5l-4 1h-2l2 2-1 1v1c-1 0-3-1-3-2l-5-3h0l3-1z" class="u"></path><path d="M116 489c0-1 1-2 2-2l2 2-1 1v1c-1 0-3-1-3-2z" class="g"></path><path d="M94 508l2-2c1-2 1-4 1-7-1-1-1-2-1-3 0-2 2-3 3-5 1 1 1 1 2 1 2 2 4 6 5 8v2 1l1 1h-2c-1 0-2 2-3 2v4 2l1 2h-1l1 2h0-1-2l-2-3c-1 0 0 0-1-1 0 0-1 0-1-1l-3-1c-1 0-1 0-1-1h1c1 0 1-1 1-1z" class="D"></path><path d="M92 509h1c1 0 1-1 1-1h1 2c1 1 1 1 1 3v1h-1s-1 0-1-1l-3-1c-1 0-1 0-1-1z" class="d"></path><path d="M104 502c0 1 0 2 1 2-1 0-2 2-3 2v4 2l1 2h-1c-1 0-1 0-1-1-1-1 0-4-1-6 0 0-1 0-1-1s0-1 1-2c1 0 0 1 1 2 1 0 2-2 3-3v-1z" class="K"></path><path d="M101 513c0-2 0-2 1-3v2l1 2h-1c-1 0-1 0-1-1zm0-21c2 2 4 6 5 8v2 1l1 1h-2c-1 0-1-1-1-2-1-2-3-4-5-7 0-1 1-2 2-3z" class="a"></path><path d="M99 491c3-1 7-2 10-1 6 1 10 3 13 8 0 1 1 2 1 4 1 2 2 5 1 8h0v7 1l-1 1h0v-2c-2-1-2 0-3-2h0-1l-2-1c-1 0-1 1-2 1l-1-1c-2 2-3 2-5 3h-2-1c-1-2-1-2-1-4-1 1-1 1-2 1l-1-2v-2-4c1 0 2-2 3-2h2l-1-1v-1-2c-1-2-3-6-5-8-1 0-1 0-2-1z" class="m"></path><path d="M110 496h4c3 1 5 3 6 6h-1l-1-1c-2-2-5-4-8-4-1 0-2 0-4-1h0 4z" class="O"></path><path d="M106 502c3-1 6-2 9-2 1 1 3 2 3 4 1 2 1 5 1 8v1h-1c0-3-1-7-4-9-2-1-5-1-7 0l-1-1v-1z" class="V"></path><path d="M107 504c2-1 5-1 7 0 3 2 4 6 4 9h1v-1l1 3h-1l-2-1c-1 0-1 1-2 1l-1-1c-2 2-3 2-5 3h-2-1c-1-2-1-2-1-4-1 1-1 1-2 1l-1-2v-2-4c1 0 2-2 3-2h2z" class="D"></path><path d="M104 508c1-1 3-2 5-2s3 1 5 2l-1 1h0c-3-2-4-2-6-1h-2 0-1z" class="g"></path><path d="M113 509l1-1c1 2 2 3 4 5h0 1v-1l1 3h-1l-2-1c-1 0-1 1-2 1l-1-1h0v-1c1-2 0-3-1-4z" class="j"></path><path d="M102 512c1 0 1 0 1-1s0-2 1-3h1 0 2l1 1-1 1c-1 1-1 2-2 2v1h0c-1 1-1 1-2 1l-1-2z" class="Y"></path><path d="M105 508h2l1 1-1 1c-1 1-1 2-2 2h0l-1-2c0-1 1-1 1-2z" class="B"></path><path d="M107 508c2-1 3-1 6 1h0c1 1 2 2 1 4v1h0c-2 2-3 2-5 3h-2-1c-1-2-1-2-1-4h0v-1c1 0 1-1 2-2l1-1-1-1z" class="L"></path><path d="M108 509c0 1 1 1 2 1s1 1 2 1c-1 0-1 1-2 1 0 1-1 1-2 1h-3v-1c1 0 1-1 2-2l1-1z" class="g"></path><path d="M108 509c0 1 1 1 2 1v1c-1 0-1 1-2 0l-1-1 1-1z" class="X"></path><path d="M107 508c2-1 3-1 6 1h0c1 1 2 2 1 4v1h0c-2 2-3 2-5 3h-2v-1c2 0 3 0 5-1 1-1 1-2 1-3l-1-1c-1 0-1-1-2-1s-2 0-2-1l-1-1z" class="D"></path><path d="M274 325h9v1h-2v1h0c2 0 3 0 4 1 0 1 0 2 1 2v1l-1 1 1 1c1 0 1 1 2 1h3l1 1h4 0 0c1-1 2-1 2-2h2l1 1h2 1v1h-1c-1 1-1 1-1 3l1-1c1-1 1-1 2-1 0-1 0-1-1-2l1-1c0-1 1-2 2-3 1 1 1 1 1 2l2-1s1 0 2 1v1l2 2v-1l2 1h0v-1h1s0-1 1-2h0 2v-1-1h3c1 0 3 0 5-1 1-1 1-1 2-1l2-1 1-1h2 3 1v2l4 1c15 4 30 10 41 21 1 2 3 4 5 6h3c2 2 3 4 4 6 4 5 6 10 8 16 1 3 3 5 4 8-3-3-5-7-7-10 0 1 1 3 2 4l15 23v1l-16-23c-1 2 0 8 0 11h0c-1 1 0 5 0 7l12 15c-1 0-2-2-3-3-3-3-7-6-9-10-1-2-2-3-4-5 0 5-1 9-2 13-3 12-8 22-14 31-3 3-5 7-7 9-2-3-3-6-4-9-2-7 0-14 0-21 1-2 1-5 0-7v10h-1v2l-1 1c1 2 0 3 0 5h-1-2c-1 1-2 1-3 1 0-3 0-6-1-9h0-1s0-1-1-1c0-2 0-3-1-4l-1 1v-1h-1v2h-1l1 1-1 1v11h-1c-1 0-2 0-3 1-2 0-5 1-7 1 1-3 1-6 1-10-2 3 0 10-3 10l-8 1h0l-27 5h-3l-64 13v1h2c-1 0-1 1-1 0h-2v2h1c12 6 24 10 34 19l-1 5c1-1 2-3 3-4l2 2 4 4 3 4c-4 7-8 12-14 17l-2 2h2c-3 3-9 5-10 9 0 3 2 5 3 8 0 0 1 1 1 2s0 1-1 1h0c-1 1-1 5-1 7h0l-1 1h0c-1 2-3 4-3 5-1 2-2 3-3 4h-1c-4 2-8 3-12 4h-4c-1 1-1 1-1 2v1h0-1v-2h-2c1 3 2 6 4 9 3 8 9 15 4 23v1l-1-1c-1 1-1 2-2 3l-2 3-1 1-1 1c0-1-5-4-6-5-3-1-7-4-8-7-1 0-1 0-2 1l11 9c3 3 7 5 10 7 0 1-1 1-2 1l4 2c-1 0-2 0-3-1v1l2 1v1l-1-1-16-11c-4-5-9-8-13-12-3-3-6-8-9-12l1-1 1 1h0c-1-3-2-6-2-8v-2c1-7 3-11 8-16 2-1 4-2 5-4 3-4 3-8 5-12 1-2 3-4 4-7 0-1 1-3 2-4 0-1 1-3 1-4 2-3 5-8 5-12l-2 5-5 9c-1 3-2 6-4 8l-1 2-1 2h0c0 2-1 4-2 6v1c-2 5-6 8-10 12l-4-11c-1-2-1-3-2-5 0-1-1-3-1-4l-3-11-6-35-1-4c0-1 1-2 0-3v-2-1c0-2 0-3-1-5v-11-5c-1-2-1-4-1-6v-11-1-5c-1-3 0-6-1-9l1-16-1-2v-6l3-2h0l-2 1v-1l-1-1c1-2 2-2 3-3h0-2v-3c-1-1-1-4-1-6 0-1 0-3 1-4 0-1-1-2 0-3v-1c-1-1-1-3 0-4 3-1 6-3 9-4 3-2 5-3 7-4l14-8c16-9 36-16 54-16z" class="k"></path><path d="M217 466c1 0 2 0 3 1h-2 0l-1-1z" class="P"></path><path d="M230 552l1 1h-1v1 1h-1l1-3z" class="i"></path><path d="M232 580h1l1 3c-1-1-2-1-2-1v-2z" class="H"></path><path d="M253 531l1-3c1 1 1 2 1 3h-1-1z" class="Y"></path><path d="M336 334c2 1 3 2 5 3-1 0-2-1-3-1l-1-1v1c-1 0-1 0-2-1l1-1z" class="b"></path><path d="M207 458h1 3 1c0 1-1 1-2 1h1v1l-4-2z" class="m"></path><path d="M220 396h1l1 2-2 1v-3z" class="B"></path><path d="M329 336c1 0 2 0 4 1v1h-2 0l-1 2c0-1 0-2-1-3v-1z" class="AB"></path><path d="M255 526h2l-1 2-1 1v2h0c0-1 0-2-1-3l1-2z" class="R"></path><path d="M365 367h1c1 1 2 2 2 3l-1 1v1l-1-1v-3c-1 0-1-1-1-1z" class="m"></path><path d="M205 451c0-1-1-2-1-3 1 1 1 1 2 0 1 2 1 3 1 5l-1-1-1-1z" class="C"></path><path d="M233 533l5-3c-2 2-3 4-3 6l-1-1-1-2z" class="g"></path><path d="M259 521h1c-1 2-2 3-3 4h0v1h-2l1-1c1-2 1-3 3-4z" class="K"></path><path d="M231 554h0v1l1-1v3l1 1c0 1 0 1-1 1v3-2c0-2-1-4-1-6z" class="I"></path><path d="M242 506v1h1c0 1-1 1-1 1h-1c-1 1-2 1-3 1v-1c1-1 3-1 4-2z" class="C"></path><path d="M201 407c1-1 3 0 4 0h-1c0 1 2 3 4 4-3-1-5-3-7-4z" class="B"></path><path d="M253 531h1c0 1-1 2-2 3h0l-1 1h-1 0l3-4z" class="R"></path><path d="M201 504c0-1 0-2 1-3l1 1-1 2 1 1c0 1 0 2-1 3l-1-1v-2-1h0z" class="P"></path><path d="M237 502c1-1 2-3 2-4 1-1 2-1 3-2h1c-2 3-5 5-8 8 1-1 1-2 2-2z" class="K"></path><path d="M203 395h8 1c-2 1-3 1-5 1h-4-1l1-1z" class="y"></path><path d="M330 333c2 0 3 0 5 1h1l-1 1c-2-1-6 0-8-1h3v-1z" class="i"></path><path d="M336 427v-2h1 0c0 1 0 2 1 3-1 2-2 5-3 7h0c0-2 0-5 1-8z" class="AR"></path><path d="M210 471h3v1c-3 0-5 0-8 1h0l-5 2c3-2 6-3 10-4z" class="R"></path><path d="M285 333h1c1 0 1 1 2 1h3l1 1h-1 0c-1 1-1 1-2 0l-2 2h0v-1c0-1-1-1-1-1h-1s1-1 0-2z" class="b"></path><path d="M206 417l1-1 6 2c2 1 5 1 7 3-3 0-7-2-10-2-2-1-3-1-4-2z" class="H"></path><path d="M210 562c1 1 1 2 2 3l3 3h-3l-3-4-1-1v1c-1-1-1-1-1-2 2 1 3 2 4 3 0-1-1-2-2-2l1-1z" class="D"></path><path d="M213 512h1v1c0 1 0 2 1 4h0c0 2 1 4 1 6-1-1-1-2-1-2l-1-1c-1-3-2-5-1-8z" class="R"></path><path d="M213 512v-2l2-1h1v3c-1 2 0 3-1 5h0c-1-2-1-3-1-4v-1h-1z" class="n"></path><path d="M208 558h1l1 2 2-1c0 1 1 3 1 4h-1v1 1c-1-1-1-2-2-3l-2-4z" class="AB"></path><path d="M197 421h0 4 2 0l-2 1c-1 1-3 1-4 1-2 1-4 2-6 2h0c2-2 4-2 6-4z" class="B"></path><path d="M204 398c-4-1-7-1-10-1 2-1 6-2 9-2l-1 1h1l1 1v1z" class="J"></path><path d="M236 573c1-1 0-3 1-5l1-1 2 2 1 5c-1 0-2-1-2-2s1-1-1-1h-1v3l-1-1z" class="v"></path><path d="M190 422c2 0 5-2 7-2h6v1h0-2-4 0c-2 1-5 2-7 2v-1z" class="P"></path><path d="M230 393c1-2 3-4 5-5h0c-1 3-2 5-4 8-1-1-1-2-1-2v-1z" class="AG"></path><path d="M230 554h1c0 2 1 4 1 6v2 1c-1-1-2-1-2-3h0-1c0-1 1-4 1-5v-1z" class="P"></path><path d="M229 375l3-4v2c-1 3-2 4-4 6h-1c0-1 1-2 2-3v-1z" class="w"></path><path d="M263 521c0 1 2 3 2 4-1 1-1 5-1 7h0l-1 1h0 0v-8l-1-1v-1h1v-1l-1-1h1z" class="O"></path><path d="M249 380v2h0c0 1 0 2 1 2-1 1-1 1-1 2l-1-1h-2 0c0 2-1 3-1 5-1 1 0 2-1 3v-5-2c1 0 1 0 1-1 1-1 0-1 2-1h0l1-1v-1c0-1 1-1 1-2z" class="b"></path><path d="M203 466v1 1h2v1h-3c-2 1-3 1-5 2h-1l2-2v-1h-1 2 2l2-2z" class="s"></path><path d="M332 416v-1-3l2-2 2 2h-1l-1 2h1 0v1c1 1 1 5 1 6-1-2-1-3-2-4h-1v-1 2c-1-1-1-1-1-2h0z" class="AB"></path><path d="M332 416v-2h1l1 1-1 1v2c-1-1-1-1-1-2z" class="y"></path><path d="M240 569l2 1c0 1 1 2 1 2-1 1-1 2 0 3v2c-1 1-1 1-1 2h1c-1 1-1 2-2 3v-1-1c1-1 0-4 0-6l-1-5z" class="B"></path><path d="M210 388c-4 1-9 2-13 1 2-1 4-1 6-2 3 0 6 0 9-1 0-1 1-1 2-1v1c-1 1-3 1-4 2z" class="z"></path><path d="M274 480c1-1 2-3 3-4l2 2-1 2h0c-1 1-2 2-4 3h0-1v-2s1 0 1-1z" class="C"></path><path d="M192 467c1 0 3 0 4-1l2-2v1 1c0 1 2-1 4 0h1l-2 2h-2-2l-4 1v2l-1-4z" class="p"></path><path d="M255 531h2v1c-2 1-1 1-2 3-1 1-2 1-3 3h0 1c0 1 0 1-1 2l-1-2c0-1 0-2 1-4h0c1-1 2-2 2-3h1 0z" class="v"></path><path d="M203 396h4v1c2 0 3 1 4 0 1 0 4 2 5 2 1-1 0-1 1 0 1 0 1 0 1 1h-3 0c-4 0-8-2-11-2v-1l-1-1z" class="y"></path><path d="M203 396h4v1h-3l-1-1z" class="I"></path><path d="M286 339h2c2 1 4-1 6 0l-1 1c0-1 0-1-1-1h-4c-1 1-1 2-2 2 0 0-1 1-1 2-2 0-2 2-3 2-1 1-2 1-2 2-2 1-4 3-6 5h0v-1c1-1 3-4 4-5 1 0 1-1 2-1h1l1-2h1c1-1 2-3 3-4h0z" class="b"></path><path d="M211 460c1 1 4 1 6 1h7v1c-4 0-10 0-13-1-2 0-3-1-5-2-1 0-2-1-3-2h0c1 0 3 1 4 1h0l4 2z" class="R"></path><path d="M192 378c0-1 1-2 2-3h0c1 0 1 0 1-1 1 0 1 0 2-1h1l2-2h1c1-1 1-1 2-1l1-1s1 0 1-1l1 1h0c-2 1-3 2-4 2-1 1-2 2-3 2-1 1-2 1-2 2v1l-2 2h-1-2 0z" class="O"></path><path d="M336 421c0-1 0-5-1-6v-1h0-1l1-2h1l2 11c0 1 0 1 1 2 0 1 0 1-1 2v1c-1-1-1-2-1-3h0-1v2-6z" class="y"></path><path d="M244 528c2-2 4-4 7-6-1 3-2 5-5 7l-3 3h-2c1-1 1-2 2-3l1-1z" class="v"></path><path d="M243 529h0c1 0 1 0 2-1 0 0 1 0 1-1v2l-3 3h-2c1-1 1-2 2-3z" class="l"></path><path d="M226 478h0c0 2-1 4-2 5-4 5-10 8-16 9h-1v-1c2-1 3-1 5-2 1 0 2 0 3-1h1c5-2 7-6 10-10z" class="u"></path><path d="M209 552v-1h1v1l2 7-2 1-1-2h-1c-1-2-1-3-2-6 1 1 2 1 3 1v-1z" class="B"></path><path d="M306 347c1 0 1 1 2 2 0 0 0 1 1 1l1-1c1 0 2 0 2 1 1 0 1-1 2-1 0 0 0 1 1 2h-2 0l-2 1v-1c-2 1-2 0-3 0-1 1-1 1-2 1 0-1-1-1-1-2l-2 1h-1l1-2 1-2h1v1h1v-1z" class="AD"></path><path d="M201 504h0v1 2l1 1c0 1-1 2-1 4h1c0-1 1-6 2-7 1 1 1 1 1 2-1 1-1 3-2 4 0 2 0 4-1 5v1l-3-11c1 0 1-1 2-2z" class="K"></path><path d="M321 335h0c2-1 3-1 5-1l-1 1v1h4v1c1 1 1 2 1 3h-1-1-2c1-1 1-1 1-2h-3 0 0l-4-1v-2h1z" class="w"></path><path d="M329 337c1 1 1 2 1 3h-1-1c0-1 1-2 1-3z" class="J"></path><path d="M321 335c1 0 2 1 3 1 0 0 0 1 1 1s1-1 2 0c-1 1-2 1-3 1h0l-4-1v-2h1z" class="z"></path><path d="M238 508h-1l3-3c3-3 6-8 6-12v-1h1v1c0 3-2 7-3 9l-3 4c2-1 4-3 6-5 0-1 1-2 2-2v1c-1 2-5 5-7 6-1 1-3 1-4 2z" class="p"></path><path d="M189 408c1 1 1 1 1 2h1c1-1 4-2 5-3v-1c4-1 7-1 11 0 2 1 4 2 5 4-1 0-3-1-3-2-1 0-3-1-4-1s-3-1-4 0c-1 0-2 0-3 1-2 0-3 1-5 2-1 0-2 0-3 1v6c-1-3 0-6-1-9z" class="i"></path><path d="M206 448c-1-1-2-3-2-4 2 1 3 5 4 6 0 0 1 1 2 1 2 1 5 2 7 3-3 0-5 0-8-2v1c1 1 2 2 4 3 1 1 3 1 5 2h1v1h-2l-2-1c-1 0-2 0-3-1l-5-4c0-2 0-3-1-5z" class="t"></path><path d="M324 338h3c0 1 0 1-1 2s-2 0-3 1c1 1 2 3 3 4 1 2 3 4 4 6h0c0 1 1 1 1 2l1 1v1 1 1c-1-1-2-4-3-6-1-1-1-2-2-3s-2-2-2-3c-1-1-3-3-5-4 0 0-1 0-1-1h-1-1l1-1h0c1 0 4 1 5 1v-1h1v-1z" class="J"></path><path d="M364 390c2 5 4 10 4 15-1 0-2-2-2-3-1-2-4-6-4-8l1-1 1 2v-1c-1-1-1-2-1-3s1-1 1-1z" class="B"></path><path d="M270 505h2c-3 3-9 5-10 9 0 3 2 5 3 8 0 0 1 1 1 2s0 1-1 1h0c0-1-2-3-2-4-2-3-3-6-2-9 1-4 7-6 9-7z" class="M"></path><path d="M197 442h0c0-1 0-2 1-3l2 1v1c0 3 0 7 2 9h1 1l1 1 1 1h0c-1 1-3 0-4-1-2 0-3-1-4-2-1-3-1-4-1-7z" class="n"></path><path d="M197 442h0c0-1 0-2 1-3l2 1v1l-1 1v4c0 1 1 1 1 2-2-2-2-4-3-6z" class="H"></path><path d="M240 385c0 5-3 9-5 13-3 4-7 5-12 6-1 0-2-1-3 0h-5-1v-1c2 0 6-1 8-1 1 1 2 1 3 0 9-2 11-10 15-17z" class="AD"></path><path d="M260 532l1-1c0 1 0 2-1 4h0c0 1-1 2 0 3-1 2-2 3-3 4h-1l1-2h-1v-1-1c-2 1-2 1-3 0h-1 0c1-2 2-2 3-3l3-2 2-1z" class="v"></path><path d="M260 532l1-1c0 1 0 2-1 4h0l-1-1c-1 1-2 2-3 2s-3 1-3 2h0-1 0c1-2 2-2 3-3l3-2 2-1z" class="I"></path><path d="M333 417c0 1 1 3 1 4s-1 2-1 3h-1v-2h0 0c-1 2-1 4-3 5-1 1-1 1-2 0s0-4 1-6v-6c-1-1-2-3-1-5h0c1 2 1 4 2 6 1 4-1 8-1 12 1-1 2-3 2-5 0-1 1-2 1-3 1-1 1-3 1-4h0c0 1 0 1 1 2v-2 1z" class="J"></path><path d="M354 419c-2-8-7-14-10-21v-1c1 1 2 4 3 5 2 4 5 7 6 11 1 3 2 5 3 8v11h-1l-1-13z" class="z"></path><path d="M317 409h0c1 2 1 3 2 5 0 4 1 7 0 11s-3 11-6 13h-1c0-2 3-7 5-9 3-6 1-14 0-20z" class="X"></path><path d="M230 393v1s0 1 1 2c-3 2-5 3-8 4h-4l1-1 2-1-1-2 6-3 1 1 2-1z" class="AD"></path><path d="M227 393l1 1c-1 1-4 3-6 4l-1-2 6-3z" class="O"></path><path d="M237 502l-1-1-3 3h-1l-1 1v-1l1-1c0-2 2-3 3-4 2-2 3-4 5-5 0-1 0-1 1-2h1 0c-1 1-1 1-1 2h0c1 0 2-2 2-3l1 1c0 1 0 1-1 2h0l1 2h-1-1c-1 1-2 1-3 2 0 1-1 3-2 4z" class="I"></path><path d="M220 490c0 2 0 2-1 4l-3 3c-1 1-2 1-3 3h0v1c-1 2-2 1-2 3v1h0s-1 0-1 1c-1 0-1 1-2 2v1c0 1 1 1 1 1h1c1 2 0 5 0 7v-2l-1 1c0-2 0-4-1-5h-1-1c1-2 1-4 2-5 1-3 4-7 6-9 0-1 1-1 1-2s4-3 5-5z" class="t"></path><path d="M261 400h1 0c-1 1 0 2 0 3v1c0 3-2 6-4 8 0 1-1 2-2 2-2 0-3 1-4 2s-2 1-3 1v1l-1-1c1-1 2-1 3-2h0c1 0 1 0 1-1 1 0 1-1 2-1h0c1-1 0-1 1-1 1-1 1-2 2-3 1-2 2-3 3-5v-2l1-1v-1z" class="O"></path><path d="M309 340v1c1 0 1-1 2 0 0 0 1 0 1 1l1 1v2h-2c-1 0-1-1-2-1v1h0l-1-1h-1v1c-1 0-1-1-2-1v1l-2-1h-1-2 0v-2c1-1 3-1 4-1 1-1 1-1 2-1 1 1 1 1 2 1l1-1z" class="AP"></path><path d="M320 331v-1h3c1 1 2 0 4 0h1l1 3h1v1h-3 0-1c-2 0-3 0-5 1h0-1v2h-1c0-1 0-1-1-2-1 0-1 0-1-1 0 0 0-1 1-2h0 2v-1z" class="AR"></path><path d="M327 330h1l1 3c-1-1-1-1-3-2l1-1z" class="AM"></path><path d="M297 407h0c1 5 0 10-1 14-1 1-1 1-1 2 3-5 4-10 4-15 2 3 0 9-1 12-3 6-6 9-12 11 1-1 2-2 3-2 6-6 7-14 8-22z" class="j"></path><path d="M234 543c-2-1-5-4-5-6l-1-2c1-2 3-5 5-6l2-2c1 0 4-2 5-2v1c-3 2-6 3-9 6v1c-1 1-1 1-1 3h0l3-3 1 2 1 1-1 3c0 2-1 2 0 3v1z" class="n"></path><path d="M234 535l1 1-1 3h-1s0-1-1-2l2-2z" class="R"></path><path d="M259 521l1-1 1 1c0 2 1 7 0 9v1l-1 1-2 1-3 2c1-2 0-2 2-3v-1h-2v-2l1-1 1-2v-1h0c1-1 2-2 3-4h-1z" class="P"></path><path d="M257 525l1 1v2 2c-1-1-2-1-2-2l1-2v-1z" class="a"></path><path d="M259 529l1-3h1v4 1l-1 1c-1-1-1-2-1-3z" class="R"></path><path d="M255 531v-2l1-1c0 1 1 1 2 2v-2l1 1c0 1 0 2 1 3l-2 1-3 2c1-2 0-2 2-3v-1h-2z" class="g"></path><path d="M258 528l1 1c0 1 0 2 1 3l-2 1-1-1 1-2v-2z" class="J"></path><path d="M235 539c1-1 2-3 3-4 1-2 1-3 2-5 2-3 7-9 11-10h1v1l-2 1c-2 0-6 3-6 6l-1 1c-1 1-1 2-2 3h2c0 1 1 1 1 1-1 2-2 2-4 4-1 0-2 1-3 2h-1-1z" class="Y"></path><path d="M225 539l1-2 1 1h1v1c1 2-1 3 0 5l1 1v2h0v2c0 1 0 2 1 3l-1 3h-1c0-1 0-2-1-2l-1-1c0-1 0-1-1-2v-3l-1-1h0c0-1 0-1 1-2v-3l1-1-1-1z" class="K"></path><path d="M225 539l1-2 1 1h1v1c1 2-1 3 0 5l1 1v2h0v2c-1 0-1 0-1-1v-2h-1v-3-1h-1v3h-1v-1-3l1-1-1-1z" class="P"></path><path d="M225 539l1-2 1 1v1l-1 1-1-1z" class="K"></path><path d="M347 413h-1l1-1c0 1 1 1 1 2 1 1 2 2 2 3 1 1 1 2 2 3h0 1v-1h1l1 13c-1 0-2 0-3 1 0-3 0-5-1-7v-1-1l-2-4v-2l-2-5z" class="B"></path><path d="M307 345v-1h1l1 1h0v-1c1 0 1 1 2 1h2 3 1l1-1v1l1 1 1 1c-1 1-2 1-2 2l-1 1-1 1h0-1c-1-1-1-2-1-2-1 0-1 1-2 1 0-1-1-1-2-1l-1 1c-1 0-1-1-1-1-1-1-1-2-2-2 0-1 1-2 1-2z" class="b"></path><path d="M295 345c-1 0-1 0-1-1s1-1 1-2c1 1 1 1 2 1s2 1 3 1h0 0 2 1l2 1v-1c1 0 1 1 2 1 0 0-1 1-1 2v1h-1v-1h-1l-1 2v-1l-2 3-1-1v-1l-1-1-1 1h-1c-1-1-2-2-2-4z" class="AB"></path><path d="M299 348l2-1h1v1c0 1-1 1-2 2v-1l-1-1z" class="B"></path><path d="M295 345c-1 0-1 0-1-1s1-1 1-2c1 1 1 1 2 1h-1v1c1 1 1 1 2 1l-1 1h-1c-1 0-1 0-1-1z" class="b"></path><path d="M207 511h1c1 1 1 3 1 5l1-1v2 1 5s0 1-1 1h-1v-3c-1 1-1 1-1 2l-1-1h0v3l-1 1c-1-2-1-3-2-5 2-1 3-8 3-10h1z" class="R"></path><path d="M207 511h1c1 1 1 3 1 5v2h-1c0-3-1-5-1-7z" class="c"></path><path d="M206 525v-4-6h1c0 1 0 2 1 3v3h0c-1 1-1 1-1 2l-1-1h0v3z" class="H"></path><path d="M209 516l1-1v2 1 5s0 1-1 1h-1v-3h0v-3h0 1v-2z" class="f"></path><path d="M208 518h1v6h-1v-3h0v-3h0z" class="B"></path><path d="M254 383v-1l1-1c1 3 0 7 0 10 1 0 0 1 0 2-1 2-1 7-3 9h-1c-1 0-1 2-2 3h0l-2 2h0 2v1h1c1-1 1-1 1-2l1-1 3-3v1c-1 2-3 5-4 7-1 1-4 4-6 5h-2 0l2-2v-1s-1 0-1 1h-2l2-1c1 0 1-1 1-1 1 0 1-1 1-1 1 0 2-1 2-2h0c-1 0-1 0-2 1h0l-1-1c1-2 3-3 4-5l2-2c0-1 1-2 1-3s1-1 1-2l1-3v-2h1v-1-4h0c0-1 0-2-1-3z" class="i"></path><path d="M223 514c0-1-1-3 0-4 0-2 1-5 2-7l1-1v-1h1l-1-1c-2 1-4 2-6 4l-1-1c1 0 1-1 2-1 1-1 2-2 3-2l1-1c2-1 3-3 4-4s1-2 2-2h0v1c-1 1-1 2-1 2h1l1 1-5 9c-1 3-2 6-4 8z" class="O"></path><path d="M237 574v-3h1c2 0 1 0 1 1s1 2 2 2c0 2 1 5 0 6v1 1l-2 3-1 1c-1-1-2-2-4-3l1-1v-6c0-1 0-2 1-3l1 1z" class="l"></path><path d="M237 579l1-2v-1l1 1c0 1 0 1-1 2v1 1l-1 1v-3z" class="Aa"></path><path d="M238 581c1-1 1-1 2-1l1 1v1l-2 3c-1-1-2-2-2-3l1-1z" class="V"></path><path d="M234 583l1-1v-6c0-1 0-2 1-3l1 1v5 3c0 1 1 2 2 3l-1 1c-1-1-2-2-4-3z" class="O"></path><path d="M294 339c1 0 2-1 2-1 1 0 1 0 2-1h0v-1s1-1 2-1l2-1 1 1c-1 1-1 1-1 3s-3 3-4 4h0-2v-2c-1 0-2 1-3 1-2 1-5 5-6 7l-1 1-3 3v1c0 1-1 2-1 3-2 1-3 4-4 6 0 1-1 3-2 4h0c0-1 1-2 1-3 1-4 3-8 5-11 4-5 6-9 11-12l1-1z" class="w"></path><path d="M244 533c1-1 3-2 4-2 0-1 1-2 2-2-2 2-3 4-5 6-1 1-2 1-3 2-3 2-5 3-7 6l1 4c1 3 2 6 4 9 3 8 9 15 4 23v1l-1-1c1-2 1-3 1-5 1-7-3-13-6-19-2-4-3-8-4-12v-1l1-3h1 1c1-1 2-2 3-2 2-2 3-2 4-4z" class="E"></path><path d="M206 461l-2-1h1l3 2c1 0 2 1 3 1h0c1 1 6 2 6 3l1 1h0l-1 1h-2c0 1-1 1-1 2l-2-1h-2c-1-1-2-1-2-1l-1-1c-1 0-1-1-2-1v-1c-1-1-1-2-2-3h1 1l-1-1h2z" class="B"></path><path d="M205 462l-1-1h2l1 1c1 1 1 1 2 1 0 0 0 1 1 1 1 1 3 2 5 2v1c-1 0-2 0-4-1-1 0-3-1-4-2h-2v-2z" class="m"></path><path d="M220 490c1 0 1-1 3-1l-1 1v1h-1l1 1-2 2c0 1 0 1 1 0s2-3 3-4v-1h1v-1h1c1-1 1-3 3-5 0 2-1 2-1 4l-1 1v1c-1 2-3 3-4 5 0 1 0 1-1 1v1l-3 3c0 1-1 1-2 2 0 1-1 2-2 3v-1s0-1 1-2c0-1 1-1 1-2v-1c-1 1-3 2-3 3l-1 1c-1 2-1 3-3 4 0-1 1-1 1-1h0v-1c0-2 1-1 2-3v-1h0c1-2 2-2 3-3l3-3c1-2 1-2 1-4z" class="O"></path><path d="M218 509h1c1 0 1 2 1 4 0 1 0 2 1 4l1-1-1 2h0c0 2-1 4-2 6v1c0-1-1-1-2-1h-1v-1c0-2-1-4-1-6 1-2 0-3 1-5h1c0-1 0-2 1-3z" class="H"></path><path d="M219 524v-1c-1-1-1-2 0-3v-1l1-1h0 1c0 2-1 4-2 6z" class="B"></path><path d="M219 509c1 0 1 2 1 4 0 1 0 2 1 4h-1c-2-1-1-6-1-8z" class="p"></path><path d="M218 509v8c0 1-1 1-1 3l-1-1c0-1 0-2 1-3v-4c0-1 0-2 1-3z" class="Z"></path><path d="M229 560h1 0c0 2 1 2 2 3v1 11l1 5h-1l-3-2-1-3c-1-4-1-7 0-11l1-4z" class="R"></path><path d="M229 560h1 0c0 2 1 2 2 3v1c-1 0-1-1-2-1v-1h0-1l-1 2 1-4zm-1 15c1 0 2-1 2-1 0-1-1-2-1-2h1c1 1 1 2 1 4l1-1 1 5h-1l-3-2-1-3z" class="Y"></path><path d="M248 540l1-2c0 1 0 3 1 3l2-1c1-1 1-1 1-2 1 1 1 1 3 0v1 1h1l-1 2c-4 2-8 3-12 4h-4c-1 1-1 1-1 2v1h0-1v-2-1c1-2 1-3 2-5h1 2v1h-1 0v1h2v-1h3v-1l1-1z" class="l"></path><path d="M238 547v-1c1 1 1 1 1 3h0-1v-2z" class="e"></path><path d="M253 538c1 1 1 1 3 0v1 1c-1 1-2 1-3 2h-2-1-1l1-1 2-1c1-1 1-1 1-2z" class="V"></path><path d="M241 541h2v1h-1 0v1h2v-1h3v-1l1-1v3c-1 1-3 1-4 1h-1c-1 1-3 1-4 1h0c0-1 2-3 2-4z" class="I"></path><path d="M305 440c0-3 3-9 3-12 1-1 1-2 1-3 1 0 1-1 1-2 1-1 1-1 1-2l1-1v-1c0-1 0-1 1-1h0v1c0 1-1 3-1 4-1 1-1 2-2 4 0 0 0 1-1 2h1l1-1c0-1 1-1 1-2 1 0 1 0 1 1l3-7h0c1 2-1 4-1 6-1 3-3 6-5 9 0 1-2 3-2 4v1h-3z" class="H"></path><path d="M309 429h1l1-1c0-1 1-1 1-2 1 0 1 0 1 1s-1 2-2 3c-1 3-2 6-5 8 1-2 2-4 2-5l1-2v-2z" class="s"></path><path d="M249 386c0-1 0-1 1-2 0 7-3 15-8 20-1 1-4 2-5 4l2-1v1c-1 2-4 3-7 3l-3 1-1 1c-1-1 0-2-1-2h-3v-1h4c3-1 6-2 8-3 3-2 7-5 8-8l5-13z" class="AK"></path><path d="M311 337l1-1c-1-1-1-1-1-2l1-1 2 2v-1l2 1h0v-1h1c0 1 0 1 1 1 1 1 1 1 1 2h1l4 1h0 0v1h-1v1c-1 0-4-1-5-1h0l-1 1h-1l-1-1h-1c0 1-1 1 0 2 0 1 1 1 2 1h1c1 1 1 2 2 3v1l-1-1v-1l-1 1h-1-3v-2l-1-1c0-1-1-1-1-1-1-1-1 0-2 0v-1l1-1v-2h1z" class="y"></path><path d="M311 337c1 1 2 2 2 3v1 1 1l-1-1c0-1-1-1-1-1-1-1-1 0-2 0v-1l1-1v-2h1z" class="B"></path><path d="M317 334c0 1 0 1 1 1 1 1 1 1 1 2h1l4 1h0 0v1h-1v1c-1 0-4-1-5-1h0-1l-1-2h-2c-1-1 0-2 0-2v-1l2 1h0v-1h1z" class="AK"></path><path d="M319 337h1l4 1h0 0v1h-1c-2 0-2 0-4-1v-1z" class="i"></path><path d="M324 338h0 0v1h-1v-1h1z" class="B"></path><defs><linearGradient id="r" x1="212.842" y1="374.168" x2="214.561" y2="380.761" xlink:href="#B"><stop offset="0" stop-color="#101113"></stop><stop offset="1" stop-color="#302b2a"></stop></linearGradient></defs><path fill="url(#r)" d="M221 372h0v1-1l1 1c-1 0-1 1-1 1v1c0 1-3 4-4 5h0c-1 1-3 2-4 3-1 0-2 1-3 1h0l-6 1h0l1-1h2v-1c-1-1-2-1-3-2h1l3-1h1l1-1 5-3c1-1 2-2 3-2v-1l2-1h0 1z"></path><path d="M254 383c1 1 1 2 1 3h0v4 1h-1v2l-1 3c0 1-1 1-1 2s-1 2-1 3l-2 2c-1 2-3 3-4 5l1 1h0c1-1 1-1 2-1h0c0 1-1 2-2 2 0 0 0 1-1 1 0 0 0 1-1 1l-2 1h-1c-1 1-2 1-3 2-3 1-6 1-9 1 1 0 2-1 3-1s1 0 2-1c7-4 14-9 17-17 1-2 1-4 2-6 1-3 1-6 1-8z" class="y"></path><path d="M229 375v1c-1 1-2 2-2 3h1c-7 7-16 11-26 12-2 1-5 0-7 1h-5l-1-2c6 1 10 0 15 0 1-1 3-1 4-1l2-1h0c1-1 3-1 4-2v-1h0c5-1 12-7 15-10z" class="AE"></path><path d="M366 429v-4-3c-1 0-1 0-1-1v-1h0c0-1-1-2-1-3s-1-2-1-3v-3c-1-1-1-2-1-3v-1h0 1c0 1 0 2 1 2l1 1c1 1 1 2 1 4 1 0 1 0 1 1v1s0 1 1 1c0 0 0-1-1-2v-1-2-2h0c1 1 1 2 1 3 1 3 0 5 1 8h0c0 1 0 2 1 3l-1 1c1 2 0 3 0 5h-1-2v-1z" class="O"></path><path d="M366 429l1-1v-3-5h-1v-2l-1-1v-2l1 1s0 1 1 1c0 1 0 2 1 3v1 2c0 1 1 1 1 2h0c1 2 0 3 0 5h-1-2v-1z" class="s"></path><path d="M346 424c1-1 0-3 0-4v-2c0-1-1-3 0-5h1l2 5v2l2 4v1 1c1 2 1 4 1 7-2 0-5 1-7 1 1-3 1-6 1-10z" class="C"></path><path d="M209 564l3 4h3l1 1 2 1 2 4v1 1h1 0l11 9c3 3 7 5 10 7 0 1-1 1-2 1-9-4-16-12-23-19-3-2-8-7-8-10z" class="AA"></path><path d="M215 568l1 1 2 1 2 4v1 1a30.44 30.44 0 0 1-8-8h3z" class="I"></path><path d="M356 419v-1h0v-4-2c-1 0-1-1-1-2 0 0-1-1-1-2v-1c0-1-1-3-1-3-1-2-2-4-3-5-2-2-3-4-4-5l-2-2h-1l-1-1h1 1l1 1c1 0 2 2 3 3 0 1 1 2 2 3s1 1 2 1v1l2 2v-1-1l-1-1v-2h0v-1c1 1 2 3 2 4 1 1 1 1 1 2 0 0 0 1 1 1v2 1c0 1 1 1 1 2v2s0 1 1 2c0 1 1 2 1 3s1 2 1 3h0v1 1s0 1 1 2h-1s0-1-1-1c0-2 0-3-1-4l-1 1v-1h-1v2h-1z" class="b"></path><path d="M244 393c1-1 0-2 1-3 0-2 1-3 1-5h0 2l1 1-5 13c-1 3-5 6-8 8-2 1-5 2-8 3 0-1 0-2-1-2-1-1-2 1-4-1h3 1c1 0 1-1 2-1h0c1 0 1 0 2-1 1 0 1 0 2-1h1c1-1 2-2 2-3h0l2-2 1 1c-1 1-1 1-1 2 1-1 1 0 2 0 1-2 2-3 2-5l1-1v-2c1 0 1 0 1-1h0z" class="s"></path><path d="M296 335h0c1-1 2-1 2-2h2l1 1h2 1v1h-1l-1-1-2 1c-1 0-2 1-2 1v1h0c-1 1-1 1-2 1 0 0-1 1-2 1-2-1-4 1-6 0h-2c-2 1-4 3-6 4-1 1-1 1-2 1v1l-2 1c-1 0-1 1-1 1l-1 1h-1l-1 2h-1l-3 3c-1 0-3 3-3 4-1 1-3 1-3 3l-3 3c0 1-1 1-2 2 0 1 0 1-1 1v1c-2 2-3 4-4 6-1 1-1 3-2 4 0 1 0 1-1 2h0v1c0 1-1 1-1 2v1l-1 1h0v-2h0c0-1 1-3 1-4 2-3 4-7 6-11l3-3v-1l2-2 2-2c1-2 4-4 6-6 0-1 1-2 1-2 2-1 2-2 3-3 1 0 1 1 1 0l3-3c1 0 1-1 2-1h1c0-1 1-2 1-2 3-1 5-4 8-5h0l2-2c1 1 1 1 2 0h0 1 4 0z" class="J"></path><path d="M296 335h1v1c-2 0-4 0-5-1h-1 1 4z" class="B"></path><defs><linearGradient id="s" x1="223.13" y1="566.328" x2="232.171" y2="563.079" xlink:href="#B"><stop offset="0" stop-color="#101315"></stop><stop offset="1" stop-color="#2a1c19"></stop></linearGradient></defs><path fill="url(#s)" d="M227 553c1 0 1 1 1 2h1 1c0 1-1 4-1 5l-1 4c-1 4-1 7 0 11l1 3 3 2v2s1 0 2 1h0c2 1 3 2 4 3l-1 1c0-1-5-4-6-5-3-1-7-4-8-7 0-1-1-2-1-3v-2c1-3 3-6 3-9h0 1v-2-1h0v-5h1z"></path><path d="M225 561h1c-1 2-3 6-2 9 0 1 2 5 3 7l1 2 1-1 3 2v2s1 0 2 1h0c2 1 3 2 4 3l-1 1c0-1-5-4-6-5-3-1-7-4-8-7 0-1-1-2-1-3v-2c1-3 3-6 3-9h0z" class="AA"></path><path d="M229 578l3 2v2c-2-1-3-2-4-3l1-1z" class="P"></path><path d="M220 396l-4-2h-5-8l-1-1c4 0 9-1 12 0 2 1 6 2 8 1h1c3-1 5-3 7-5 1 0 1-1 1-1v-1c0-1 2-2 2-3 2-5 4-9 7-14l5-7c1-2 2-4 4-5 4-6 9-10 15-13 2-2 4-2 5-4 3-1 6-2 8-3 1 0 1-1 1-1 1 0 1 0 1 1-1 1-2 1-4 2-3 1-6 3-9 5l-9 6c-2 1-5 4-6 6-3 4-6 7-9 12-3 4-5 10-7 15h0v2l1 1-1 1h0 0c-2 1-4 3-5 5l-2 1-1-1-6 3h-1z" class="y"></path><path d="M235 384h0v2l1 1-1 1h0 0c-2 1-4 3-5 5l-2 1-1-1c4-3 6-5 8-9z" class="s"></path><path d="M210 518l1 1h1l1 1h1l1 1s0 1 1 2v1h1c1 0 2 0 2 1-2 5-6 8-10 12l-4-11 1-1v-3h0l1 1c0-1 0-1 1-2v3h1c1 0 1-1 1-1v-5z" class="m"></path><path d="M207 523c0-1 0-1 1-2v3c0 2 0 4-1 6v-7z" class="t"></path><path d="M210 523h1v-3h1c0 1 0 3-1 4h0c0 1 0 1 1 2 1 0 1-1 1-2h1c0 2 0 5-1 6-1 0 0-2-2-3h0c-1 1-1 1-1 2h-1c1-2 1-3 0-4v-1c1 0 1-1 1-1z" class="X"></path><path d="M210 518l1 1h1l1 1h1l1 1c-1 1-1 2-1 3h-1c0 1 0 2-1 2-1-1-1-1-1-2h0c1-1 1-3 1-4h-1v3h-1v-5z" class="K"></path><path d="M220 534c1-1 3-1 4 0h1l1 1h1v1h0v2l-1-1-1 2 1 1-1 1v3c-1 1-1 1-1 2h-1-1l-1-1c-2-1-3-3-3-5h-1c-2 1-3 1-4 3-1 1-1 2-2 3 0 2 0 4-1 6v-1h-1v1 1c-1 0-2 0-3-1 0-1 0-1 1-2 0-1 0-3 1-4 1-3 3-8 6-9 2-1 4-3 6-3z" class="P"></path><path d="M224 539v-1c1 0 1 0 1 1l-1 1v-1z" class="i"></path><path d="M219 539v-1c1-1 1-1 2-1v1l-2 1z" class="AB"></path><path d="M207 550h3c-1 1-1 1-2 1h0l1 1v1c-1 0-2 0-3-1 0-1 0-1 1-2z" class="J"></path><path d="M220 534c1-1 3-1 4 0h1l1 1h1-4c-1 1-1 1-3 1v-2z" class="O"></path><path d="M221 538h2l1 1v1l1-1h0l1 1-1 1v3c-1 1-1 1-1 2h-1-1l-1-1c-2-1-3-3-3-5h-1c0-1 0-1 1-1v1c0-1 0-1 1-1h0l2-1z" class="I"></path><path d="M217 540c0-1 0-1 1-1v1c0-1 0-1 1-1v3c1 0 1 0 1 1 1 1 2 2 2 3l-1-1c-2-1-3-3-3-5h-1z" class="B"></path><path d="M225 539h0l1 1-1 1v3c-1 1-1 1-1 2h-1c-1-3-1-4 1-6h0l1-1z" class="Y"></path><path d="M225 539h0l1 1-1 1-1-1h0l1-1z" class="H"></path><path d="M364 390c-1-3-2-5-3-7 1 1 2 3 3 4 1 2 3 5 4 7l-3-7 4 5c0-2-1-4-2-5v-1h0c0-2-2-3-2-5 1 0 1 0 2 1 0-2-1-3-2-4-1-3-2-6-4-9-3-6-7-12-12-17-2-3-4-4-6-7h0c10 8 18 20 24 32 3 7 4 14 4 22l-2-4h0c0 1 0 2 1 3v1 5c0 3 1 6 1 8v10h-1c0-6 0-11-2-17 0-5-2-10-4-15z" class="AH"></path><path d="M283 482l3 4c-4 7-8 12-14 17 0 0 0-1 1-1v-1h-1c-1 1-2 1-3 2h-1-1c0 1 0 1-1 1h0-1l-1 1h-1v-1h-1v-1c1 0 1-1 2-1h1l1-1c2-1 5-4 5-5s0-1 1-1v-1-1c1-1 1-1 1-2h0v-1c1-2 2-2 3-3l1-1c1-1 2-1 3-2h0v-1c1 0 2 0 3-1z" class="C"></path><path d="M332 327l1-1h2 3 1v2c-1 0-1 0-1 1l3 1c-1 1-1 2-1 2v1 2c4 2 8 5 12 8 18 15 31 33 46 50 1 2 3 4 4 6l12 15c-1 0-2-2-3-3-3-3-7-6-9-10-1-2-2-3-4-5l-16-20c-10-11-20-23-32-32-3-3-6-5-9-7-2-1-3-2-5-3h-1c-2-1-3-1-5-1h-1l-1-3h-1c-2 0-3 1-4 0 1 0 3 0 5-1 1-1 1-1 2-1l2-1z" class="AL"></path><path d="M332 327l1-1h2 3 1v2c-1 0-1 0-1 1l3 1c-1 1-1 2-1 2v1 2c-3-2-5-2-8-3v-1c1 0 1 0 1-1h-5-1c-2 0-3 1-4 0 1 0 3 0 5-1 1-1 1-1 2-1l2-1z" class="B"></path><path d="M332 327l1-1h2 3 1v2c-1 0-1 0-1 1h-1c-1-1-2 0-3-1h-4l2-1z" class="w"></path><path d="M217 540h1c0 2 1 4 3 5l1 1h1 1 0l1 1v3c1 1 1 1 1 2l1 1h-1v5h0v1 2h-1 0c0 3-2 6-3 9v2c0 1 1 2 1 3-1 0-1 0-2 1h0-1v-1-1l-2-4-2-1-1-1-3-3v-1-1h1c0-1-1-3-1-4l-2-7c1-2 1-4 1-6 1-1 1-2 2-3 1-2 2-2 4-3z" class="c"></path><path d="M216 554l-1-2c1 0 1-1 1-2h-2v-1l2-2c1-1 1-1 3-1v1c0 1-1 2-1 3-1 1 0 1 0 2l-1-1c-1 1-1 2-1 3zm7-8h1 0l1 1v3c1 1 1 1 1 2l1 1h-1v5h0v1 2h-1v-5l-1-1-1-4v-1c-1-1-2-3-2-5l1 1h1z" class="J"></path><path d="M216 554c0-1 0-2 1-3l1 1c0 1 1 2 2 4h-1v1c1 2 1 2 1 4-1 0 0 1 0 1l-1-1h-1v1 3c0 1-1 2 0 3v1l1 1h-1l-2-1-1-1-3-3v-1-1h1c1 0 1 0 2-1v-2c0-1 0-3 1-4v-2z" class="v"></path><path d="M217 558v-3h0v-1h1c0 1 0 1 1 2h0v1h-1 0c0 1 0 1-1 1z" class="l"></path><path d="M217 558c1 0 1 0 1-1h0 1c1 2 1 2 1 4-1 0 0 1 0 1l-1-1h-1v1c0-1 0-3-1-4z" class="V"></path><path d="M216 556v4c0 2 1 4 1 6 0 1-1 2-1 3l-1-1-3-3v-1-1h1c1 0 1 0 2-1v-2c0-1 0-3 1-4z" class="H"></path><path d="M215 560c1 2 1 3 0 4v1l-1 1-2-2v-1h1c1 0 1 0 2-1v-2z" class="i"></path><path d="M220 556c1 1 1 2 2 2l1 1v3h1v-1h1c0 3-2 6-3 9v2c0 1 1 2 1 3-1 0-1 0-2 1h0-1v-1-1l-2-4h1l-1-1v-1c-1-1 0-2 0-3v-3-1h1l1 1s-1-1 0-1c0-2 0-2-1-4v-1h1z" class="R"></path><path d="M222 572c0 1 1 2 1 3-1 0-1 0-2 1h0-1v-1c1-1 1-2 2-3z" class="q"></path><path d="M221 564c0 2 1 7-1 9v1l-2-4h1c1-1 0-3 0-4 1-1 1-2 2-2z" class="V"></path><path d="M220 562v-1c1 0 1 1 1 2h0v1c-1 0-1 1-2 2 0 1 1 3 0 4l-1-1v-1c-1-1 0-2 0-3v-3-1h1l1 1h0z" class="Aa"></path><path d="M218 562v-1h1l1 1h0c0 2-1 2-2 3h0v-3z" class="N"></path><path d="M222 558l1 1v3h1v-1h1c0 3-2 6-3 9v-1c-1-2-1-5 0-7 1-1 0-3 0-4z" class="I"></path><path d="M338 329c0-1 0-1 1-1l4 1c15 4 30 10 41 21 1 2 3 4 5 6h3c2 2 3 4 4 6 4 5 6 10 8 16 1 3 3 5 4 8-3-3-5-7-7-10 0 1 1 3 2 4l15 23v1l-16-23c-1 2 0 8 0 11h0c-1 1 0 5 0 7-1-2-3-4-4-6-15-17-28-35-46-50-4-3-8-6-12-8v-2-1s0-1 1-2l-3-1z" class="C"></path><path d="M398 393c1-2 1-4 0-6-1-1-2-3-4-5-3-4-6-7-10-11-1-2-4-3-5-5v-7s0 1 1 1c0 1 1 2 2 4h-1c0-1 0-1-1-1 0 1 1 3 2 4 2 3 5 5 7 7l9 12v-6c0-1 1-1 2-2 1 1 1 2 2 3h0c-1 2 0 8 0 11h0c-1 1 0 5 0 7-1-2-3-4-4-6z" class="r"></path><path d="M398 380c0-2-1-3-2-5-2-3-6-6-9-8-1-2-2-3-3-5l-12-11h1c3 2 6 5 8 7 1 1 2 3 3 3 2 1 3 4 5 6s4 4 7 5l-3-10c4 3 5 6 7 10 0 1 1 2 1 4 0 1 1 3 2 4l15 23v1l-16-23h0c-1-1-1-2-2-3-1 1-2 1-2 2z" class="AA"></path><path fill="#e4af20" d="M338 329c0-1 0-1 1-1l4 1c15 4 30 10 41 21 1 2 3 4 5 6h3c2 2 3 4 4 6 4 5 6 10 8 16 1 3 3 5 4 8-3-3-5-7-7-10 0-2-1-3-1-4-2-4-3-7-7-10h0c-2-3-6-6-9-9-1-1-2-3-3-4-11-10-25-15-40-19l-3-1z"></path><path d="M389 356h3c2 2 3 4 4 6-3-1-5-4-7-6z" class="AX"></path><path d="M274 325h9v1h-2v1h0c2 0 3 0 4 1 0 1 0 2 1 2v1l-1 1 1 1h-1c-1-1-2-1-3-1h-1l-9 3h-1l-2 1c-1 0-1 0-2 1h-1-1 0l-3 1-2 1-2 1-15 8-3 1-6 3-1 1c-1 1-1 1-2 1l-2 2c-2 0-3 2-5 2-2 1-4 3-6 4 0 0-1 0-2 1l-3 2c-1 1-2 1-3 1 0 1-1 1-1 1v-1l1-1h1c1-1 1-1 2-1l1-1c1 0 1 0 2-1 2-2 5-3 7-4l2-1 5-3v-1h-1l-1 1c0-1 1-1 1-1 1 0 2-1 3-2 0 0 1 0 1-1h1 1c0-1 1-1 2-2h1l1-1c1-1 1-1 2-1 1-1 1-1 1-2h0l1-1s1 0 1-1h-1c-1 1-2 1-3 2l-1 1c-2 1-5 3-7 4 0 0-1 1-2 1-1 1-4 3-6 4-1 0-3 1-4 2s-2 2-3 2-1 1-2 1l-1 1c-1 0-1 0-1 1-1 0-2 0-2 1-1 0-1 0-1 1-2 1-4 2-5 3-1 0-2 1-2 1l-2 1-3 2c0-1-1-1-1-1-1 0-2 0-3 1h1l-1 1s0 1-1 1v1l-3 2c-1-1-1-4-1-6 0-1 0-3 1-4 0-1-1-2 0-3v-1c-1-1-1-3 0-4 3-1 6-3 9-4 3-2 5-3 7-4l14-8c16-9 36-16 54-16z" class="C"></path><path d="M190 365c1 0 3-1 4-2h0 2 0c-1 1-1 2-2 2l-1 1c1 0 2 0 2-1 1 0 2 0 3-1h0v1c-3 1-6 3-9 4 0-1 0-3 1-4z" class="k"></path><path d="M199 353h1 2l1 1v1l-2 2c-1 0-2 1-3 1s-1 1-1 1c1 1 2 0 3 0l-2 2h-2v1c1 0 2-1 4-2l6-3 2-2c1 0 2-1 2-1l7-4c1-1 2-1 3-2s2-1 3-2h1 1c-2 2-5 3-7 4l-1 1c-1 1-1 1-2 1l-4 3c-1 0-2 1-3 2-1 0-2 1-3 1s-2 1-3 2h-1c0 1-1 1-2 1-1 1-1 2-3 2h0-2 0c-1 1-3 2-4 2 0-1-1-2 0-3v-1c-1-1-1-3 0-4 3-1 6-3 9-4z" class="b"></path><path d="M230 353h2c3-3 7-4 11-6l5-3 1-1 3-1c3-2 6-4 9-5h2c0-1 1-1 1-1 2 0 3-1 4-1s1-1 2-1h2c1-1 3-1 5-2h3c2 0 2-1 3-1v-1c-2-1-6-1-8 0l-5 1h-1l-5 1-1 1c-1 0-2 1-3 1-2 1-3 1-4 2h-1-1v-1c2 0 3-1 4-2h2v-1h0l2-1h1l4-1h2c1 0 1-1 1-1h2s2-1 3-1h3c1-1 2-2 3-2v1h0c2 0 3 0 4 1 0 1 0 2 1 2v1l-1 1 1 1h-1c-1-1-2-1-3-1h-1l-9 3h-1l-2 1c-1 0-1 0-2 1h-1-1 0l-3 1-2 1-2 1-15 8-3 1-6 3-1 1c-1 1-1 1-2 1l-2 2c-2 0-3 2-5 2-2 1-4 3-6 4 0 0-1 0-2 1l-3 2c-1 1-2 1-3 1 0 1-1 1-1 1v-1l1-1h1c1-1 1-1 2-1l1-1c1 0 1 0 2-1 2-2 5-3 7-4l2-1 5-3v-1z" class="w"></path><path d="M190 321v-1c-1-1-1-3-1-5v-9c0-20 0-40 2-60l3-24c3-11 7-22 12-33 15-28 41-49 72-59 25-7 50-4 73 8 29 16 50 45 59 77 4 14 7 29 8 43 1 15 1 30 1 44v68 24c0 2 0 7-1 8v1l-15-23c-1-1-2-3-2-4 2 3 4 7 7 10-1-3-3-5-4-8-2-6-4-11-8-16-1-2-2-4-4-6h-3c-2-2-4-4-5-6-11-11-26-17-41-21l-4-1v-2h-1-3-2l-1 1-2 1c-1 0-1 0-2 1-2 1-4 1-5 1h-3v1 1h-2 0c-1 1-1 2-1 2h-1v1h0l-2-1v1l-2-2v-1c-1-1-2-1-2-1l-2 1c0-1 0-1-1-2-1 1-2 2-2 3l-1 1c1 1 1 1 1 2-1 0-1 0-2 1l-1 1c0-2 0-2 1-3h1v-1h-1-2l-1-1h-2c0 1-1 1-2 2h0 0-4l-1-1h-3c-1 0-1-1-2-1l-1-1 1-1v-1c-1 0-1-1-1-2-1-1-2-1-4-1h0v-1h2v-1h-9c-18 0-38 7-54 16l-14 8c-2 1-4 2-7 4-3 1-6 3-9 4l-1-1v-6l1-5c-1-2-1-3-1-5l1-2s1 1 2 1v-1-3h0v-1c0-1-1-1-2-1v-2-5-1-4h1-1z" class="C"></path><path d="M224 281c1 1 1 2 2 2l-1 1-1 1-1-1 1-3zm163-10v-1l2-1c0 2 1 3 1 4h0-1-1s0-1-1-1v-1z" class="AN"></path><path d="M232 177c1-1 3-1 4-1 1 1 2 1 3 2-1 0-1 1-2 1l-2-1h0c-1 0-2-1-3-1z" class="AV"></path><path d="M221 288l2-4 1 1 1-1 1 1 1-1-3 4v1h-1v-1h-2z" class="AQ"></path><path d="M385 264c1 2 2 4 4 5l-2 1v1l-2-1c1-1 0-2-1-4h0c0-1 1-2 1-2zm-156 22v1c-2 1-3 2-5 4h0l-3 2h-1l1-3v-1-1h2v1h1l5-3z" class="AY"></path><path d="M382 246c0 1 1 2 1 3v3l1 1-2 2c1 1 2 1 2 2v1c-1 1-1 1 0 2l-2 2c0-2-1-4-2-5v-1h1v-3l1-7zm-143-68c5 3 9 8 13 12h-3c-3-4-7-9-12-11 1 0 1-1 2-1zm-5 73v-1c1 0 1 0 2-1-1-2-1-5-1-7 0-1 0-1 1-2l-2-2c1 0 2-1 3-1 1 1 1 1 1 2l-1 2v3c1 3 1 5 0 8v1h-1c0-1 0-2-1-2h-1z" class="AZ"></path><path d="M237 244v2h-1v-1-3-1h1v3z" class="AS"></path><path d="M384 346h2 0v2c1 3 5 6 6 8h-3c-2-2-4-4-5-6v-4z" class="AA"></path><path d="M378 229h2c0 1 1 3 2 4 0 2 2 4 2 5 1 3 0 8-1 11 0-1-1-2-1-3v-5c1-4-2-7-4-11v-1h0z" class="AV"></path><path d="M228 200c-1-3-1-9 0-12 0-5 0-8 4-11 1 0 2 1 3 1h0c-2 1-3 2-4 4-1 3-1 8-2 11v2 1h-1c0 2 1 3 0 4z" class="AS"></path><path d="M234 251h1c1 0 1 1 1 2h1v-1l1 1c1 0 3 2 3 3-1 0-2-1-3-1 0 1-1 1-1 2h-1l1 1-3 3v1 2l-2-1-1 1s-1 0-1-1 2-2 2-3v-1c0-2 2-3 4-4l-1-1c-1-1-1-2-1-3z" class="AQ"></path><path d="M234 262c-1-1 0-1-1-2l2-2 1-1 1 1-3 3v1zm151-79c1 3 1 5 1 8 1 8-1 16-2 23-1 5-2 11-4 15h0-2l1-2c4-12 7-32 4-43l1-1h0 1z" class="AN"></path><path d="M379 227c1 0 1 0 1 2h-2l1-2z" class="AS"></path><path d="M283 213c4 0 8 1 12 2 4 0 6-1 10-1 2 0 4 1 6 0h11 6 3c3 0 8-1 10-3h2v1c1 0 2-1 2-2 5-8 11-16 17-24 3-4 7-9 12-11 2 0 4 0 5 1 3 1 5 4 6 7h-1 0l-1 1c-1-3-2-5-4-6s-3-1-5 0c-3 1-7 5-9 8-5 6-9 12-14 19-4 6-9 13-12 21l-3 6h0c0-2 0-3-1-5-1 1-1 2-2 3v1s0 1-1 1l1 3h-1v1c1 2 2 4 2 6l-1 1-1-1c0-2-1-3-1-4-1 0-1 0-1-1l-1-1c0-1-1-2-2-3v2c-1 0-1 1-2 1h0-1l-1 1h-1l-1 1-1-1v-3l2-2h0l-1-1v1c-1-1-2-2-2-4v-2 1h1v-1c-1-2-1-3-1-4-1-1-2-2-2-3l-1 1h0c-1-1-1-2-1-3h-1v3h-1l-2-3c0 1 0 1-1 1h0l-2-1v1 1h-1l-1-1c-1-1-4-1-6 0h-1c-1-1-2-1-2 0-1-1-1-1-2-1-1 1-2 1-3 0h-2v1l-1-1-1-1h-1v2c-1-1-1-1-1-2h0c-2 0-3 0-5-1h-1 0-1-2-2l-1-1h-1l-1-1-1-1c1 0 1-1 2-1 3 1 6 2 9 1 1 0 1 0 1 1z" class="AZ"></path><path d="M273 214l-1-1-1-1c1 0 1-1 2-1 3 1 6 2 9 1 1 0 1 0 1 1h0c-1 0-5 1-6 0s-2 0-3 0l-1 1h2-1-1z" class="AV"></path><path d="M341 216l1-1v1c0 1-1 3-2 4l-1-1c-1 0-1 0-1 1h0l-1 1-1-2h0c1 0 2-1 3-2 0 0 1-1 2-1z" class="y"></path><path d="M334 218h1 1l-1-1 1-1v1c1-1 1-1 2-1h2 1c-1 0-2 1-2 1-1 1-2 2-3 2h0l-2 1c0-1-1-1-1-2h1z" class="z"></path><path d="M320 217c2 0 2-1 3 0 1 0 1 1 1 1 1 2 2 4 2 6 0 1 1 4 1 4l-1 1c1 2 1 2 1 3h-1-1c0 1 0 2-1 2v2l-1 1h-1l-1 1-1-1v-3l2-2h0l-1-1v1c-1-1-2-2-2-4v-2 1h1v-1c-1-2-1-3-1-4-1-1-2-2-2-3l-1 1h0c0-2 0-2 1-3 1 0 1 0 2 1v-1h1z" class="AC"></path><path d="M324 234l-1-1 1-1c0-1-1-1-2-3 1 0 1-1 2-1 1 1 1 2 2 4h-1c0 1 0 2-1 2z" class="AI"></path><path d="M320 217c2 0 2-1 3 0 1 0 1 1 1 1l-1 1h0c1 1 1 2 1 2v1l1 1c0 1 0 1-1 2-2-1-1-3-1-5h-1v1h-1c0-2 0-3-1-4h0z" class="AT"></path><path d="M326 224h1c-1-1-2-6-2-7h1c0 1 1 3 2 3 0-1-1-1-1-2h0v-1h2c1 1 1 1 1 2h1v-1c1-1 1-1 2-1l1 1h-1c0 1 1 1 1 2l2-1 1 2 1-1h0c0-1 0-1 1-1l1 1-2 3v1s0 1 1 2l-3 6h0c0-2 0-3-1-5-1 1-1 2-2 3v1s0 1-1 1l1 3h-1v1c1 2 2 4 2 6l-1 1-1-1c0-2-1-3-1-4-1 0-1 0-1-1l-1-1c0-1-1-2-2-3v2c-1 0-1 1-2 1h0-1v-2c1 0 1-1 1-2h1 1c0-1 0-1-1-3l1-1s-1-3-1-4z" class="AM"></path><path d="M336 223h2v1c0 1-1 2-1 3h-1v-4z" class="w"></path><path d="M337 221l1-1h0c0-1 0-1 1-1l1 1-2 3h-2c0-1 0-1 1-2z" class="AF"></path><path d="M327 228c1 2 1 2 1 4h1l1-1c-1-1-2-2-2-4h1v1c1 0 1 0 1-1h1v2h0c1-1 2-1 2-2v-1h-2v-2l1-1c0 1 0 1 1 2h0v-2c-1 0-1-1-1-2v-3h0c1 1 1 3 1 5h1c0 1 1 3 1 4-1 1-1 2-2 3v1s0 1-1 1l1 3h-1v1c1 2 2 4 2 6l-1 1-1-1c0-2-1-3-1-4-1 0-1 0-1-1l-1-1c0-1-1-2-2-3v2c-1 0-1 1-2 1h0-1v-2c1 0 1-1 1-2h1 1c0-1 0-1-1-3l1-1z" class="AE"></path><path d="M339 226c3-8 8-15 12-21 5-7 9-13 14-19 2-3 6-7 9-8 2-1 3-1 5 0s3 3 4 6c3 11 0 31-4 43l-1 2h0v1c2 4 5 7 4 11v5l-1 7v3h-1v1c1 1 2 3 2 5l2-2 1 4s-1 1-1 2h0v1c-1 0-3-1-4-1h-3-1-3s0-1-1-1h0-3-1c-1 1-1 1-2 1l-2-1-3 2c-1 0-1 0-2 1l-4 1h-2l-1 1c-1 0-1 0-2 1v-1h-2 0l-1 1h-2-1l-3 2h-3v-1l-4 2v-1h0c0-1 0-1 1-2 1 0 1 0 1-1l-1-1c-1 1-3 1-4 1h0c1 0 2-1 3-1v-1l-1-1h-1l3-2c-1-1-1-1-2-1s-1 1-2 0h1v-2-1h-1c-1-1-1-2-2-3 1-2 2-5 2-7h1 1v-2c1-2 1-4 0-6l1-1c0-2-1-4-2-6v-1h1l-1-3c1 0 1-1 1-1v-1c1-1 1-2 2-3 1 2 1 3 1 5h0l3-6z" class="C"></path><path d="M363 240c1 0 3 1 4 2h-1-1-5 0 2l1-2z" class="I"></path><path d="M376 229v-1h1 0v1h1v1c-1 1-1 2-2 4h0c-1-1-1-3-1-4 0 0 0-1 1-1z" class="AY"></path><path d="M355 249v2h3c1 0 1 1 1 1h3-1-2-3-3l-1-1 2-2h1z" class="b"></path><path d="M341 246l1 2c1 0 1-2 3-1v3h-1v1l-2-2v1c-1-1-1-2-1-4z" class="AK"></path><path d="M353 228v-1c-1-2 1-3 2-4v1h3v1h-1l-4 3z" class="i"></path><path d="M375 209h2 1l1 1-1 1h0-2c1 1 1 1 2 1v1c-2 0-4-1-5-1l2-2v-1z" class="Aa"></path><path d="M373 223c1 0 2 1 3 2v1c-2 0-5 0-7-1h0l1-1h1c1 0 1-1 2-1z" class="z"></path><path d="M368 209h7v1l-2 2h-2c2 1 6 1 6 3h0c-2-1-5-2-7-2v-1-1h-1v-1l-1-1z" class="x"></path><path d="M366 221c2 0 4 1 6 1l1 1c-1 0-1 1-2 1h-1l-1 1h-2l-1-1h-1v1l-1-1 1-1h-2c1-1 1 0 1-1 1-1 1-1 2-1z" class="AO"></path><path d="M366 224h4l-1 1h-2l-1-1z" class="AL"></path><path d="M365 259c2-1 4-1 6 0h1 1 1 1 3l1 2c-1 1-1 1-2 1h0-1c0-1-1-1-2-1h-1 0 0c-2-1-3 0-4 0-1-1-1-1-2-1s-1-1-2-1z" class="x"></path><path d="M380 189h0c0-1 0-1 1-1v-2h1c0 2-1 4-2 5h1c1 0 1 1 1 2-1 0-1 1-2 2h-1c-1 1-3 1-4 2h0l-1-1v-1c1 0 2 0 2-1h1l1-1h0l1-1c1-1 1-1 1-2h0v-1z" class="s"></path><path d="M357 225c2-1 4-2 6-2h2l-1 1 1 1v-1h1l1 1h2 0c-1 0-3 0-4 1h-1c-2 0-4 1-5 1 1-1 2-1 3-2-2 0-2 1-4 1l-6 3-2 1c0-1 2-2 3-2l4-3z" class="AJ"></path><path d="M361 218c3-1 6-2 9-1 1 0 3 1 4 1-1 0-2 0-2 1-2 1-4-1-6 2h0c-1 0-1 0-2 1 0 1 0 0-1 1-2 0-4 1-6 2h1v-1h-3c1 0 3-1 4-2h1 0c1 0 2-1 2-1h1c1 0 2 0 3-1l-1-1h-1c-1-1-2-1-3-1z" class="J"></path><path d="M353 220h1c1 0 5-1 7-2 1 0 2 0 3 1h1l1 1c-1 1-2 1-3 1h-1s-1 1-2 1h0-1c-1 1-3 2-4 2v-1-1c-1 0-1 0-1-1-2 0-2 1-3 1l-1-1c1 0 2-1 3-1z" class="O"></path><path d="M351 247c2-1 4-1 6-1h0c0 1-2 1-4 1l1 1c1 0 3 0 4-1h3 6 0l1 1c1 0 3 0 4 1-1 1-1 1-2 1v-1h-3c-2-1-7-1-9 0h-1-2-1-1l-5 3h-2c1-1 1-2 2-3s3-1 3-2z" class="AB"></path><defs><linearGradient id="t" x1="357.51" y1="240.221" x2="357.705" y2="237.695" xlink:href="#B"><stop offset="0" stop-color="#221f21"></stop><stop offset="1" stop-color="#322f26"></stop></linearGradient></defs><path fill="url(#t)" d="M355 237h2c1 1 1 1 2 1h1 0c1 0 1 1 2 1l1 1-1 2h-2 0-1 0-2c-1-1-1-1-2-1s-2 0-3-1c-1 0-3-1-4-1v-1h2l1-1h4z"></path><path d="M351 237h4v1h0c-2 0-3 0-5 1v-1h0l1-1z" class="AE"></path><path d="M355 215c2 0 3-2 5-3 1-1 3-2 5-2h0c-1 2-3 2-5 3h3c2-1 3-1 5-1v1h-1c1 1 3 1 4 2h0-2 0l6 3h0-1c-1 0-3-1-4-1-3-1-6 0-9 1-2 1-6 2-7 2h-1c5-2 10-4 15-4v-1h-2v-1c-3 0-7 1-10 1-1 1-2 2-3 2v-1l2-1z" class="AP"></path><path d="M379 261l1 1c0-2 0-3-2-4h-3c2-1 3-1 3-2-1-1-2-2-4-2h0v-1h1 3v1-2l3 1v3h-1v1c1 1 2 3 2 5l2-2 1 4s-1 1-1 2h0v1c-1 0-3-1-4-1h-3l3-3h-1-3 0l1-1h0c1 0 1 0 2-1z" class="AI"></path><path d="M384 260l1 4s-1 1-1 2c-1-2-2-2-2-4l2-2z" class="AS"></path><path d="M365 259c1 0 1 1 2 1s1 0 2 1c1 0 2-1 4 0h0 0 1c1 0 2 0 2 1h1l-1 1h0 3 1l-3 3h-1-3s0-1-1-1h0-3-1c-1 1-1 1-2 1l-2-1-3 2-1-1c1-1 1-1 3-1 0-1 1-2 1-3h0v-1h-1c0-1 1-2 2-2z" class="AJ"></path><path d="M373 261h0 1c1 0 2 0 2 1h1l-1 1c-3-1-4 0-6 1h-1 0 1 3v1h-1-3-1c-1 1-1 1-2 1l-2-1 2-1c1 0 1-1 2-1l5-2z" class="AG"></path><path d="M365 259c1 0 1 1 2 1s1 0 2 1c1 0 2-1 4 0h0l-5 2c-1 0-1 1-2 1l-2 1-3 2-1-1c1-1 1-1 3-1 0-1 1-2 1-3h0v-1h-1c0-1 1-2 2-2z" class="AD"></path><path d="M380 189v1h0c0 1 0 1-1 2l-1 1h0l-1 1h-1c0 1-1 1-2 1v1l1 1h-1c-2 1-3 1-5 1h2 7c-2 1-3 1-4 1-3 0-5 0-8 1-2 0-5 2-7 3 1-1 1-2 2-3v-1c1 0 1-1 2-2h1c0-1 1-1 1-1h0c1 0 1 0 1-1l8-3c1-1 2-1 3-2h1l2-1z" class="B"></path><path d="M352 229l6-3c2 0 2-1 4-1-1 1-2 1-3 2 1 0 3-1 5-1h1 0c1 1 2 1 2 2h4v1c0 1 0 1 1 2h-1c-4 0-8 0-12 1-1 0-1-1-2 0h-2 0c-1 0-1 1-2 1h-2c-1 1-2 1-3 1h-1c1 0 1-1 2-1l1-1h1c2 0 2-2 4-2h0l-1-1h-2z" class="i"></path><path d="M362 206c1 0 2-1 3-1 3-1 5-2 8-2v1h-2l1 1-3 1c-1 0 0 0-1 1 1 0 2 0 3-1h1-1c-1 1-2 2-3 2v1l1 1v1h1v1 1h-2v-1c-2 0-3 0-5 1h-3c2-1 4-1 5-3h0c-2 0-4 1-5 2-2 1-3 3-5 3l-2 1v-1c0-1 1-2 2-3h0c1-1 1-2 2-3l5-3z" class="AK"></path><path d="M362 206c1 0 2-1 3-1 3-1 5-2 8-2v1h-2c-2 1-5 1-7 3-1 0-1 1-3 2s-4 3-6 5v1l-2 1v-1c0-1 1-2 2-3h0c1-1 1-2 2-3l5-3z" class="i"></path><path d="M351 247c0 1-2 1-3 2s-1 2-2 3h2l5-3h1l-2 2-4 3c1 0 2 0 2 1-2 1-4 2-5 4s-2 2-2 4h-1v-1c-1 0-2 1-2 2h0c-1 1-2 2-3 2l-2 2-1 1v-1l-1-1h-1l3-2c-1-1-1-1-2-1 1-1 3-2 4-2h0c1-1 2-2 2-3l2-3c0-1 1-3 2-4l1-1v-1l1 1c1-3 4-3 6-4z" class="AD"></path><path d="M333 267l3-1c1-1 2-2 4-2-1 1-2 2-3 2l-2 2-1 1v-1l-1-1z" class="i"></path><path d="M341 256v3c-1 1-4 5-5 5 0 1-1 1-1 1-1-1-1-1-2-1 1-1 3-2 4-2h0c1-1 2-2 2-3l2-3z" class="w"></path><path d="M351 247c0 1-2 1-3 2s-1 2-2 3h2l5-3h1l-2 2-4 3h-1c0 2-2 5-4 6h0c1-1 1-3 2-4h0v-1l-3 3h0l1-1c0-1-1-1-1-1 0-1 2-4 3-5 1-3 4-3 6-4z" class="z"></path><defs><linearGradient id="u" x1="371.588" y1="205.05" x2="362.4" y2="200.4" xlink:href="#B"><stop offset="0" stop-color="#1d181d"></stop><stop offset="1" stop-color="#393831"></stop></linearGradient></defs><path fill="url(#u)" d="M378 198l4-1v1c-1 0-2 1-3 1l-1 1c2 0 3 0 4-1l1 1c-2 2-3 2-6 3-2 1-3 1-5 2l-1-1h2v-1c-3 0-5 1-8 2-1 0-2 1-3 1l-5 3c-1 1-1 2-2 3h0c0-2 2-3 1-5l2-2 1-2c2-1 5-3 7-3 3-1 5-1 8-1 1 0 2 0 4-1z"></path><path d="M378 198l4-1v1c-1 0-2 1-3 1-5 1-12 1-16 3-2 1-3 2-5 3h0l1-2c2-1 5-3 7-3 3-1 5-1 8-1 1 0 2 0 4-1z" class="z"></path><path d="M378 200c2 0 3 0 4-1l1 1c-2 2-3 2-6 3-2 1-3 1-5 2l-1-1h2v-1c-3 0-5 1-8 2-1 0-2 1-3 1v-1c4-3 11-3 16-5z" class="AD"></path><path d="M334 242c1 0 1 0 2-1 0 0 2 2 2 3 1 0 2 0 2 1l1 1h0c0 2 0 3 1 4v-1l2 2-1 1c-1 1-2 3-2 4l-2 3c0 1-1 2-2 3h0c-1 0-3 1-4 2-1 0-1 1-2 0h1v-2-1h-1c-1-1-1-2-2-3 1-2 2-5 2-7h1 1v-2c1-2 1-4 0-6l1-1z" class="AL"></path><path d="M338 244c1 0 2 0 2 1l1 1c-2 2 0 4-2 6h-2c0-1 1-2 1-3v-3h1c-1-1-1 0-2-1l1-1z" class="AB"></path><path d="M333 257h0 1l3-5h2c0 2 0 3-1 4l-3 3-1-1c0-1-1 0-2 0l1-1z" class="AF"></path><path d="M334 242c1 0 1 0 2-1 0 0 2 2 2 3l-1 1v-1c-1 1-1 2-1 3l-3 10-1 1v-1c0-1 1-1 1-3 0-1 0-2-1-3h1v-2c1-2 1-4 0-6l1-1z" class="s"></path><path d="M341 246h0c0 2 0 3 1 4v-1l2 2-1 1c-1 1-2 3-2 4l-2 3c0 1-1 2-2 3h0c-1 0-3 1-4 2-1 0-1 1-2 0h1v-2-1h-1c-1-1-1-2-2-3 1-2 2-5 2-7h1c1 1 1 2 1 3 0 2-1 2-1 3v1c1 0 2-1 2 0l1 1 3-3c1-1 1-2 1-4 2-2 0-4 2-6z" class="AO"></path><path d="M339 259h-1c1-2 2-2 2-4 0 0 0-1 1-2h-1v-1h3c-1 1-2 3-2 4l-2 3z" class="AH"></path><path d="M329 258c1-2 2-5 2-7h1c1 1 1 2 1 3 0 2-1 2-1 3v1c1 0 2-1 2 0l1 1h2 0c-1 2-3 3-4 4l-1 1v-2-1h-1c-1-1-1-2-2-3z" class="b"></path><path d="M332 257v1c1 0 2-1 2 0l1 1h2 0c-1 2-3 3-4 4v-2-1c1-1 0-1 0-1h0-2 0l1-2z" class="O"></path><path d="M371 228c1 1 2 1 4 0l1 1c-1 0-1 1-1 1 0 1 0 3 1 4h0c0 1 0 2-1 3h-1c-2 1-3 1-6 1 1 1 2 1 3 1s1 1 1 1h0c-1 1-4-1-5-2h-1-2c0 1 3 3 5 4 1 1 2 1 3 3-2-1-3-2-5-3-1-1-3-2-4-2l-1-1c-1 0-1-1-2-1h0-1c-1 0-1 0-2-1h-2c-3-2-6 0-9 0 3-2 6-2 8-4h0-1c1 0 1-1 2-1h0 2c1-1 1 0 2 0 4-1 8-1 12-1h1c-1-1-1-1-1-2v-1z" class="AE"></path><path d="M376 234h0c0 1 0 2-1 3h-1c-2 1-3 1-6 1 0-1-1-1-2-2h-2c0-1-1 0-2 0-2-1-3-1-4-2h0 10c2 0 4 2 6 1h1l1-1z" class="i"></path><path d="M371 228c1 1 2 1 4 0l1 1c-1 0-1 1-1 1 0 1 0 3 1 4l-1 1h-1c-2 1-4-1-6-1h-3c-3 0-9 0-11-1h0-1c1 0 1-1 2-1h0 2c1-1 1 0 2 0 4-1 8-1 12-1h1c-1-1-1-1-1-2v-1z" class="b"></path><path d="M364 232c2 1 5 1 6 2 1 0 3 0 4 1-2 1-4-1-6-1h-3l-1-2z" class="AM"></path><path d="M354 233c4-1 7-1 10-1l1 2c-3 0-9 0-11-1z" class="AC"></path><path d="M352 251l1 1h3 3 2 1s1 0 1 1c1 0 2 0 3 1h-1c0 1-1 2-1 2h3v1s-1 0-2 1h-1l1 1c-1 0-2 1-2 2h1v1h0c0 1-1 2-1 3-2 0-2 0-3 1l1 1c-1 0-1 0-2 1l-4 1h-2l-1 1c-1 0-1 0-2 1v-1h-2 0l-1 1h-2-1l-3 2h-3v-1l-4 2v-1h0c0-1 0-1 1-2 1 0 1 0 1-1l-1-1c-1 1-3 1-4 1h0c1 0 2-1 3-1l1-1 2-2c1 0 2-1 3-2h0c0-1 1-2 2-2v1h1c0-2 1-2 2-4s3-3 5-4c0-1-1-1-2-1l4-3z" class="x"></path><path d="M350 263l2 2h2 1v2h-2 0-3c-1 1-2 2-3 2v-1c1-1 2-1 4-3h-1c0-1-1-1 0-2z" class="AC"></path><path d="M338 272c1 0 3-2 4-2 1-1 3-1 4-1s2 0 2 1l-1 1h-2-1l-3 2h-3v-1z" class="O"></path><path d="M346 269c1 0 2 0 2 1l-1 1h-2-1 0c1-2 1-1 2-2z" class="s"></path><path d="M364 262h0c0 1-1 2-1 3-2 0-2 0-3 1h-2l-1 2c-1 0-1-1-2 0h-2v-1h0 2l1-1c1 0 1 0 2-1 1 0 2-1 2-2h1c1 0 2-1 3-1z" class="AI"></path><path d="M349 260h1l-5 5c-1 1-1 2-2 2-2 1-2 2-3 3s-4 2-6 3c0-1 0-1 1-2 1 0 1 0 1-1l-1-1h3c1-1 2-2 4-2 1 0 1-1 2-1 2-2 4-4 5-6z" class="J"></path><path d="M340 264c1 0 3 0 4 2-1 0-1 1-2 1-2 0-3 1-4 2h-3c-1 1-3 1-4 1h0c1 0 2-1 3-1l1-1 2-2c1 0 2-1 3-2h0z" class="AK"></path><path d="M337 266l1 1c1-1 2-1 3-2l-3 3v1h-3c-1 1-3 1-4 1h0c1 0 2-1 3-1l1-1 2-2z" class="AF"></path><path d="M356 258c1 0 1 0 2-1v2h2c-1 2-5 5-8 6l-2-2s0-1 1-1c2-1 4-2 5-4h0z" class="AP"></path><path d="M364 256h3v1s-1 0-2 1h-1l1 1c-1 0-2 1-2 2h1v1c-1 0-2 1-3 1h-1c0 1-1 2-2 2-1 1-1 1-2 1l-1 1v-2h-1-2c3-1 7-4 8-6h1c1-1 3-1 3-2v-1h0z" class="AI"></path><path d="M363 261h1v1c-1 0-2 1-3 1l-2-1c1-1 3-1 4-1z" class="AP"></path><path d="M359 262l2 1h-1c0 1-1 2-2 2-1 1-1 1-2 1l-1 1v-2h-1s1-1 2-1c1-1 2-2 3-2z" class="AL"></path><path d="M352 251l1 1h3 3 2 1s1 0 1 1c1 0 2 0 3 1h-1c0 1-1 2-1 2h0v1c0 1-2 1-3 2h-1-2v-2c-1 1-1 1-2 1h0c-3 1-5 3-7 4 0 1 0 1-1 1 0 1-1 2-2 2h-1l5-5h-1c-1 2-3 4-5 6-1-2-3-2-4-2 0-1 1-2 2-2v1h1c0-2 1-2 2-4s3-3 5-4c0-1-1-1-2-1l4-3z" class="z"></path><path d="M352 251l1 1h3 3c-2 1-4 1-6 1-1 0-2 1-3 2 0-1-1-1-2-1l4-3z" class="J"></path><path d="M345 259c1 1 1 1 2 0h0c1-1 3-2 4-3h0c-1 1-2 2-3 4h1c-1 2-3 4-5 6-1-2-3-2-4-2 0-1 1-2 2-2v1h1c0-2 1-2 2-4z" class="AO"></path><path d="M361 252h1s1 0 1 1c1 0 2 0 3 1h-1c0 1-1 2-1 2h0v1c0 1-2 1-3 2h-1-2v-2c-1 1-1 1-2 1v-1c1 0 1-1 2-1s4-1 5-2h0-4l2-2z" class="J"></path><path d="M364 256v1c0 1-2 1-3 2h-1-2v-2c1 0 4 0 6-1z" class="AM"></path><path d="M366 266c1 0 1 0 2-1h1 3 0c1 0 1 1 1 1h3 1 3c1 0 3 1 4 1v-1c1 2 2 3 1 4l2 1v1c1 0 1 1 1 1 1 1 2 1 2 2s1 2 1 2c0 2 1 4 1 5l1 1c0 1-1 1-1 2s1 3 1 4h0l-4 49c8 7 15 15 21 23h0c-8-7-14-16-22-22-1 3-1 5-2 7h0-2v4c-11-11-26-17-41-21l-4-1v-2h-1-3-2l-1 1-2 1c-1 0-1 0-2 1-2 1-4 1-5 1h-3v1 1h-2 0c-1 1-1 2-1 2h-1v1h0l-2-1v1l-2-2v-1-1c1-1 2-2 3-2h1c0-1 1-1 2-1l-1-1v-1h2l-1-1v-1c1 0 1 0 1-1l1-1v-3c1-1 1-3 2-4h1v-3c1 0 1 0 2-1 0-1-1-1 0-1h1v-1-1c1 1 2 1 3 1h0v-1h-1v-1h2v-1h0c0-1 0-1-1-2l-1-1h1c1 0 1 1 2 1v-1-1h0l2 1c1 0 1 1 2 1l1 1h2v-1c1 0 1 0 2 1v-1h7v-1c1 0 4 1 5 1l-1-1v-1c1 0 1 0 2-1v-1c-1-1-2-1-3-1l1-1c-1 0-3-1-4-1v-2l-2 1-1-2v-1c2 1 6-2 8-3v-1c0-1 1-1 1-2l1-1c0-1 1-1 1-2h-2c-1-1 1-5-1-8 0-1-1-2-1-3h-2 0-2c-2 0-5 1-6 0l3-2h1 2l1-1h0 2v1c1-1 1-1 2-1l1-1h2l4-1c1-1 1-1 2-1l3-2 2 1z" class="C"></path><path d="M351 315c1 0 3-1 4-1v2c-1 0-3-1-4-1z" class="I"></path><path d="M349 319c2 0 3 1 5 1-4 1-8 1-12 1v-1c2 0 5 0 7-1z" class="s"></path><path d="M339 320c3-1 6-1 8-1h2c-2 1-5 1-7 1h0-2l2 2c1 0 1 0 2 1h-1c-2-1-2-1-3-2-1 0-1 0-1-1h0z" class="O"></path><path d="M350 308c2-1 8-1 9-1 0 1-1 3-2 4h-5c-1 1-1 0-1 0l-5 2h-1l-1-2s1 0 1-1c2 0 4 0 6-1h0l-1-1z" class="AB"></path><path d="M344 311s1 0 1-1c2 2 4 1 6 1l-5 2h-1l-1-2z" class="AF"></path><path d="M350 299l1-1c3 1 5 1 7 0-1 1-1 1-2 1 0 0 1 0 2 1 2 0 4-1 6-1-2 1-3 3-5 4h0l-1-1-1 1c-1 0-3 1-5 1l-1-1v-1c1 0 1 0 2-1v-1c-1-1-2-1-3-1z" class="O"></path><path d="M344 294c2-1 4 1 6 1h6c2 0 5-2 7-2-2 1-4 3-7 4h0c4 0 8-3 11-4-3 3-5 3-9 5-2 1-4 1-7 0-1 0-3-1-4-1v-2l-2 1-1-2z" class="AJ"></path><path d="M340 313c1-1 3 0 3-1 0 0 0-1 1-1l1 2h1c-1 1-2 1-2 2v1c2 0 5-1 7-1 1 0 3 1 4 1v1c1 1 1 1 1 2l-1 1h-1c-2 0-3-1-5-1h-2c-1-2-4 0-5-1 0-1 0-1 1-1h0c0-1-1-1-2-1l-1-1h2v-1h0c-1 0-1 0-2-1h0z" class="B"></path><path d="M355 320l-2-2v-1h2c1 1 1 1 1 2l-1 1z" class="k"></path><path d="M340 313c1-1 3 0 3-1 0 0 0-1 1-1l1 2h-3c0 1 1 1 1 1v1h-1v-1h0c-1 0-1 0-2-1h0z" class="AE"></path><path d="M388 285h0 1c1 1 1 1 1 2-1 1-1 1-1 2 1 0 1 1 2 1v-1 5 1h0s-1 0-1 1h0c1 1 1 4 1 5l-1 1c1 0 1 1 0 2h0-1c0-2-1-3-1-5h0l-3-3c-1 0-2-1-2-2 2 1 3 1 4 2v1h1c-1-2-1-3-2-4h-1v-1c1 0 2 1 4 2-2-1-3-3-5-4l-3-2c2 0 5 2 7 3 0-1-1-2-1-3-1-1-1-1-1-2 1 0 1 0 2-1h0z" class="AY"></path><path d="M352 304c2 0 4-1 5-1l1-1 1 1-6 2v1c-2 1-3 2-5 2h0l1 1 1-1 1 1h0c-2 1-4 1-6 1 0 1-1 1-1 1-1 0-1 1-1 1 0 1-2 0-3 1v-1h0c0-1-1-1-1-1-2 0-3 0-4-1 1 0 1 0 2-1l1 1h4c0-1-2-1-3-1v-1c0-1-1-1-1-2v-1-1c1 0 1 0 2 1v-1h7v-1c1 0 4 1 5 1z" class="AG"></path><path d="M348 306c1 0 3-1 5-1v1c-2 1-3 2-5 2h0l1 1h0-4v-1c1 0 2 0 3-1-1-1-1 0-2-1h2z" class="J"></path><path d="M338 305v-1c1 0 1 0 2 1h4c1 1 2-1 4 1h-2-2v2h-5c0-1-1-1-1-2v-1z" class="AL"></path><path d="M352 304c2 0 4-1 5-1l1-1 1 1-6 2c-2 0-4 1-5 1-2-2-3 0-4-1h-4v-1h7v-1c1 0 4 1 5 1z" class="x"></path><path d="M356 282c1-1 3-2 4-3l2 2 1 1 2-2h1 3 3c0 1-2 1-2 1v1c-2 1-4 2-5 3-3 1-6 2-8 4h-2c0 1 0 1 1 1 2-1 3-2 5-2 1-1 1-1 2-1 0-1 1-1 2-1v-1h2l1-1c1 1 3 0 4 1-1 0-1 0-2 1-1 0-2 1-3 1s-1 0-2 1h0-1c-1 1-1 1-2 1l-1 1c1 0 1 0 2-1h2 0c0 1-1 1-2 1l-1 1v1c1 0 2 0 3 1h-2c-2 0-5 2-7 2h-6c-2 0-4-2-6-1v-1c2 1 6-2 8-3v-1c0-1 1-1 1-2l1-1c0-1 1-1 1-2h0v-2h1z" class="i"></path><path d="M356 282c1-1 3-2 4-3l2 2 1 1 2-2h1 3 3c0 1-2 1-2 1l-16 8h0v-1s0-2 1-2c0-1 2-1 2-3h-1v-1z" class="x"></path><path d="M384 272l1-1h0c1 1 1 1 2 1s1 1 1 1c1 1 2 1 2 2s1 2 1 2c0 2 1 4 1 5l1 1c0 1-1 1-1 2s1 3 1 4h0-2v1c-1 0-1-1-2-1 0-1 0-1 1-2 0-1 0-1-1-2h-1 0 0c-1 1-1 1-2 1 0 1 0 1 1 2h-2c-1-1-1-1-1-3-1 0-1 0-2-1h0v-1h-1c-2-1-2-3-5-3-2 1-2 0-4 0h0-3-3-1l-2 2-1-1-2-2c1-1 1-1 2-1 1-1 2-2 4-3h0c2-1 4-1 6-2l11 1c0-1 1-1 2-2h-1z" class="AK"></path><path d="M390 275c0 1 1 2 1 2 0 2 1 4 1 5v1c-1-2-3-3-4-5h2v-3z" class="AV"></path><path d="M360 279c1-1 1-1 2-1 1-1 2-2 4-3 0 2-1 2-1 4h0c-1 0-2 1-3 2l-2-2z" class="AD"></path><path d="M378 279c2 1 6 2 7 4 0 0-1-1-2 0l1 1h-2 0v-1h-1c-2-1-2-3-5-3v-1l1 1 1-1z" class="s"></path><path d="M374 277c2-1 3-1 5 0h1c2 1 4 2 7 4h0-1 0c-1 0-1-1-2-1-2 0-3-2-5-2l-1 1-1 1-1-1c0-1-1-1-2-2z" class="J"></path><path d="M366 280l8-3c1 1 2 1 2 2v1c-2 1-2 0-4 0h0-3-3z" class="B"></path><path d="M388 285h0c-1-1-1-2-2-2v-1c1 1 2 1 3 2v-1-1c-1 0-1 0-1-1h0s1 0 1 1c1 0 1 0 2 1l1 2c0 1 1 3 1 4h0-2v1c-1 0-1-1-2-1 0-1 0-1 1-2 0-1 0-1-1-2h-1 0z" class="AN"></path><path d="M384 272l1-1h0c1 1 1 1 2 1s1 1 1 1c1 1 2 1 2 2v3h-2c-1 0-2-2-3-2v-1l-2-1c0-1 1-1 2-2h-1z" class="AS"></path><path d="M385 272c2 1 3 3 4 5-1-1-2-2-4-2l-2-1c0-1 1-1 2-2z" class="AE"></path><path d="M391 289h2l-4 49c8 7 15 15 21 23h0c-8-7-14-16-22-22-1 0-1 0-2-1-10-7-22-12-34-13-4-1-8-1-12-1h-5v-1h1 7 1c11 0 23 3 33 8 3 1 6 3 9 5h0c0-5-2-10-2-16h1c0 1 0 0 1 1l1-2-1-2h0l-1-2c-1-1-2-1-2-2v-1h1v-2h0l1-1c1 1 1 2 2 3v1-2-1c-1-2-2-5-4-7h0c2 1 4 4 4 6 0 0 1 0 1 1v-1c0-2 0-3-1-5v-1h1l1 1h0 1 0c1-1 1-2 0-2l1-1c0-1 0-4-1-5h0c0-1 1-1 1-1h0v-1-5z" class="AM"></path><path d="M384 310l3 3c0 1 1 4 1 5v1c0 1 1 1 1 1-1 0-1 0-1-1s-1-2-2-2h0l-1-2c-1-1-2-1-2-2v-1h1v-2z" class="b"></path><path d="M387 319c1 4 1 6 1 10h-1c-1-2-2-6-2-9 0 1 0 0 1 1l1-2z" class="k"></path><path d="M366 266c1 0 1 0 2-1h1 3 0c1 0 1 1 1 1h3 1 3c1 0 3 1 4 1v-1c1 2 2 3 1 4l2 1v1c-1 0-1 0-2-1h0l-1 1h1c-1 1-2 1-2 2l-11-1c-2 1-4 1-6 2h0c-2 1-3 2-4 3-1 0-1 0-2 1s-3 2-4 3h-1v2h0-2c-1-1 1-5-1-8 0-1-1-2-1-3h-2 0-2c-2 0-5 1-6 0l3-2h1 2l1-1h0 2v1c1-1 1-1 2-1l1-1h2l4-1c1-1 1-1 2-1l3-2 2 1z" class="z"></path><path d="M367 268c-1 0-1 1-2 1s-1 1-2 1-2 1-3 1h-2c-3 2-9 1-13 0h2l1-1h0 2v1c3 1 6 0 9-1 2-1 5-1 8-2z" class="J"></path><path d="M360 273l14-4c-1 2-2 2-2 4-2 1-4 1-6 2v-1c-1-1-3 0-4 0s-1 0-2-1z" class="AB"></path><path d="M374 269c4 0 6 2 10 3h1c-1 1-2 1-2 2l-11-1c0-2 1-2 2-4z" class="b"></path><path d="M366 266c1 0 1 0 2-1h1 3 0c1 0 1 1 1 1h3 1 3c1 0 3 1 4 1v-1c1 2 2 3 1 4h0c-1 0-1-1-2-2h0-2c0-1 0 0 0 0-1 0-2 0-2-1h-6v-1h-3l-1 1c-1 0-1 0-2 1-3 1-6 1-8 2-3 1-6 2-9 1 1-1 1-1 2-1l1-1h2l4-1c1-1 1-1 2-1l3-2 2 1z" class="AP"></path><path d="M364 265l2 1c-1 1-2 1-3 1 0 1 0 1-1 1h-2c-2 1-2 2-4 2h-1-3l1-1h2l4-1c1-1 1-1 2-1l3-2z" class="y"></path><path d="M351 273l4 1h0v1c2 0 3-1 5-2 1 1 1 1 2 1s3-1 4 0v1h0c-2 1-3 2-4 3-1 0-1 0-2 1s-3 2-4 3h-1v2h0-2c-1-1 1-5-1-8 0-1-1-2-1-3z" class="b"></path><path d="M340 324c4 0 8 0 12 1 12 1 24 6 34 13 1 1 1 1 2 1-1 3-1 5-2 7h0-2v4c-11-11-26-17-41-21l-4-1v-2h-1v-2h2z" class="C"></path><path d="M339 326c1 1 2 1 3 2l1 1-4-1v-2z" class="B"></path><path d="M386 338c1 1 1 1 2 1-1 3-1 5-2 7h0-2l-2-4h1c1 1 1 2 1 4 2-3 2-5 2-8z" class="AX"></path><path d="M331 303v-1h0l2 1c1 0 1 1 2 1l1 1h2v1c0 1 1 1 1 2v1c1 0 3 0 3 1h-4l-1-1c-1 1-1 1-2 1 1 1 2 1 4 1 0 0 1 0 1 1h0v1h0c1 1 1 1 2 1h0v1h-2l1 1c1 0 2 0 2 1h0c-1 0-1 0-1 1 1 1 4-1 5 1-2 0-5 0-8 1h0c0 1 0 1 1 1 1 1 1 1 3 2h-7-1v1h5-2v2h-3-2l-1 1-2 1c-1 0-1 0-2 1-2 1-4 1-5 1h-3v1 1h-2 0c-1 1-1 2-1 2h-1v1h0l-2-1v1l-2-2v-1-1c1-1 2-2 3-2h1c0-1 1-1 2-1l-1-1v-1h2l-1-1v-1c1 0 1 0 1-1l1-1v-3c1-1 1-3 2-4h1v-3c1 0 1 0 2-1 0-1-1-1 0-1h1v-1-1c1 1 2 1 3 1h0v-1h-1v-1h2v-1h0c0-1 0-1-1-2l-1-1h1c1 0 1 1 2 1v-1z" class="z"></path><path d="M334 305c1 1 2 2 4 3h0l-1 1c-1-1-3-2-4-2l1-2z" class="AI"></path><path d="M329 304l-1-1h1c1 0 1 1 2 1v-1c1 1 2 1 3 2l-1 2c0-1-1-1-2-2h0l-1 2c1 0 2 1 3 1l1 1c-2 0-2 0-3-1h-2-1v-1h2v-1h0c0-1 0-1-1-2z" class="AG"></path><path d="M327 318h1v-1h1 0c0 1 0 1-1 2h3v-1c3 0 3 1 5 3h-5-3 0l-1-1h-2c0-1 1-1 2-2z" class="AL"></path><path d="M335 310c1 1 2 1 4 1 0 0 1 0 1 1h0v1h0c1 1 1 1 2 1h0v1h-2c-1 1-1 1-2 1l-1-1h-1c-1 0-1 0-2-1v-1h0c-1 0-1 0-2-1l1-1 2-1z" class="x"></path><path d="M335 310c1 1 2 1 4 1l-1 1h-2c0 1 1 1 2 2h-2l-2-1h0c-1 0-1 0-2-1l1-1 2-1z" class="AC"></path><path d="M329 309h1v1h0c-1 0-2 1-2 2-1-1-1-1-2-1l-1 1h0v1l2-1v1s-1 1-2 1v2h-1l-1 1h1c1 0 1 0 2-1l1 1h0l-2 1c-2 0-3 1-4 2 1 1 2 1 1 2h0c-1 0-2 0-2 1v1h1l-1 1s-1 0-1-1h-1c1 0 1 0 1-1l1-1v-3c1-1 1-3 2-4h1v-3c1 0 1 0 2-1 0-1-1-1 0-1h1v-1-1c1 1 2 1 3 1h0z" class="AE"></path><path d="M331 318v-1h2c-1-1-2-2-3-2l-1-1h1c2 1 5 2 7 2v-1l1 1c1 0 1 0 2-1l1 1c1 0 2 0 2 1h0c-1 0-1 0-1 1 1 1 4-1 5 1-2 0-5 0-8 1h0c-2 0-3 0-3 1-2-2-2-3-5-3z" class="AO"></path><path d="M338 316c1 0 1 0 2-1l1 1c1 0 2 0 2 1h0c-1 0-1 0-1 1 1 1 4-1 5 1-2 0-5 0-8 1v-1h-2 0l-1-1c2-1 3 0 5 0-1-1-2-1-3-2z" class="AD"></path><path d="M325 318h2c-1 1-2 1-2 2h2l1 1h0 3 5c0-1 1-1 3-1 0 1 0 1 1 1 1 1 1 1 3 2h-7-1v1h5-2v2h-3-2l-1 1-2 1c-1 0-1 0-2 1-2 1-4 1-5 1h-3v1 1h-2 0c-1 1-1 2-1 2h-1v1h0l-2-1v1l-2-2v-1-1c1-1 2-2 3-2h1c0-1 1-1 2-1l-1-1v-1h2l-1-1v-1h1c0 1 1 1 1 1l1-1h-1v-1c0-1 1-1 2-1h0c1-1 0-1-1-2 1-1 2-2 4-2z" class="AC"></path><path d="M325 324h-1c0-1-1-1-1-2h1 2c0 1 0 1-1 2z" class="AQ"></path><path d="M312 331c1-1 2-2 3-2h1v1h2 0c1 1 2 1 2 1v1h-2 0c-1 1-1 2-1 2h-1v1h0l-2-1v1l-2-2v-1-1z" class="x"></path><path d="M315 329h1v1h2 0c1 1 2 1 2 1v1h-2 0c-1 1-1 2-1 2h-1s-1-1-2-1v-1l2-2h-1v-1z" class="AL"></path><path d="M336 321c0-1 1-1 3-1 0 1 0 1 1 1 1 1 1 1 3 2h-7-1v1h5-2v2h-3-2l-1 1c-2-1-3-1-5-1l1-1v-1h-3c1-1 1-1 1-2h0c0-1 1-1 2-1h0 3 5z" class="AO"></path><path d="M328 321h3 0c1 0 2 1 4 1h0l-1 1c-1-1-2-1-3-1h-2v-1h-1z" class="i"></path><path d="M336 321c0-1 1-1 3-1 0 1 0 1 1 1 1 1 1 1 3 2h-7-2l1-1h0c-2 0-3-1-4-1h0 5z" class="s"></path><path d="M328 325h3l1 1v-1c1-1 4-1 6-1v2h-3-2l-1 1c-2-1-3-1-5-1l1-1z" class="J"></path><path d="M235 178l2 1c5 2 9 7 12 11h3c1 1 3 4 4 5 5 5 10 13 17 16-1 0-1 1-2 1l1 1 1 1h1l1 1h2 2 1 0 1c2 1 3 1 5 1h0c0 1 0 1 1 2v-2h1l1 1c0 1 0 2 1 2l1-1h0c1 1 1 2 1 3h0l-1 1v2c-1 1-1 2-1 4h-1l2 2v1c-1 1-1 4-2 6-1 0-1 0-2-1l-1 1 1 1v2h-1v-1c-1 0-1 2-2 2h-1c-1 0-1 1-1 1-1 1-1 2-2 2h-1c1 1 1 2 1 3v3h-1c0 1 0 1-1 1 0 1 1 2 1 3v3l1 1h-2 0v1h-1-1-1l-1-1-3-1c2 1 2 2 2 3v1l2 2-1 1s0 1 1 1v1s1 0 1 1c1 1 3 2 5 3l1 1c-1 0-2 0-4-1 0 1 1 1 0 2l-1 1h0-2c-1-1-3-1-4-1s-1 1-2 1c-1-1-2-1-3-2h-1l-1 1 1 1c-3 2-6 1-9 1h-1 0c-1-1-3-3-5-3v1c1 1 3 2 4 3h0c-1 0-6-1-7-2h-3c-2 0-4-1-6 0h-1l-1 1h0l-1 2c-1-1-1-1-2-1-1 1-1 1-2 1v-1h-1c-1-1-2 0-3-1l2-4v-1c0-1 1-1 1-2s0-2 1-3l1-1 2 1v-2-1l3-3-1-1h1c0-1 1-1 1-2 1 0 2 1 3 1 0-1-2-3-3-3l-1-1c1-3 1-5 0-8v-3l1-2c0-1 0-1-1-2s-1-2-1-2l1-1 1-1c-3-6-6-13-7-20-1-4-2-9-3-13 1-1 0-2 0-4h1v-1-2c1-3 1-8 2-11 1-2 2-3 4-4z" class="m"></path><path d="M265 249l2 2c0 1 0 2-1 3v-1h-2l1-1h1c0-1-1-1-1-1v-2z" class="s"></path><path d="M238 233l2 2h0c-2 1-2 2-2 4 0-1 0-1-1-2s-1-2-1-2l1-1 1-1z" class="AY"></path><path d="M238 233l2 2h0c-2 0-2 0-3-1l1-1z" class="AN"></path><path d="M265 249c-1-1-2-1-2-2h2 3 0c0 1 0 1 1 2h-1l-1 2h0l-2-2z" class="AI"></path><path d="M251 210c1 0 1 0 2 1h3v-1c1 1 3 1 4 1h1l2 1h-11s-1-1-1-2z" class="AL"></path><path d="M268 247c1-1 1-1 2-1l2 1c0 1-1 1-1 1l-2 2h0v2l-2-1 1-2h1c-1-1-1-1-1-2h0z" class="x"></path><path d="M252 190c1 1 3 4 4 5h-1v1c-2-1-4-4-6-6h3z" class="AS"></path><path d="M242 258l3 1c1 0 3 0 4 1h-3c-1 1-2 1-3 1h-1c0 1-1 1-1 1-1 0-1-1-2-2 1-1 1-1 3-1v-1z" class="AI"></path><path d="M245 259c1 0 3 0 4 1h-3c-1 1-2 1-3 1h-1v-1h3 0v-1zm44-31l2 2v1c-1 1-1 4-2 6-1 0-1 0-2-1h1v-3h-1l-1 1h0c-1-2-1-3 0-4 0 1 0 1 1 2h1l1-2v-2z" class="AF"></path><path d="M250 208c2 0 7 0 9 2h0-2l-1-1v1 1h-3c-1-1-1-1-2-1h-2-5-1 0c2-1 5-2 7-2z" class="AO"></path><path d="M249 210h2c0 1 1 2 1 2-5 1-9 3-14 3h0c1-1 2-1 3-1 1-1 2-1 3-2 2-1 3-1 5-2z" class="AC"></path><path d="M253 219c2-1 8 0 10 1h0-11c-4 1-7 2-11 2 1 0 2 0 3-1 1 0 2-1 3-1 2-1 4-1 6-1z" class="AT"></path><path d="M229 193l2 9c0 2 1 6 1 7v4h0v1 1c0-1 0-1-1-2h0c-1-4-2-9-3-13 1-1 0-2 0-4h1v-1-2z" class="AZ"></path><path d="M237 257c0-1 1-1 1-2 0 1 1 2 2 2 0 1 1 1 2 1h0v1c-2 0-2 0-3 1 1 1 1 2 2 2v1c-1 0-2-1-3 1h1c-1 1-1 0-2 0l-1 1h-1l-1-1h0v-2-1l3-3-1-1h1z" class="z"></path><path d="M239 258v2 1c-1 0-1 1-1 1h-1v-3l2-1z" class="AR"></path><path d="M237 257c0-1 1-1 1-2 0 1 1 2 2 2 0 1 1 1 2 1h0v1c-2 0-2 0-3 1h0v-2-1h-1-1 0z" class="AD"></path><path d="M239 260h0c1 1 1 2 2 2v1c-1 0-2-1-3 1h1c-1 1-1 0-2 0l-1 1h-1l-1-1 1-1c1 0 1-1 2-1h1s0-1 1-1v-1z" class="AO"></path><path d="M234 204s0-1-1-1c0-1 1-1 1-2h1 1 1l1-1h1 6 3 1c1 0 3 1 4 2 0 1-1 0-1 1v1l-5-2c-2 0-5 0-6 1h-1v-1h-1c-2 1-4 1-5 2z" class="k"></path><path d="M256 238h0 6 2v1h-1 0c-2 0-5 0-6 1h-1c-1 0-2 1-3 1v2c1-1 3 0 4 0h1c1-1 2-1 3-1v1h-2l-1 1h-1l-7 2h0v-1h-1s-1 0-2 1v-1l4-4c1-1 1 0 1-1 1 0 1-1 2-1l2-1z" class="O"></path><path d="M254 221c1 0 2 0 3 1-1 0-1 1-1 1-1 0-2 0-2 1v1c1 0 2-1 4-1v1c-1 0-3 1-4 2h-1-1l-1 1-1 1h0-1v-1c1-1 2-1 3-2 1 0 0 0 1-1-1 0-1 0-2 1h-1c-1 1-2 2-3 2 0 1 0 1-1 1h-1c1-1 2-2 2-3l-1-1h-1v-1h1c1-1 1-1 2-1 1-1 2-1 3-1h1c1 0 1-1 2-1z" class="b"></path><path d="M237 183c1 0 2 1 3 2l1 1c1 0 4 3 4 4h-1l1 1-1 1-3-2-4-2h0c0 1 1 2 2 2 0 1 1 2 1 2v1c-1-1-1-2-2-2h0c-2-1-4-3-5-4l1-1h2c0-1 1-1 1-2v-1z" class="i"></path><path d="M232 209c1 6 5 22 10 24h0c0 1-1 1-2 2l-2-2c-3-6-6-13-7-20h0c1 1 1 1 1 2v-1-1h0v-4z" class="AV"></path><path d="M254 221c4 0 8 0 12 1h2s1 1 2 1l1 2h0 1c-1 1-1 1-2 1-2-1-5 0-8-1h-4v-1c-2 0-3 1-4 1v-1c0-1 1-1 2-1 0 0 0-1 1-1-1-1-2-1-3-1z" class="c"></path><path d="M283 221c1 0 3-1 3-2v-1l1 1v2c0 1 1 1 2 2h0l2-1v2c-1 1-1 2-1 4h-1v2l-1 2h-1c-1-1-1-1-1-2v-2h-1l-2-1c-1-2-2-3-3-4l3-2z" class="x"></path><path d="M280 223l3-2 1 1c1 1 1 2 0 3 0 1-1 1-1 2-1-2-2-3-3-4z" class="AJ"></path><path d="M291 222v2c-1 1-1 2-1 4h-1v2h-1v-1-2-1h1v-3l2-1z" class="AU"></path><path d="M255 196v-1h1c5 5 10 13 17 16-1 0-1 1-2 1l1 1 1 1v1c2 2 4 5 5 7 3 3 5 7 5 11-1-2-2-5-3-7-2-3-5-6-7-10-6-7-13-13-18-20z" class="AZ"></path><path d="M278 222c-1-2-3-5-5-7v-1h1l1 1h2 2 1 0 1c2 1 3 1 5 1h0c0 1 0 1 1 2v-2h1l1 1c0 1 0 2 1 2l1-1h0c1 1 1 2 1 3h0l-1 1-2 1h0c-1-1-2-1-2-2v-2l-1-1v1c0 1-2 2-3 2l-3 2c0-2 0-3-1-4l-2-3h0c0 1 1 3 2 4 0 1 0 1-1 2z" class="AC"></path><path d="M234 204c1-1 3-1 5-2h1v1h1c1-1 4-1 6-1l5 2 1 1c-1 0-4-1-5-1-3-1-5-1-7-1 2 1 3 2 5 3 3 0 5 0 7 1h0-4l1 1c-2 0-5 1-7 2h0 1 5c-2 1-3 1-5 2-1 1-2 1-3 2-1 0-2 0-3 1l-3-3c1-1 3-3 5-4 1 0 3 0 4-1v-1c-1 1-2 1-3 1-2 1-4 2-5 3h-1l1-1-1-1c0-1 0-1-1-1v-1c2-1 4-1 6-3h-1c-2 1-3 1-4 1h-1z" class="AJ"></path><path d="M234 207c2-1 3-1 4-1 1-1 2-1 3-2v1h1c-2 1-4 2-6 4l-1-1c0-1 0-1-1-1z" class="B"></path><path d="M244 212c-3-1-4 0-7 1v-1c2-2 6-4 8-4s3-1 4-1l1 1c-2 0-5 1-7 2h0 1 5c-2 1-3 1-5 2z" class="c"></path><path d="M272 247h1c1-1 0-2 0-3h0c1 0 1 1 2 1v-2c-1-1-1-1-1-2h1c0 1 1 2 1 2 0 1 1 1 1 1 1-1 1-1 1-2v-1h0c1 1 1 2 1 3 1 1 1 2 1 3v3h-1c0 1 0 1-1 1 0 1 1 2 1 3v3l1 1h-2 0v1h-1-1-1l-1-1-3-1c0-2-1-3-2-5v-2h0l2-2s1 0 1-1z" class="AW"></path><path d="M278 258c0-1-1-1-1-2v-1l2-1v3l1 1h-2 0z" class="AF"></path><path d="M278 251s0-1-1-1v1c-1 0-1 0-2-1v-2c1 0 1 1 1 1h3v1c0 1 0 1-1 1z" class="AM"></path><path d="M267 251h0l2 1c1 2 2 3 2 5 2 1 2 2 2 3v1l2 2-1 1s0 1 1 1v1s1 0 1 1c1 1 3 2 5 3l1 1c-1 0-2 0-4-1 0 1 1 1 0 2l-1 1h0-2c-1-1-3-1-4-1s-1 1-2 1c-1-1-2-1-3-2h-1l-1 1 1 1c-3 2-6 1-9 1h-1 0c-1-1-3-3-5-3v1c1 1 3 2 4 3h0c-1 0-6-1-7-2h-3c-2 0-4-1-6 0h-1l-1 1h0l-1 2c-1-1-1-1-2-1-1 1-1 1-2 1v-1h-1c-1-1-2 0-3-1l2-4v-1c0-1 1-1 1-2s0-2 1-3l1-1 2 1h0l1 1h1l1-1c1 0 1 1 2 0h-1c1-2 2-1 3-1v-1s1 0 1-1h1c1 0 2 0 3-1h3 0l5-2-1-1h-3 0c2-1 4-2 6-2h2l-2-1v-1h2v-1h-1-1v-1h3c1 0 2 0 3 1l1 1h1 2v1c1-1 1-2 1-3z" class="AR"></path><path d="M261 261c1 0 1 1 2 1 0 1-1 2-1 2h-3v-1s1-1 1-2h1z" class="AH"></path><path d="M239 264h3c-1 1-1 2-3 2h-4v-1h1l1-1c1 0 1 1 2 0z" class="AQ"></path><path d="M259 258h0c1 1 2 1 2 2 1 0 1 1 1 1h1 1s1 1 1 2h-2v-1c-1 0-1-1-2-1h-2c-1 0-1 0-2-1h2v-2h0z" class="AI"></path><path d="M246 260h3 0c1 0 2 1 2 1-1 1-1 1-1 3h-2l-1-1h1l-1-1h1l-2-2z" class="AK"></path><path d="M246 260l2 2h-1 0l-1 1h-4v1h-3-1c1-2 2-1 3-1v-1s1 0 1-1h1c1 0 2 0 3-1z" class="AW"></path><path d="M246 260l2 2h-1 0c-2 0-2 1-4-1 1 0 2 0 3-1z" class="x"></path><path d="M249 260l5-2-1-1h-3 0c2-1 4-2 6-2v1h0l3 2h0l-1 1c-1 0-1 0-2 1h0c-1-1-2-1-3-1l-1 2h3v1c-2 0-3-1-4-1 0 0-1-1-2-1z" class="AE"></path><path d="M275 266s1 0 1 1c1 1 3 2 5 3l1 1c-1 0-2 0-4-1 0 1 1 1 0 2l-1 1h0-2c-1-1-3-1-4-1s-1 1-2 1c-1-1-2-1-3-2 2 0 5 1 7 1-1-1-2-1-3-2-2 0-4-1-6-1-2-1-4 0-6 0 0-1 1-1 1-1h1 1l1-1c4 0 10 2 12 4l1 1v-1c-1 0-1 0-1-1-1-1-2-1-3-2 2-1 2-1 4-1v-1z" class="w"></path><path d="M239 267c1 0 1 1 2 1h4c1 1 0 1 1 1h2v1h-2l1 1h2 0c0-1 0-1 1-1h0c1 0 2 0 2 1 1 0 2 1 3 1l1 1h0c2 0 5 1 6 0 1 0 1-1 2-1l1 1c-3 2-6 1-9 1h-1 0c-1-1-3-3-5-3v1c1 1 3 2 4 3h0c-1 0-6-1-7-2h-3l1-1v-1h-1c-1 0-1 0-2 1-1-1-1-1-2-1 0-1 1-1 2-1v-1h-3v-2z" class="AH"></path><path d="M231 264l1-1 2 1h0l1 1v1c-1 0-1 0-2 2h1 2l3-1v2h3v1c-1 0-2 0-2 1 1 0 1 0 2 1 1-1 1-1 2-1h1v1l-1 1c-2 0-4-1-6 0h-1l-1 1h0l-1 2c-1-1-1-1-2-1-1 1-1 1-2 1v-1h-1c-1-1-2 0-3-1l2-4v-1c0-1 1-1 1-2s0-2 1-3z" class="AC"></path><path d="M231 264l1-1 2 1h0l1 1v1c-1 0-1 0-2 2h1 2 2 0c-1 2-3 0-5 1-1 1-1 2-2 3l1 1c-1 0-1 0-2 1h2l-1 1h-1c-1-1-2 0-3-1l2-4v-1c0-1 1-1 1-2s0-2 1-3z" class="AN"></path><path d="M267 251h0l2 1c1 2 2 3 2 5 2 1 2 2 2 3v1l2 2-1 1s0 1 1 1v1 1c-2 0-2 0-4 1 1 1 2 1 3 2 0 1 0 1 1 1v1l-1-1c-2-2-8-4-12-4l-1 1h-1l1-1 1-1h0 3v1c1 0 1 0 2-1 0-1 1-1 1-2h-1l-1-1v1h-2l-1-1h2c0-1-1-2-1-2h-1-1s0-1-1-1c0-1-1-1-2-2h0l-3-2h0v-1h2l-2-1v-1h2v-1h-1-1v-1h3c1 0 2 0 3 1l1 1h1 2v1c1-1 1-2 1-3z" class="AH"></path><path d="M271 262c1 0 2 0 2-1l2 2-1 1s0 1 1 1c-1 1-2 1-3 1 0-1 0-2 1-2l-1-1-1 1-1-1h0l1-1z" class="J"></path><path d="M256 256h3l1-1h2v1l3 3v1h-1v1h-1-1s0-1-1-1c0-1-1-1-2-2h0l-3-2h0z" class="AM"></path><path d="M256 255h2l-2-1v-1h2v-1h-1-1v-1h3c1 0 2 0 3 1l-1 1c1 1 3 2 5 3v1h-1 0c-1-1-2-1-3-2h-2l-1 1h-3v-1zm11-4h0l2 1c1 2 2 3 2 5 2 1 2 2 2 3v1c0 1-1 1-2 1l1-1h-1l-2-1c0-1 0-1 1-1v-1l-1-1-1-1c0-1 0-1-1-2h-1v-1 1c1-1 1-2 1-3z" class="w"></path><path d="M289 217l1 1v-1h2c1 1 2 1 3 0 1 0 1 0 2 1 0-1 1-1 2 0h1c2-1 5-1 6 0l1 1h1v-1-1l2 1h0c1 0 1 0 1-1l2 3h1v-3h1c0 1 0 2 1 3h0l1-1c0 1 1 2 2 3 0 1 0 2 1 4v1h-1v-1 2c0 2 1 3 2 4v-1l1 1h0l-2 2v3l1 1 1-1h1l1-1h1 0c1 0 1-1 2-1v-2c1 1 2 2 2 3l1 1c0 1 0 1 1 1 0 1 1 2 1 4l1 1c1 2 1 4 0 6v2h-1-1c0 2-1 5-2 7 1 1 1 2 2 3h1v1 2h-1c1 1 1 0 2 0s1 0 2 1l-3 2h1l1 1v1c-1 0-2 1-3 1h0c1 0 3 0 4-1l1 1c0 1 0 1-1 1-1 1-1 1-1 2h0v1l4-2v1h3c1 1 4 0 6 0h2 0 2c0 1 1 2 1 3 2 3 0 7 1 8h2c0 1-1 1-1 2l-1 1c0 1-1 1-1 2v1c-2 1-6 4-8 3v1l1 2 2-1v2c1 0 3 1 4 1l-1 1c1 0 2 0 3 1v1c-1 1-1 1-2 1v1l1 1c-1 0-4-1-5-1v1h-7v1c-1-1-1-1-2-1v1h-2l-1-1c-1 0-1-1-2-1l-2-1h0v1 1c-1 0-1-1-2-1h-1l1 1c1 1 1 1 1 2h0v1h-2v1h1v1h0c-1 0-2 0-3-1v1 1h-1c-1 0 0 0 0 1-1 1-1 1-2 1v3h-1c-1 1-1 3-2 4v3l-1 1c0 1 0 1-1 1v1l1 1h-2v1l1 1c-1 0-2 0-2 1h-1c-1 0-2 1-3 2v1c-1-1-2-1-2-1l-2 1c0-1 0-1-1-2-1 1-2 2-2 3l-1 1c1 1 1 1 1 2-1 0-1 0-2 1l-1 1c0-2 0-2 1-3h1v-1h-1-2l-1-1h-2c0 1-1 1-2 2h0 0-4l-1-1h-3c-1 0-1-1-2-1l-1-1 1-1v-1c-1 0-1-1-1-2-1-1-2-1-4-1h0v-1h2v-1h-9c-18 0-38 7-54 16l-14 8c-2 1-4 2-7 4-3 1-6 3-9 4l-1-1v-6l1-5c-1-2-1-3-1-5l1-2s1 1 2 1v-1-3h0v-1c0-1-1-1-2-1v-2-5-1-4h1-1l8-4 6-5 1-1c1-2 2-2 4-3 3-3 6-6 8-10l3-5h1l3-2h0c2-2 3-3 5-4v-1l-5 3v-1l3-4-1 1-1-1 1-1c-1 0-1-1-2-2 1-1 1-2 2-3h-2c0-2 2-3 3-4 1 1 2 0 3 1h1v1c1 0 1 0 2-1 1 0 1 0 2 1l1-2h0l1-1h1c2-1 4 0 6 0h3c1 1 6 2 7 2h0c-1-1-3-2-4-3v-1c2 0 4 2 5 3h0 1c3 0 6 1 9-1l-1-1 1-1h1c1 1 2 1 3 2 1 0 1-1 2-1s3 0 4 1h2 0l1-1c1-1 0-1 0-2 2 1 3 1 4 1l-1-1c-2-1-4-2-5-3 0-1-1-1-1-1v-1c-1 0-1-1-1-1l1-1-2-2v-1c0-1 0-2-2-3l3 1 1 1h1 1 1v-1h0 2l-1-1v-3c0-1-1-2-1-3 1 0 1 0 1-1h1v-3c0-1 0-2-1-3h1c1 0 1-1 2-2 0 0 0-1 1-1h1c1 0 1-2 2-2v1h1v-2l-1-1 1-1c1 1 1 1 2 1 1-2 1-5 2-6v-1l-2-2h1c0-2 0-3 1-4v-2l1-1h0c0-1 0-2-1-3h0l-1 1c-1 0-1-1-1-2z" class="m"></path><path d="M274 294h3c-1 1-2 2-3 2h0-1v-1s1 0 1-1h0z" class="O"></path><path d="M264 272l1-1h1c1 1 2 1 3 2h-4l-1-1z" class="AM"></path><path d="M325 261l1 2c0 2-1 4-2 6 0-3 0-5 1-8zm-27 39c0-1 0-2 1-2 0-1 4-5 4-5v2c-1 0-2 1-2 2v1l-1 1-2 1z" class="w"></path><path d="M298 300l2-1v2c1 0 1 1 1 1h-2c-1 1-1 1-1 2 0 0-1-1-1-2s1-1 1-2z" class="y"></path><path d="M298 300l2-1v2c1 0 1 1 1 1h-2c0-1 0-2-1-2z" class="Aa"></path><path d="M332 267l-1 1v-1-1l-1-1h0l1-1c1 1 1 0 2 0s1 0 2 1l-3 2z" class="O"></path><path d="M314 257v4h1v3h-1v-1h-1v-2-2l1-2z" class="b"></path><path d="M288 248h1c1 1 1 2 1 2v2 1c-2 3-1 7-1 10-1-2 0-6 0-9l-1-6z" class="AE"></path><path d="M299 302h2 0l-1 1c0 1 1 2 1 2v1c-1 0-2 0-2 1h0-1-1l2-2-1-1c0-1 0-1 1-2z" class="x"></path><path d="M309 260v-1h1c0 1 1 1 2 1 0 1 0 1 1 1v2c0 1-1 3-1 4h-1v-3c-1-1-1-2-2-3v-1z" class="B"></path><path d="M292 260h2c0 2-1 4-1 6s1 3 0 5c0-3-2-7-2-10l1-1zm7-3c1 0 1 0 2 1l2 3c1 1 1 2 2 3-2-1-2-2-4-3v3c-2-1-2-4-2-6h0v-1z" class="J"></path><path d="M299 257c1 0 1 0 2 1l-1 1-1-1h0v-1z" class="AF"></path><path d="M320 259c1 3-1 8 0 10 1-1 1-4 2-5l1-1-1 1v1c0 4-2 7-4 10 1-4 0-9 1-14 0 1 0 3 1 4v-6z" class="AB"></path><path d="M329 304c1 1 1 1 1 2h0v1h-2v1h1v1h0c-1 0-2 0-3-1v1 1h-1c-1 0 0 0 0 1-1 1-1 1-2 1 0-1 1-2 2-4-1 0-1 0-1-1h1c0-2 0-2 1-3l2 1 1-1zm-7-48l1-1v3c0 1-1 4 0 5l-1 1c-1 1-1 4-2 5-1-2 1-7 0-10v-2h2v-1z" class="s"></path><path d="M322 256l1-1v3c0 1-1 4 0 5l-1 1v-7-1z" class="AK"></path><path d="M274 291l6-2 1 1h2 1c-1 1-1 1-2 1-1 1-3 2-4 2l-1 1h-3v-1-2z" class="AB"></path><path d="M314 249h1v-2c1 0 1 1 1 1h1v2 4c0 2-2 4-2 7h-1v-4-1-3-2-2z" class="x"></path><path d="M314 249h1v-2c1 0 1 1 1 1-1 2-1 4-1 5v2l-1 1v-3-2-2z" class="AL"></path><path d="M292 246h1v5l1 1v3c1 1 1 2 1 3h-1v2h-2l-1 1v-1c0-2-1-4 0-6h0l-1-1v-1c1-2 2-4 2-6z" class="AE"></path><path d="M292 252c1 2 1 4 1 6 0 1 0 1-1 2l-1 1v-1-1c0-3 0-4 1-7z" class="AG"></path><path d="M292 246h1v5l-1 1c-1 3-1 4-1 7v1c0-2-1-4 0-6h0l-1-1v-1c1-2 2-4 2-6z" class="AF"></path><path d="M304 251l1 1v2c0 1-1 2-1 3h1v-1h1l-1 3s0 1 1 1c0 1-1 3 0 4v5l-1-5c-1-1-1-2-2-3l-2-3c-1-1-1-1-2-1v-3h0l1 1h0c1 0 2-1 2-2h1 1v-1-1z" class="AG"></path><path d="M304 253l-1 3v1s0 1 1 2c0 1 0 1-1 2l-2-3c-1-1-1-1-2-1v-3h0l1 1h0c1 0 2-1 2-2h1 1z" class="x"></path><path d="M304 253l-1 3h0-3v-1h0c1 0 2-1 2-2h1 1z" class="AD"></path><path d="M305 295c0-1 1-2 1-4 0-1 1-2 2-3h0c1 0 1 1 1 1 1 1 1 3 2 3l1 1c0 2 1 3 1 4l-1 1h0-1v1h-1l-1-1 1-1h0c-1 0-2 0-2 1h-1l1-1v-1h-2l-1-1z" class="I"></path><path d="M309 298l1 1h1v-1h1 0v1h1c0-1 0-1 1-1s1 1 1 2c0 0 0 1-1 2h2v1c0 1-1 1-1 1h-1c-1 0-1-1-1-1l-1 1h1c-1 1-1 1-2 1l-1-2c-1 0-1 0-2 1v1l-1-1h-1c-1-1-1-2-1-4h2v2h1c0-1 1-1 1-2v-2z" class="B"></path><path d="M310 300l1-1c0 1 0 1 1 2 0 0 0 1-1 2 0-1-1-1-1-2v-1z" class="y"></path><path d="M312 301l1-1c0 1 1 2 2 3h1c0 1-1 1-1 1h-1c-1 0-1-1-1-1l-1 1h1c-1 1-1 1-2 1l-1-2h1c1-1 1-2 1-2z" class="J"></path><path d="M305 300h2v2h1c0-1 1-1 1-2h1v1c0 1 1 1 1 2h-1c-1 0-1 0-2 1v1l-1-1h-1c-1-1-1-2-1-4z" class="x"></path><path d="M310 301c0 1 1 1 1 2h-1c-1 0-1 0-2 1v1l-1-1c1-1 1-2 3-3z" class="w"></path><path d="M301 298v-1c0-1 1-2 2-2h2l1 1h2v1l-1 1h1c0-1 1-1 2-1h0l-1 1v2c0 1-1 1-1 2h-1v-2h-2 0v2h-1v-1c-1 1-2 1-3 1h0s0-1-1-1v-2l1-1z" class="J"></path><path d="M301 298l2-1v1c0 1 1 1 1 1 1 0 2 0 3 1h-2 0v2h-1v-1c-1 1-2 1-3 1h0s0-1-1-1v-2l1-1z" class="AE"></path><path d="M300 301c1-1 1-1 2-1 0 0 1 1 2 1-1 1-2 1-3 1h0s0-1-1-1z" class="y"></path><path d="M301 298l2-1v1l-1 2c-1 0-1 0-2 1v-2l1-1z" class="AD"></path><path d="M306 260c1-3 3-6 3-9 0 0 1 0 2 1h0v1 1c0 1 0 1 1 1 0 1 0 1 1 1v3 2c-1 0-1 0-1-1-1 0-2 0-2-1h-1v1c0 3 0 6-1 9v-10l-1-2c0 2 0 3-1 4h1l-1 3c-1-1 0-3 0-4z" class="s"></path><path d="M311 252v1 1c0 1 0 1 1 1 0 1 0 1 1 1v3 2c-1 0-1 0-1-1-1-3-2-5-1-8z" class="AR"></path><path d="M294 252h1 1 1v2h1 1v3 1-1c-1 0 0 1-1 1v5h0c0 2 1 4 0 5 0-1-1-1-2-2-2 0-1-1-2-3 1-1 1-3 1-4v-1c0-1 0-2-1-3v-3z" class="B"></path><path d="M298 263h0-1c0-2 0-3 1-5v5z" class="w"></path><path d="M294 252h1 1 1v2h1v2c-1 2-2 2-2 4v1h0-1v-2-1c0-1 0-2-1-3v-3z" class="AG"></path><path d="M294 252h1 1l-1 3h-1v-3z" class="AL"></path><path d="M333 303l1-1c-1-1-2-1-3-1 0-1 1-1 1-2-1 0-1 0-1-1 2 0 1 1 3 1l1-1-1-2h0 1c1 1 2 2 3 2h0l2 1v1h1v1h1v1h-2v1h4 3v1h-7v1c-1-1-1-1-2-1v1h-2l-1-1c-1 0-1-1-2-1z" class="AJ"></path><path d="M338 298l2 1v1h1v1h1v1h-2s0-1-1-1h-1c-1-1-1-1-1-2l1-1z" class="AP"></path><path d="M334 273v1l4-2v1c-2 1-4 1-4 3l-1 1h0c-1 0-1 1-1 1v2c-1 1-2 2-2 4l1 1v3h-1c-1-1-1-1-2-1h-1l-2 4v1c0 1-1 1-1 2h-1c0-2 1-2 1-3v-1c0-1 1-2 1-3 1-3 3-5 4-8 1-2 3-4 5-6z" class="w"></path><path d="M327 287v-2c1-1 1-2 1-3 1 0 2 1 2 2l1 1v3h-1c-1-1-1-1-2-1h-1z" class="b"></path><path d="M287 249v-1h1l1 6c0 3-1 7 0 9 0 2 0 4 1 5 1 2 2 4 2 6l-3-5c-2-2-3-4-3-7v-2-1l-1 1v1h-1l-1-1 1-1h0c0-1 1-1 1-2l1-3 1-1v-4z" class="AG"></path><path d="M286 262l1 2c0-2 0-3 1-4 0 2 0 5 1 7v2c-2-2-3-4-3-7z" class="AE"></path><path d="M285 257l1-3 1-1v1c0 2 0 4-1 6v-1l-1 1v1h-1l-1-1 1-1h0c0-1 1-1 1-2z" class="AF"></path><path d="M287 249v-1h1l1 6v2l-1 1-1-1v-2-1-4zm40-3v3l1 1c-1 2 0 3 0 5v5c-1 5-2 9-5 13l1-4c1-2 2-4 2-6l-1-2c-1-1-1-2-1-3h-1v-3-1h1v-1-3-1-2c1 0 2 0 3-1z" class="AD"></path><path d="M323 255v-1h1c0 1 0 2 1 3h0l1-2h1c0 2 0 6-1 8l-1-2c-1-1-1-2-1-3h-1v-3z" class="y"></path><path d="M307 238h2v1c1 1 1 2 1 3v1h0v1 3c0 1 0 2 1 2v3c-1-1-2-1-2-1 0 3-2 6-3 9-1 0-1-1-1-1l1-3h-1v1h-1c0-1 1-2 1-3v-2l-1-1c0-1 0-2 1-3 0-1 0-3 1-4v-3c1-1 1-2 1-3z" class="AK"></path><path d="M306 244c0 2 0 4 1 6l-2 2-1-1c0-1 0-2 1-3 0-1 0-3 1-4z" class="y"></path><path d="M307 250c1 3 0 4-1 6h-1v1h-1c0-1 1-2 1-3v-2l2-2z" class="AI"></path><path d="M307 238h2v1c1 1 1 2 1 3v1h0v1c0 1-1 1-1 2h0l-1 1c-1-1-1-1-1-2l-1-1v-3c1-1 1-2 1-3z" class="z"></path><path d="M307 238h2l-1 4v1c-1 0-1 1-1 1v1l-1-1v-3c1-1 1-2 1-3z" class="AC"></path><path d="M276 279l1-1h3c0-1-1-1-1-1l-2-1h-2c-2-1-8 1-9 0 0-1 0-1 1-1h1 8s2 0 3 1h1l1 1h1l4 4h0v1c1 1 2 3 3 5v1h0c1 1 1 3 2 4h-1l-1-1c0-1-1-1-1-2s-1-1-2 0h-1l-1 1h-1-2l-1-1 3-2h0c0-1 1-2 1-4 0 0 0-1-1-1 0-1-1-2-2-2h-1c-1-1-1-1-2-1h-2z" class="b"></path><path d="M280 289l3-2c0 1-1 1-1 2h3l-1 1h-1-2l-1-1z" class="O"></path><path d="M342 294l2-1v1l1 2 2-1v2c1 0 3 1 4 1l-1 1c1 0 2 0 3 1v1c-1 1-1 1-2 1v1l1 1c-1 0-4-1-5-1h-3-4v-1h2v-1h-1v-1h-1v-1h1 0c-1-1-2-1-3-2l1-1-1-2c1 0 3 1 4 2h1c0-1-1-1-1-2z" class="AO"></path><path d="M344 303l-1-1 2-1c2 0 3 0 4 1v1l2-1v1l1 1c-1 0-4-1-5-1h-3z" class="AF"></path><path d="M342 294l2-1v1l1 2 2-1v2c1 0 3 1 4 1l-1 1c-1 0-2 0-2-1h-3s0-1-1-1c-1-1-1 0-2-1h1c0-1-1-1-1-2z" class="w"></path><path d="M342 294l2-1v1l1 2h-2c0-1-1-1-1-2z" class="s"></path><path d="M271 257l3 1 1 1h1 1 1v-1h0 2l1 2h0c0 1 1 2 2 2-1 0-1 1-2 1v1c2 2 3 2 4 5v2l-1 1-1-1h-1l-1-1c-2-1-4-2-5-3 0-1-1-1-1-1v-1c-1 0-1-1-1-1l1-1-2-2v-1c0-1 0-2-2-3z" class="AG"></path><path d="M279 261c1 0 1 0 2-1 0 1 1 2 2 2-1 0-1 1-2 1s-1-1-2-2z" class="O"></path><path d="M280 258l1 2h0c-1 1-1 1-2 1v-1c-1 0-1-1-1-2h2z" class="i"></path><path d="M275 265c-1 0-1-1-1-1l1-1c1 0 2 2 3 2l-2 2c0-1-1-1-1-1v-1z" class="s"></path><path d="M271 257l3 1 1 1h1c1 1 1 2 2 2v2h0c-2-1-2-2-3-3h-2c0-1 0-2-2-3z" class="x"></path><path d="M278 265l1 2h1c-1-1-1-2-2-2l2-2c0 1 1 1 1 1h0c2 2 3 2 4 5v2l-1 1-1-1h-1l-1-1c-2-1-4-2-5-3l2-2z" class="AB"></path><path d="M283 271h0c0-1 0-2-1-3 0-1-1-1-1-2v-1l1 1h0c0 1 1 2 2 3h1v2l-1 1-1-1z" class="B"></path><path d="M324 241v1h2v3l1 1h0c-1 1-2 1-3 1v2 1 3 1h-1v1l-1 1v1h-2v2 6c-1-1-1-3-1-4 0-2-1-5-1-7h-1v-4l1-1v-1c1 0 1 0 2-1v-1h0c1-1 1-1 1-2h2 0s1 0 1-1h0v-2z" class="AK"></path><path d="M317 250l1-1v4 1h-1v-4z" class="AE"></path><path d="M324 245h0 2l1 1h0c-1 1-2 1-3 1v-2z" class="x"></path><path d="M324 249h-1c0-1-1-2-1-2 0-1 1-2 2-2v2 2z" class="AT"></path><path d="M320 255h1v-5h0l2 2v-1-1h1v3 1h-1v1l-1 1v-4c-1 1-1 3-1 4l-1-1z" class="x"></path><path d="M318 253v1h1c0-2 0-3 1-5v6l1 1c0-1 0-3 1-4v4 1h-2v2 6c-1-1-1-3-1-4 0-2-1-5-1-7v-1zm-7-21h1 0v1c0 1 1 2 1 3v1h1v1h1c1 2 1 2 0 3v1c0 1 1 3 0 5v2h-1v2 2 3 1l-1 2v-3c-1 0-1 0-1-1-1 0-1 0-1-1v-1-1h0v-3c-1 0-1-1-1-2v-3-1h0v-1c0-1 0-2-1-3v-1-3h1v-3h1z" class="AG"></path><path d="M309 238v-3h1c0 2 0 4 1 6v1h-1c0-1 0-2-1-3v-1z" class="AM"></path><path d="M311 232h1 0v1c0 1 1 2 1 3v1h1v1h1c1 2 1 2 0 3v1l-1-1h-1 0c0-3-2-6-2-9z" class="AC"></path><path d="M313 241h1l1 1c0 1 1 3 0 5v2h-1v2c0-1-1-1-1-2h-1c0-1-1-3 0-4h1v-2-2z" class="AH"></path><path d="M313 243c1 2 1 4 1 6v2c0-1-1-1-1-2h-1c0-1-1-3 0-4h1v-2z" class="x"></path><path d="M311 241h0v1c0 1 0 2 1 3-1 1 0 3 0 4h1c0 1 1 1 1 2v2 3 1l-1 2v-3c-1 0-1 0-1-1-1 0-1 0-1-1v-1-1h0v-3c-1 0-1-1-1-2v-3-1h0v-1h1v-1z" class="AC"></path><path d="M312 249h1c0 1 1 1 1 2v2 3 1l-1 2v-3c0-2 0-5-1-6v-1z" class="AI"></path><path d="M326 239v-2l1-1 1 1v1c0 1 1 2 2 2v-1l1-1c0 1 1 2 1 4l1 1c1 2 1 4 0 6v2h-1-1c0 2-1 5-2 7l-1-2v4-5c0-2-1-3 0-5l-1-1v-3h0l-1-1v-3-2h1l-1-1z" class="AR"></path><path d="M328 250c0 2 0 4 1 6 1-1 1-2 0-3v-1l1-1c0-2 0-3 1-5h0v1l1 1s0-1 1-1v2 2h-1-1c0 2-1 5-2 7l-1-2v4-5c0-2-1-3 0-5z" class="AF"></path><path d="M326 239v-2l1-1 1 1v1c0 1 1 2 2 2v-1l1-1c0 1 1 2 1 4h0c-1 1-1 2-1 3l-1 1c-1-1 0-1 0-2s1-2 1-3l-1-1v2h0l-3 3v1l-1-1v-3-2h1l-1-1z" class="AC"></path><path d="M304 302h1v-2h0c0 2 0 3 1 4h1l1 1v-1c1-1 1-1 2-1l1 2c1 0 1 0 2-1h-1l1-1s0 1 1 1h1l-1 1v2h2c-1 1-1 1-1 2h-1c-1-1-1 0-2 0l1-2h-1l-3 3s-1 1-2 1v-1c-1 0-1 0-2 1h-1c-1-1-1-2-2-2 0 1-1 2-1 3h-1l-2 2-1-1 1-1-1-1c0-1 0-1 1-1l1-3h0c0-1 1-1 2-1v-1s-1-1-1-2l1-1c1 0 2 0 3-1v1z" class="AO"></path><path d="M304 302h1v-2h0c0 2 0 3 1 4h-1l-1 1-2-2c1 0 1-1 2-1z" class="AD"></path><path d="M308 304c1-1 1-1 2-1l1 2c1 0 1 0 2-1h-1l1-1s0 1 1 1h1l-1 1v2h2c-1 1-1 1-1 2h-1c-1-1-1 0-2 0l1-2h-1l-3 3h0l-1-1c1 0 2-2 3-2v-1h0c-1 0-1 0-2 1 0-1-1-1-1-1l-1-1-1 1-1 2h-1v-1l2-2-1-1h1 1l1 1v-1z" class="AI"></path><path d="M308 304c1-1 1-1 2-1l1 2c-1 0-1 0-3-1z" class="AG"></path><path d="M319 228c0 2 1 3 2 4v-1l1 1h0l-2 2v3l1 1 1-1h1l1-1h1 0c1 0 1-1 2-1v-2c1 1 2 2 2 3l1 1c0 1 0 1 1 1l-1 1v1c-1 0-2-1-2-2v-1l-1-1-1 1v2l1 1h-1v2h-2v-1 2h0c0 1-1 1-1 1h0-2c0 1 0 1-1 2h0v1c-1 1-1 1-2 1v1l-1 1v-2h-1s0-1-1-1c1-2 0-4 0-5v-1c1-1 1-1 0-3h-1v-1h1c1-2 1-2 3-3h0v-2l-1-1 1-2h0l1-1z" class="AR"></path><path d="M326 239l1 1h-1v2h-2v-1-1-1l2 1v-1z" class="z"></path><path d="M319 228c0 2 1 3 2 4l-2 1v-1c-1-1-1-2-1-3h0 0l1-1z" class="AT"></path><path d="M315 237c1-2 1-2 3-3v3c0 1 1 3 1 4v2c1 0 2 1 2 1 0 1 0 1-1 2h0v1c-1 1-1 1-2 1v1l-1 1v-2h-1s0-1-1-1c1-2 0-4 0-5v-1c1-1 1-1 0-3h-1v-1h1z" class="AH"></path><path d="M319 243c1 0 2 1 2 1 0 1 0 1-1 2h0c-1-1-1-2-1-3z" class="AW"></path><path d="M315 242v-1c1 1 1 2 2 2h1v5 1l-1 1v-2h-1s0-1-1-1c1-2 0-4 0-5z" class="AG"></path><path d="M302 238l1-1v-1h1l1 1h1 1v1c0 1 0 2-1 3v3c-1 1-1 3-1 4-1 1-1 2-1 3v1 1h-1-1c0 1-1 2-2 2h0l-1-1h0-1-1v-2h-1-1-1l-1-1v-5h0l2-2-1-3 1-1h1c1-1 1-1 1-2l1 2 3-3 1 1z" class="AW"></path><path d="M302 240v1 1 1l-2-1 2-2z" class="AD"></path><path d="M303 253v-2c0-1 0-1 1-2v3 1h-1z" class="AJ"></path><path d="M299 242h1l2 1h0v2 1l-2-1c0-1 0-2-1-3z" class="AC"></path><path d="M301 237l1 1c-1 1-1 1 0 2l-2 2h-1l-1-2 3-3z" class="AH"></path><path d="M300 245l2 1v-1c0 1 1 1 1 2-1 1-2 1-2 2s0 2-1 3c1 1 2 1 2 1 0 1-1 2-2 2h0l-1-1v-4h0l1-1v-4z" class="AL"></path><path d="M307 237v1c0 1 0 2-1 3v3c-1 1-1 3-1 4-1-1 0-3-1-4 0-1-1-1-1-1v-4h1c0-1 1-1 1-2h1 1z" class="AK"></path><path d="M297 238l1 2 1 2c1 1 1 2 1 3v4l-1 1h0v4h0-1-1v-2h-1-1-1l-1-1v-5h0l2-2-1-3 1-1h1c1-1 1-1 1-2z" class="AW"></path><path d="M295 240l2 3h0v2l-1 1h0c-1-1-1-1-1-2h0l-1-3 1-1z" class="AH"></path><path d="M297 243v-1l1 1c0 2-1 5 1 7v4h0-1-1v-2h-1-1l2-1-1-1c0-1 0-2 1-3v-2-2z" class="AG"></path><path d="M297 247l1 7h-1v-2h-1-1l2-1-1-1c0-1 0-2 1-3z" class="AJ"></path><path d="M297 238l1 2 1 2c1 1 1 2 1 3v4l-1 1h0c-2-2-1-5-1-7l-1-1v1h0l-2-3h1c1-1 1-1 1-2z" class="AW"></path><path d="M262 281c0-1 1-2 2-3s3-1 5 0h1c1 1 0 3 0 4 1 1 1 3 2 5h1l2-4 1-4h2c1 0 1 0 2 1h1c1 0 2 1 2 2 1 0 1 1 1 1 0 2-1 3-1 4h0l-3 2-6 2-5-1c-3-2-5-4-6-7-1-1-1-1-1-2z" class="AS"></path><path d="M262 281c0-1 1-2 2-3s3-1 5 0h-1-2 0l-2 2v1l-1 1v1c-1-1-1-1-1-2z" class="AN"></path><path d="M275 283l1 1c0 1-1 2-1 2v1c1 1 2 0 3 0v1h-3c0 1-1 1-2 1l-1-1v-1h1l2-4z" class="AV"></path><path d="M276 284c1-1 2-1 2-1 1 0 2 1 2 1 0 2-1 2-2 3-1 0-2 1-3 0v-1s1-1 1-2zm-11-3c1 0 1 0 2 1s1 2 2 3 1 2 1 3h-1l-2-2-2-2v-3z" class="AZ"></path><path d="M291 231h0c1 0 2 0 3 1h0 0c0 2 0 3 1 4v1h-1v2 1 1l1 3-2 2h0-1c0 2-1 4-2 6v-2s0-1-1-2h-1-1v1 4l-1 1-1 3c0 1-1 1-1 2h0l-1 1h-1c0 1 0 1 1 2-1 0-2-1-2-2h0l-1-2-1-1v-3c0-1-1-2-1-3 1 0 1 0 1-1h1v-3c0-1 0-2-1-3h1c1 0 1-1 2-2 0 0 0-1 1-1h1c1 0 1-2 2-2v1h1v-2l-1-1 1-1c1 1 1 1 2 1 1-2 1-5 2-6z" class="x"></path><path d="M290 250v-4h1v-2c-1-1-1-1 0-2l1 1v3c0 2-1 4-2 6v-2z" class="AH"></path><path d="M291 231c1 0 2 0 3 1h0 0c0 2 0 3 1 4v1h-1v2 1c-1 0-1 0-1 1s0 1-1 2c0-1 0-2-1-3v-3c1-1 1-1 1-2s0-2-1-2v-1-1z" class="AW"></path><path d="M282 242s0-1 1-1h1c1 0 1-2 2-2v1h1v-2l-1-1 1-1c1 1 1 1 2 1 0 2-1 3-2 4h-1v4l1 1-1 1c-1 1-1 2-1 4v2h0c1-1 1-3 2-5v1 4l-1 1-1 3c-1-1-1-3-1-5h0v-3s-1 0-1-1l-1-1h-2c0-1 0-2-1-3h1c1 0 1-1 2-2z" class="y"></path><path d="M282 244h0c1-1 2-1 3-2 0 2 0 5-1 7 0 0-1 0-1-1l-1-1h-2c0-1 0-2-1-3h1c1 0 1-1 2-2v2z" class="AP"></path><path d="M279 244h1c1 0 1-1 2-2v2 3h-2c0-1 0-2-1-3z" class="AE"></path><path d="M280 247h2l1 1c0 1 1 1 1 1v3h0c0 2 0 4 1 5 0 1-1 1-1 2h0l-1 1h-1c0 1 0 1 1 2-1 0-2-1-2-2h0l-1-2-1-1v-3c0-1-1-2-1-3 1 0 1 0 1-1h1v-3z" class="AI"></path><path d="M283 248c0 1 1 1 1 1v3h0c-1 0-1-1-2-2 0-1 0-1 1-2z" class="AJ"></path><path d="M279 250h1v2 4l-1 1v-3c0-1-1-2-1-3 1 0 1 0 1-1z" class="z"></path><path d="M284 252h0c0 2 0 4 1 5 0 1-1 1-1 2h0l-1 1h-1c0 1 0 1 1 2-1 0-2-1-2-2h0v-4c1-1 1 1 2 1 0-2 0-3 1-5h0z" class="w"></path><path d="M338 273h3c1 1 4 0 6 0h2 0 2c0 1 1 2 1 3 2 3 0 7 1 8h2c0 1-1 1-1 2l-1 1c0 1-1 1-1 2v1c-2 1-6 4-8 3l-2 1c0 1 1 1 1 2h-1c-1-1-3-2-4-2s-1-1-1-1c-4-1-7-3-9-6 1 0 1 0 2 1h1v-3l-1-1c0-2 1-3 2-4v-2s0-1 1-1h0l1-1c0-2 2-2 4-3z" class="AZ"></path><path d="M344 279v-3h1c1-1 2-1 3-1s2 1 3 2c0 1 0 2-1 3 0-1-1-2-2-2-2 1-3 4-4 6 0 0 0 1-1 1 0 1-1 1-1 1 1-3 2-5 2-7zm-12 1c1-2 3-3 5-4l2 7h-1l-1-2c-1 0-2 1-2 1-1 1-1 2-1 3l3 3h4l-1 1h-3c-3-1-5-2-6-4l-1-1c0-2 1-3 2-4z" class="AS"></path><path d="M338 273h3c1 1 4 0 6 0h2 0 2c0 1 1 2 1 3v3l-1-2c-1-1-2-2-3-2s-2 0-3 1h-1v3c0 2-1 4-2 7h-1c-1-1-1-2-2-3l-2-7c-2 1-4 2-5 4v-2s0-1 1-1h0l1-1c0-2 2-2 4-3z" class="m"></path><path d="M338 273h3c1 1 4 0 6 0h2 0 2c0 1 1 2 1 3v3l-1-2c-1-1-2-2-3-2s-2 0-3 1h-1v3c0-2 0-4-1-5-2 0-4 0-6 1v1c-2 1-4 2-5 4v-2s0-1 1-1h0l1-1c0-2 2-2 4-3z" class="AE"></path><path d="M352 276c2 3 0 7 1 8h2c0 1-1 1-1 2l-1 1c0 1-1 1-1 2v1c-2 1-6 4-8 3l-2 1c0 1 1 1 1 2h-1c-1-1-3-2-4-2s-1-1-1-1c-4-1-7-3-9-6 1 0 1 0 2 1h1v-3c1 2 3 3 6 4h3l1-1h3c2-1 3-3 4-4l2-4c1-1 1-2 1-3l1 2v-3z" class="AU"></path><path d="M337 293c2 0 3 1 5 1 0 1 1 1 1 2h-1c-1-1-3-2-4-2s-1-1-1-1z" class="i"></path><path d="M331 285c1 2 3 3 6 4l2 1h1 3v2h-2c-3 0-8-1-11-4h1v-3z" class="B"></path><path d="M341 288h3c2-1 3-3 4-4l1 2-2 2h2l-1 3c-2 1-4 1-5 1v-2h-3-1l-2-1h3l1-1z" class="AV"></path><path d="M347 288h2l-1 3c-2 1-4 1-5 1v-2h-3-1c3 0 5 0 8-2z" class="AB"></path><path d="M352 276c2 3 0 7 1 8h2c0 1-1 1-1 2l-1 1c0 1-1 1-1 2-2 0-3 1-4 2l1-3h-2l2-2-1-2 2-4c1-1 1-2 1-3l1 2v-3z" class="w"></path><path d="M351 277l1 2v3c-1 1-2 3-3 4l-1-2 2-4c1-1 1-2 1-3z" class="AN"></path><path d="M352 276c2 3 0 7 1 8h2c0 1-1 1-1 2l-1 1h-2c0-2 0-2 1-3v-2-3-3z" class="s"></path><path d="M289 217l1 1v-1h2c1 1 2 1 3 0 1 0 1 0 2 1 0-1 1-1 2 0h1c2-1 5-1 6 0l1 1h1v-1-1l2 1h0c1 0 1 0 1-1l2 3h1v-3h1c0 1 0 2 1 3h0l1-1c0 1 1 2 2 3 0 1 0 2 1 4v1h-1v-1 2l-1 1h0l-1 2 1 1v2h0c-2 1-2 1-3 3h-1-1v-1c0-1-1-2-1-3v-1h0-1-1v3h-1v3 1-1h-2v-1h-1-1l-1-1h-1v1l-1 1-1-1-3 3-1-2c0 1 0 1-1 2h-1l-1 1v-1-1-2h1v-1c-1-1-1-2-1-4h0 0c-1-1-2-1-3-1h0v-1l-2-2h1c0-2 0-3 1-4v-2l1-1h0c0-1 0-2-1-3h0l-1 1c-1 0-1-1-1-2z" class="AH"></path><path d="M307 224c1 1 1 1 1 2s-1 2-1 2l-1 1v-4h1v-1z" class="z"></path><path d="M313 223h2l1 1v1h-2l-1-2z" class="AQ"></path><path d="M314 220c1 1 1 1 0 2l1 1h-2c-1-1 0-1-2-3v-1c1 1 1 1 2 1h1z" class="AT"></path><path d="M308 218h0l2 2 1 1v2h-1c0-1 0-1-1-2h0l-2 2-1-1v-2h0-1v-1-1l1 1 1 1h0v-1h1v-1z" class="AN"></path><path d="M303 222h1v-2c0-1 0 0 1-1v1h1 0v2l1 1v1 1h-1c-1 0-2 1-2 2 0-1-1-2-1-3v-2z" class="AT"></path><path d="M300 218c2-1 5-1 6 0l1 1v1h0l-1-1-1-1v1c-1 1-1 0-1 1v2h-1l-1-1h0-2c1-1 1-2 0-3z" class="AQ"></path><path d="M302 229l1-1v-1c0-1 0-1-1-2h0v-1h1c0 1 1 2 1 3 0-1 1-2 2-2h1-1v4 1c-1 0-2 0-3 1v-1l-1-1z" class="AM"></path><path d="M304 227c0-1 1-2 2-2h1-1c0 1 0 2-1 2h-1z" class="AQ"></path><path d="M306 229l1-1v5h0-1l1 2h-1c1 1 1 1 1 2h-1-1l-1-1h-1v1l-1 1-1-1 1-1v-1c1 0 0-1 0-1 1-2 1-3 1-4v1c1-1 2-1 3-1v-1z" class="AL"></path><path d="M305 233h1l1 2h-1c1 1 1 1 1 2h-1c0-1 0-1-1-2v-2z" class="AJ"></path><path d="M306 229l1-1v5h0-1-1 0c-1-1-1-1-1-2h1l1-1v-1z" class="AR"></path><path d="M307 233c2-1 0-4 2-6h1v3c0 1 0 1 1 1h1l-1-3h0c1 0 1 1 1 2 1 2 0 4 1 5l2 1v1h-1-1v-1c0-1-1-2-1-3v-1h0-1-1v3h-1v3 1-1h-2v-1c0-1 0-1-1-2h1l-1-2h1z" class="z"></path><path d="M307 235h0 0l2 2v1 1-1h-2v-1c0-1 0-1-1-2h1z" class="AO"></path><path d="M314 220v-3h1c0 1 0 2 1 3h0l1-1c0 1 1 2 2 3 0 1 0 2 1 4v1h-1v-1 2l-1 1h0l-1 2 1 1v2h0c-2 1-2 1-3 3v-1l-2-1c-1-1 0-3-1-5l2-1c-1-1-1-1-1-2l1-1h0c0 1 0 2 1 2 0-1 0-2 1-3v-1l-1-1-1-1c1-1 1-1 0-2z" class="AP"></path><path d="M318 229l-1-1c0-1 0-2 1-2h1v2l-1 1z" class="AQ"></path><path d="M314 229c1 0 0 1 1 2 1 0 1-1 1-2 1 1 1 1 1 2l1 1v2h0c-2 1-2 1-3 3v-1l-2-1c-1-1 0-3-1-5l2-1z" class="AC"></path><path d="M315 231c1 0 1-1 1-2 1 1 1 1 1 2l1 1-1 1-1 1-1-1 1-1-1-1z" class="AT"></path><path d="M289 217l1 1v-1h2c1 1 2 1 3 0 1 0 1 0 2 1 0-1 1-1 2 0h1c1 1 1 2 0 3h2 0l1 1v2h-1v1h0c1 1 1 1 1 2v1l-1 1 1 1c0 1 0 2-1 4 0 0 1 1 0 1v1l-1 1-3 3-1-2c0 1 0 1-1 2h-1l-1 1v-1-1-2h1v-1c-1-1-1-2-1-4h0 0c-1-1-2-1-3-1h0v-1l-2-2h1c0-2 0-3 1-4v-2l1-1h0c0-1 0-2-1-3h0l-1 1c-1 0-1-1-1-2z" class="AH"></path><path d="M297 218c0-1 1-1 2 0h1c1 1 1 2 0 3h2c0 1-1 2-1 2 0 1 0 1-1 1h-1c0-1-1-2-1-3s0-1-1-1v-2z" class="AY"></path><path d="M299 218h1c1 1 1 2 0 3h0-1v-3z" class="x"></path><path d="M289 228h1c0-2 0-3 1-4v4l1-1v2h1l1-2h1v1c0 2-1 2-1 4h0c-1-1-2-1-3-1h0v-1l-2-2z" class="AI"></path><path d="M294 227h0c1-1 1-1 1-2h1c1 0 2 1 3 2l-1 1v1c-1 1-1 1-1 2s-1 1-1 3l-1-1-1-1h0c0-2 1-2 1-4v-1h-1z" class="AJ"></path><path d="M299 227l1-1v2l2 1 1 1c0 1 0 2-1 4 0 0 1 1 0 1v1l-1 1-3 3-1-2c0 1 0 1-1 2h-1l-1 1v-1-1-2h1v-1c-1-1-1-2-1-4l1 1 1 1c0-2 1-2 1-3s0-1 1-2v-1l1-1z" class="z"></path><path d="M298 228h1c0 4-1 7-2 10 0 1 0 1-1 2h-1l-1 1v-1-1-2h1v-1c-1-1-1-2-1-4l1 1 1 1c0-2 1-2 1-3s0-1 1-2v-1z" class="AK"></path><path d="M295 236s0-1 1-1v-1h1c0 1 0 2-1 4l-1-1v-1z" class="AC"></path><path d="M294 232l1 1 1 1c0-2 1-2 1-3s0-1 1-2c0 2 0 3-1 5h-1v1c-1 0-1 1-1 1-1-1-1-2-1-4z" class="AP"></path><path d="M289 305v1c1 0 1-1 2-2l1 1-1 1h1l1-1 2 2c0 1 0 2-1 3l1 1v2c0 1 0 1-1 2h0v1c1 1 2 0 1 3 1 1 1 2 2 2 0 1 0 1 1 1v-2c-1-1 0-2 0-3v-3h-1 0c-1-1-1-1-1-2v-2c0-1 0-1 1-2v-1h1 1l-1 3c-1 0-1 0-1 1l1 1-1 1 1 1 2-2h1c0-1 1-2 1-3 1 0 1 1 2 2h1c1-1 1-1 2-1v1c1 0 2-1 2-1l3-3h1l-1 2c1 0 1-1 2 0l-1 1 2 2v1l1 1 2 1v1l-1 1c1 1 1 0 1 1v2h-1c1 1 1 1 2 1h0 0c0-1 1-1 1-2v3l-1 1c0 1 0 1-1 1v1l1 1h-2v1l1 1c-1 0-2 0-2 1h-1c-1 0-2 1-3 2v1c-1-1-2-1-2-1l-2 1c0-1 0-1-1-2-1 1-2 2-2 3l-1 1c1 1 1 1 1 2-1 0-1 0-2 1l-1 1c0-2 0-2 1-3h1v-1h-1-2l-1-1h-2c0 1-1 1-2 2h0 0-4l-1-1h-3c-1 0-1-1-2-1l-1-1 1-1v-1c-1 0-1-1-1-2-1-1-2-1-4-1h0v-1h2v-1l2-1c1 1 2 1 2 1v-2h2v-1h-2 0l2-2c1 0 1-1 2-2h2 0c0-3-1-1-2-3 2 0 3-1 3-2v-1c-1 1-1 2-3 2v-1c1 0 1 0 2-1-1 0-1 0-1-1h1v-1h-1v-2h-1c-1 1-2 2-3 2l-1-1h1c1 0 2-1 2-2-1 0-2 0-3 1v-1c1 0 1-1 2-2z" class="AB"></path><path d="M313 319h4l-1 1c0 1 0 1 1 1-2 2-3 1-5 1 0 1-1 1-1 1h-1c0 1-1 1-2 1l-2-1c1-1 1-1 2-1h0 2c1 0 1 0 2-1s1-1 1-2z" class="J"></path><path d="M298 325c1-1 1-1 1-2v-1c1 0 2 0 3 1 0 1 0 2 1 3h-1c0 2 2 3 1 4l-2-3h-2c0-1 0-1-1-1h0v-1z" class="I"></path><path d="M308 332v-1c0-2 0-3 2-4 1-2 2-2 5-2h0c-1 0-1 0-2 1 0 1-1 4-1 5v1c-1-1-2-1-2-1l-2 1z" class="AJ"></path><path d="M310 331l1-3 2-2c0 1-1 4-1 5v1c-1-1-2-1-2-1z" class="J"></path><path d="M296 323c1 1 2 1 2 2v1h0c1 0 1 0 1 1h2l2 3v1 1c-1 0-1 0-2 1l-1-1c-1-1-1-2-2-2h0c0-1 0-1 1-1h0v-1h-2l1-1v-1c-1 0-2-1-2-1v-2z" class="AF"></path><path d="M293 318l1-1 1 1-2 2h0l2 1-1 1s-1 1-2 1h-1v1h-2v-1-1h-2 0l2-2c1 0 1-1 2-2h2z" class="z"></path><path d="M293 320l2 1-1 1s-1 1-2 1h-1v-1c1 0 2-1 2-2z" class="AU"></path><path d="M301 319c1-1 2 0 4 0h5 3c0 1 0 1-1 2s-1 1-2 1h-2 0c-1 0-1 0-2 1h0c-1 0-1 0-1-1-1 0-3 0-3-1-1 0-2 0-2 1l-1-1 1-1h0s1 0 1-1h0z" class="s"></path><path d="M311 312h1c0-1 0-1 1-2l2 2v1l1 1c-1 1-2 2-2 3 1 1 2 1 3 1v1h0-4-3-5c-2 0-3-1-4 0h-2 0c1-1 2-2 3-2v-1l1 1h2c0 1 0 1 1 1h1v-1h-1 2 0v-1h1l-1-1h0 1l1-1 1-2z" class="AC"></path><path d="M309 315l1-1h3c0 1-1 2-2 3-1-1-2-1-2-2z" class="AH"></path><path d="M309 310l3-3h1l-1 2c1 0 1-1 2 0l-1 1c-1 1-1 1-1 2h-1l-1 2-1 1h-1 0l1 1h-1v1h0-2 1v1h-1c-1 0-1 0-1-1h-2l-1-1h0-1-1c0-1 2-2 2-3-1 0-1 1-3 2 0-1 1-2 2-3 0-1 1-2 1-3 1 0 1 1 2 2h1c1-1 1-1 2-1v1c1 0 2-1 2-1z" class="z"></path><path d="M309 310l3-3h1l-1 2c1 0 1-1 2 0l-1 1c-1 1-1 1-1 2h-1l-1 2-1 1h-1 0-2v-1c1 0 1 0 2-1 0-1 0-1-1-1 0 0 0 1-1 1 0-1 0-1-1-2 1-1 1-1 2-1v1c1 0 2-1 2-1z" class="AH"></path><path d="M308 315v-1c1 0 1-1 1-2h2l-1 2-1 1h-1z" class="AU"></path><path d="M291 323h1c1 0 2-1 2-1 1 0 2 1 2 1v2s1 1 2 1v1l-1 1h2v1h0c-1 0-1 0-1 1h0c1 0 1 1 2 2l1 1h-1-2c0 1-1 1-2 2h0 0-4l-1-1h-3c-1 0-1-1-2-1l-1-1 1-1v-1c-1 0-1-1-1-2-1-1-2-1-4-1h0v-1h2v-1l2-1c1 1 2 1 2 1v-2h2v1h2v-1z" class="AM"></path><path d="M285 324c1 1 2 1 2 1 2 0 2 0 4 1l-2 1h3v1h-1c0 1 0 1 1 2h-3v1h-2v1l-1-1v-1c-1 0-1-1-1-2-1-1-2-1-4-1h0v-1h2v-1l2-1z" class="y"></path><path d="M285 324c1 1 2 1 2 1 2 0 2 0 4 1l-2 1h-2c-1-1-2-1-4-1v-1l2-1z" class="J"></path><path d="M293 331h0l1-1c1 0 1 0 2-1h1l-1-1h0 1 2v1h0c-1 0-1 0-1 1h0c1 0 1 1 2 2l1 1h-1-2c0 1-1 1-2 2h0 0-4l-1-1h-3c-1 0-1-1-2-1l-1-1 1-1 1 1v-1h2 4z" class="z"></path><path d="M286 331l1 1v-1h2 4 1v1c-1 0-2 0-2 1h2v1l2 1h0-4l-1-1h-3c-1 0-1-1-2-1l-1-1 1-1z" class="AE"></path><path d="M288 334l1-2c2 1 2 1 2 2h-3z" class="AF"></path><path d="M244 273h3c1 1 6 2 7 2h0c-1-1-3-2-4-3v-1c2 0 4 2 5 3h0v1h-3l-1 1 2 1 2 2v1c2 0 4 1 5 3l1 1 1-1v-2c0 1 0 1 1 2 1 3 3 5 6 7l5 1v2 1h0c0 1-1 1-1 1v1h1 0c1 1 1 2 2 2s2-1 3-1l1 1h0c1 1 2 0 3 0v-1h1v1h1v-1l1 1v3c1 1 1 0 2 1h0c-1 1-1 1-1 2l2-1h0v2c-1 1-1 2-2 2v1c1-1 2-1 3-1 0 1-1 2-2 2h-1l1 1c1 0 2-1 3-2h1v2h1v1h-1c0 1 0 1 1 1-1 1-1 1-2 1v1c2 0 2-1 3-2v1c0 1-1 2-3 2 1 2 2 0 2 3h0-2c-1 1-1 2-2 2l-2 2h0 2v1h-2v2s-1 0-2-1l-2 1h-9c-18 0-38 7-54 16l-14 8c-2 1-4 2-7 4-3 1-6 3-9 4l-1-1v-6l1-5c-1-2-1-3-1-5l1-2s1 1 2 1v-1-3h0v-1c0-1-1-1-2-1v-2-5-1-4h1-1l8-4 6-5 1-1c1-2 2-2 4-3 3-3 6-6 8-10l3-5h1l3-2h0c2-2 3-3 5-4v-1l-5 3v-1l3-4-1 1-1-1 1-1c-1 0-1-1-2-2 1-1 1-2 2-3h-2c0-2 2-3 3-4 1 1 2 0 3 1h1v1c1 0 1 0 2-1 1 0 1 0 2 1l1-2h0l1-1h1c2-1 4 0 6 0z" class="C"></path><path d="M256 300l4 1v2c-1-1-1-1-2-1l-2-2z" class="AB"></path><path d="M264 304s1 1 2 1l-1 2c-1 0-2-1-2-2l1-1z" class="s"></path><path d="M266 310c1 1 2 1 3 2h1c1 0 1 0 2 1h0s-1 0-2 1h0l-5-1h-1l-2-1v-1h0c1 1 2 1 3 1h1 1 0l-1-1v-1z" class="i"></path><path d="M245 293c2 0 3 2 4 3s3 2 4 2c2 1 5 1 7 1h1v1l1 1h-2l-4-1c-4-1-8-3-11-7z" class="AJ"></path><path d="M264 313h1l5 1h0c1-1 2-1 2-1 3 0 5 1 8 1l2 1-1 1v1h-1l-5-1-11-3z" class="AD"></path><path d="M272 313c3 0 5 1 8 1l2 1-1 1c-2-1-4-1-5-1-2 0-4-1-6-1 1-1 2-1 2-1zm-27-20s-1 0-1-1l1-1c1 1 1 2 2 2l3 1c1 1 1 1 2 1h0c1 1 3 1 4 1 2 1 3 1 4 2v1c-2 0-5 0-7-1-1 0-3-1-4-2s-2-3-4-3z" class="B"></path><path d="M219 298v1c3-3 6-6 9-8h1c-5 4-10 9-14 14h0c-1 1-2 1-2 2l-3 3-1-2c3-3 6-6 8-10h2z" class="AQ"></path><path d="M215 305h1 0c1-2 3-4 5-5h0c3-3 6-5 9-7-2 3-4 4-6 6-3 4-5 8-9 11v-2h0l-3 3s-3 2-4 2h-1c0 1 0 0-1 1v-1c-1 0-2 0-2-1l1-1c1-2 2-2 4-3l1 2 3-3c0-1 1-1 2-2z" class="AH"></path><path d="M205 311c1-2 2-2 4-3l1 2h0-2l-1 1h-2z" class="AU"></path><path d="M233 285l1 2c1 0 1 0 2-1h1 7v1h-1c-1 0-2 0-3 1-1 0-1 1-3 1-1 0-3 0-4 1l-4 1h-1c-3 2-6 5-9 8v-1h-2l3-5h1l3-2h0c2-2 3-3 5-4 1 0 2-1 4-2z" class="w"></path><path d="M237 286h7v1h-1c-1 0-5 1-6 0v-1z" class="AI"></path><path d="M224 291h1c2-1 3-3 5-4 0 1 0 2-1 2-2 2-5 4-7 6-1 1-2 3-3 3h-2l3-5h1l3-2h0z" class="AN"></path><path d="M215 310h0c3-2 5-5 7-7 1-2 6-7 8-7 0 1-8 9-10 11h0c0 1-1 2-2 3h1l2-2h1c-1 1-4 3-4 5l-2 2c-1 2-2 3-4 4v-1-3h0c-1 0-1 0-2 1v1 1l-1-1-1 1h1l1 1h-1-1-1 0-1v-2h-1v-1h-1v-1c1-1 1-1 2-1 1-1 1 0 1-1h1c1 0 4-2 4-2l3-3h0v2z" class="AU"></path><path d="M220 307h0c0 1-1 2-2 3h1l2-2h1c-1 1-4 3-4 5l-2 2c-1 2-2 3-4 4v-1-3h0c-1 0-1 0-2 1h-1v-1l4-2c3-2 5-4 7-6z" class="i"></path><path d="M212 315h1v-1c1 0 2-1 3-2h0 1l1 1-2 2c-1 2-2 3-4 4v-1-3z" class="B"></path><path d="M222 308h1c-1 2-2 3-3 4h0 1c2-3 5-5 7-7h1c1-1 2-1 2-1 1-1 2-1 3-1h1 0v1h-2c0 1 0 1-1 1s-3 1-4 2c-2 1-3 3-4 4s-2 1-2 2c-1 2-1 3-2 4s-2 2-2 3l-1 1v1h0c2-1 4-3 5-5l1 1c-1 1-2 2-2 3-2 2-5 4-7 6l-1 1-1-1h-1v-1-3c1-1 3-2 3-3h-1l-4 2-1 1h-1c1-1 1-2 2-2l1-1s1-1 2-1h0c2-1 3-2 4-4l2-2c0-2 3-4 4-5z" class="y"></path><path d="M210 316c1-1 1-1 2-1h0v3 1h0c-1 0-2 1-2 1l-1 1c-1 0-1 1-2 2h1l1-1 4-2h1c0 1-2 2-3 3v3 1h1l1 1c-1 1-1 1-2 1h-1c0 1 0 1-1 2v1h-3-2c-1-1-1-1-1-2-1 0-1 1-2 1s0 0-1-1h-1v-2h-1l1-1c-1 0-1 0-1-1s1 0 2 0v-2c1-1 3-2 5-3 1 0 2-1 4-2h1l-1-1h-1l1-1 1 1v-1-1z" class="J"></path><path d="M204 325c1 0 2-1 2-1h1v1l-3 3v-2h0v-1z" class="O"></path><path d="M204 325v1h0v2h-1-1-3-1l1-1 2-1 3-1z" class="b"></path><path d="M207 325c1 0 1-1 2-1h1v1c-1 1-2 2-4 2v1s-1 1-2 1c0 0-1 0-1 1-1 0-1 1-2 1s0 0-1-1h-1v-2h3 1 1l3-3z" class="AK"></path><path d="M210 325h0c0 1 0 3 1 4h-1c0 1 0 1-1 2v1h-3-2c-1-1-1-1-1-2s1-1 1-1c1 0 2-1 2-1v-1c2 0 3-1 4-2z" class="c"></path><path d="M210 325h0c0 1 0 3 1 4h-1v-1h-2l-2 2-2-1c1 0 2-1 2-1v-1c2 0 3-1 4-2z" class="B"></path><path d="M198 317l6-5c0 1 1 1 2 1v1c-1 0-1 0-2 1v1h1v1h1v2h1 0 1 1c-2 1-3 2-4 2-2 1-4 2-5 3v2c-1 0-2-1-2 0s0 1 1 1l-1 1-8-2v-1-4h1-1l8-4z" class="AT"></path><path d="M190 325c3 0 6 0 10-1v2c-1 0-2-1-2 0s0 1 1 1l-1 1-8-2v-1z" class="i"></path><path d="M198 317h0 2c1-1 2-1 3-1l1 1h-3v1c-1 1-1 1-2 1 0 0 0 1-1 1h-1-1-1c-1 1-2 0-4 1h-1l8-4z" class="AN"></path><path d="M222 317l6-6c3-1 6-3 9-4 2 0 5 1 8 1-6 1-10 2-14 6h0c1 0 1 1 2 0h1v1c-1 1-2 1-3 2v1l-1 1h1s1 0 1-1c1 0 2-1 3-1h1 0l-4 3c0 1 1 0 0 1l-2 1h0l-2 2c-1 0-1 1-2 1l-1 1-1 1c0 1 0 0-1 1l-1 1h-1l-2 1c0 1-2 2-3 2v-1l1-1h-1v-1c2 0 2-2 4-3l2-1v-1c1 0 1 0 1-1l1-1h0c-1 0-2 1-3 1v1h-1c-2 2-3 4-5 5h0c-1 1-2 1-3 2h-1c-1 0-1 1-2 1h-3 3v-1c1-1 1-1 1-2h1c1 0 1 0 2-1l1-1c2-2 5-4 7-6 0-1 1-2 2-3l-1-1z" class="AB"></path><path d="M224 322l6-6v1c0 1 0 1-1 2h0l-1 1v1h0v1h2l-2 2c-1 0-1 1-2 1l-1 1-1 1c0 1 0 0-1 1l-1 1h-1l-2 1c0 1-2 2-3 2v-1l1-1h-1v-1c2 0 2-2 4-3l2-1v-1c1 0 1 0 1-1l1-1h0z" class="O"></path><path d="M222 317l6-6c3-1 6-3 9-4 2 0 5 1 8 1-6 1-10 2-14 6l-1 1c-2 1-3 3-5 4l-2 2h-1 0c1-2 4-5 6-6l2-2c2-1 3-2 5-3v-1l-1 1c-1 1-3 1-5 2-1 1-3 2-4 4l-2 2-1-1z" class="AD"></path><path d="M262 281c0 1 0 1 1 2 1 3 3 5 6 7l5 1v2 1h0c0 1-1 1-1 1v1h1c-4 0-7 3-10 2l-3 1h-1v-1c-1-1-2-1-4-2-1 0-3 0-4-1h0c-1 0-1 0-2-1l-3-1c-1 0-1-1-2-2l2-1h2c0 1 1 1 2 1 1-1 1-1 2-1v-1c-3-1-5-2-9-2v-1c2 0 3 0 4 1 3 1 7 1 9 3h3c1 1 3 1 4 2 0 1 1 1 1 1h1v-1c-1 0-1 0-1-1h-1c0-1-1-2-2-3h0c-1-1-1-2-1-3h-1l1-1 1-1v-2z" class="J"></path><path d="M269 290l5 1v2h-3l-2-1c-1 0-1 0-2-1 1 0 1-1 2-1z" class="i"></path><path d="M262 281c0 1 0 1 1 2 1 3 3 5 6 7-1 0-1 1-2 1s-1 0-2-1c-1 0-1-2-2-3l-2-2h-1l1-1 1-1v-2z" class="AB"></path><path d="M244 286c2 0 3 0 4 1 3 1 7 1 9 3h3c1 1 3 1 4 2 0 1 1 1 1 1l1 1 1 1h0c-1 0-2 1-2 1-2 0-5 0-6-1h-1l-1-1c-1 0-7-2-7-3 1 0 1 0 2 1h2 0c1-1 1-1 2-1l-3-2c-3-1-5-2-9-2v-1z" class="AU"></path><path d="M257 290h3c1 1 3 1 4 2 0 1 1 1 1 1l1 1c-1 0-2 1-2 0-1 0-2-1-2-1-1 0-1 1-1 1-1 0-5-1-6-1v-1h2s1 0 1-1l-1-1z" class="AF"></path><path d="M224 322h0l-1 1c0 1 0 1-1 1v1l-2 1c-2 1-2 3-4 3v1h1l-1 1v1c1 0 3-1 3-2l2-1h1v2l-8 5c-1 0-3 1-4 2h1c1 0 1-1 2-1h0c1-1 2-1 2-1h0c1-1 1-1 2-1h0 1v-1h1 1c-1 1-2 1-3 2l-4 2v1c-1 1-4 2-6 3l-3 1c-1 0-2 1-2 1-3 2-5 4-9 5-1 1-2 1-4 1l1-5c-1-2-1-3-1-5l1-2s1 1 2 1v-1-3h0v-1c0-1-1-1-2-1v-2-5l8 2h1v2h1c1 1 0 1 1 1s1-1 2-1c0 1 0 1 1 2h2 3c1 0 1-1 2-1h1c1-1 2-1 3-2h0c2-1 3-3 5-5h1v-1c1 0 2-1 3-1z" class="B"></path><path d="M203 340h1-1v1h0s-2 0-2 1h-3 0v-1h-5c2-1 3-1 4-1l1 1c1 0 2 0 3-1h2z" class="I"></path><path d="M189 340l4 1h5v1h0 3v1h-1c-1 1-5 2-6 3h3 0c1 0 2-1 2-1 1-1 1-1 3-1-3 2-5 4-9 5-1 1-2 1-4 1l1-5c-1-2-1-3-1-5z" class="w"></path><path d="M189 340l4 1h5v1h0c-1 1-1 1-2 1-2 1-4 2-6 2-1-2-1-3-1-5z" class="m"></path><path d="M224 322h0l-1 1c0 1 0 1-1 1v1l-2 1c-2 1-2 3-4 3v1c-1 0-2 1-3 2-1 0-2 0-2 1-5 1-10 2-14 1h-1v1h2c1 1 3 1 4 1 1 1 5 0 7 0h0c1-1 3-2 4-2h1 1 0l-6 3c-1 0-2 1-3 2-1 0-3 1-3 1h-2c-1 1-2 1-3 1l-1-1c-1 0-2 0-4 1l-4-1 1-2s1 1 2 1v-1-3h0v-1c0-1-1-1-2-1v-2-5l8 2h1v2h1c1 1 0 1 1 1s1-1 2-1c0 1 0 1 1 2h2 3c1 0 1-1 2-1h1c1-1 2-1 3-2h0c2-1 3-3 5-5h1v-1c1 0 2-1 3-1z" class="w"></path><path d="M194 331h0 3l1 1c-1 1-1 1-3 1 0-1 0-1-1-2z" class="b"></path><path d="M203 330c0 1 0 1 1 2h-4l1-1c1 0 1-1 2-1z" class="B"></path><path d="M190 331h3c0 2 0 2 2 3h-3c0-1-1-1-2-1v-2z" class="k"></path><path d="M190 326l8 2h1v2c-2-1-4-1-6-2 1 2 3 2 4 3h-3 0-1-3v-5z" class="AC"></path><path d="M192 335c1 0 1 0 2 1h1c1 0 1 1 2 1h1c1 1 1 2 2 2h1v1c-1 1-2 1-3 1l-1-1c-1 0-2 0-4 1l-4-1 1-2s1 1 2 1v-1-3z" class="O"></path><path d="M192 338l1 1h2c1 0 1 1 2 1h0c-1 0-2 0-4 1l-4-1 1-2s1 1 2 1v-1z" class="AQ"></path><path d="M244 273h3c1 1 6 2 7 2h0c-1-1-3-2-4-3v-1c2 0 4 2 5 3h0v1h-3l-1 1 2 1 2 2v1c2 0 4 1 5 3l1 1-1 1h1c0 1 0 2 1 3h0c1 1 2 2 2 3h1c0 1 0 1 1 1v1h-1s-1 0-1-1c-1-1-3-1-4-2h-3c-2-2-6-2-9-3-1-1-2-1-4-1h-7-1c-1 1-1 1-2 1l-1-2c-2 1-3 2-4 2v-1l-5 3v-1l3-4-1 1-1-1 1-1c-1 0-1-1-2-2 1-1 1-2 2-3h-2c0-2 2-3 3-4 1 1 2 0 3 1h1v1c1 0 1 0 2-1 1 0 1 0 2 1l1-2h0l1-1h1c2-1 4 0 6 0z" class="x"></path><path d="M235 281c1 1 4 0 5 0h1c-2 0-3 0-4 2h-1c-1 0-2 0-3 1 0 0-2-1-2-2l4-1z" class="J"></path><path d="M255 280c2 0 4 1 5 3l1 1-1 1h1c0 1 0 2 1 3h0c1 1 2 2 2 3l-2-1h0c-1-2-1-3-2-4s-2-3-3-5c-1 0-1 0-2-1z" class="s"></path><path d="M227 284c1 0 2-1 4-2 0 1 2 2 2 2l-2 1c-1 0-2 0-2 1l-5 3v-1l3-4z" class="w"></path><path d="M233 285h9 4 0l1 1h0 2 1c1 1 0 1 1 1h1 1c0 1 1 1 1 1h1l3 1c1 0 1 1 2 1h-3c-2-2-6-2-9-3-1-1-2-1-4-1h-7-1c-1 1-1 1-2 1l-1-2z" class="B"></path><path d="M241 277c1-1 2-1 3 0l1 1 1 2c-1-1-2-1-3-1v1c1 1 2 0 3 2h0-2c-1-1-2-1-3-1h-1c-1-1-2-2-3-2s-1 0-2-1h2c2-1 3-1 4-1z" class="AU"></path><path d="M245 278c2 0 4 0 5 1h1 2 0c0 1 1 1 2 1v2l1 1h-1c0 1 0 1 1 2h0c1 0 1 0 2 1h0c-1 0-2-1-3-1-1-1-3-1-4-2-1 0-2 0-3-1h2l1-1h-4 0c0-1 0-1-1-1l-1-2z" class="AN"></path><path d="M241 276v1c-1 0-2 0-4 1h-2c1 1 1 1 2 1s2 1 3 2c-1 0-4 1-5 0l-4 1c-2 1-3 2-4 2l-1 1-1-1 1-1c1-3 4-4 8-5l7-2z" class="AP"></path><g class="AM"><path d="M235 281c-1 0-1-1-1-1h-2v-1h3v-1c1 1 1 1 2 1s2 1 3 2c-1 0-4 1-5 0z"></path><path d="M236 274h2c2 1 3 0 5 1-1 0-1 1-2 1l-7 2c-4 1-7 2-8 5-1 0-1-1-2-2 1-1 1-2 2-3h-2c0-2 2-3 3-4 1 1 2 0 3 1h1v1c1 0 1 0 2-1 1 0 1 0 2 1l1-2z"></path></g><path d="M224 281c1-1 1-2 2-3h-2c0-2 2-3 3-4 1 1 2 0 3 1-1 1-2 1-3 2v1l1 1c1 0 1 0 2-1l2-1h2v1c-4 1-7 2-8 5-1 0-1-1-2-2z" class="AY"></path><path d="M244 273h3c1 1 6 2 7 2h0c-1-1-3-2-4-3v-1c2 0 4 2 5 3h0v1h-3l-1 1 2 1v2h-2-1c-1-1-3-1-5-1l-1-1c-1-1-2-1-3 0v-1c1 0 1-1 2-1-2-1-3 0-5-1h-2 0l1-1h1c2-1 4 0 6 0z" class="s"></path><path d="M243 275c2 0 3 0 5 1h1 1 1l2 1v2h-2-1c-1-1-3-1-5-1l-1-1c-1-1-2-1-3 0v-1c1 0 1-1 2-1z" class="AL"></path><path d="M250 276h1l2 1v2h-2-1c1-1 0-2 0-3z" class="AD"></path><path d="M261 299l3-1c3 1 6-2 10-2h0c1 1 1 2 2 2s2-1 3-1l1 1h0c1 1 2 0 3 0v-1h1v1h1v-1l1 1v3c1 1 1 0 2 1h0c-1 1-1 1-1 2l2-1h0v2c-1 1-1 2-2 2v1c1-1 2-1 3-1 0 1-1 2-2 2h-1l1 1c1 0 2-1 3-2h1v2h1v1h-1c0 1 0 1 1 1-1 1-1 1-2 1v1c2 0 2-1 3-2v1c0 1-1 2-3 2 1 2 2 0 2 3h0-2c-1 1-1 2-2 2h-4 0l2-1v-1h-2c1-1 2-2 3-2v-1l-1 1h-3 0s-1 1-1 2h-2 0c-1 0-1 0-1-1h1v-1l1-1-2-1c-3 0-5-1-8-1h0c-1-1-1-1-2-1h-1c-1-1-2-1-3-2l-4-2c2 1 5 1 7 2 1 0 2 0 3-1h-1-1s-1 0-1-1l-3-1h-1l1-2c-1 0-2-1-2-1h-2c-1-1-1-1-2-1v-2h2l-1-1v-1z" class="AJ"></path><path d="M282 315l5-1h0l-3 2h0s-1 1-1 2h-2 0c-1 0-1 0-1-1h1v-1l1-1z" class="w"></path><path d="M284 298h1v-1l1 1v3c1 1 1 0 2 1h0c-1 1-1 1-1 2s-2 3-4 3c1-1 4-3 4-4h-1 0c-1 1-2 2-4 3v-1h0 1c1-1 2-3 2-4l-2-1v-1l1-1z" class="AF"></path><path d="M271 311h5c4 1 6 3 9 1h1c0 1-1 1-1 1l-4 1h-1c-3 0-5-1-8-1h0c-1-1-1-1-2-1l1-1z" class="w"></path><path d="M266 310l-4-2c2 1 5 1 7 2 1 0 2 0 3-1h-1-1c3 0 5 0 7 1h3c1 1 2 1 2 1l1 1c-2 0-5-1-6-2-2 0-4 0-5 1h-1l-1 1h-1c-1-1-2-1-3-2z" class="y"></path><path d="M283 297h1v1l-1 1v1l2 1c0 1-1 3-2 4h-1 0v1c-1 0-2 0-3 1h-2c-2 2-5 1-7 1v-1h0c0-1 1-1 2-1l-2-1c1 0 2-1 3-1h1 1c1 0 1-1 2-1s1-1 1-1c1 0 2 0 2-1l1-1h0l1-1h1v-2z" class="AI"></path><g class="y"><path d="M272 306l1-1h1 2c1 0 1-1 2-1v1l-1 1h0c1 0 2 0 3-1h2v1c-1 0-2 0-3 1h-2c-2 2-5 1-7 1v-1h0c0-1 1-1 2-1z"></path><path d="M261 299l3-1c3 1 6-2 10-2h0c1 1 1 2 2 2s2-1 3-1l1 1h0c1 1 2 0 3 0v-1 2h-1l-1 1h0l-1 1c0 1-1 1-2 1 0 0 0 1-1 1s-1 1-2 1h-1-1c-1 0-2 1-3 1l2 1c-1 0-2 0-2 1h0v1 1s-1 0-1-1l-3-1h-1l1-2c-1 0-2-1-2-1h-2c-1-1-1-1-2-1v-2h2l-1-1v-1z"></path></g><path d="M260 301h2 2l1 2v1c-1 0-2-1-3-1v1c-1-1-1-1-2-1v-2z" class="s"></path><path d="M266 307v-2h4l2 1c-1 0-2 0-2 1h0v1 1s-1 0-1-1l-3-1z" class="AG"></path><path d="M261 299l3-1c3 1 6-2 10-2-1 2-3 2-5 2h0v1c1 0 3 0 4 1-2 0-5 1-7 1h-2-2l-1-1v-1z" class="AC"></path><path d="M284 316h3l1-1v1c-1 0-2 1-3 2h2v1l-2 1h0 4l-2 2h0 2v1h-2v2s-1 0-2-1l-2 1h-9c-18 0-38 7-54 16l-14 8c-2 1-4 2-7 4-3 1-6 3-9 4l-1-1v-6c2 0 3 0 4-1 4-1 6-3 9-5 0 0 1-1 2-1l3-1c2-1 5-2 6-3v-1l4-2c1-1 2-1 3-2l8-5h0c4-2 8-5 13-7h2l9-3h5c4-1 8-1 12-1h4v-1c1 0 1 0 1-1h1l5 1c0 1 0 1 1 1h0 2c0-1 1-2 1-2h0z" class="C"></path><path d="M285 322v1h2 0v2s-1 0-2-1h-4l3-1 1-1z" class="AF"></path><path d="M274 323l7 1h4l-2 1h-9v-2z" class="AO"></path><path d="M189 356c5-3 11-5 16-8l13-7c18-10 36-17 56-18v2c-18 0-38 7-54 16l-14 8c-2 1-4 2-7 4-3 1-6 3-9 4l-1-1z" class="AJ"></path><path d="M284 316h3l1-1v1c-1 0-2 1-3 2h2v1l-2 1h0 4l-2 2h0 2v1h-2 0-2v-1l-1-1c-3 0-5-1-8-1-8 0-16-1-24 1h-2-1l-4 1-6 3c-1 0-2 0-3 1h-1c-3 2-7 4-10 6l-10 6c-1 0-1 1-2 1v-1l4-2c1-1 2-1 3-2l8-5h0c4-2 8-5 13-7h2l9-3h5c4-1 8-1 12-1h4v-1c1 0 1 0 1-1h1l5 1c0 1 0 1 1 1h0 2c0-1 1-2 1-2h0z" class="AG"></path><path d="M275 316l5 1c0 1 0 1 1 1-2 1-3 1-5 1-2-1-4 0-7-1h4v-1c1 0 1 0 1-1h1z" class="B"></path></svg>