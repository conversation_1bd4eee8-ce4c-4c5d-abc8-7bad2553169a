<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="55 49 502 627"><!--oldViewBox="0 0 592 752"--><style>.B{fill:#d5d4d5}.C{fill:#201f20}.D{fill:#f6f5f6}.E{fill:#dcdbdc}.F{fill:#e4e3e4}.G{fill:#cdcccc}.H{fill:#d6d4d5}.I{fill:#303030}.J{fill:#4f4e4f}.K{fill:#868485}.L{fill:#2b2b2b}.M{fill:#262527}.N{fill:#8e8d8d}.O{fill:#242322}.P{fill:#060506}.Q{fill:#797879}.R{fill:#434343}.S{fill:#b8b6b7}.T{fill:#afaeae}.U{fill:#a3a2a2}.V{fill:#cac8c9}.W{fill:#f0eeef}.X{fill:#0e0e0e}.Y{fill:#e9e8e9}.Z{fill:#6b6a6a}.a{fill:#3c3c3c}.b{fill:#1a1919}.c{fill:#121212}.d{fill:#a9a7a9}.e{fill:#383738}.f{fill:#9c9a9a}.g{fill:#969596}.h{fill:#333233}.i{fill:#ecebec}.j{fill:#181718}.k{fill:#5b5a5b}.l{fill:#f2f0f2}</style><path d="M435 471l1-1v3h-1-1c1-1 1-2 1-2z" class="P"></path><path d="M427 523h1v1l-1 1h-1v-2h1z" class="X"></path><path d="M440 386l-1-1v-1c1 0 1 0 2-1h2l-1 1v1c-1 0-1 0-2 1z" class="C"></path><path d="M366 568l1-1c1 0 1 1 2 2h0-4l1-1z" class="J"></path><path d="M367 475h1c0 1 0 2-1 3h0-1c0-2 0-2 1-3z" class="N"></path><path d="M365 456c0-1 0-2 1-2h0c1 1 1 2 0 3h0l-1-1z" class="S"></path><path d="M427 455l1 1c1 1 1 2 0 3-1-1-1-2-1-4z" class="G"></path><path d="M434 665h1c1-1 2-1 3-1v2h-1c-1 0-2 0-3-1z" class="O"></path><path d="M385 569h0c-1-1-1-2-1-3h0 1v2c1 1 3 2 2 3l-2-2z" class="c"></path><path d="M442 490h1v2 2l-1-1c-1 0-1-1-1-2l1-1z" class="U"></path><path d="M397 367h1 1c0 2 0 2-1 3h-1v-3z" class="K"></path><path d="M480 483h2v3c-1-1-2-1-2-2v-1z" class="U"></path><path d="M373 499h0 1c1 1 1 3 1 4-1 0-2-1-2-2-1-1 0-1 0-2z" class="K"></path><path d="M386 681h2c1 1 1 2 2 3-1 0-1 0-2-1l-2-2z" class="Z"></path><path d="M360 504v-1-3c1 0 1 0 2 1v2c-1 1-1 1-2 1z" class="f"></path><path d="M475 414c1 0 0 0 1 1 0 1 0 2 1 2v1h-1l-2-1 1-3z" class="K"></path><path d="M346 453h1l1 1-1 1c-1 1-2 1-3 1 0-1 1-2 2-3z" class="g"></path><path d="M368 458v-1-4h1c1 1 1 2 1 3l-1 2h-1z" class="T"></path><path d="M357 403h2c0 1-1 1-1 2l-2 2h0c0-2-1-2 1-4z" class="d"></path><path d="M460 299c1-1 3-1 5-1h0l-2 2s-1-1-2 0h-1v-1zm-28 254h1l-1 5h-1l-1-1c0-2 1-3 2-4z" class="f"></path><path d="M352 291h-2v-1c2 0 3-1 4-2l1 2c-1 0-2 1-3 1z" class="J"></path><path d="M446 327l-1-1v-1h4l1 1h-1c-1 0-1 1-2 2l-1-1z" class="Q"></path><path d="M431 502h1s0-1 1-1c0-1 1-1 2-1h0c0 1 0 2-1 3h-1-1 0l-1-1z" class="U"></path><path d="M422 453h1v5h-1-1c0-2 0-3 1-5z" class="V"></path><path d="M341 480l2-2c1 1 1 1 1 2v1c0 1-1 1-2 2 0-2-1-2-1-3z" class="S"></path><path d="M450 551c1 1 3 3 4 5h-1c0 1 0 1-1 0-1 0-2-4-2-5z" class="Q"></path><path d="M343 416v-2c0-1 1-3 2-4l1 1c-1 1-2 4-3 5z" class="g"></path><path d="M447 461h1c2 1 1 3 2 5h-1 0c-2-1-2-3-2-5z" class="V"></path><path d="M416 452h1c0 2 1 3 1 5h-1l-2-2c0-1 1-2 1-3z" class="d"></path><path d="M349 256l1-1 1-1v3 1c-1 1-1 2-1 3l-1-1v-4z" class="D"></path><path d="M478 559h1c0 1 1 1 1 2s0 2 1 2h0l-1 1c-1-1-3-3-3-4l1-1z" class="f"></path><path d="M332 471h1c0 2 0 4-1 5h-1 0c0-2 0-4 1-5z" class="U"></path><path d="M538 552c1 1 1 2 2 2v3l-1 1-2-6h1z" class="N"></path><path d="M319 474l3 3c-1 1-1 1-1 2-1 1-1 1-1 2l-1-7z" class="O"></path><path d="M360 404l2-2h1c-1 2-1 5-3 6h-1c1-1 1-3 1-4z" class="g"></path><path d="M349 499h1l1 1c0 2-1 2-1 4-1-1-1-1-1-2-1-1 0-2 0-3z" class="K"></path><path d="M523 582l1-10c1 2 1 6 1 9-1 0-1 0-2 1z" class="Q"></path><path d="M388 272c0-1 0-2 2-3h1c0 2-1 3-2 5h-1v-2z" class="Z"></path><path d="M451 406h1c1 0 1 0 2 1 0 1 1 2 1 3h-1c-2-1-2-2-3-4z" class="T"></path><path d="M409 331h0c-1 0-2-1-2-2 1-1 1-1 2-1 1 1 2 2 3 2-1 0-2 1-3 1zm21 93h1v5l-1 1-1-3c0-1 0-2 1-3z" class="H"></path><path d="M375 528h0v6h-1c-1 0-1-1-1-2 0-2 1-3 2-4z" class="T"></path><path d="M449 487h0c2 1 2 3 2 5h-1c-1-2-1-3-1-5z" class="d"></path><path d="M409 379h0c1 1 2 1 2 2v2c-1 0-2 0-3-1 0-1 0-2 1-3z" class="Q"></path><path d="M352 450l2-2h1c0 1 0 1-1 2v2s-1 0-2 1v-1-2z" class="g"></path><path d="M381 375c1-1 1-2 2-2 1 1 0 2 0 3-1 1-1 2-2 2h-1c0-1 1-2 1-3z" class="K"></path><path d="M401 379h1v1h1v-1h1c0 1 0 1-1 2 0 1-1 2-1 2h-1v-4z" class="Q"></path><path d="M412 649c1 0 1-1 2-1 0 1 1 2 2 3-2 0-3 1-5 1h0l2-2-1-1z" class="N"></path><path d="M327 454h0l1 3-2 2h-1 0c0-2 1-3 2-5z" class="Q"></path><path d="M459 602c1 0 2 1 2 1 1 1 2 2 2 3-1 1-1 1-2 1l-1-2c-1-1-1-2-1-3zm-84-125c1 0 1 0 1 1s1 4 0 5h-1v-2c-1-1-1-3 0-4z" class="K"></path><path d="M353 499h1v1c0 1 0 3-1 5 0-1-1-2-1-3s0-2 1-3z" class="f"></path><path d="M339 401c0-1 0-2 1-3s2-2 3-2v1c-1 2-2 3-4 4z" class="N"></path><path d="M440 592v-1h1c0 1 1 1 1 2s1 1 1 2h0v5c0-1-1-2-1-3 0-2 0-3-2-5z" class="M"></path><path d="M450 273h1v1c-1 2-2 2-3 3s-1 0-2 0c1-2 2-3 4-4z" class="Z"></path><path d="M412 336c1 1 2 1 3 1h0l2 2h0-4c-1-1-1-2-1-3z" class="S"></path><path d="M431 502l1 1h0v1c-1 1-1 1-1 2l-1 1h0v-2l-1 1h-1c1-2 2-3 3-4z" class="T"></path><path d="M340 438h1c0 2-1 4-2 5h-1v-1c0-2 1-3 2-4z" class="N"></path><path d="M352 435l1-1 1 1-4 4h0-1v-1c1-1 1-3 3-3z" class="g"></path><path d="M338 507h2v1l-3 3h-2 0c1-2 1-3 3-4z" class="K"></path><path d="M475 429c1 0 2 1 3 1v1c1 1 1 2 1 2h-2c-1-1-2-3-2-4z" class="Q"></path><path d="M424 453h1c1 2 1 4 1 6h-2c-1-2 0-5 0-6z" class="G"></path><path d="M507 537c1-1 1-1 2 0v1s-1 1-2 1c0 1 0 1-1 2h0c-1 0-2 0-3-1l1-1h2c0-1 1-1 1-2z" class="X"></path><path d="M392 269h1l1 1v4h-1v-1h-1v1h-1c0-2 0-3 1-5z" class="Z"></path><path d="M347 485c1 1 0 2 1 3-1 0-1 1-2 2h-1v-1c0-1 1-2 2-4h0z" class="T"></path><path d="M499 560c2 0 3 1 4 2s0 1 0 2h0c-1 0-4-3-4-4z" class="U"></path><path d="M434 577v1c1 2 1 3 2 5v1h-2l1-1h0l-2-1c1-2 1-3 1-5z" class="P"></path><path d="M458 680c-1 0-2 1-3 1s-1-1-2-2c0-1 0-1 1-2l1 1v1l3 1z" class="Q"></path><path d="M363 568c-1 0-1 0-1-1v-2h0c2 1 3 2 4 3l-1 1c-1 0-1 0-1 1l-1-2z" class="k"></path><path d="M359 485c2 2 3 4 3 7h-1c0-1-1-1-1-2-1-2-1-4-1-5z" class="U"></path><path d="M391 389c1-1 1-2 1-2h2l1 3h-1 0c-1 0-1 1-2 2v-3h-1z" class="Q"></path><path d="M360 400h0 4c0 1-1 2-1 2h-1l-2 2v-1l1-1c0-1-1-1-1-2z" class="I"></path><path d="M466 424c1 0 2 0 2 1 1 0 2 1 3 2l-1 1c-1 0-2 0-3-1s-1-2-1-3zm-20-19c2 0 3 1 4 3 1 1 1 1 1 3h-1c-1-2-3-4-4-6z" class="U"></path><path d="M521 588v-9h0 0c1 2 1 5 1 8 0 1 0 2-1 3v-2z" class="G"></path><path d="M432 652v-1h0l3 3v2h0c-2-1-3-2-3-4z" class="N"></path><path d="M320 429h0l1 1c-1 0-1 1-1 2l-1 1v2c-1-1-2-1-2-2 1-2 2-3 3-4z" class="Z"></path><path d="M341 462h2l-1 1c0 2-1 3-2 4h-2v-1c1-2 1-3 3-4z" class="K"></path><path d="M388 672c2-1 3-1 5-2h1l2 1-1 1h-2-2 0-1v1h-1l-1-1z" class="J"></path><path d="M454 443c1 1 3 1 4 2s1 3 2 3l-1 1c-2-1-4-4-5-6z" class="V"></path><path d="M426 479l1 1c-1 2-3 3-4 5h-1l1-2c0-1 1-3 3-4z" class="g"></path><path d="M460 440c2 0 4 1 4 3h1v2h0c-1-1-3-1-3-3h-1c0-1-1-2-1-2z" class="B"></path><path d="M426 307h1v2l1 1h1l-1 1c-1 1-2 2-3 2 0-2 1-5 1-6z" class="E"></path><path d="M436 465h1v1c1 2 1 4 0 6 0 1 0 1-1 1v-8z" class="H"></path><path d="M469 591c1 0 2 2 3 3s1 2 1 3h-1c-2-2-2-3-3-6h0 0z" class="g"></path><path d="M458 678c1-1 1-1 1-2s0-1 1-1v2 1l-2 2-3-1v-1l1-2 2 2z" class="K"></path><path d="M456 676l2 2h0c-1 0-1 0-1 1h-2v-1l1-2z" class="g"></path><path d="M350 432h1c0 2-2 4-3 5h-1 0l1-2s-1-1 0-1c0-1 1-2 2-2z" class="N"></path><path d="M442 611l-1-3h1c1 1 2 3 4 4h0-1v1c0 1 1 1 1 2h-1c0-1-2-2-2-3v-1h-1z" class="P"></path><path d="M434 535h1c0 1 1 2 1 3-1 2-1 2-1 3l-1 3v-5h-1l1-3v-1z" class="N"></path><path d="M483 552h1c1 1 2 3 3 4h-2v2h0c-2-2-2-4-2-6z" class="g"></path><path d="M426 677c0 2 0 3-1 5-1 1-1 1-2 1s-1 0-1-1c0 0 1 0 1-1 1 0 1-1 1-2l1-1h1v-1z" class="K"></path><path d="M349 454l2 1c0 1-1 1-2 2l-2 2s-1 1-2 1c0-2 3-4 4-6h0z" class="U"></path><path d="M545 558l1 1h1c0 1 1 2 2 3l-1 3v1c-1-2 0-3-1-5h-1v3c0-2 0-4-1-6z" class="J"></path><path d="M344 509c1 0 1 0 2 1 0 2-3 3-4 4l-1 1v-1c1-2 2-3 3-5z" class="U"></path><path d="M514 522c2-1 4-2 6-2v1h1v1h1l-2 1h0-1c-2-1-3-1-5-1z" class="J"></path><path d="M453 382l2 2-1 1-2-1c-1 0-2-1-3-2l-1-1c-1 0 0 0-1-1 2 0 2 1 3 1 1 1 2 2 3 1h0z" class="U"></path><path d="M392 331c2 1 7 1 8 2v1l-10-2h2v-1z" class="K"></path><path d="M337 458h1c-1 2-2 3-4 4h-1 0c0-1 1-2 1-3 1-1 2-1 3-1z" class="Z"></path><path d="M431 537c1 0 2 0 3-1l-1 3-1 6-1-2c0-2 1-4 0-6z" class="L"></path><path d="M451 519h3c1 1 2 2 2 3v1h0c-2 0-3 0-3-2-1-1-2-1-3-2h1z" class="N"></path><path d="M436 538l1 3v6h-1c-1-2-1-4-1-6 0-1 0-1 1-3z" class="T"></path><path d="M383 655c0-2-1-4-1-6l3 4 3 6c-1-1-4-3-5-4z" class="S"></path><path d="M429 629c1-1 1-2 2-3h1v1c0 1 0 1 1 2s1 1 1 2v1h-1l-1-1h0v3h-1 0v-1c0-1 0-2 1-3h0v-1h-3z" class="K"></path><path d="M436 628c1 1 1 1 1 2-1 1-1 2-1 2v2c-1-1-1-2-2-3v1-1c0-1 0-1-1-2l1-1h2 0z" class="Q"></path><path d="M341 509h2 0l-4 4c0 1 0 1-1 1v-1c0-2 2-3 3-4z" class="N"></path><path d="M373 387c0 1-1 3-1 5 0 1-1 1-1 2h-1v-2c0-1 1-4 1-5h2z" class="d"></path><path d="M438 386h0c2 1 4 3 5 5l1 2h-1l-3-3c-1-1-1-3-2-4z" class="U"></path><path d="M461 480c3 2 4 6 5 8-2-1-5-4-6-6 0-1 0-1 1-2z" class="G"></path><path d="M431 634c-1-2-1-3-1-4l-3 3c0-1 1-3 2-4h3v1h0c-1 1-1 2-1 3v1z" class="d"></path><path d="M457 441h1c2 2 4 3 4 5l1 2h-1l-3-3c0-1-2-2-2-4z" class="B"></path><path d="M354 435c1 0 1 0 2 1v1c-1 2-2 3-4 3h-1 0c0-2 2-3 3-5z" class="f"></path><path d="M412 494c0 2 1 3 1 5 0 1-1 4-1 5h-1v-4-2c0-1 0-3 1-4z" class="K"></path><path d="M470 563h1c1 1 1 1 1 2s1 2 1 3l-1-1h-1l-1 1c0-1 0-1-1-1v-2l1-2zm12-63c1 0 1 0 1 1 1 0 2 1 2 2 1 2 2 3 3 4v1l-1 1c-1-2-2-4-4-5 0-2 0-3-1-4z" class="N"></path><path d="M443 406l5 5c0 1 0 1-1 2h0 0c0-1 0-1-1-1l-1-1c-1 0-2-3-2-5z" class="d"></path><path d="M456 662h3c2 0 3 1 5 2h1v1c-2 0-4-1-6-1l-1 1v-1l-2-2z" class="L"></path><path d="M457 429c1 0 3 3 4 4v1h-1-1s0 1-1 1h0c-1-2-1-4-1-6z" class="U"></path><path d="M344 398h2 1c-1 2-3 3-4 4l-1 1-1-1c0-1 2-3 3-4z" class="N"></path><path d="M455 470h0c2 1 2 4 2 6h0-1c-1-1-2-3-2-4-1 0-1 0-1-1 1 0 1 0 1 1l1-1v-1z" class="K"></path><path d="M371 576l1 1v1 1c0 1 0 4-1 5h-1v-5c0-1 0-2 1-3z" class="U"></path><path d="M433 539h1v5l-2 4h0v-3l1-6z" class="S"></path><path d="M427 455h1v-3h1v2h1l2 2c-1 1 0 2-1 3-1 0-1-2-2-3h-1l-1-1z" class="T"></path><path d="M476 536c1 0 1 1 1 2 1 0 1 1 1 1-1 0-1 0-1 1v2h0l-3-3c0-1 1-2 2-3z" class="U"></path><path d="M476 391c2 2 2 6 2 8v2c-1-2-2-4-2-6l-1-1v-3h1 0z" class="J"></path><path d="M439 373v-1l3 3-1 2v3h0 0c-2-1-2-5-2-7z" class="K"></path><path d="M354 386h1v4c-1 1-1 1-2 1v-2l-1 1c0 1 0 1-1 2l-1-1c1-2 2-3 4-5z" class="Q"></path><path d="M387 610c-2-2-2-3-3-4s-1-2-1-3h1c2 2 3 4 4 6v1h-1z" class="E"></path><path d="M386 578l-4-4c0-1 0-2-1-3h0v-1l2 1 2 2c0 1 0 2 1 3v2z" class="N"></path><path d="M446 327l1 1c1-1 1-2 2-2h1c0 1-1 2 0 3l-6 2 1-1v-1c0-1 1-1 1-2z" class="C"></path><path d="M349 398h1l1 1c-1 1-2 3-3 4h-1v-1h-1c0 1-1 1-1 1h-1c1-2 4-4 5-5z" class="g"></path><path d="M419 356v1l1-1h1l1 1c1 0 1 1 2 1-1 0-1 1-1 1-1 0-1 1-1 1-1 0-1 0-2 1v-3l-1-1h-3c1-1 1-1 3-1z" class="J"></path><path d="M428 625h1v1c-1 1-1 2-2 3s-2 2-4 3c1-3 3-5 5-7z" class="f"></path><path d="M469 611h-1c-1 1-1 2-1 2h-1l-1-1c0-1-1-1-1-2v-1h1c2-1 1-1 2-2h1l-1 1c0 1 1 2 2 3z" class="C"></path><path d="M479 579h0c1 2 2 5 2 7v-1c-2-1-3-2-4-3 0-1 1-2 2-3z" class="U"></path><path d="M495 502v-1h1v1l3 3c0 1 1 2 2 2v1l-1 1v1s-1 0-1-1c-1-1-1-2-2-3s-1-2-2-3v-1z" class="N"></path><path d="M431 270v-1-2c-1-1-1-3 0-4h1v1c0 1 1 2 2 4h0l1 1h-1l-3 1z" class="Q"></path><path d="M531 613h4l-5 3-2 1c0-1-1-1-2-1l3-3v1l2-1z" class="S"></path><path d="M467 433h2c1 1 3 2 4 4l1 4-4-4c-1-1-2-2-3-4z" class="G"></path><path d="M502 551c1 1 2 2 4 3 1 0 1 1 3 1h-1l-1 1v1h-1-1-3v-1h1l2-1v-1l-1-1h-1v-1l-1-1z" class="X"></path><path d="M379 564h1 1c1 0 1 1 2 2l-1 1h-1 0l-1 1h-2c0-1-1-1-1-2l1-1v1c1-1 1-1 1-2z" class="C"></path><path d="M473 395c0 1 0 2 1 3-1 1-1 3-2 4-1-1-2-1-4-2h1l3-1h0l-1-1v-1h1 1l-1-1 1-1z" class="K"></path><path d="M474 576h1l1 1c0 2-1 3-2 4h-1l-2-2h0l3-3h0z" class="h"></path><path d="M386 540h0c2 3 3 5 4 9l-1-1-3-3c-1-1 0-4 0-5z" class="T"></path><path d="M432 522c1-1 2-3 2-4h1c1 1 2 2 1 4v1h-3l-1-1z" class="Q"></path><path d="M467 592h1c0 2 1 3 2 4s1 2 1 3c-1 0-2-1-3-1l-4-6c2 1 3 2 4 3 0-1-1-2-1-3z" class="g"></path><path d="M371 663c1 0 2 0 3 1v1h-2l1 1c-1 2-1 3-2 4h-1-1v-1s1 0 1-1h-1c0-1 2-2 3-3l-1-2z" class="J"></path><path d="M456 669l1 1v2 1h-1v1h-1c1 1 1 2 1 2l-1 2-1-1c0-1 0-3 1-4v-2c0-1 1-1 1-2z" class="K"></path><path d="M470 432c1 0 4 1 5 2s1 3 1 4l-1 1c-1-3-3-5-5-7h0z" class="T"></path><path d="M425 533v1c-1 1-1 2-2 3-2 1-3 3-4 4h-1l1-2c0-1 1-3 3-4 1 0 2-1 3-2z" class="U"></path><path d="M454 528c2 0 4 0 5 2 2 1 2 3 2 5h0c-1-3-5-5-7-7z" class="S"></path><path d="M409 512l2 2v1h0c-1 2-1 5-2 6v-1c-1-2-1-6 0-8z" class="U"></path><path d="M449 396l1-1v1h1c1 0 1 1 1 1l-3 3h0-1-1v-1h-1c0-1 0-1 1-2h0c1 0 1 0 2-1z" class="M"></path><path d="M478 642c2-1 5-3 7-2-1 1-3 2-5 3l-5 1c1-1 2-2 3-2z" class="g"></path><path d="M465 435c2 0 4 3 6 4v4h0c-1-1-1-1-1-2-1 0-2-1-2-2-1 0-2-2-3-2v-2z" class="H"></path><path d="M368 553c-2 1-3 4-4 5v1h-1c1-3 1-6 3-9v2c1 0 1 0 2 1z" class="T"></path><path d="M332 396h0-1c1-1 1-2 2-3 1-2 5-2 6-2-1 2-5 3-7 5z" class="f"></path><path d="M455 332c2 0 5-1 8-1v1h1 3 0c-4 1-8 1-12 2l-2-2h2z" class="d"></path><path d="M430 479h1c-1 3-3 5-4 7h-1c0-2 0-4 1-5 1-2 2-2 3-2z" class="K"></path><path d="M383 609c0-2-1-5-1-7l1-1 1 2h-1c0 1 0 2 1 3s1 2 3 4h-1l1 1h0l-1 1c-2-1-2-2-3-3z" class="g"></path><path d="M357 403h0c0-2 1-3 3-3 0 1 1 1 1 2l-1 1v1c0 1 0 3-1 4-1-1-1-2-1-3s1-1 1-2h-2z" class="Z"></path><path d="M437 622h0l-2-1h-2c0-1-1-2 0-3v-2h1c0 1 0 2 1 3 0-1 0-2 1-2 1 1 1 2 1 3h0v2z" class="K"></path><path d="M355 487v1c-1 2-1 4-2 6 0-1 0-2-1-4 0 0 0-1-1-2 1-1 2-3 3-4h0v1 1l1 1z" class="H"></path><path d="M373 387l1 1v1l1-1h0 1v1l-1 2v1 1l-1 1c-1 0-1-1-1-1l-1-1c0-2 1-4 1-5z" class="M"></path><path d="M382 322h4l-1 1h-1 0c0 1 1 1 1 2l-1 2h0l-1 1h-1c0-1 0-3-1-4v-1l1-1z" class="Z"></path><path d="M408 492l1 1h1 1l1 1c-1 1-1 3-1 4v2h-2v-3c-1 0 0-2 0-3h-1v-2z" class="a"></path><path d="M408 492l1 1h1 1l1 1c-1 1-1 3-1 4l-1-1v-2l-1-1h-1v-2z" class="I"></path><path d="M478 655l-2-1h1 1 0c4 2 9 1 13 1-4 2-8 2-12 1 0 0-1 0-1-1z" class="U"></path><path d="M431 270l3-1v2 4h1c-1 1-2 2-2 3l-1 1c-1-2 1-5 0-7 0-1 0-2-1-2z" class="K"></path><path d="M431 270l3-1v2c-1 0-2 0-2 1 0-1 0-2-1-2zm-71 254h0c1 1-2 6-2 7h-1c0-1 0-1-1-1h0c-1 1-1 1-2 1v-1l6-6z" class="N"></path><path d="M494 513h1c1 1 3 6 3 6-1 2-1 2-1 3v1h-1c-2-4-2-6-2-10z" class="H"></path><path d="M340 523h3 0c-2 2-4 3-5 5h-1s0 1-1 1v-1c0-1 0-2 1-3s2-2 3-2zm44 160c2 1 2 1 3 3-1 1-2 2-3 2-1 1-2 1-3 1 1-1 1-2 2-2 0-2 0-2-1-3l2-1z" class="Q"></path><path d="M380 543h1c2 2 3 5 4 8v1h0-1c-2-2-4-6-4-9z" class="B"></path><path d="M451 531c2 1 4 1 5 2 2 1 3 2 3 5l-2-2c-2-1-4-3-6-5z" class="G"></path><path d="M363 437h1v2h-1c0 2-1 4-2 5h-1c1-1 1-2 1-3v-1c-1 1-1 3-2 3v-1c1-2 2-3 4-5z" class="d"></path><path d="M430 525c1 1 2 1 3 2l1 1c1 2 2 3 3 4h-1v1c-1 1-1 1-1 2h-1v-2h1v-1c-1-1-1-2-2-3l-1-2h-1v3h-1c0-2-1-3 0-5z" class="Q"></path><path d="M540 557h0c0 1 1 1 1 1v1l1-1v5c1 1 1 2 1 3-1 0-1 0-2-2l-1-1v-1c-1-1-1-3-1-4l1-1z" class="O"></path><path d="M359 474l1-1 2 2v-1c1 1 1 1 1 2h1c0-1 0-1 1-2l1 1c-1 2-1 3-3 4-1 0-1-1-2-1v-2h-1c0-1 0-1-1-2z" class="Z"></path><path d="M458 516l1 1c1 1 1 2 2 3-1 1-2 0-3 0l1 1-1 1-1-1c-1 0-1-1-2-1l-1-2h0 1s1 1 2 1v-1l1-2z" class="Q"></path><path d="M443 564c1 1 4 2 5 3s2 3 2 5h-1l-1-1c-2-1-3-4-5-7zm42 60c0-3 0-6 1-9 0 1 0 4 1 5v-1l1 1c-1 2-2 5-3 7h0c-1-1 0-2 0-3z" class="T"></path><path d="M390 579s1 1 1 2c1 0 0 1 1 1 1 1 1 3 1 4v1c-1 0-2-2-3-3s-2-3-3-4h1s2 2 3 2l-2-2 1-1z" class="U"></path><path d="M486 590l2 1c1 1 1 2 1 3-1 0-1-1-2-1l-1 3h-1c-1-1-2-2-2-3 1-2 2-1 3-1v-2z" class="f"></path><path d="M528 328c5 0 11 2 15 5h-1c-5-1-9-3-15-4h1v-1z" class="J"></path><path d="M438 480h1v5c-1 1-1 2-2 3l-1 1v-1c0-3 0-5 2-8z" class="N"></path><path d="M354 540h1v1s-1 1-1 2c-2 1-3 4-4 5v1c-1-1-1-3-1-4s4-4 5-5z" class="S"></path><path d="M488 584c3 1 5 4 6 6h0-1l-6-3 1-1h1l-1-2z" class="N"></path><path d="M430 252c1 0 1-1 2-1v3l2-2h0c0 1 1 1 0 1 0 2-2 3-3 5h0-1v-1-2-3z" class="Z"></path><path d="M354 485l1 1 1-2 2 2c0 2 1 4 1 6l-1 1c-1-2-1-4-3-6l-1-1v-1z" class="U"></path><path d="M444 449h1c0 1 1 1 1 2l1-1h1c0 2 2 3 1 5h0l-2-2-1-1h0c0 2 1 3 1 5-2-2-4-5-3-8z" class="G"></path><path d="M356 500c1 0 2 0 3 1 0 1-1 3-1 4l-1 1-1-1h-1v-2c0-1 0-2 1-3z" class="N"></path><path d="M426 624h1v1c-2 2-3 4-5 5h0v-1h-1l-1 1h-1c1-2 3-4 4-5h3v-1z" class="T"></path><path d="M427 619l1-2c1-2 2-3 3-4h1c-1 1-1 2-1 3-1 0-1 0-1 1h1c0-1 0-1 1-1v5l-1 1c-1-1-1-1-1-2v-2c-1 0-2 1-3 1z" class="N"></path><path d="M455 334c-6 0-12 1-18 0 3-1 6-1 9-1s5-1 7-1l2 2z" class="T"></path><path d="M540 568s0 3 1 4h0c0 2-1 5-2 7h-1c-1-2 0-5 1-6 0-2 0-4 1-5z" class="N"></path><path d="M468 607l1 1h1l-1-2 1-1c1 0 2 1 2 1v3c-1 0-1 1-2 1l-1 1c-1-1-2-2-2-3l1-1z" class="d"></path><path d="M421 493h1v2l1-1v-1c1 0 0 0 1 1h2 0l-4 3-4 4 3-8z" class="U"></path><path d="M445 337l12-1c0 1-1 1-1 1 1 1 3 0 4 0-4 1-11 1-15 1-1 0-2-1-2-1h-1 3z" class="D"></path><path d="M494 646v1s-1 1-2 1l-2 1c-2 1-5 2-7 0h0c4-1 7-2 11-3z" class="S"></path><path d="M433 503l1 1c1-1 2-2 2-3h1c0 1 0 2 1 3v2l-1-1h-1v2h-1v-2c-1 1-1 2-3 3 0-1 1-3 0-4v-1h1z" class="K"></path><path d="M457 672l1-1c1 0 2 1 2 1v1 1 1h0c-1 0-1 0-1 1s0 1-1 2l-2-2s0-1-1-2h1v-1h1v-1z" class="d"></path><path d="M457 672l1-1c1 0 2 1 2 1v1h-1v1 1h-1c-1 0-1-1-2-1v-1h1v-1zm-32-170h-1c0 1-1 2-2 2v-1c3-4 5-6 10-8l-7 7h0z" class="S"></path><path d="M394 479h0c0 1-1 2-1 3s-1 1-1 1c-1 1 0 2-1 3l-1-1v-2h-1l-1 2h0l-1-2h0c0-1 1-2 2-3 0 1 0 1 1 2v-1c0-1 1-1 1-1v2h1c0-1 0-2 1-3h1z" class="Z"></path><path d="M465 496c0-1 0 0 1 0v1c-1 0-1 1-2 1h-1c-1 1-1 1-2 1h0c-1 0-2 0-2 1h-1 0v1l-2-1c0-1-1-1 0-2h1c0 1 0 1 1 1s0-1 1-2c1 0 1 1 2 2v-2h1v1h1v-2h2z" class="J"></path><path d="M358 436h2 0v2c-1 1-3 4-4 4h0l1-2h-1l-3 3c1-1 1-2 2-4l3-3z" class="U"></path><path d="M362 503c1-1 1-1 1-2h1l1 1v-1-1c1 0 2 1 2 2 1 1 1 2 1 3h-1l-2-2h0v2h-1 0l-1-1c-1 1-1 1-1 2h-1c0-1 0-1-1-2 1 0 1 0 2-1z" class="K"></path><path d="M425 267c0-1-1-1-1-2s1-2 1-3c1 1 1 2 1 2v1s1 0 1 1v3c1 1 1 3 0 4v1-2-1l-1 1c0-1-1-1-1-1-1-1-1-1 0-3v-1z" class="C"></path><path d="M425 267c0-1-1-1-1-2s1-2 1-3c1 1 1 2 1 2v1s1 0 1 1v3h0c-1-1-1-2-1-3l-1 1z" class="R"></path><path d="M409 328l6 1c3 0 6-1 8 0l1 1c-1 0-1 1-1 1l-1-1h-4-6c-1 0-2-1-3-2z" class="S"></path><path d="M481 494h0l-2-1-2-2c1-1 1 0 2 0s1-1 2-1c0-2-1-3-2-4h-1l1-1c1 0 1 1 2 2h1v2s1 1 1 2c0 0-1 1-2 1v2z" class="I"></path><path d="M476 536h0c1-1 2-1 2-2h0 1 0c0 2 2 3 2 5 1 0 1 1 1 2h-1l-1-1h-2-1c0-1 0-1 1-1 0 0 0-1-1-1 0-1 0-2-1-2z" class="C"></path><path d="M354 288c1 0 2 1 3 1 1 1 2 1 3 1v1h-1-2 0c-1 2-2 2-4 2l-1 1h-2c1-1 1-2 2-3 1 0 2-1 3-1l-1-2z" class="K"></path><path d="M534 520h2 1c-1-1-4-2-4-3 1 0 3 1 4 1s2 1 2 2c1 0 2 0 2 1v1l1 1h-1c-1 0-1-1-3 0 0 0 0-1-1-1h-1c0-1-1-1-2-2z" class="L"></path><path d="M386 402c1 1 1 1 1 2v1l-1 1v2l-1 1v3l-4 5 5-15z" class="d"></path><path d="M528 544c-1-1-3-2-5-3s-5-2-7-2h0c4-1 9-1 13 2h-1c0 1-1 0-2 0v1c2 0 3 1 3 2v1-1h-1z" class="Q"></path><path d="M426 435h1 0c-1 1-1 2-2 3v1c2-2 2-3 4-4l-3 6c-2 0-3 3-4 4h0c0-4 2-8 4-10z" class="T"></path><path d="M521 588v2h1-1c0 2 0 6-1 7h0c-1-1-2-2-2-3 0-2 0-4 1-6h0c1 1 1 1 1 2 1-1 1-1 1-2z" class="R"></path><path d="M364 307v1h0v1s1 0 1 1l-1 1h0 1c0 1 0 1-1 2h-1c-1 0-2 0-3 1v-1s1-1 1-2l-1-1v-2c1 0 2 0 2-1h2z" class="K"></path><path d="M338 396c0-1 0-1 1-1 0-1 1-1 1-1l1 1h1c1 0 1 1 1 1-1 0-2 1-3 2s-1 2-1 3c-1-1-1-1-1-2h-1l-1 1c-1 0-1 0-1-1l1-1c1-1 1-2 2-2z" class="I"></path><path d="M473 293h1 0l-2 3h0l4-1v1 1c-2-1-3 1-5 1-1 0-2 1-3 0l2-1v-1l-2 1h0c1-2 3-3 5-4z" class="U"></path><path d="M442 556h1 1v-1-1c1 1 3 4 4 4h0l-2-6c1 2 3 3 2 6v1l-1 1c-1-1-2-3-3-3v1l-2 2-1-1h1v-3z" class="N"></path><path d="M418 398l1-1v1c0 1 1 2 1 3s1 2 1 4l1 1c0 1 1 7 0 8h0l-3-12c-1-1-1-3-1-4z" class="H"></path><path d="M368 651c1 0 2-1 3-2 0 1-1 2-2 3v1 1c1-1 2-3 3-4 1 1 1 1 1 2s-1 2-1 2c0 1-1 2-2 3v-2h-1c-1 1-1 1-2 0v-2l1-2z" class="T"></path><path d="M399 503h0c-1-1-1-1-1-2v-3c1 0 1 2 1 3 1-1 1-2 2-3 1 3 1 5 1 8h0c-1 0-1-1-1-2h-1v3l-1-1v-3z" class="S"></path><path d="M434 375h1c1 1 0 2 1 3v1c0 1 1 1 1 1 0 1 0 2-1 3h-1l-1-1c0-1-1-1-2-2 0-1 0-3 1-3h0 1v-2z" class="C"></path><path d="M528 570l-1-9h0 0c0 1 1 2 1 3 1 2 1 4 2 6v1 5h0-1l-1-6z" class="Q"></path><path d="M330 442v1 2h1 0 1 1c0 2 0 3-1 5h-1v-3l-1 2h-1l-1-1v-2-1c1-1 1-2 2-3z" class="K"></path><path d="M477 654c5-2 11-1 17-3h4c-3 1-5 2-9 3-3 0-8-1-11 0h-1z" class="g"></path><path d="M462 523h1c1 2 2 3 3 5l-4-1c1 1 2 2 2 4l-3-1h-1v-2c0-1-1-1-1-2 1 0 1 0 2 1h1v-1-2-1zm-84 155l1-1 3-3v2l-2 2c0 1 0 1 1 2v1c1 0 2 0 3 1v1l-2 1c0-1-1-1-2-2v-1h-1 0-1l1-1-1-1-1 1c0 1-1 1-1 1 0-1 1-2 2-3z" class="Z"></path><path d="M360 476l-1 2h-1v-3c-1 0-2 2-3 3l-1-1c0-1-1-2 0-4 0-1 1-1 1-1h1c0 1-1 2-1 4h0c1-1 1-2 2-2 0-1 0-1 1-2l1 1v1c1 1 1 1 1 2z" class="N"></path><path d="M429 300h1v7c-1 1-1 2-1 3h-1l-1-1v-2h-1c1-2 2-5 3-7z" class="S"></path><path d="M373 684v-3c1-1 3-2 4-3h1c-1 1-2 2-2 3 0 0 1 0 1-1l1 1h-1c-1 2-1 4-2 5v2c-1-1-2-3-2-4z" class="G"></path><path d="M420 343l1 1c2 3 7 8 7 12h-1l-3-4s0 1-1 1c0-1 0-3-1-4-1-2-1-4-2-6z" class="d"></path><path d="M379 666h4c0 1 1 1 1 2v2h1v1h-1v1h0l-1 1h-2c1-1 1-1 1-2l-1-1h-2v-1h2v-1h-1l-1-1c1 0 1 0 1-1h-1z" class="Z"></path><path d="M425 502h2c1-1 2-3 3-4s2-2 4-3c-1 3-2 6-6 8h-1c-1 1-1 1-1 2h-1c-1-1 0-2 0-3h0z" class="U"></path><path d="M445 340c-2 0-3-1-4-1-1-1-1-1-2-1h-4-6c5-2 11-2 16-1h-3 1s1 1 2 1c-1 0-4-1-4 0h1c1 0 3 1 4 2h-1z" class="K"></path><path d="M436 533v-1h1c2 5 4 11 4 16-1-1-3-3-3-5-1-3-1-6-2-10z" class="D"></path><path d="M432 480h1v1l1 1h1 0v5l-1 1-1-1v-2-1h-1c0 1-1 1-1 2s0 1-1 1h0c0-3 1-4 2-7z" class="C"></path><path d="M431 537c1 2 0 4 0 6s-1 3-2 5v-6l-3 7c0-3 1-5 2-7s2-4 3-5z" class="H"></path><path d="M406 271c1 0 1-1 1-2h0c1-1 2-1 2-1l2 1h0-1c-1 1-1 3-2 3-1 2-1 3-1 5v1h0c-1 0-1 1-2 1-1-2 1-6 1-8z" class="k"></path><path d="M437 620l1 1h1v-1-1c1 0 1 1 2 1v1h1l1-1h2v1l-1 1h0l-1 1h-2c0 1 0 1 1 2-2 0-1-2-3-1h0-1l-1-1h0v-1-2z" class="X"></path><path d="M361 512v1l1 1c1 0 1-1 1-1h1c1-1 2-1 3-1v2c0 2-1 2-2 3-1-1-1-2-2-2l-1 1h0c-1 0-1 1-1 1h-1v-3c0-1 0-1 1-2z" class="Z"></path><path d="M366 606v1 1l1 1c0-1 0-1 1-2h1s0 1-1 1v1c-1 2-2 2-2 4 0 1 0 1-1 1v-1l-1-1c0 1-1 1-1 2h-1c0-1 1-2 1-3h0-1v-1s1-2 2-2 1-1 2-2z" class="X"></path><path d="M449 532c2 1 4 2 6 5 2 1 2 3 3 5-5-2-7-6-9-10z" class="G"></path><path d="M349 667l2 1c-1 2-2 5-2 7l-1 1-1-1v1l-1 1v-6h0v-1l1 1c0-2 1-3 2-4h0z" class="M"></path><path d="M365 588h1c0 2-1 3-1 5l3-4c0 2-2 5-2 8-1-2-1-3 0-4h-1l-2 2v-1-2c-1 0-1 1-3 1v-1l3-3h0v1h1c0-1 0-1 1-2z" class="K"></path><path d="M451 406h0c-1-1-2-1-3-2 1-1 2-1 3-2 1 0 1 1 3 2 1 0 2 2 3 4h0c-1 0-2 0-3-1s-1-1-2-1h-1z" class="Z"></path><path d="M385 494h1c0 1 0 1 1 2-1 1-2 1-2 3v1c-1 0-2-2-3-2l-1 2h0c-1-1-1-2-1-3s1-1 2-2l2 1c0-1 0-1 1-2z" class="J"></path><path d="M381 670l1 1c0 1 0 1-1 2h2l1-1 1 1-3 1-3 3-1 1h-1c-1 1-3 2-4 3v3-3c-1-1-1-1-1-2l1 1c1 0 3-2 4-3 1-2 2-3 3-4h0v-1h-2l1-1h2v-1z" class="Q"></path><path d="M557 566l-3 8-3 3c-1 3-3 4-5 6l-1-1c1-1 2-3 3-4 4-3 6-7 9-12z" class="g"></path><path d="M326 420l1-1c1 1 2 2 2 3 0 3 0 3-2 5v1l-1-1c0-1 0-1-1-2l-1-2 1-1c0-1 1-2 1-2z" class="a"></path><path d="M339 391h1v3s-1 0-1 1c-1 0-1 0-1 1h-1l-3 2h-1-1v-1-1c2-2 6-3 7-5z" class="C"></path><path d="M505 613h-1c0-2 0-5 1-7 0-1 1-3 2-4v4h0 0c1 0 1-1 1-1 1-2 1-3 1-5h1c0 3-1 6-2 8-1 1-1 1-1 0v1c-1 1-1 3-2 4z" class="N"></path><path d="M368 681v1l2 2c1 1 1 2 1 3s-1 1-1 1l-1 1c-1 0-3 1-4 0h-1-1l-2-1h3c2 0 3-1 4-2v-3h0v-2h0z" class="Q"></path><path d="M356 310v1c-1 1 0 2 0 2v2h-1v1l1-1h1v1l-3 2h0v-1h0-1-1v-1l1-1c0-1-1-1-2-2l1-1h-1l1-1h1 1c1-1 2-1 2-1z" class="g"></path><path d="M346 411s1 1 1 2l2-2v1 1 2 2l-1 1h-1v-3h-1c0 1-1 2-1 3l-2-2h0c1-1 2-4 3-5z" class="e"></path><path d="M436 438h1v7h0c-1-1-1-2-2-3 0 1 0 3-2 3h0v-3c-1 1-1 1-1 2l-2 2h0c0-3 3-6 5-8h0l-1 3h1 0c1-1 1-2 1-3h0z" class="H"></path><path d="M418 650h1v-2h1v3h1c0-1 0-1 1-1v1l1 1-3 3h-1l1-2c-1 0-2 1-3 2h0c0-1 1-2 1-3-1 0-3 2-4 3 0-1 1-1 1-2 1-1 1-3 3-3z" class="K"></path><path d="M333 404h1v1s-1 0-1 1v1h0c2 1 1 0 3 0v1 1c-1 1-2 2-3 2v1l-1-1v-1h-2c-1 0-1-1-1-1v-1l4-4z" class="h"></path><path d="M447 523v-1-3h1 0l1 1c1 0 2 1 3 1 1 1 1 2 2 3l-1 1h0c-1-1-2-1-2-1-1 0-2-1-2-1-1 0-1-1-2-1 1 1 3 2 3 4h0c-1-1-2-2-3-2-1-1 0-1 0-1z" class="K"></path><path d="M400 426v-1h1c0 2 1 5 0 7-1 0-1 1-1 1l1 1 1 2c-1 1-1 2-2 2-1 1-1 3-2 4l1-6v-6c0-2 1-3 1-4z" class="d"></path><path d="M366 550l1-3h1v3c1-1 1-3 2-4v4c0 3-1 7-2 9h-1c0-2 1-2 1-3v-3c-1-1-1-1-2-1v-2z" class="S"></path><path d="M476 583h1c1 1 3 3 3 4h-1c0 1 1 1 1 2l-1 1c-1-2-2-3-3-3 1 1 2 3 3 5-2-1-4-4-5-6 0-2 1-2 2-3z" class="N"></path><path d="M409 500h2v4h1v4c-1 1-1 1-1 2-1 1 0 3 0 4l-2-2c1-1 1-2 1-2h0c0-2 0-3-1-5v-3h-1c1-1 1-1 1-2z" class="h"></path><path d="M409 502l1 1v2h-1v-3zm93 46c1 1 1 1 2 1l1 1c1 1 2 1 4 1 0 1 1 1 0 2h-1c0 1 1 1 2 1v1h-1c-2 0-2-1-3-1-2-1-3-2-4-3 0 0 0-1-1-2h0c1-1 1 0 1-1h0z" class="M"></path><path d="M536 547l1 1h0l-1 1c0 1 1 2 2 3h-1l-1-2h-1c1 2 2 4 2 5 1 2 0 5 0 7l-3-8v-4c0-1 1 0 1-1s0-1 1-2z" class="Q"></path><path d="M556 554h1c0 1 0 3-1 4s-1 3-1 5h-1v-3l-2 4c-1-2 0-5 0-8l1 1 2-3v2h1v-2z" class="g"></path><path d="M389 278c0 1 1 1 2 2 1 0 1-1 2 0l3 4-1 3h0-1c-1-1-1-2-2-3l-2-3-2-2 1-1z" class="k"></path><path d="M492 641h5 0c-2 1-3 2-4 2v1h1c-1 1-1 1-2 1s-2 1-2 1c-2 0-3 0-4 1-2 0-4 1-5 1h-1-1l2-1 4-1c3-1 5-3 7-5z" class="U"></path><path d="M317 420l1 1-1 1c1 1 1 1 1 2-1 1-1 1-1 2v1l1-1h1 0c0 2-1 4-2 6l-1-1v-1-1c-1-1 0-5 1-6v-1h0 0c-1 2-1 3-2 4h-1v-2c0-1 2-3 3-4z" class="Z"></path><path d="M415 462c1 0 2 1 3 1v1l2-1h1l1 1 1-1 1 1c0 1-3 4-4 5h-1 0c1-2 1-3 2-4v-1h0 0c-1 1-1 2-2 2h-3c0-1-1-2-1-3v-1z" class="Q"></path><path d="M520 616l1-2c1 0 2-1 2-1l1-1h1s1-1 2 0h1c0 1 1 0 2 0l1 1-2 1v-1l-3 3-3 1-1-1h-2zm-135-47l2 2v2 1c1 0 1 1 1 2h1c0 1 1 1 1 3l-1 1 2 2c-1 0-3-2-3-2h-1v-1c0-1-1-1-1-1v-2c-1-1-1-2-1-3v-4z" class="C"></path><path d="M354 573h0c0-1-1-1-1-2h-1v-2c1-1 1-2 3-2 0-1 2-2 2-3v-1l1 1v1c1 0 1 1 1 1l-3 3c0 1-1 2-1 3l-1 1z" class="M"></path><path d="M458 482h1c1 0 3 3 4 5h-1-1-1c-1 0-1 0-2 1l1 1v1l-1-1c-1-1-2-2-2-3s-1-1-1-2h0c0-2 1 0 2 0l1-1v-1z" class="C"></path><path d="M359 461l1 1v1c1 0 1 0 1-1h1 2c0 1 1 1 1 2s1 3 0 5l-1-1c0-1 0-3-1-5v5l-1-1c-1-1-1-2-1-4l-2 4h-1c0-2 0-4 1-6z" class="d"></path><path d="M440 641l2 2 1 1h1l1 1-1 1 1 2h0c0-1-1-1-1-1l-1 1h0l2 2h-1l1 2h0c-1 0-2-2-3-2l-1-1-1-3v-1h1c0-1 0-1-1-2v-2z" class="P"></path><path d="M357 289h1c1 0 2-1 3-1l-1-1v-1c2 0 2-1 3-1s1 1 2 0l1 1h-2c1 1 2 1 2 2h-2c1 1 1 1 2 1l-1 1-1 1h-1c-1-1-1-1-3-1h0c-1 0-2 0-3-1zm20 134h1v2h1l1-1 1 1c0 2-1 5-2 7l-1-1v-1-1c-1 1-1 1-1 2l-1 1v-1c0-1 0-4 1-5v-3z" class="h"></path><path d="M459 413l1-1c0 1 1 1 1 1l1 1c1 0 2 1 3 2l-1 1h-1c0 1 0 1-1 2v1h0l-1-1c-1-1-3-2-3-3v-1-2h1z" class="C"></path><path d="M440 409s0 1 1 0h0l3 2v3l-2-2h-1c1 1 1 1 1 2-1 1-1 0-2 0v1h0-1l-1-1v2h0l-1-1c-1-1-2-2-1-3h0l2 1v-2h1l1 1v-1-1-1z" class="f"></path><path d="M394 353l1 1c1-1 3-4 5-4-1 1-1 2-2 3l-1 1-2 2v1c-1 1-2 2-2 3h-1v3l-1 1h-1 0v-2-1-1-1c1 0 1-1 2-2h0c1-1 1-1 1-2 1-1 1-1 1-2z" class="X"></path><path d="M322 467c1-1 1-1 2-1v1c1 1 1 1 2 1l1-1h0 1c1 2 0 2 0 4 0 1-1 2-2 3v-1c0-1 0-2 1-2v-2h-1c0 2-1 4-2 5h0l-1-1c1 0 1 0 1-1v-1c-1-1-1-1-1-2h0-1c-1-1-1-1-1-2h1 0z" class="I"></path><path d="M401 346v1l-1 1c-2 1-4 4-6 5 0 1 0 1-1 2 0 1 0 1-1 2h0c-1 1-1 2-2 2v1 1l-2 2v-1c-1-1 0-1 0-2v-2c4-4 8-8 13-12z" class="P"></path><path d="M473 418h2v3 1c-1-1-1-1-2-1l1 2v1h-2c-1 0 0 1 0 2h-1l-3-3 1-1 1 1v-1l-1-1 2-2 1 1v-1l1-1z" class="f"></path><path d="M389 596c1 0 3 2 3 3h0c1 1 1 2 1 3v2h0 0c-1-1-2-2-3-2 1 1 2 3 3 4l-1 1-3-4-3-5 4 4c0-1-1-2-1-2l-2-3c1 1 3 3 4 3-1-1-2-3-2-4z" class="T"></path><path d="M505 328c2 0 3 0 5-1 6 0 12 0 18 1v1h-1-8-6-4c-1-1-3-1-4-1z" class="k"></path><path d="M482 252l3-10h1l-5 21c0-1 0-2-1-2v-1c1-1 1-7 2-8z" class="T"></path><path d="M396 466h2c-1 3-2 5-4 7 0 0-1 0-1-1h-1 0l-1 2h-1v-1c0-1 1-2 1-3h1v-1c1-2 1-1 2-1 0 0 0-1 1-2h1z" class="C"></path><path d="M491 491c-1 0-2-2-2-3s-1-1-1-2c-1 0-1 0-1-1s-1-1-2-2h0l1-1h0c0-1-1-1-1-2s0-1 1-2c0 2 3 5 4 6l2 1h-1v2 4z" class="O"></path><path d="M330 425v1c0 1-1 2 0 4 1-1 1-2 1-2h1v1h0c2 2 1 4 0 6 0 1 0 1-1 1h0v-2h-1 0l-3-6 3-3z" class="K"></path><path d="M439 373c-1-1-1-2 0-3h-1v-1c0-1-1-2-2-2v-1l-1-1v-1l1-1c1 1 1 2 2 3 2 2 0 4 1 5h1 2 0 1c1 1 0 2 1 2v5h0-1v-1h-2l1-2-3-3v1z" class="I"></path><path d="M430 454c0-1 0-2 1-3 1 1 0 3 1 4 1-2 1-3 0-5h1c1 2 1 4 2 5v-5h0c1 1 1 3 2 4 0 2 1 2 1 4h0c-1 0-1-1-1-1h-1v1h-1v-2c-1 1-1 2-2 2 0 0 0-1-1-2h0l-2-2z" class="G"></path><path d="M342 483h0c0 2-2 5-3 6h-1v-2c1-1 1-1 2-3h-1c-2 2-3 4-5 5 1-3 4-5 6-8v-1c0-1 1-3 2-4v1l-1 3c0 1 1 1 1 3z" class="f"></path><path d="M491 511c0-1-1-3 0-4 1 0 1 1 1 2v1l-1 1v4l-1 1c1 2 1 3 2 4 1 2 2 5 2 7v1c-1-1-2-2-2-3s-1-1-1-2c-1-1-1-2 0-3-1-1-1-1-1-2h0-1-1v-2h2l-1-2v-1l2-2z" class="T"></path><path d="M430 378h1v1c1 0 1 2 1 3h-1v3h-3l-1-1c0-1 0-1-1-2 0-1 0-1-1-2 0-1 0-1 1-1h1 1c1-1 1 0 2 0v-1z" class="C"></path><path d="M405 512h1c2 1 0 4 1 6 0 2 1 5-1 6l-1-1c-1-1-2-4-2-6v-1h2v-4z" class="h"></path><path d="M422 651c1 0 1-1 2-1v1s0 1 1 1v1 1c1-1 2-2 3-2l1 1h0c1 1 1 2 1 4-1 0-1-1-2-1l-1 2c-1 0 0-1-1-1-1-1-2-1-2-1v-1c-1-1-2 1-3 1h0c0-1 1-2 2-3v-1h0l-1-1z" class="Z"></path><path d="M403 498c1-1 2-2 3-2h1c-1 1-1 1-1 2-1 3-1 6-1 9 1 0 1 0 1-1 1 2 0 3-1 5h0-1c0-1 1-1 1-2s0-2-1-2h-1 0v-7-2h0zm15-250h0l1-1c1 1 1 2 1 3l1 2h-1-2-3v1c0-1-1-1-1-2-1-1-2-2-1-3h2 3z" class="a"></path><path d="M455 400h2c2 0 3 1 4 2v1l-1 1-1-1h-1c1 0 1 1 1 1 1 1 0 2 0 2h-2c0-1-1-1-2-2l-3-3v-1c2 0 3 0 4 2l1-1c-1 0-2-1-2-1z" class="f"></path><path d="M430 300c0-1 0-1 1-1 0 3 0 6 3 8 2 2 4 3 6 3h-1c-1 1-1 2-2 1h-3-2c0-1-1-2-1-3 1-1 1-1 1-2l-1-1h0c-1 0-1 1-1 1v1-7z" class="O"></path><path d="M439 605l1-1v1-2h1l2 2c0 1 1 3 2 4l-1 1h3c1 1 2 3 3 4v1h0c-1 0-1 0-1-1h-1l-2-2h0c-2-1-3-3-4-4h-1l1 3h0l-1-2c-2-1-2-2-2-4z" class="d"></path><path d="M355 658l1 1-3 2v1c1-1 1-1 3-1-1 1-5 4-7 6h0c-1 1-2 2-2 4l-1-1c1-2 1-3 1-4 2-4 5-6 8-8z" class="T"></path><path d="M459 329l5-1v1h2v1h1l-4 1c-3 0-6 1-8 1h-4l-1-1 9-2z" class="X"></path><path d="M405 402h1c0-2-1-3-1-5v-7h1v5c0 2 1 5 2 8v1 3h-1v5c-2-1-2-2-2-4l1-1-1-2v-3z" class="D"></path><path d="M356 569l2 2c-1 1-2 2-2 3h0v1h1v1h-1 0l-1 1h-1v-3c-1 1-1 2-2 3l-2 2h0-1v-2c0-2 0-3 1-5h1v4h0l3-3 1-1c0-1 1-2 1-3z" class="Q"></path><path d="M542 558c1-1 1-2 2-2 1 1 1 2 1 2 1 2 1 4 1 6l-2 4h0l-1-2c0-1 0-2-1-3v-5z" class="Z"></path><path d="M428 255l2-3v3 2 1l-1 2c0 1-1 1-2 2v1l-1 1s0-1-1-2h1v-2-5l1-2c1 1 1 1 1 2z" class="C"></path><path d="M428 255l2-3v3 2 1l-1 2c0-1 0-3-1-4v5l-1-1c-1-2 0-4 0-5h1z" class="Q"></path><path d="M408 360c-1 0-1-1-2-1h-2v-1h0c2-2 6-2 8-2 2-1 5-1 7 0-2 0-2 0-3 1h-2l-1-1h0c-1 0-1 1-1 1h-1v4h-2l-1-1z" class="R"></path><path d="M408 360c0-1 1-2 2-3v1 2l-1 1-1-1z" class="N"></path><path d="M406 506c0 1 0 1-1 1 0-3 0-6 1-9 0-1 0-1 1-2v3l2-2v3c0 1 0 1-1 2h1v3c1 2 1 3 1 5h0c-1-2-2-3-3-4v-2h-1v2z" class="U"></path><path d="M407 499l2-2v3c0 1 0 1-1 2 0-1 0-2-1-3z" class="c"></path><path d="M394 253h1 0v1h1v-1c1 0 1 0 1 1h1s1 0 1 1v2h1v-2c1 1 1 2 1 3s0 2-1 3v-1l-1 1-1-1c-1 0-2-1-3-2 0-1-1-2-1-3v-2z" class="L"></path><path d="M349 326l-1-2h-1v-2c-1 0-1-1-1-1 0-1 1-1 1-1 1 0 2-1 2-1h1v1l1 2s1 1 1 2c1 0 1 1 1 2 2 1 3 0 4 1h-5 0c-1 0-2-1-3-1z" class="h"></path><path d="M494 646c2 0 5-1 7-2 1 0 5-2 6-1-1 1-2 1-3 2-3 1-6 2-10 3v1l2 1c-3 2-5 1-8 1h0c1-2 4-1 5-2l-1-1c1 0 2-1 2-1v-1z" class="K"></path><path d="M406 251h1c1 0 1 0 2 1l-1 2h0c-1 0-3 2-3 2 0 2 0 4 1 5v1c-2 0-2 0-3-1l-1-1h0v-5c1 0 1 0 2-1 0-1 0-1 1-1 0-1 1-1 1-1v-1z" class="C"></path><path d="M368 282c1 1 3 1 3 2 1 2 2 3 4 4l2 3h0c-1 0-1 1-2 1l-4-4c-1 0-1 0-2-1h0c-1 0-1 0-2-1h-1l-1-1-1-1h1c1 0 2 1 3 1h1l-2-2v-1h1z" class="Q"></path><path d="M391 312h0c2 1 4 0 6-1-1 1-2 2-4 2-1 2-3 3-5 4v1h0v1c-2 1-5 1-7 1h-1v-1c1 0 1 0 1-1h3c1-1 2-1 3-2v-1h0c1 0 1 0 2-1 1 0 1-1 2-2z" class="R"></path><path d="M481 644c1 0 2-1 3-1 1-1 2-1 3-1h1 1l1-1h1 0 1c-2 2-4 4-7 5l-4 1h-1v-1h-1v-1c1-1 2-1 2-1z" class="M"></path><path d="M481 647h-1v-1h-1v-1l1 1 1-1h3l1 1-4 1z" class="c"></path><path d="M357 328c2 1 3 1 5 1l5 1h1l-1 1h-3 0v1c1 1 2 0 3 0v1h-2-4-5c0-1 0-1 1-1h1l-1-1h-1l-1-2c1 0 1 0 2-1z" class="P"></path><path d="M419 295v-1h1c-1 2-1 4-2 6h1v4 2l-1-1c-1-1-1-4-2-5 0 1 0 4-1 5-1-1-1-4-1-5s0-1 1-2c1 0 1-3 2-3h1v2h0l1-2z" class="K"></path><path d="M365 334c3-1 7 0 9 0 7 1 15 1 22 3-3 0-8 0-11-1l-23-1c1-1 2-1 3-1h0z" class="T"></path><path d="M428 672h1v-1c1 1 2 1 2 2l1 1h1l2 2v1h1l-1 1c-1 0-2-1-3-2l-1 1-4 5h0c1-1 0-2 1-3v-4-3h0z" class="I"></path><path d="M432 674h1l2 2v1h1l-1 1c-1 0-2-1-3-2h0v-1h-2c0-1-1-1-1-1h3z" class="k"></path><path d="M423 514l1 1c1 1 1 1 2 0l2 2-1 3h0c1-1 1-2 1-3h0 1c1 1 1 1 1 2s-1 2-1 3h-2c-1-1-2-2-2-3h0-1v1c-1-1-1-1-2-1l-1-1c1-1 1-3 2-4z" class="Z"></path><path d="M366 670h1 1c1 1 0 2 1 3-1 0-1 0-2 1v1c0 1-1 1-2 2h1 1 0c0 1 1 1 1 1 0 1-1 1-2 2h-1l-1 1v-1-1h-1v-2h0l-1 1-1-1c1-1 2-2 3-4h0 2c0-1-1-1-1-1 0-1 1-1 1-2z" class="Q"></path><path d="M480 596h1l1 1c1 1 1 2 1 3-1 0-2 1-2 1l-1 1h-2l1 1v1h-2v-1 1 1h0c-1 0-2-1-3-1 0-1-1-1-1-2h1l2 1c0-1-1-1-1-2l1-1c0 1 0 1 1 1v-1-1c1 0 1 1 2 1l-1-2h1 1 0v-2z" class="N"></path><path d="M398 524c2 1 0 3 2 4v-1c1 1 2 3 3 4 0 2-1 2-1 3-1 1-1 1-2 1l-3-3c0-2 0-6 1-8z" class="Z"></path><path d="M551 569c0 2-2 3-3 4h0v1c-1 3-4 7-6 9l-1-1c0-2 1-4 2-6v-1c1 0 2-1 3-2h1c1-2 2-3 4-4z" class="K"></path><path d="M358 415h2s0 1 1 2h1 1c-1 1-1 2-2 3-1 0-2 1-2 2-1 1-2 1-2 2s0 2-1 3l-1 1v-1-2l1-1c0-1 1-1 2-2-1-1-2-2-2-3s1-3 2-4z" class="M"></path><path d="M411 357h1s0-1 1-1h0l1 1h2 3l1 1v3h-1v-1c-1 1-1 1-2 1h-3-3 0v-4z" class="K"></path><path d="M411 361v-4l1 2s0 1-1 2h0z" class="U"></path><path d="M412 649h-1l3-3v-1c2-2 3-4 5-5l1-1c1 0 2-1 3-1v1c0 1-1 1-2 2l-1 2-1-1-3 3v1c1 0 1 1 2 2v1l-2 2c-1-1-2-2-2-3-1 0-1 1-2 1z" class="g"></path><path d="M441 558h-1l-1 1c0 1-1 1-1 1l-1-1h-1-1v-1s1-1 1-2v-2l1-1c1 0 2 0 2 1h1l1-1h0c1 1 1 2 1 3v3h-1v-1z" class="O"></path><path d="M441 553c1 1 1 2 1 3v3h-1v-1-5z" class="U"></path><path d="M310 435l2 3c0-3 0-4 1-6l2 16c-1 1-1 2-1 3v-5l-1-1c-1 1-1 1-2 1 0-1 0-1 1-2v-2h-3c0-1 0-2 1-2h1v-1c-1-1-1-2-1-4z" class="D"></path><path d="M427 682l4-5v2h1v1 1c-1 0-1 1-1 2s-1 1-1 2c1 1 2 1 3 1h4c-3 1-4 2-7 1 0 1-1 0-2 0s-2-1-2-3c0-1 0-1 1-2z" class="Q"></path><path d="M427 682l4-5v2c-1 1-2 2-2 3-1 1-1 2-1 3l1 1v1h1 0c0 1-1 0-2 0s-2-1-2-3c0-1 0-1 1-2z" class="J"></path><path d="M511 598v-1c1 0 1 3 1 4v6h0c1-4 1-9 2-13v-5l1 3c0 4 0 12-2 15 0 1 0 1-1 1s-1 1-2 2h0c1-3 1-8 1-11v-1z" class="B"></path><path d="M372 606h1 2c0 1 0 2 1 2h1c1 1 0 3 0 4s-1 1-2 1c0 0 0-1-1-1l-1 1c0-1 0-1-1-1h-1v-1c0-1-1-2-1-3 1-1 1-2 2-2z" class="M"></path><path d="M372 606c0 2 1 4 0 6h0l-1-1c0-1-1-2-1-3 1-1 1-2 2-2z" class="S"></path><path d="M388 274h-1c-1 0-1 1-2 2v-1h-1v1l-1 1c0-1 0-1-1-1v1h-1s-2-2-2-3v-2h2 0c2-1 3-1 4-1h2l1 1v2z" class="k"></path><path d="M419 300c1-1 1-3 2-4v-2h1v4l-1 1h1c1-2 1-3 1-5 1-1 2-1 2-2v-3c0-1 1-2 2-2 0 1-1 4-1 5s-1 1-1 2c-1 1-1 2-1 4-1 1-2 2-2 4-1 1-1 2-1 3h-1l-1-1v-4z" class="N"></path><path d="M410 344h1c1 1 1 1 1 2 0 2-1 3-2 5-1 0-2 1-3 1v-1h0c-1 1-2 2-3 4h-1v2h-1v-2c0-1 2-3 3-4 2-3 3-5 5-7z" class="K"></path><path d="M545 598h1 0c-1 1-1 1-2 3l1 1 1-1h1 0c0 1-1 2-2 2l-1 1c-2 2-4 2-6 3h-1v-1c1-1 2-1 2-2h0-1v-1c1-1 2-2 3-2 2-1 3-2 4-3z" class="C"></path><path d="M420 602l-1 1c-1 1-2 1-3 1v-2s0-1 1-2h0-1-1c1-1 1-2 1-3s2-3 3-4h3c-1 0-3 1-3 2s-1 1-1 2v1l1-1h1c0 1-1 1-1 2h0c2 0 3 0 5-1 0 1-2 2-2 3-1 0-2 1-2 1z" class="d"></path><path d="M524 520c3-1 8-1 10 0 1 1 2 1 2 2h1c1 0 1 1 1 1 1 1 3 2 3 4l3 3h-1c-1 0-1-1-2-2l-1-1h0c-2-1-5-4-7-5h-3l-1-1c-2 0-3 0-5-1z" class="J"></path><path d="M399 295c4 5 5 10 6 17h0c-1 0-2 0-2-1-1-1-2-2-2-3v-2c0-2 0-4-1-6v-1c0-1-1-2-1-3v-1z" class="D"></path><path d="M420 388h1c0 1 1 1 1 2h2 0c0-1 0 0 1-1 0 1 1 1 1 2 1 1 1 2 2 3h0l-1 1v2c0 1-1 2-2 2v1l-1-3-2-3v-1c0-1-1-2-1-3l-1-1v-1z" class="a"></path><path d="M424 397v-1-2c1 0 1 1 2 1v-1h2l-1 1v2c0 1-1 2-2 2v1l-1-3z" class="C"></path><path d="M372 308v-2c0-2 0-3 1-5 1-1 2-3 3-4h1v4c0 1 2 3 2 4-2 0-2 0-4 1 0 1-1 1-2 1l-1 1z" class="N"></path><path d="M460 440l1-1 1-1c1-1 1-1 2-1l1-2v2c1 0 2 2 3 2 0 1 1 2 2 2 0 1 0 1 1 2l-1 1c-1-1-1-1-2-1h0v2h-1c0-1 0-1-1-2h-1-1c0-2-2-3-4-3z" class="O"></path><path d="M431 677l1-1c1 1 2 2 3 2l1-1c0 2 0 2-1 3 0 1-1 1-1 1 0 1 1 3 1 4 1 0 1-1 2-1h0l1-1 1-1v1c-1 1-1 2-2 3h-4c-1 0-2 0-3-1 0-1 1-1 1-2s0-2 1-2v-1-1h-1v-2z" class="g"></path><path d="M431 677l1-1c1 1 2 2 3 2h-1c-1 0-1 0-1 1h-1-1v-2z" class="Z"></path><path d="M440 386c1-1 1-1 2-1v-1l1-1c1 1 2 1 2 2h1l1 1 1 1 2 2v1h-2l-1 1h0v2h-1c0-1 0-1-1-2l-1-1-2-2-1-1-1-1z" class="L"></path><path d="M434 578c0-2 1-4 1-6 0-1 0-3 1-4 0 1 0 1 1 2h0 0v-2h1v1c0 1-1 4 0 5h0c-1 1-1 2-1 3 2 1 2 4 2 6h0c-2-1-1-3-3-5 0 2 0 3 1 5h-1c-1-2-1-3-2-5z" class="U"></path><path d="M541 521l2 2v1c0 1 3 5 3 6-1-1-1-1-2 0 0 1 1 2 1 3v2c0 1 0 1 1 2h-1 0-1v1h0-1l1-2c0-1-1-2-2-4s-1-3-2-5h0l1 1c1 1 1 2 2 2h1l-3-3c0-2-2-3-3-4 2-1 2 0 3 0h1l-1-1v-1z" class="e"></path><path d="M434 603h1c1 0 1 0 2 1l2 3v2 1l-1 1c-1 0-1 0-1 1-1 0-1-1-2-1h-2v-1c-1-2 0-5 1-6v-1z" class="Z"></path><path d="M437 604l2 3v2l-1 1c-1-1-1-1-1-2-1-1 0-3 0-4z" class="D"></path><path d="M434 604h1 0c0 3 1 4 1 6h0l-1-1-1 1h-1c-1-2 0-5 1-6z" class="G"></path><path d="M365 328c3 0 6 1 10 1l17 2v1h-2l-23-2-5-1h0c1-1 2-1 3-1z" class="T"></path><path d="M434 268l1-1v-2h1c0 2 0 4 2 5v1l1-1 1-1v5h-1v-1c-1 1-1 3-2 3-1 1-3 1-4 2 0-1 1-2 2-3h-1v-4-2h1l-1-1h0z" class="Z"></path><path d="M435 269c0 1 1 2 1 3-1 1-1 2-1 3h-1v-4-2h1z" class="d"></path><path d="M502 548l-1-1 1-1c0-1-1-1 0-2s1-1 2-1h1c0 1 0 1 1 1v1c1 0 1 0 2-1v1h0l1 1c0 1-1 1 0 2h0v2h1l-1 1h0c-2 0-3 0-4-1l-1-1c-1 0-1 0-2-1z" class="C"></path><path d="M399 436h-1v1l-1 1v1h-1c0-1 0-3 1-3v-3c-1 0-1 1-1 1 0 2-1 3-2 4h0c-1-1 0-1 0-1v-1l1-4c0-1 0-3 1-3 1-1 0-2 1-3v1h1 1c1 0 1-1 1-1 0 1-1 2-1 4v6z" class="I"></path><path d="M437 630c0 1 1 1 2 2v-1c1 0 2 1 3 1 0-1-1-2-1-2 2 1 3 2 4 3-1 3 0 3 0 3h-1v1l1 1h-1 0l-2-3-1 1h0l1 1-1 1v-1h-1v1 1l-1-1c0-1-1-1-1-2h-1v-3h0l-1-1s0-1 1-2z" class="P"></path><path d="M480 261c1 0 1 1 1 2-1 7-1 14-7 19h-1c0-2 1-2 1-3 2-1 2-2 3-4 0-1 1-5 1-6-1-2 0-4 0-5l2-3z" class="S"></path><path d="M426 255v5 2h-1c0 1-1 2-1 3s1 1 1 2v1c-1-1-1-2-2-3v-2h-3s-1-1-1-2c-1-1 0-2 0-3l2-1h0v2h0l2-2v2h0c1-1 2-3 3-4h0z" class="Z"></path><path d="M424 570h2c0 1-1 2-2 3 1-1 3-3 4-3 1 1 0 2 0 2-2 3-4 4-5 7 0 1-1 1-1 2h-1c1-1 1-3 1-4 0 1-1 2-2 3v-1c0-1 1-2 2-3v-1c1 0 1 0 2-1-2 0-3 2-4 3l-1 1h-1c0-2 1-3 1-4h1v-1c2-1 2-2 4-2v-1z" class="U"></path><path d="M372 308l1-1c1 0 2 0 2-1 2-1 2-1 4-1l3 3c0 1 0 1 1 1v1c-1 0-3 0-4-1l-1 1c1 0 1 1 2 1h1c-1 1-2 1-3 1-1-1-1-1-1-2-1 0-2 1-3 1h-1 0c0-1 1-1 1-2l-2 1v-2z" class="K"></path><path d="M411 448v3c1 0 1-1 1-1l1-1 1 1h0c1 1 1 2 1 3v1c-1 0-1 1-1 1h-1c-1 3 0 5-1 8-1-2-1-4-2-5-1 0-2 1-2 1v3c-1 0-1 0-1-1h0v-3h1 1v-4h1c1-2 1-4 1-6z" class="Q"></path><path d="M444 290c-2-1-3 0-4-2v-1h0-1c0-2 2-4 3-5l1 1 1-1h1s1 0 1 1h2v1l-2 2h0c-1 1-2 2-2 3v1z" class="Z"></path><path d="M366 289c-1 0-1 0-2-1h2c0-1-1-1-2-2h2 1c1 1 1 1 2 1h0c1 1 1 1 2 1l4 4h-3l-1 1h2c0 1 0 1-1 1-2-1-5-3-7-3h-1l1-1 1-1z" class="K"></path><path d="M366 289c1 0 2-1 2-1 1 0 2 1 3 2h0 0c-1 0-1 0-2 1l-4-1 1-1z" class="a"></path><path d="M419 636h2 1v1c-1 0 0 0-1 1 0 1-1 1-2 1-1 1-3 2-4 4l-3 3-1 1v-1c0-1 2-2 2-4h-1-1c1-1 1-1 1-2l1-1c1-2 4-3 6-3z" class="N"></path><path d="M383 609v1 2h0c-1 0-1-1-2-1 0 1 0 1-1 2 0-1 0-1-1-2v-1c0-1 0-1-1-1 0-1 0-2-1-3v-2l2-2 1 1 1-1 1-1h0 1l-1 1c0 2 1 5 1 7z" class="a"></path><path d="M486 478c1 0 1 0 2 1 0-2-2-3-1-4l4 4v2h1c0-1-1-3-2-4s-2-2-3-4h1l-1-2h0 1c1 1 3 3 3 4v1c1 2 2 3 2 5l-2 1v1c1 1 1 1 1 2h0l-2-1c-1-1-4-4-4-6z" class="N"></path><path d="M393 367h0l2-2c1-2 1-5 2-8 2-2 4-4 5-7 2-3 4-5 6-7h1c0 2-7 10-8 12-2 2-4 5-5 8 0 2-1 3 0 5h-1c0 1-1 3-1 4h-1l1-4-1-1z" class="d"></path><path d="M450 377h0 2c1 0 2-1 2-2h2c2 0 2 0 3 1v1 1l-1 1-1 1h-2 0c1 1 1 2 2 2-2 1-3 0-4 0h0c-1 1-2 0-3-1h1c-1-1-1-1-1-2h0l2 1c-1-2-1-2-2-2v-1z" class="K"></path><path d="M335 383h1c2-2 4-3 5-6l1 3h3l1 3v1c-1 1-1 2-2 3h-1v-1h0l1-1c-1-1-1-1-1-2h-1l-1 2-1-1c1-1 1-1 1-2v-1h0l-3 4v-1l-2 2c-1 0-2-1-2-2s0-1 1-1z" class="a"></path><path d="M344 481l2-1h1c-1 3-4 4-4 7h0l5-5h1l-1 3c1-1 2-1 2-2 1-1 1-1 1-2h1v5c0 1-1 1-1 2-1 0-1 1-1 1l-1 1c0-1 0-2-1-2-1-1 0-2-1-3-2 0-3 3-5 5v-1-2c0-2 1-4 2-5v-1z" class="H"></path><path d="M438 569c1 2 2 6 3 7 0-2 0-3-1-5v-2h0c2 1 2 3 3 5h1 0c-1-1-1-2-2-3v-3l1 1c0 3 2 4 4 7h-1l-2-2c-1 0 0 1-1 1v2h0c-1 1-1 0-1 1 0 0 0 1 1 1v1c-1 0-1-1-2 0 0 0 0 1 1 1l-1 1c-2-2-2-5-3-8h0c-1-1 0-4 0-5z" class="G"></path><path d="M377 680l1-1 1 1-1 1h1 0 1v1c1 1 2 1 2 2 1 1 1 1 1 3-1 0-1 1-2 2h-1v-1c-1 1-1 1-2 1l-1-1h0c-1 1-2 0-2 0v-2c1-1 1-3 2-5h1l-1-1z" class="f"></path><path d="M467 273c2 0 3 0 4-1h2c1 0 1 0 1 1h0l1 1-1 2h0c2-1 2-1 3-1-1 2-1 3-3 4-1 0-2 1-3 0v-1c0 1-1 1-2 1v-1l1-1c-1 0-1 0-2-1h0s1-1 2-1h-4v-1l1-1z" class="N"></path><path d="M474 273l1 1-1 2h0c2-1 2-1 3-1-1 2-1 3-3 4-1 0-2 1-3 0v-1c1 0 2-1 3-2l-1-1c0-1 1-1 1-2z" class="K"></path><defs><linearGradient id="A" x1="370.427" y1="665.054" x2="365.898" y2="665.989" xlink:href="#B"><stop offset="0" stop-color="#656465"></stop><stop offset="1" stop-color="#7b7b7c"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M366 663h5l1 2c-1 1-3 2-3 3h1c0 1-1 1-1 1v1h1c0 1-1 2-1 3-1-1 0-2-1-3h-1-1-2 0v-1c1-1 0-1 0-2h-2l2-2 2-2z"></path><path d="M364 665l2-2c1 1 1 2 1 2 0 1-1 1-1 1h0s-1-1-2-1z" class="K"></path><path d="M364 665c1 0 2 1 2 1l-1 1c0 1 0 1 1 1v1h-2c1-1 0-1 0-2h-2l2-2z" class="f"></path><path d="M452 659c2 1 3 2 4 3l2 2v1l1 1 1 6s-1-1-2-1l-1 1v-2l-1-1h0v-1c-1-1-1-2-2-3l-3-3c-1 0-2-1-2-2h1l1 1h1v-2z" class="T"></path><path d="M458 664v1l1 1v3h-2c0-1-1-3-1-4 1 0 1 0 2-1z" class="H"></path><path d="M452 659c2 1 3 2 4 3l2 2c-1 1-1 1-2 1-3-2-1-2-2-4h-2v-2z" class="D"></path><path d="M448 255c-2 1-3 2-5 3 3-4 7-6 11-10a79.93 79.93 0 0 0 13-13v1c0 1-1 1-1 2h0 2c1 0 2-1 3-1v1h0 0c-2 1-5 1-7 2-2 2-4 5-6 7-2 1-4 3-6 5l-1 1-3 2z" class="G"></path><path d="M458 372c1 0 1 1 2 1l1 1c1 2 1 4 2 5-1 0-1 1-2 1h1v1c-1 1-1 1-2 1v2c-1-1-2-1-3-2-1 0-1-1-2-2h0 2l1-1 1-1v-1-1c-1-1-1-1-3-1 0-1 0-1-1-1h-1v-1l4-1z" class="X"></path><path d="M458 372c1 0 1 1 2 1l1 1c-2 0-4-1-6 0h-1v-1l4-1z" class="f"></path><path d="M527 576c2 2 1 9 0 12v4c-1 0-2 1-2 2l-1 1h0v-4h0l-1 1h-1v-2h-1c1-1 1-2 1-3 0 0 1 0 1 1 1-1 0-5 0-6 1-1 1-1 2-1v9c2-5 2-10 2-14z" class="K"></path><path d="M335 421h1v2h1l1-1h2l1 2h2c0 1 0 1-1 2 1 0 1-1 2-1v1c0 1 0 2-1 3 0 1-1 2-2 2v-1-2c0 1-1 1-2 1-1-1-3-3-3-4l-1-1c0-2-1-2 0-3z" class="k"></path><path d="M421 564c0-1 1-1 1-2l2 1c0 1-1 1-1 1v1h1c0-1 1-1 2-1h0l-3 3h0c1 1 1 1 1 2v1 1c-2 0-2 1-4 2v1h-1 0-1c-1 1-1 2-2 3v1h-1c1-3 2-7 5-8v-1h-1v-1l1-1c0-1 1-2 1-3z" class="K"></path><path d="M382 585c0-1 0-2 2-2h0 2c1 1 2 2 2 3h1c0 1 1 2 1 3 1 0 1 1 1 1l-1 1v-1h-1l1 1c-1 1-2 1-2 2-1 0-1 0-1-1-2-1-4-3-4-5 0-1-1-2-1-2z" class="e"></path><path d="M383 587c1-1 1-1 2-1 2 1 3 3 4 4l1 1c-1 1-2 1-2 2-1 0-1 0-1-1-2-1-4-3-4-5z" class="G"></path><path d="M331 323l18 3c1 0 2 1 3 1h0 5 2c2 0 4 1 6 1-1 0-2 0-3 1h0c-2 0-3 0-5-1h-7l-15-3c-1 0-4 0-5-1h1v-1z" class="V"></path><path d="M352 327h5 2c2 0 4 1 6 1-1 0-2 0-3 1h0c-2 0-3 0-5-1h-7 6v-1h-3-1z" class="f"></path><path d="M363 313h1c1 0 2 0 3 1h0-2v1h1c1 0 2 1 2 2h-1v1h2c0 1 0 1-1 1v1l2 2c-1 0-1 0-1 1 1 0 1 1 2 1v-1h1v1l-1 1h-2 0l2 2v1c-1 0-2 0-3-1v-1l-1-1c0-1 1-1 1-2h-2l1-1h0c0-1-1-1-1-1v-2l-2 1v-1l1-1c-1-1-1-1-2-1h-1v-1c1 0 1 0 2-1-1 0-1 0-2 1h-1v-1l2-2z" class="Q"></path><path d="M477 641h-1l1-1-1-1 2-5c2-3 2-7 3-11 1 5-2 11-3 16 1-2 3-4 4-6h0c0 2-1 4-2 6l4-4h0v1c0 1 1 1 2 2v1l-1 1c-2-1-5 1-7 2 0-1 0-1-1-1z" class="g"></path><path d="M477 641c1-1 2-1 2-1 2-1 3-2 5-4 0 1 1 1 2 2v1l-1 1c-2-1-5 1-7 2 0-1 0-1-1-1z" class="h"></path><path d="M422 601h0c1 0 2 0 3-1v1l-3 2 5-2h1c-1 1-1 1-1 2-1 3-3 3-4 5-2-1-3 2-4 2-1-2 0-2 0-3-1 0-2 1-3 1h-1c1-2 2-3 3-3l2-2v-1s1-1 2-1z" class="K"></path><path d="M406 296c0-1 1-2 2-3l1 1c0-1 0-1 1-2v4h1v-2l2 2v2c1 3 0 5-1 8l-3-3v-1c-1 0-2-5-3-6z" class="Z"></path><path d="M411 300c0 1 0 1-1 1h0c-1-2-1-4-1-6h1v2l1 3z" class="N"></path><path d="M411 296v-2l2 2v2l-2 2-1-3h1v-1z" class="Q"></path><path d="M491 526l2 5-4-5c0 1 1 2 1 3 1 1 1 1 1 2l-1 1-1-1 1 2h-1l-3-3 2 5h0c-1 0-3-4-5-5v-1h0c1-1 2 0 3 0l-2-2h1c0-1 0-1 1-2 0 1 1 1 1 2l1 1-1-3c0-1 1-2 2-2l2 3z" class="N"></path><path d="M423 683h0-4l-4-4c-1-1-2-3-2-5h1 0v-1h-1 0c0-1 1-1 1-1 1-1 1-2 2-3 2-1 4-1 7 0h-3l-1 1-1 1v1c-1 1-1 2 0 3 0 1 1 1 1 2 0 0-1 0 0 1v2l1 2h1 1c0 1 0 1 1 1z" class="Z"></path><path d="M416 677c0-1 0-1-1-2 0-1 0-1 1-2 0-1 1-1 2-2v1h-1v1 1c-1 1-1 2-1 3z" class="Q"></path><path d="M418 672c-1 1-1 2 0 3 0 1 1 1 1 2 0 0-1 0 0 1v2l1 2-1-1h-1c0-1-1-2-2-3v-1c0-1 0-2 1-3v-1-1h1z" class="K"></path><path d="M467 235c4-5 8-9 14-12 1 0 3-1 4-1s2 0 3 1 1 3 1 4c0 5-2 10-3 15h-1l2-11c0-2 1-5 1-7l-1-1h-3v1c-3 0-6 2-9 4-1 1-1 2-2 3s-3 2-4 3l-2 2v-1z" class="V"></path><path d="M529 541l1 1 1-1c2 2 3 4 5 6-1 1-1 1-1 2s-1 0-1 1v4 2c1 1 1 2 1 3h0-1v-1c-1-1-1-3-2-4h0 0v-3l-1-1v-1c-1-2-2-3-3-5h1v1-1c0-1-1-2-3-2v-1c1 0 2 1 2 0h1z" class="J"></path><path d="M439 638l1 1v-1-1h1v1l1-1-1-1h0l1-1 2 3h0l1 1h0c1 2 2 2 2 4h0c1 1 1 2 1 3 1 1 1 2 1 3h0-1 0c-1 0-2-1-2-1h-1l-1-2 1-1-1-1h-1l-1-1-2-2-2-2 1-1z" class="M"></path><path d="M382 585s1 1 1 2c0 2 2 4 4 5l-2 2v1h-1l-3-3c0 1 1 2 1 3h-1v1l-1 1c-1-2-3-2-3-4v-4-1c1 1 1 2 1 3h1v-2c1-1 0-2 0-3h0 1c1 0 1-1 2-1z" class="H"></path><path d="M382 585s1 1 1 2c0 2 2 4 4 5l-2 2v1h-1v-1c0-1 0-3-1-4s-2-2-3-4h0c1 0 1-1 2-1z" class="O"></path><path d="M436 523s1 0 2 1h0c1-1 1-1 1-2 1 0 1 0 2 1h2v-1c1-1 1-1 3-1l1 2s-1 0 0 1c-1 1-2 0-3 2l-1-1h-1 0c0 1 0 0-1 1h0c-1 0-1 1-1 1v1c-1 0-1 0-2 1v-1l-1-1c-1-1-1-1-2-1 0 1 1 1-1 2h0l-1-1c-1-1-2-1-3-2v-1c1-1 1-1 2-1v-1h0l1 1h3z" class="X"></path><path d="M504 638h1c1-1 4-1 6-2 3-1 5 0 8-1h1c-5 3-10 5-16 7-3 1-6 1-8 2h-1c1-1 2-1 4-1l6-3-8 1h0l7-3z" class="Z"></path><path d="M361 543h0c-1 1-2 3-3 4 2 0 5-3 6-4-2 5-3 9-6 14 0-2 1-5 2-7-1 2-2 3-2 4h-1c0-1 2-4 2-6-2 2-3 4-4 6 0-1 0-3 1-4v-2c-1 2-3 4-4 5 0-1 1-4 2-6 3-1 5-3 7-4z" class="d"></path><path d="M374 408h-1 0v3l-1 1c0-1 0-2-1-3v-1c3-2 3-8 6-10 1 1 1 1 1 2l-1 1c0 1 0 1-1 1v2 1c1-1 2-2 2-3 1 0 1 1 2 1-1 1-2 2-2 3-1 1-1 3-1 4l-3 3v1h-1c0-2 1-4 1-5v-1z" class="Q"></path><path d="M376 402v2 1c1-1 2-2 2-3 1 0 1 1 2 1-1 1-2 2-2 3-1 1-1 3-1 4l-3 3v1h-1c0-2 1-4 1-5v-1c0 1 0 1 1 1v-2l1-1c-1-1-1 0-2 0v-1c1-1 1-2 2-3z" class="Z"></path><path d="M493 621h1c0-2 1-3 1-4 1-2 1-4 2-6 0-1 0-1 1-1h0v3h1v-3-1h0c1-1 1-1 1-2 1 1 1 2 1 3h1v1h0c-1 1-1 2-2 4 0 1-1 1-1 2-1 1-1 3-2 4h0c0 1-1 1-1 2h-1v2h0l-1-1c0 1-1 2-1 2h-1v-2h1v-3z" class="I"></path><path d="M408 404h1c0 2 0 4 1 7h0c1 0 1 1 1 1 0 1 1 2 1 3v1c1 0 1 0 2 1h0l3 3v1c1 0 1 1 1 2v1l-1-1c-1 0-2-1-3-1s-1 0-1-1 0-1-1-2c0-1-1-2-2-3h-1c-1-1-2-3-2-4v-5h1v-3z" class="M"></path><path d="M407 412v-5h1l2 9h-1c-1-1-2-3-2-4z" class="E"></path><path d="M401 434c0-2 1-3 1-4s1-2 2-3c1 1 2 4 2 6v1c-1 1 0 1 0 2h0 0v-3c1-1 1-1 0-2v-3h1l3 3c0 1 0 2 1 3-1 0-1 0-2-1v1c1 2 0 3 0 4h-1s-1-1-2-1v2c-1 0-1-1-1-1-1-2-1-4-2-5h-1v3l-1-2z" class="S"></path><path d="M396 492s1 0 1 1c1-1 0-5 1-6v-2l2-5c0-2 0-4 1-5v1c0 3 0 6-2 9h1v6h1v4l-1 1h2-2c0 1 0 1 1 2-1 1-1 2-2 3 0-1 0-3-1-3v3c0 1 0 1 1 2h0c-1 1-1 1-1 2h0-1 0v-3h0c0-2 0-6-1-7 1-2 0-2 0-3z" class="O"></path><path d="M399 485h1v6c0 1 0 2-1 3v-1c-1-2 0-6 0-8z" class="B"></path><path d="M406 296v-1h-1v-2h0v-1c0-1-1-2-2-2l-1 1v-1-2-1l1-1v-1-1c1 0 1-1 1-1v-1l1 1v1c0 1-1 1 0 2l1-1v-1c0-1 1-1 2-1 0 0 1 1 1 2v3 1c0 1 0 2 1 3h0c-1 1-1 1-1 2l-1-1c-1 1-2 2-2 3z" class="M"></path><path d="M412 330h6 4l1 1c-1 0-3 1-4 2s-1 4-2 6l-2-2h0c-1 0-2 0-3-1-1-2-1-3-3-5 1 0 2-1 3-1z" class="B"></path><path d="M387 452v-2h0l2 1v-2h1l1 1h1l-1-2 1 1 2-1h0v2h1v3c0 3 0 9-2 11l-2-2v-2h-1c0 3 2 3 0 6h-1c-1-1-1-2-2-3v-3h1l1-1h1v1h2v1h1c0-3 2-5 1-8h-1v1c-1-1-2-2-2-3-1 1 0 2-1 3l-3-2z" class="d"></path><path d="M405 468c0-1 0-4 1-4 0 1 0 1 1 2l1-1v1l1 1s1 1 2 1c2 1 2 1 4 3 1 1 2 2 2 4h0-1v-1h-1c-2 1-3-1-5 0l-1 2v-1c-1 0-2-4-3-5 0-1-1-1-1-2z" class="C"></path><path d="M409 467s1 1 2 1v1c1 1 1 2 1 4h-1l-1-1c-1-2-1-3-1-5z" class="K"></path><path d="M405 468c0-1 0-4 1-4 0 1 0 1 1 2l1-1v1c0 3 1 6 1 9-1 0-2-4-3-5 0-1-1-1-1-2z" class="U"></path><path d="M436 668l1 2 1 1c0 2 1 3 1 5h0v4 2l-1 1-1 1h0c-1 0-1 1-2 1 0-1-1-3-1-4 0 0 1 0 1-1 1-1 1-1 1-3h-1v-1l-2-2 1-1-1-2h0l1-1h-1v-1c1 0 2 0 2-1h1z" class="T"></path><path d="M435 674v-1-1c1 0 1 0 2 1h1v1s0 1-1 1l-2-1z" class="D"></path><path d="M435 674l2 1v1c1 0 1 0 1 1v1c-1 0-2-1-3-1v-1-2zm-78-402c1 1 3 1 5 2-2 0-6-1-7 0 1 1 2 1 3 2h-1c-1 1-1 2-2 3v-1h-1c-1-1-2-1-2-3h-1 0 0c0 2 2 3 3 4v1h-1c0-1-1-1-2-2-3-4-3-12-2-18l1 1c0 3-1 9 2 12l2 3c0-1-1-3-2-4v-1h1 1c1 0 2 1 3 1z" class="G"></path><path d="M427 665c2 0 3-1 4 0h3c1 1 2 1 3 1 0 1 1 1 1 2 0 0-1 1-1 2l-1-2h-1c0 1-1 1-2 1v1h1l-1 1h0l1 2-1 1h-1l-1-1c0-1-1-1-2-2v1h-1l-1-1c-1-1-1-3-1-4s0-1 1-2z" class="J"></path><path d="M427 665v1h1v1 2l-1 2c-1-1-1-3-1-4s0-1 1-2z" class="I"></path><path d="M431 673v-3h-1-1c0-2 1-2 2-3h0c1-1 1-1 2-1 1 1 2 1 3 2h-1c0 1-1 1-2 1v1h1l-1 1h0l1 2-1 1h-1l-1-1z" class="Q"></path><path d="M505 328c1 0 3 0 4 1-13 3-26 6-39 7-3 1-7 1-10 1-1 0-3 1-4 0 0 0 1 0 1-1 8 0 16-1 24-2l24-6z" class="H"></path><path d="M481 487c1 0 2 1 3 1s2 0 2 1c0 0 1 1 1 2l3 3h2 0v1l-1-1c-1 1-2 1-2 2h0v2 1h0v1l1 1c0 1-1 1-2 1 0 0 0-1-1-2h0v-1c-1-1-3-3-4-3h0v-1c-1-1-1-1-2-1v-2c1 0 2-1 2-1 0-1-1-2-1-2v-2h-1z" class="M"></path><path d="M483 491c1 0 1 1 2 1-1 1-1 1-2 1v1h0v1c-1-1-1-1-2-1v-2c1 0 2-1 2-1z" class="X"></path><path d="M489 496c-1 0-1 0-2 1h0 0 0l-2-2 1-1s1 0 1 1l1-1c-1-1-2-2-3-2v-1-1c1 0 1 1 2 1h0l3 3h2 0v1l-1-1c-1 1-2 1-2 2h0z" class="c"></path><path d="M375 668c1-1 2-2 4-2h1c0 1 0 1-1 1l1 1h1v1h-2v1h2v1h-2l-1 1h2v1h0c-1 1-2 2-3 4-1 1-3 3-4 3l-1-1v-2c0-2 1-4 1-6 1-1 2-2 2-3h0z" class="f"></path><path d="M375 672l1-1h2l-1 1v1h1v1l-2 1-1-1c1 0 1-1 1-1l-1-1z" class="T"></path><path d="M375 668c1-1 2-2 4-2h1c0 1 0 1-1 1l1 1c-1 0-2 1-3 1s-1-1-2-1z" class="Q"></path><path d="M375 672l1 1s0 1-1 1l1 1 1 2c-1 1-3 3-4 3l-1-1v-2c1-1 1-2 1-3l2-2z" class="H"></path><path d="M518 534c3 0 6 0 9 1 1 1 2 2 3 2l3 2c1 1 5 6 5 7v1 3h1v1c-1 0-1 0-1-1s-1-1-1-2h0l-1-1c-2-2-3-4-5-6l-2-1c-5-5-11-2-17-1 1-1 3-2 5-3 3 0 7 0 9 1s5 3 7 4c-1-2-2-3-5-4-1-1-2-1-3-2-6 0-10 0-15 3h0-1c3-2 6-3 9-4h0z" class="T"></path><path d="M362 667h2c0 1 1 1 0 2v1h0 2c0 1-1 1-1 2 0 0 1 0 1 1h-2 0c-1 2-2 3-3 4l-2 3h-1l-1 2v-1c-1 0-1 0-1-1h0v-2-2h1c0-1 1-2 1-2-1-1-1-1-1-2l2-1 3-4z" class="S"></path><path d="M364 673l-1-1h-1v-1l2-1h0 2c0 1-1 1-1 2 0 0 1 0 1 1h-2 0z" class="g"></path><path d="M356 676h1c0-1 1-2 1-2-1-1-1-1-1-2l2-1c0 2 0 3-1 5 0 2 0 3 1 4h-1l-1 2v-1c-1 0-1 0-1-1h0v-2-2z" class="e"></path><path d="M510 527c1-1 1-1 1-2l3-3c2 0 3 0 5 1h1c2 1 8 2 9 4h0c2 1 4 2 5 4-1-1-4-3-5-2-1-1-2-2-3-2-2 0-3-1-5-1-6 0-10 2-15 4 1-2 3-3 4-3z" class="f"></path><path d="M510 527c1-1 1-1 1-2l3-3c2 0 3 0 5 1h1c2 1 8 2 9 4h0c-6-3-12-3-19 0z" class="M"></path><path d="M391 643l1-1 2 2 4 4-5-3c1 2 4 4 5 5v1h-2c2 2 4 5 6 7-2-1-4-2-5-3-2-1-3-3-5-4 0 1 1 2 2 3 2 1 3 2 4 4l-1 1c-1-2-2-3-4-4h0c-1-2-2-3-3-4l-2-2c-1 0-2 0-3-1h0 4c1 1 0 1 1 1h2 0 1c-1-1-2-2-2-3h1 1c0-1-1-2-2-3z" class="d"></path><path d="M444 659c2-1 5-1 7-1l1 1v2h-1l-1-1h-1c0 1 1 2 2 2l3 3c1 1 1 2 2 3v1l-2 1-1-1c-1-1-2-1-3-1l-2-2-1-2c-1 0-2-1-3-1l-2-2c1 0 1 0 1-1v-1h1z" class="Q"></path><path d="M448 666l2-2c1 0 2 1 2 2 1 1 0 1 2 1l-1 2c-1-1-2-1-3-1l-2-2z" class="M"></path><path d="M442 661c1 0 1 0 1-1v-1h1c1 0 1 1 1 1 2 1 3 1 4 3l1 1-2 2-1-2c-1 0-2-1-3-1l-2-2z" class="h"></path><path d="M458 258h4 1v3s1 0 2 1v2h3c1 0 1 1 1 1h0v1c-2 0-5 0-7 1l-6-1 5-1c0-1-2-1-3-1h-2v-1h2v-1h-4l-1-1v-1c2 0 3-1 4-1h1c1 0 1 0 2-1h-2z" class="f"></path><path d="M458 258h4s0 1-1 1l1 1v1h-1-4l1 1h5c-1 1-4 1-5 2h0-2v-1h2v-1h-4l-1-1v-1c2 0 3-1 4-1h1c1 0 1 0 2-1h-2z" class="k"></path><path d="M458 258h4s0 1-1 1l1 1v1h-1c-1-1-3-1-4-1v-1h1c1 0 1 0 2-1h-2z" class="N"></path><path d="M450 329c1 0 2 0 2-1h0c1-1 2-3 3-4h0v-2h1v-1c0-1 1-1 2-2 0 1-1 2-1 3l2 1c-1 0-1 1-1 1 1 1 1 1 2 1 0 1 0 1-1 2s-3 1-4 1h3l1 1-9 2c-4 0-9 1-12 2s-7 1-10 1c5-2 11-2 16-3l6-2z" class="U"></path><path d="M457 322l2 1c-1 0-1 1-1 1 1 1 1 1 2 1 0 1 0 1-1 2s-3 1-4 1h-2c1-1 1-1 2-1 1-2 2-3 2-5z" class="P"></path><path d="M406 395c1-2 1-6 1-8 1 2 1 4 2 7l1-5c1 0 1 0 1-1h0c1 1 2 1 2 3v1 2h0-1c0 1 0 3 1 4-1 0-2 0-3-1v3 1 10c-1-3-1-5-1-7h-1v-1c-1-3-2-6-2-8z" class="N"></path><path d="M410 397c1-1 1-2 1-3h1c0 1 0 3 1 4-1 0-2 0-3-1z" class="f"></path><path d="M409 404c0-1-1-1-1-1 0-3 0-4-1-6v-5h1c0 3 1 6 2 9v10c-1-3-1-5-1-7z" class="D"></path><path d="M392 352l1-1c3-3 6-6 11-8l-3 3c-5 4-9 8-13 12l-9 11c-1 1-1 3-2 4-1 3-3 4-4 7v-1l2-6 5-6 5-7c-1-1-1-1-1-2h1l4-4-1-1 1-2h2l1 1z" class="g"></path><path d="M389 351h2l1 1-7 8c-1-1-1-1-1-2h1l4-4-1-1 1-2z" class="P"></path><path d="M364 297l1-1h1c0-1 0-1-1-1v-1c1 0 4-1 5 0 1 0 1 0 1 1s-1 2-2 3c0 1 0 1-1 1v2c-1 0-1 1-2 2v1l-2 2v1h-2c0 1-1 1-2 1 0-1 0-1 1-2 0-1-1-1-1-2s0-2 1-2c1-1 1-2 1-2 1-1 1-2 2-3h0z" class="k"></path><path d="M362 303c2 0 2 0 4 1l-2 2-2-1v-2zm2-6h0c1 1 2 1 3 1v1c-1 1-3 1-4 1h-1c1-1 1-2 2-3z" class="K"></path><path d="M361 306v-2s1 0 1-1v2l2 1v1h-2c0 1-1 1-2 1 0-1 0-1 1-2z" class="Q"></path><path d="M410 397c1 1 2 1 3 1 1 5 1 10 2 15 0 2 1 4 2 7l-3-3h0c-1-1-1-1-2-1v-1c0-1-1-2-1-3 0 0 0-1-1-1h0v-10-1-3z" class="D"></path><path d="M410 401v-1c1 4 2 8 3 11 0 2 1 4 1 6h0c-1-1-1-1-2-1v-1c0-1-1-2-1-3 0 0 0-1-1-1h0v-10z" class="O"></path><path d="M451 285c1-1 2-1 2-2v-2c1-1 2-2 3-2h1 0l-1 2 3-1 1 1c-1 1-1 1-2 1v1h3 1c-1 1-2 2-3 2v1c1 0 2-1 3-1h0c0 1 0 1-1 1l-2 2c2-1 3-1 4-2h0 2v1s-1 1-2 1c-2 0-3 2-5 2l-1-1v-1-1l-1 1v-1l-2-1-1 1v1c-1 0-1-1-1-1-2 0-3 2-5 3v-1c1-1 3-2 4-3 1 0 1 0 1-1h-1z" class="I"></path><path d="M379 393v-5h0 2s1 0 1-1v-1c1 2 1 2 2 3 0 0 0-1 1-2h2 0v1c-1 1-1 2-1 3h0c1-1 1-2 1-3h1v3 5l-1 2v1l-2 4v1h-1v-3h0c-1 2-2 3-3 5v-2c0-1 1-4 2-6v-1c1-1 1-2 2-3v-1c-2-1-2-1-3-3h-1c-1 1-1 2-2 3h0z" class="Z"></path><path d="M385 403h0c-1-2 0-4 1-6v-4h1v1l1 2-1 2v1l-2 4z" class="H"></path><path d="M469 330l18-3c9-2 19-6 29-6l6 1h2 2v1l-1-1c-10-2-22 2-32 5l-15 3c-3 1-7 2-11 2h0-3-1v-1l4-1h2z" class="G"></path><path d="M362 678l1-1h0v2h1v1 1l1-1h1l2 1h0v2h0v3c-1 1-2 2-4 2h-3c-1-1-1-2-2-3h-1c0-1-1-2-1-3l1-2h1l2-3 1 1z" class="f"></path><path d="M361 685h2c0 1 0 1 1 2h-2 0l-1-2z" class="G"></path><path d="M360 684v-1h0c1-1 2-2 2-3h1c0 1-1 2-1 3 1 0 1-1 2-2 1 1 2 1 2 2 1 1 1 1 1 2h0c-2 0-2 1-3 2-1-1-1-1-1-2h-2 1l-2-1z" class="T"></path><path d="M362 678l1-1h0v2h1v1h-1-1c0 1-1 2-2 3h0v1h0l-1 1h-1c0-1-1-2-1-3l1-2h1l2-3 1 1z" class="d"></path><path d="M361 677l1 1c-2 1-3 2-3 4l1 2-1 1h-1c0-1-1-2-1-3l1-2h1l2-3z" class="L"></path><path d="M353 317h1 0v1h0l3-2v1h1 1l-2 2h3v1l1 1h0c0 1 0 1 1 2h-1l1 1v1s0 1 1 1c-1 1-2 0-3 0h-1v1h-2c-1-1-2 0-4-1 0-1 0-2-1-2 0-1-1-2-1-2 0-1 1-2 2-2l-1-1 1-1v-1z" class="Q"></path><path d="M357 321h3v1l-1 1-2-1v-1z" class="N"></path><path d="M353 320l1-1c1 1 0 1 1 2h1c0 1-1 1-1 2h1c1 0 1 1 2 1v1c0 1 0 1 1 1v1h-2c-1-1-2 0-4-1 0-1 0-2-1-2 0-1-1-2-1-2 0-1 1-2 2-2z" class="X"></path><path d="M433 582h-1 0v-5-2h0c-1 2-1 6-2 8h-1c-1-1-1-1-1-2h-1v1h-1v-3c-1 1-1 2-2 3-1-1 0-2 0-4 1-2 4-5 6-6h0v1 1c1-2 1-4 2-6l1 1c1 2 1 6 1 8s0 3-1 5z" class="V"></path><path d="M345 312l-1-1 2-2h-2 0c0-1 1-1 1-2h-1 0c0-1 1-1 1-2h-1v-1c0-1 0-1-1-1l1-2c-2-2 0-2 0-4 0 1 0 1-1 1v-3c1-1 2-1 3-2l1 1c-1 0-1 1-1 2l1-1 1 1-1 2h0c1 0 1-1 2-1v1l-2 2h2l2 2c-1 1-1 1-2 1h0c0 2 0 3-1 4l1 1v1h-2 0 0c1 1 1 1 1 2h-1c-1 0-2 0-2 1z" class="N"></path><path d="M349 300l2 2c-1 1-1 1-2 1h0 0-3v-1c1 0 2-1 3-2z" class="e"></path><path d="M419 670l1-1h3c1 1 2 2 2 3 1 2 1 3 1 5v1h-1l-1 1c0 1 0 2-1 2 0 1-1 1-1 1h-1-1l-1-2v-2c-1-1 0-1 0-1 0-1-1-1-1-2-1-1-1-2 0-3v-1l1-1z" class="g"></path><path d="M419 670l1-1h3c1 1 2 2 2 3-1 0-1 0-2-1h0-3 0l-1-1z" class="Q"></path><path d="M419 670l1 1c-1 0-1 0-2 1l1 1h0 4v1h-1-2c0 1 0 2-1 3 0-1-1-1-1-2-1-1-1-2 0-3v-1l1-1z" class="N"></path><path d="M423 674h1c0 1 0 2 1 3v1l-1 1c0 1 0 2-1 2 0 1-1 1-1 1h-1-1l-1-2v-2c-1-1 0-1 0-1 1-1 1-2 1-3h2 1z" class="g"></path><path d="M423 674h1c0 1 0 2 1 3v1l-1 1h-1c-1 0-2 0-2-1-1-1 0-1 0-2v-1l1-1h1z" class="U"></path><path d="M444 622c2 0 3 2 5 4h0-1-1 0c1 1 1 1 1 2h-1v1h0v1h-1l-1 1-1-1h-1c1 2 2 3 4 3h0l1 1-1 1c0-1-1-1-2-2s-2-2-4-3c0 0 1 1 1 2-1 0-2-1-3-1v1c-1-1-2-1-2-2s0-1-1-2v-1-1c1 1 2 2 3 2h0l-2-2 1-1c1 1 1 1 2 1l-1-2c2-1 1 1 3 1-1-1-1-1-1-2h2l1-1z" class="N"></path><path d="M381 390h1c1 2 1 2 3 3v1c-1 1-1 2-2 3v1c-1 2-2 5-2 6v2 1c-1 2-1 3-3 5v1h-1s-1 0-1-1h1l-1-1-2 2 3-3c0-1 0-3 1-4 0-1 1-2 2-3-1 0-1-1-2-1 0 1-1 2-2 3v-1-2c1 0 1 0 1-1l1-1c1-1 1-2 1-3l1-1h-1v-3h0c1-1 1-2 2-3z" class="C"></path><path d="M378 412c0-1 0-1-1-1l2-3s1-1 2-1c-1 2-1 3-3 5z" class="J"></path><path d="M381 390c0 1 0 2 1 4 1 1 1 1 1 2 0 2-1 3-2 4 0 1 0 2-1 3-1 0-1-1-2-1 0 1-1 2-2 3v-1-2c1 0 1 0 1-1l1-1c1-1 1-2 1-3l1-1h-1v-3h0c1-1 1-2 2-3z" class="N"></path><path d="M381 390c0 1 0 2 1 4l-1 2h-1-1v-3h0c1-1 1-2 2-3z" class="X"></path><path d="M403 402c1 1 1 2 1 3s0 1 1 2h0v1c0 2 0 3 2 4 0 1 1 3 2 4h1c1 1 2 2 2 3 1 1 1 1 1 2-2-1-2-2-3-3s-1-1-2-1c-1-1-3-2-4-3h-1 0c-1 0-1 1-1 1-1 1-1 1-2 1-2-1 0-6-1-8 0-1 1-4 2-5h1l1-1z" class="J"></path><path d="M401 403h1v3c-2 3-1 6-2 9v1c-2-1 0-6-1-8 0-1 1-4 2-5z" class="N"></path><path d="M403 402c1 1 1 2 1 3s0 1 1 2h0c-1 1-1 4-2 6h0c-1-2-1-5-1-7v-3l1-1z" class="B"></path><defs><linearGradient id="C" x1="387.145" y1="314.08" x2="385.668" y2="310.453" xlink:href="#B"><stop offset="0" stop-color="#514f52"></stop><stop offset="1" stop-color="#666563"></stop></linearGradient></defs><path fill="url(#C)" d="M374 311c1 0 2-1 3-1 0 1 0 1 1 2 1 0 2 0 3-1h-1c-1 0-1-1-2-1l1-1c1 1 3 1 4 1 2 2 5 2 8 2-1 1-1 2-2 2-1 1-1 1-2 1h0v1c-1 1-2 1-3 2h-3c-1 0-3 0-3-1l-2-2v-1c-1-1-1-1-2-1v-1-1z"></path><path d="M442 534h0c1 0 1 1 2 1s1 1 2 2h0 1l-1-1 1-1 1 1 1 2c1-1-1-3-1-4 3 2 4 5 6 9-2-1-3-3-5-4l3 7c-2-1-4-2-5-5h0-1c1 1 1 2 0 3v-1h0c-1-2-2-4-2-6v-1c-1 2 0 4 1 6 0 2 0 4 1 6-1-1-2-3-3-4v3l-1 1c0-1-1-3-1-4 0-2-1-5-1-8 1 2 2 3 2 4l1 3c-1-3-1-6-1-9z" class="g"></path><path d="M459 249c0 1 0 1 1 1s1 0 2 1h-4c1 1 3 1 5 1 1 1 3 1 4 1-1 1-1 1-2 1h-1v1c-1 1-4 0-5 1l-1 1v1h0 2c-1 1-1 1-2 1v-1h-3c-2-1-4-1-7-1h0v-2l3-2 1-1c1 0 1 0 2-1h1c1 0 1 0 2-1 0-1 1-1 2-1z" class="d"></path><path d="M465 254c-2-1-3 0-5-1 0-1-2 0-3-1v-1h1c1 1 3 1 5 1 1 1 3 1 4 1-1 1-1 1-2 1z" class="S"></path><path d="M456 254h8v1c-1 1-4 0-5 1l-1 1v1h0 2c-1 1-1 1-2 1v-1h-3v-1c0-1 1-2 1-2h1v-1h-1z" class="Q"></path><path d="M451 253c2 0 3 0 5 1h1v1h-1s-1 1-1 2v1c-2-1-4-1-7-1h0v-2l3-2z" class="M"></path><path d="M448 257h0c2-2 6-2 8-2 0 0-1 1-1 2v1c-2-1-4-1-7-1z" class="Z"></path><path d="M421 564c-1 1-2 2-3 2h0l1-2v-1l-1 1c-1 1-1 2-2 2l-1-1c0-2 0-3 2-5h0l-1-2c1-1 2-2 4-3 1-1 4-2 5-3v1 1h0v1h1 0 2c0 2-2 2-2 4h0c-1 0-1 1-2 1v1s-1 1-2 1c0 1-1 1-1 2z" class="d"></path><path d="M425 554h0v1h1 0 2c0 2-2 2-2 4h0c-1 0-1 1-2 1-1 1-2 1-3 1h-1 0c1-1 1-1 2-1 1-1 1 0 1-1h-3v1h-1c1-1 1-2 1-2v-1c1-1 3-3 5-3z" class="c"></path><path d="M532 563c2 3-1 7 0 10 0 2-1 3-2 5v6c0 2 0 5-1 7 1-1 2-2 2-3h1v1c-1 1-1 2-1 3-1 2-3 3-3 4h-1v-3-1-4c1-3 2-10 0-12 0-2 0-4 1-6l1 6h1 0v-5-1c1-2 1-4 2-5v-2z" class="J"></path><path d="M528 570l1 6v10c0 1-1 2-1 3s0 2-1 3v1-1-4c1-3 2-10 0-12 0-2 0-4 1-6z" class="L"></path><path d="M465 326h0c2 1 4 0 6-1 0-1 1-2 2-3l-1-2h1c1 0 1 0 2 1 0 1-1 1-2 2v1c4 0 7-1 10-2 8-3 15-6 22-12l3-3c-2 3-4 4-6 6-6 5-13 8-21 11l-11 3v1h0c-1 1-1 1-1 2h-2-1v-1h-2v-1l-5 1-1-1h-3c1 0 3 0 4-1 2 0 4 0 5-1h1z" class="g"></path><path d="M464 328s5 0 6-1v1h0c-1 1-1 1-1 2h-2-1v-1h-2v-1z" class="P"></path><path d="M531 550l1 1v3h0 0c1 1 1 3 2 4v1h1 0l1 4h1c1 0 1 1 1 1 0 3 1 6 0 8v1c-1 1 0 2-1 2v1c0 1 0 1-1 2h0c-1-2 0-4 0-6l1-2v-4c-1 2-1 9-3 10-1-1 0-4 0-5l-2 2c-1-3 2-7 0-10l-1-8c0-1-1-1-1-2s0-2 1-3z" class="R"></path><path d="M531 555c2 2 3 7 3 9s-1 5 0 7l-2 2c-1-3 2-7 0-10l-1-8z" class="K"></path><path d="M417 388h2v-1h1v1 1l1 1c0 1 1 2 1 3v1l2 3 1 3c0 1 0 2 1 2 0 1 0 1-1 2h0v5h-1v1c-2-1 0-3-1-5l-1 1-1-1c0-2-1-3-1-4s-1-2-1-3v-1l-1 1v-1c0-1 1-1 1-2s-2-6-3-7v-1l1 1z" class="d"></path><path d="M422 394l2 3 1 3c0 1 0 2 1 2 0 1 0 1-1 2h0v5h-1c0-2 0-3 1-5h0-2c0-2 0-3-1-5 0-1 1-2 0-3v-2z" class="X"></path><path d="M417 388h2v-1h1v1 1l1 1c0 1 1 2 1 3v1 2c1 1 0 2 0 3-2-2-3-5-4-8 0-1-1-2-1-3z" class="L"></path><path d="M351 258c1 0 3 0 5 1h1v2c-1 0-2-1-2-2-1 0-1 0-2 1 1 1 2 2 3 2-2 1-2 0-4 0 2 2 3 2 5 3h1c-2 1-4 0-6-1h-1c2 1 5 3 8 4v1h1 1s1 0 1 1h0-2l2 2h-1 0-1 0-3c-1 0-2-1-3-1h-1-1v1c1 1 2 3 2 4l-2-3c-3-3-2-9-2-12 0-1 0-2 1-3z" class="C"></path><path d="M467 253h5 1v4l-1 1h-1 0c-1 0-1 0-1 1v1h0l-2 2c2 0 3-1 5-1v1c-1 0-2 1-3 2h-2-3v-2c-1-1-2-1-2-1v-3h-1-4 0v-1l1-1c1-1 4 0 5-1v-1h1c1 0 1 0 2-1z" class="S"></path><path d="M468 258h3c-1 0-1 0-1 1v1h0l-2 2h-1c0-2 1-2 1-4z" class="T"></path><path d="M468 255h2c0 1 1 2 1 3h0-3c-1 0-2 0-3-1h0c1 0 2 0 2-1h1v-1z" class="E"></path><path d="M467 253h5 1v4l-1 1h-1c0-1-1-2-1-3h-2v1l-4-1v-1h1c1 0 1 0 2-1z" class="U"></path><path d="M467 253c1 0 2 0 3 1h0c-1 1-1 1-2 1v1l-4-1v-1h1c1 0 1 0 2-1z" class="K"></path><path d="M359 566c1 1 1 2 2 2v2-1-1h2l1 2c0 1-1 1-1 2s-1 2-1 2l4-2 1 1c-1 2-2 4-4 6-1 0-2 1-3 1s-1 0-1-1v-1c-1 1-1 1-1 2h-2c-1-1-1-1-1-2 1-1 1-1 1-2h1v-1h-1v-1h0c0-1 1-2 2-3l-2-2 3-3z" class="U"></path><path d="M361 570v-1-1h2l1 2c0 1-1 1-1 2l-4 3v-1c0-1 1-1 1-2h1 1c-1-1-1-1-1-2z" class="X"></path><path d="M359 566c1 1 1 2 2 2v2c0 1 0 1 1 2h-1-1c-1 0-2 0-2 1-1 0-1 1-2 1h0c0-1 1-2 2-3l-2-2 3-3z" class="C"></path><path d="M359 566c1 1 1 2 2 2-1 1-3 2-3 3l-2-2 3-3z" class="k"></path><path d="M427 397l3 3s0 1 1 2v-2c1 1 2 2 2 3 1 0 1-1 1-2h0c1 2 1 4 2 6h-1l-1-2c-2 1-1 3-3 3l-1-1v-4c-1-1-1-3-2-4 0 4 1 7 3 10h-1-1c-1 0-1 0-1 1 0 0 0 1-1 1v2h-1c0-1 0-1-1-2h0c0 1 0 2-1 3v-3l-1 1v4h0c-1-1-1-1-1-2h0c1-1 0-7 0-8l1-1c1 2-1 4 1 5v-1h1v-5h0c1-1 1-1 1-2-1 0-1-1-1-2v-1c1 0 2-1 2-2z" class="Z"></path><path d="M543 523c1 1 2 1 3 3v1l1-1c1 1 1 1 1 2 3 0 4 1 5 3l1 3h0c0 1-1 2-1 3 0 2 1 4 1 5v5l-1 1v-1c-1-2-2-2-3-4 0-2-1-4-1-6-1-3-2-5-3-7 0-1-3-5-3-6v-1z" class="J"></path><path d="M548 528c3 0 4 1 5 3l1 3h0c0 1-1 2-1 3 0 2 1 4 1 5l-1-1v-2s-1-2-1-3v-1l-4-7z" class="e"></path><path d="M451 285h1c0 1 0 1-1 1-1 1-3 2-4 3v1c2-1 3-3 5-3 0 0 0 1 1 1v-1l1-1 2 1c-1 1-1 2-2 2v1l-1 1c0 1 0 1 1 1l-1 2h0 2v1h0l-3-1c-1 0-1-1-1-2h-1v2h-2l-2-2-1-1c-1 2-2 1-4 1-1 1-3 1-4 2h0c-2 2-4 3-6 5h0c-1 0-1 0-1 1h-1c1-4 5-7 8-8 2 0 5-1 6-1l1-1v-1c0-1 1-2 2-3l5-1z" class="K"></path><path d="M453 288v-1l1-1 2 1c-1 1-1 2-2 2-1 1-2 2-3 2h-2-1c1-1 2-1 3-1h0l2-2h0z" class="X"></path><path d="M446 340h0c3 1 9 1 13 1l18 1c15 0 31-2 45 1 1 1 3 1 4 2h0-1c-10-3-20-2-30-2h-34c-4-1-9-1-13-2-1 0-2-1-3-1h1z" class="H"></path><path d="M355 658c1 0 3-1 4 0 1 0 2 1 2 1 0 1-1 1-1 2 2 0 4-1 5 0v1c-2 1-2 1-3 2 0 0-1 0-1 1-1 0-3 3-4 3v1c0 1-1 1-1 2s0 2-1 3c0 0 0 1 1 2v2c-1-1-2-1-2-2 0 0 0-1-1-1h0c0-1 0-1 1-1v-1h-1l2-2c-1 0-1 0-2-1h1c0-1-1-1-1-2 0 0 1 0 1-1-1 0-1 0-2-1h0l-1 2-2-1c2-2 6-5 7-6-2 0-2 0-3 1v-1l3-2-1-1z" class="k"></path><path d="M352 666c1-2 3-4 6-4v1h0c0 1-1 1-1 2l-2 2v1h1c0 1-2 1-1 2h1l-1 1c-1 0-1 0-2-1h1c0-1-1-1-1-2 0 0 1 0 1-1-1 0-1 0-2-1h0z" class="Q"></path><path d="M352 294l1 1h-1c-1 1-1 1-1 2v1c0 1 1 1 1 2l1 1v2c-1 1-2 1-1 3h0c0 1 0 1-1 2 0 0 1 1 1 2-1 1-1 1-1 2h1l-1 1c1 1 2 1 2 2l-1 1h-2-1l1 2-1 1h-1-3v-1c1-2 0-1-1-2v-3h1v-1c0-1 1-1 2-1h1c0-1 0-1-1-2h0 0 2v-1l-1-1c1-1 1-2 1-4h0c1 0 1 0 2-1l-2-2h-2l2-2v-1c-1 0-1 1-2 1h0l1-2-1-1c1 0 2 0 3-1h2z" class="C"></path><path d="M352 666h0c1 1 1 1 2 1 0 1-1 1-1 1 0 1 1 1 1 2h-1c1 1 1 1 2 1l-2 2h1v1c-1 0-1 0-1 1h0c1 0 1 1 1 1 0 1 1 1 2 2v2h0c0 1 0 1 1 1v1c0 1 1 2 1 3-1 1-2 1-3 0v-1h-1v1h0l-3-1c-2-2-2-4-3-6v-2l1-1c0-2 1-5 2-7l1-2z" class="U"></path><path d="M349 677c1-1 2-1 2-2l1 1c0 2 0 4 1 5v2 1-1l-2 1c-2-2-2-4-3-6l1-1z" class="G"></path><path d="M349 677c1 1 1 1 1 2s2 3 3 4l-2 1c-2-2-2-4-3-6l1-1z" class="d"></path><path d="M352 666h0c1 1 1 1 2 1 0 1-1 1-1 1 0 1 1 1 1 2h-1l-1 1h0l1 1v1c-1 1-1 2-1 3l-1-1c0 1-1 1-2 2l-1 1v-2l1-1c0-2 1-5 2-7l1-2z" class="S"></path><path d="M349 675l1-1h1v1c0 1-1 1-2 2l-1 1v-2l1-1z" class="V"></path><path d="M451 353c-2-2-5-3-7-5-1-1-2-2-4-2-1 0-3-1-4-2h0l6 2c10 3 25 23 29 32l5 13h0-1v3l-1 4c-1-1-1-2-1-3l1-3c-1-1-1-2-2-2h0-1c0-1 1-1 2-2h0c-1-3-1-6-3-8-3-8-8-14-13-19l-6-8z" class="S"></path><path d="M396 671c1 1 2 3 2 4 0 0 0 1-1 1v2c-1 1-2 3-3 4 0 0-1 1-2 1 0 0-1 0-2 1-1-1-1-2-2-3h-2v-1c-1-3 1-5 2-8l1 1h1v-1h1 0 2 2l1-1z" class="N"></path><path d="M396 671c1 1 2 3 2 4 0 0 0 1-1 1v2l-1-1-1-1c-1-1-1-2-2-3h0c1 0 3 3 5 4l-3-5 1-1z" class="R"></path><path d="M388 681l1-1c1-1 1-3 2-4s0-1 1-2l2 3 1-1 1 1 1 1c-1 1-2 3-3 4 0 0-1 1-2 1 0 0-1 0-2 1-1-1-1-2-2-3z" class="K"></path><path d="M396 677l1 1c-1 1-2 3-3 4v-2-1l2-2z" class="k"></path><path d="M392 409l1-2c1-2 0-5 1-6l1-1c1-2 1-4 2-5 0 1 0 4 1 5h0c1 2 0 6 0 8h-1l-3 9-3 3h-1v-1c0-1 0-1-1-1h-1l-4 3h0c2-2 4-4 5-6 1-1 2-4 3-6z" class="D"></path><path d="M395 407c1 0 1 0 1-1v-2-1l1-1c1 1 0 4 0 6l-3 9c-1-2 0-3 1-4v-6z" class="G"></path><path d="M388 418c4-3 5-7 7-11h0v6c-1 1-2 2-1 4l-3 3h-1v-1c0-1 0-1-1-1h-1zm79-116c0-1 1-1 1-1v-1c1 0 3-1 3-1h1c1-1 1-1 2-1h0l1 1-1 1h0c1 0 2 0 2 1s-1 1-2 1v1h1 1c-1 1-2 1-2 2h3 0c0 1-1 1-2 2h0c1 0 2 0 3 1-1 0-1 0-2 1h-1l2 1h0c-1 1-1 1-2 1s-2 1-4 2v-1c0-1-1-1-1-2h-1l-1-1h-2 0l2-2h-1-1v-1s1 0 2-1v-1c-1 1-2 0-3 0h1c1-1 1-1 1-2z" class="C"></path><path d="M399 392c0-1 1-4 2-5h1v2h0c1 1 1 2 2 3l1 10v3l1 2-1 1v-1h0c-1-1-1-1-1-2s0-2-1-3l-1 1h-1c-1 1-2 4-2 5 0 2-1 5-2 7 0-2 1-5 1-7s1-6 0-8v-4l1-4z" class="T"></path><path d="M403 402l-1-1c0-2 0-3 1-5v-4h1l1 10v3l1 2-1 1v-1h0c-1-1-1-1-1-2s0-2-1-3z" class="G"></path><path d="M399 392h1v3 1c1-1 1-3 1-4h1c0 4-2 7-1 11-1 1-2 4-2 5 0 2-1 5-2 7 0-2 1-5 1-7s1-6 0-8v-4l1-4z" class="E"></path><path d="M520 520h4c2 1 3 1 5 1l1 1h3c2 1 5 4 7 5 1 2 1 3 2 5s2 3 2 4l-1 2h1 0s1 1 1 2h0c1 1 1 2 1 4l-1-1-3-6c-1-1-1-3-2-4-2-1-5-3-6-5h0c-2-4-8-5-12-6h-1v-1h-1v-1z" class="C"></path><path d="M520 520h4c2 1 3 1 5 1l1 1v1h0l-9-2h-1v-1z" class="k"></path><path d="M530 522h3c2 1 5 4 7 5 1 2 1 3 2 5s2 3 2 4l-1 2c-2-6-7-12-13-15v-1z" class="T"></path><path d="M554 534l3 3c0 2 0 3 1 5 1 1 1 2 1 3 1 0 1 1 1 2 1 1 0 3 0 4s-1 3-1 4v2c0 1-1 1-1 2l-1-1h0 0c1-1 1-2 1-3v-2c1-2 1-5 1-7-1-1 0-3-2-3 1 4 1 7 0 11h-1v2h-1v-2l-2 3-1-1 1-4c0-2-1-3 0-5v1l1-1v-5c0-1-1-3-1-5 0-1 1-2 1-3h0z" class="L"></path><path d="M553 552v-1h1c0 1 0 3 1 3l-2 3-1-1 1-4z" class="Q"></path><path d="M556 554v-9c0-1 0-2 1-4v2c1 4 1 7 0 11h-1z" class="S"></path><path d="M460 300h1c1-1 2 0 2 0 1 0 2-1 3 0 0 1-1 1-2 2h1 2c0 1 0 1-1 2h-1c1 0 2 1 3 0v1c-1 1-2 1-2 1v1h1 1l-2 2h0 2l1 1-3 1-1 1h3v1h1v1l-2 1s-1 1-1 2h-1v1c2-1 2-2 4-3v1c0 1 0 1-1 1v1h2s-1 2-2 2c0 1-1 1-1 2 1 0 1-1 2-1 0 1-1 3-2 4l-2 1h-1s1-1 1-2h0-2-1c1-1 2-1 2-2h-2l1-1c1 0 1-1 2-2h0-2 0v-1l1-1h-2v-1c2-1 4-1 5-3-2 1-3 2-5 2 0-1 1-2 1-3h-1v-1h2v-1c-1 0-1 0-2-1l1-1s0-1-1-1h2c0-1-1-2-1-2-1 0-1 0-1-1s0-1 1-1v-1h-2l-1-2z" class="N"></path><path d="M522 522c4 1 10 2 12 6h0c1 2 4 4 6 5 1 1 1 3 2 4l3 6 1 1v1h3c1 2 1 4 1 6v1c0 2-1 4-2 6l-1-6-1-2c-2-2-2-5-3-8-1-2-2-5-4-7 0-1-1-2-2-2-1-2-3-3-5-3v1c3 0 5 2 6 5h0c-1 0-2-2-3-2 0-1-1-1-2-1-2-2-6-3-8-4 2-1 3 0 4 0 1-1 4 1 5 2-1-2-3-3-5-4h0c-1-2-7-3-9-4h0l2-1z" class="K"></path><path d="M547 552v-6c1 1 3 2 3 3v2 1c0 2-1 4-2 6l-1-6z" class="g"></path><path d="M550 552l-2 1v-1-2s1-1 2-1v2 1z" class="U"></path><path d="M489 523v-1l1-1v-1h-4c0 1-1 2-1 3h0 0v-2-1c1-1 1 0 1-1v-2l-4-4-1-1c0-1 0-1 1-2h0v-2h1c-1-1-2 0-3-1-1 0-1-1-1-2s1 0 2-1h-1v-1-1h-1c0-1 0-1 1-2l1-1 1 1c1 1 1 2 1 4 2 1 3 3 4 5l1-1c1 1 2 1 2 3h1l-2 2v1l1 2h-2v2h1 1 0c0 1 0 1 1 2-1 1-1 2 0 3 0 1 1 1 1 2l-1 1-2-3z" class="c"></path><path d="M488 508c1 1 2 1 2 3h1l-2 2c-1 0-2-1-2-2-1 0-1-1-1-1-1 0-1 0-1 1v-2h2l1-1z" class="P"></path><path d="M315 448c0 3 1 7 1 10l-1 6v3h1c2 3 3 3 2 6h-2 0l-2-2h-1-1c0-1 0-1-1-2h0l-1-1v-1c-1 3-5 5-7 6l-1-1c2-1 4-2 5-3 2-3 3-7 4-10v-1-3c1 0 2 0 3 1v-5c0-1 0-2 1-3z" class="H"></path><path d="M311 458v-3c1 0 2 0 3 1v4c0 1 0 2-1 3h0c-1 1-2 2-2 3s2 3 2 5h-1c0-1 0-1-1-2h0l-1-1v-1c-1 3-5 5-7 6l-1-1c2-1 4-2 5-3 2-3 3-7 4-10v-1z" class="L"></path><path d="M391 389h1v3c1-1 1-2 2-2h0c0 2 0 3 1 4v2 1c1-1 1-3 2-4h1v2 1 4h0c-1-1-1-4-1-5-1 1-1 3-2 5l-1 1c-1 1 0 4-1 6l-1 2c-1-1-1-2-1-3l1-2v-2l-1 1c-1 2-2 5-4 7 0 1-1 2-2 2v-3l1-1v-2l1-1v-1c0-1 0-1-1-2 1-1 1-2 1-3v-1 1c2-3 3-7 4-10z" class="f"></path><path d="M392 392c1-1 1-2 2-2h0c0 2 0 3 1 4v2 1c-1-1-1-1-1-2h-1v-3h-1z" class="K"></path><path d="M387 410c0-5 2-9 4-14 0 0 1-1 1-2 0 1 0 2 1 2v5 3l-1 1s0 1-1 1l1-2v-2l-1 1c-1 2-2 5-4 7z" class="E"></path><path d="M465 655c3 0 6-1 9-1v1h4c0 1 1 1 1 1h0c2 2 4 1 6 2-2 1-4 1-6 0l-1 1h0c-3 0-6-1-9 1h0v1c1 0 2 0 4 1h0l-1 1c-4 0-9-2-12-4h-1v-1h0c-1 0-2-1-3-2 2-2 6-1 9-1z" class="k"></path><path d="M465 655c3 0 6-1 9-1v1s1 1 1 2h-4c-1-1-1-2-1-2h-5z" class="O"></path><path d="M460 659h0c1 0 1 0 1-1 0 0-1 0-1-1 2-1 6 0 8 0l1 1c-1 1-3 0-3 1h0c1 0 2 0 2 1h-2v1h3c1 0 2 0 4 1h0l-1 1c-4 0-9-2-12-4z" class="Z"></path><path d="M400 448v-2h2v-3h1 0c1 1 1 2 2 3l1-1c1 0 1 1 1 1h1l2 2h1c0 2 0 4-1 6h-1v4h-1-1v3h0c0 1 0 1 1 1h-1-1v2c-1 0-1 3-1 4v-2h-1v-5-5c-1-1-1-2 0-4l-1 1c-1 1-1 1-2 1v-2l-2-2 1-2z" class="k"></path><path d="M410 448h1c0 2 0 4-1 6h-1v-2c0-1 0-3 1-4z" class="d"></path><path d="M400 448l1-1s0 1 1 1v-2c1 0 2 1 2 2v3 1l-1 1c-1 1-1 1-2 1v-2l-2-2 1-2z" class="U"></path><path d="M405 451v-3h1 0c0 4 0 6 1 10v-9h1c1 2 0 6 0 9h-1v3h0c0 1 0 1 1 1h-1-1v2c-1 0-1 3-1 4v-2h-1v-5-5c-1-1-1-2 0-4v-1h1z" class="G"></path><path d="M404 451h1c0 4 0 7 2 10 0 1 0 1 1 1h-1-1v2c-1 0-1 3-1 4v-2h-1v-5-5c-1-1-1-2 0-4v-1z" class="C"></path><path d="M492 485l1-1h0v1l2 2h-1v1h1c0 1 0 1-1 1 0 1 0 1 1 2h1l1 1c1 0 2 1 3 2h0-1v1h0v1c-1 0-1 0-1 1l1 1h0l-1 1v1l1 1v1c-1 0-1-1-2-1h-1-1v1 1l-2 1c-1-1 0-1-2-1l-1 1v4c-1-1-2-2-2-3v-3c1 0 2 0 2-1l-1-1v-1h0v-1-2h0c0-1 1-1 2-2l1 1v-1h0c0-1 0-2-1-3v-4-2h1 0z" class="P"></path><path d="M328 329v-1c0-1-1-3 0-4h2c1 1 4 1 5 1l15 3h7c-1 1-1 1-2 1l1 2h1l1 1h-1c-1 0-1 0-1 1l-2-1c-9 0-18-1-26-3z" class="X"></path><path d="M406 271c0-1-1-3-1-4l1-1c0 1 0 2 1 2l2-1v-2c-1 0-2-2-2-3v-1h-1c0-1 0-1 1-2h2l1-1 1 1 1-1h1 0c0 1 0 1 1 2v-1-1h1v1h1v-2h1c0 1 1 1 2 1h0c0 1-1 2 0 3 0 1 1 2 1 2l1 3h-2c-1 0-1 1-2 1l-1-1v3 3h-1l-1 1v-2c0-1-1-2-2-3v2c-1 0-1 0-1-1h0l-2-1s-1 0-2 1h0c0 1 0 2-1 2z" class="O"></path><path d="M412 268v-1l1-1c1 1 0 2 1 2 0 0 1 1 2 1v3h-1l-1 1v-2c0-1-1-2-2-3z" class="a"></path><path d="M413 391l1-1v-1-1s1-1 2-1v1c1 1 3 6 3 7s-1 1-1 2v1c0 1 0 3 1 4h-1c0 6 1 11 2 17-1 0-1 0-1-1l-1 1-1 2v-1c-1-3-2-5-2-7-1-5-1-10-2-15-1-1-1-3-1-4h1 0v-2-1z" class="S"></path><path d="M413 391l1-1v-1-1s1-1 2-1v1c1 1 3 6 3 7s-1 1-1 2v1c0 1 0 3 1 4h-1c0-1-1-2-2-3-2-3-2-5-3-8zm-1 3h1c1 5 3 9 4 14 0 3 0 8 2 10l-1 1-1 2v-1c-1-3-2-5-2-7-1-5-1-10-2-15-1-1-1-3-1-4z" class="C"></path><path d="M420 263h3v2c1 1 1 2 2 3-1 2-1 2 0 3 0 0 1 0 1 1l1-1v1 2c0 3 1 6-1 8h0v-3l-1 1v1h-1-1-1v-2h-1 0l-2 2-1-5h-1v-3l-1-1v-3-3l1 1c1 0 1-1 2-1h2l-1-3z" class="M"></path><path d="M421 275c1 1 1 2 2 4h-1-1 0v-4z" class="C"></path><path d="M420 274l1 1h0v4l-2 2-1-5c1 0 2-1 2-2z" class="Z"></path><path d="M424 281l-1-3v-1h1v-4h1l1 5v-5l1-1v2c0 3 1 6-1 8h0v-3l-1 1v1h-1z" class="J"></path><path d="M417 267c1 0 1-1 2-1l2 2c1 2 0 3 1 5 0 1 0 1-1 2l-1-1c0 1-1 2-2 2h-1v-3l-1-1v-3-3l1 1z" class="Q"></path><path d="M416 269v-3l1 1h0c0 1 1 1 1 2v2c-1 1-1 1-1 2l-1-1v-3z" class="N"></path><path d="M418 271c1 1 2 2 2 3s-1 2-2 2h-1v-3c0-1 0-1 1-2z" class="a"></path><path d="M349 256c0-4-1-9-1-13-1-7-1-14 1-21 1-2 2-5 4-6 0-1 2-2 3-2 2 0 4 1 5 2 6 7 8 16 14 24 2 4 5 7 8 11 2 2 5 5 6 9-2-1-3-3-4-4-1-2-2-3-3-4l-7-9h-1v-1-1c0-1-2-3-3-4-3-6-5-12-9-18-1-1-1-2-2-3-2-1-3-1-5-1v1c1 0 2 0 4 1h0-3v1c-1 0-2 0-3-1h0c-3 5-4 11-4 16v11l1 3v2c0 2 1 4 1 5l-1 1-1 1z" class="G"></path><path d="M438 671h1l2 2c0-1 1-1 1-1h1c1 1 3 2 4 3h0 0l1 1c0 1 0 2 1 4 0-1 0 0 1-1v-4c1 2 1 4 1 5 1-1 1-1 1-3v3 1c0 1 0 2-2 3h0c-1 1-1 2-2 2s-1 0-1-1l-1 1c-1 1-1 0-1 0h-1c-1 1-2 1-3 0s-1-1-1-3h-1v-1-2-4h0c0-2-1-3-1-5z" class="Z"></path><path d="M444 685v1c-1 0-1-1-2-1 0-3 1-3 2-5v-1c1 0 2 0 2 1l-1-2h1 1v2 1l-1-1-1 1c1 1 1 1 1 2h-1v-2h-1v4z" class="N"></path><path d="M450 675c1 2 1 4 1 5 1-1 1-1 1-3v3 1c0 1 0 2-2 3h0c-1 1-1 2-2 2s-1 0-1-1l-1 1-2-1v-4h1v2h1c0-1 0-1-1-2l1-1 1 1v-1-2h1v-2c0 1 0 2 1 4 0-1 0 0 1-1v-4z" class="d"></path><path d="M450 675c1 2 1 4 1 5l-1 1v1c0 1 0 1-1 2 0-2-1-3 0-4 0-1 0 0 1-1v-4z" class="G"></path><path d="M438 671h1l2 2c0-1 1-1 1-1h1c1 1 3 2 4 3h0 0l-1 2h1c-1 0-3-1-4-1v-1h-2v1h2v3c-1 1-2 3-3 4h-1v-1-2-4h0c0-2-1-3-1-5z" class="h"></path><path d="M333 374h4 1l-1 1v2l1 1-2 2-1 3c-1 0-1 0-1 1s1 2 2 2l2-2v1c-1 2-2 2-4 2v1h3v1h-2c-1 2-3 3-4 4h0c-1 0-1-1-1-2-1 0 0-1 0-2h-1c0-1 0-2 1-2h0v-1h-1c0 1-1 2-1 2l-1-1c1 0 1-1 1-2 0 1-1 1-1 1 0-2-1-3-1-4s0-3 1-4 2-2 3-2 3-1 3-2z" class="Q"></path><path d="M333 374h4 1l-1 1v2l1 1-2 2-1 3c-1 0-1 0-1 1h-1-1-1l-1 1-1-1c0-3 2-3 2-5v-1h-1l-1 1c0-1 1-2 1-3 1 0 3-1 3-2z" class="C"></path><path d="M331 379c1 1 1 1 0 2l-1 4c1-1 2-3 4-4h0c1-1 1-1 2-1l-1 3c-1 0-1 0-1 1h-1-1-1l-1 1-1-1c0-3 2-3 2-5z" class="J"></path><path d="M334 381h0c1-1 1-1 2-1l-1 3c-1 0-1 0-1 1h-1-1c1-1 2-2 2-3z" class="M"></path><path d="M442 661l2 2c1 0 2 1 3 1l1 2 2 2c2 3 2 5 2 9 0 2 0 2-1 3 0-1 0-3-1-5v4c-1 1-1 0-1 1-1-2-1-3-1-4l-1-1h0 0c-1-1-3-2-4-3h-1s-1 0-1 1l-2-2h-1l-1-1c0-1 1-2 1-2 0-1-1-1-1-2h1v-2c2-1 3-2 4-3z" class="T"></path><path d="M444 663c1 0 2 1 3 1l-2 2v1l2 1v1l-1-1h-1c0 1 1 2 1 3-1-1-1-1-2 0l-1-1v-2s1 1 1 0-2-2-3-3v-1c1 0 2 0 3-1z" class="N"></path><path d="M447 675c0-2 2-2 1-3 0-1-1-1-1-2h0c1-1 1-1 1-2 0 0 1 0 1 1s1 2 0 3l2 1c-1 1-1 1-1 2v4c-1 1-1 0-1 1-1-2-1-3-1-4l-1-1h0z" class="B"></path><path d="M442 661l2 2c-1 1-2 1-3 1v1c1 1 3 2 3 3s-1 0-1 0v2l1 1c1 1 2 2 3 4-1-1-3-2-4-3h-1s-1 0-1 1l-2-2h-1l-1-1c0-1 1-2 1-2 0-1-1-1-1-2h1v-2c2-1 3-2 4-3z" class="k"></path><path d="M438 668h1l1 2-1 1h-1l-1-1c0-1 1-2 1-2z" class="I"></path><path d="M442 661l2 2c-1 1-2 1-3 1v1c0 1-1 1-1 1l2 2v1l-1-1h-2-1c0-1-1-1-1-2h1v-2c2-1 3-2 4-3z" class="R"></path><path d="M440 666h-1v-2h2v1c0 1-1 1-1 1zm-80-375h0l2 2v2 2h2c-1 1-1 2-2 3 0 0 0 1-1 2-1 0-1 1-1 2h-1l-1 1-1 1-1 3v1s-1 0-2 1h-1-1l-1 1c0-1 0-1 1-2 0-1-1-2-1-2 1-1 1-1 1-2h0c-1-2 0-2 1-3v-2l-1-1c0-1-1-1-1-2v-1c0-1 0-1 1-2h1l-1-1 1-1c2 0 3 0 4-2h0 2 1z" class="J"></path><path d="M358 302c-2 0-3 0-4-2h0 4 1v2h-1z" class="K"></path><path d="M353 295c1-1 3-1 4-2l-1-1h1c2 0 2 1 4 1h0c-2 2-3 3-5 2h-3-1 1z" class="N"></path><path d="M362 295v2h2c-1 1-1 2-2 3 0 0 0 1-1 2-1 0-1 1-1 2h-1l-1 1c-1-1 0-2 0-3h1v-2h-1l1-1c0 1 0 1 1 0-1-1 0-1 1-2h-1v-1c1 0 1 0 1-1h1z" class="L"></path><path d="M359 300h2v2c-1 0-1 1-1 2h-1l-1 1c-1-1 0-2 0-3h1v-2z" class="M"></path><path d="M352 311v-3l1-1v-2s1-1 2 0c1 0 2 0 2 1l-1 3v1s-1 0-2 1h-1-1z" class="K"></path><path d="M353 311v-3h3v1 1s-1 0-2 1h-1z" class="N"></path><path d="M469 234c1-1 3-2 4-3s1-2 2-3c3-2 6-4 9-4v-1h3l1 1c0 2-1 5-1 7v-3h0 0c0 1 0 2-1 3v1h0l-1 1h-3v1h0c-2 0-3 0-4 1h4l-1 1c-2 0-7 0-10 1-1 0-2 1-3 1h-2 0c0-1 1-1 1-2l2-2z" class="U"></path><path d="M476 233c1-1 4-2 6-2 1 0 3 0 4 1l-1 1h-3v1h0c-2 0-3 0-4 1h-2l-1-1 1-1z" class="H"></path><path d="M469 234v1c2 0 2-2 3-3h3v1h1l-1 1 1 1h2 4l-1 1c-2 0-7 0-10 1-1 0-2 1-3 1h-2 0c0-1 1-1 1-2l2-2z" class="d"></path><path d="M377 373c1-1 1-3 2-4l9-11v2c0 1-1 1 0 2v1l2-2v1 2h0 1l1-1c0 1 1 2 0 4h1l1 1-1 4v1c0 1 0 1-1 2v1h-2c-1 1-1 1-1 2h-1v-2h0c-1 0-1 0-1 1l-1 1v-2s0-1-1 0h-1-1c0-1 1-2 0-3-1 0-1 1-2 2 0 0-1-1-2-1v-1h-2z" class="X"></path><path d="M390 361v1 2h0 1l1-1c0 1 1 2 0 4-1 1-2 3-2 4h-1c0-2 1-4 1-6l-4 4 2-6 2-2z" class="N"></path><path d="M432 601v1l1 1h1v1c-1 1-2 4-1 6-1 0-1 1-1 1v2h-1c-1 1-2 2-3 4l-1 2v-1l-1 1-1-1v-1h0l-2 2s0 1-1 1c0 0 0 1-1 1h1 1 0l-2 3c-1 0-1 0-2 1h0-2c0 1-1 1-2 1h0l2-2c0-1 1-1 1-2h-1l-1 1h-1c0-1 0-1 1-2h0v-1-1h-1-1c1-1 1-1 1-2 2-1 5-3 5-5v-1h1l2-3c1-2 3-2 4-5l2-1v1l-2 3c1-1 3-4 5-5z" class="e"></path><path d="M432 601c0 3-1 5-1 8l-3 3c0-1 1-3 2-4-2 1-3 3-5 4v-1-1c-1 0-1 1-1 1-1 0-1 0-2-1h0c0 1 0 1-1 1h0l2-3c1-2 3-2 4-5l2-1v1l-2 3c1-1 3-4 5-5z" class="d"></path><path d="M422 642c1-1 1-1 1-2 1 0 1 0 1-1h1v2c1 0 1 0 1-1 1 1 1 1 1 2h0c0 1-1 1-1 2h1c0-2 1-3 1-4h1l1 3v-3h1c0 1 1 2 1 2l1 1v1c1 1 1 2 2 2h0l1-2h1 0c1 0 1-1 1-1l2 1h0v-1c1 1 1 1 1 2h-1v1l1 3c-1 0-1 1-1 1l-1 1h0 0-2v1h-2v2l-3-3h0v1c-2 0-2 0-3 1h0l-1-1c-1 0-2 1-3 2v-1-1c-1 0-1-1-1-1v-1c-1 0-1 1-2 1v-1c-1 0-1 0-1 1h-1v-3h-1v2h-1v-1-1c-1-1-1-2-2-2v-1l3-3 1 1 1-2 1 1z" class="X"></path><path d="M430 643v-3h1c0 1 1 2 1 2v4 1l-2-2h0c1-1 0-1 0-2z" class="G"></path><path d="M427 644c0-2 1-3 1-4h1l1 3c0 1 1 1 0 2v-1c-1 1-1 2-3 3v-3z" class="U"></path><path d="M422 642c1-1 1-1 1-2 1 0 1 0 1-1h1v2c1 0 1 0 1-1 1 1 1 1 1 2h0c0 1-1 1-1 2h0v1 1h-1l-1-1v1h-1 0v-1l-1-1h-1v-1l1-1z" class="L"></path><path d="M422 593h0l2-2h1v1h0 1l1-1h0v2h1 1v-1h0s0 1 1 1v2l2 1c1 0 1 0 1-1v-1c1-1 1-1 1-2h0v-3h1c0 2-1 4 0 5h1l1-1h0c1 0 1 1 2 1v1l1-1v-2c2 2 2 3 2 5 0 1 1 2 1 3h0c0 1 1 1 1 1v2h-1v2l-2-2h-1v2-1l-1 1v2l-2-3c-1-1-1-1-2-1h-1-1l-1-1v-1c-2 1-4 4-5 5l2-3v-1l-2 1c0-1 0-1 1-2h-1l-5 2 3-2v-1c-1 1-2 1-3 1h0c0-1 2-2 2-3-2 1-3 1-5 1h0c0-1 1-1 1-2h-1l-1 1v-1c0-1 1-1 1-2s2-2 3-2z" class="c"></path><path d="M386 342h3c1-1 3-1 4 0-1 0-3 0-5 1l-15 6-15 6-12 5c-4 2-10 3-14 6-4 1-6 2-9 5-2 4-4 8-4 12h0c-1 0-2-1-2-2v-2h1v-4-2c0-2 1-4 0-6h2c1-3 0-6 1-9 0-1 0-2-1-3v-1c1-1 0-1 0-2 1-1 1-1 1-2 1 3 1 6 1 9v1h3l1 1v1h1c0 1-1 3-1 5l52-22h-2l-1-1s4-1 5-1l3-1h3z" class="W"></path><path d="M383 342h3c-3 2-5 3-8 3h0-2l-1-1 5-1 3-1z" class="I"></path><path d="M321 358c0-1 0-2-1-3v-1c1-1 0-1 0-2 1-1 1-1 1-2 1 3 1 6 1 9v1 1h1 0 1v1l-1 1c0 1 1 1 1 2l-2 2c0 1-1 1-1 1 0 1-1 3-1 3-1 2-1 3-2 4v-2c0-2 1-4 0-6h2c1-3 0-6 1-9z" class="H"></path><path d="M321 358s0 1 1 1v7c-1 1-1 1-1 2s-1 3-1 3c-1 2-1 3-2 4v-2c0-2 1-4 0-6h2c1-3 0-6 1-9z" class="V"></path><path d="M399 450l2 2v2c1 0 1 0 2-1l1-1c-1 2-1 3 0 4v5h-1v2h0l-1-1c-1 3-1 5-1 7h1l1-5v5h0c-2 2-2 5-2 7v-1c-1 1-1 3-1 5l-2 5v2c-1 1 0 5-1 6 0-1-1-1-1-1 0 1 1 1 0 3 1 1 1 5 1 7h0c-1 2-1 3-2 4l-1-1v-1-1c-1-1-1-3 0-4v-2c-1 0-1 1-1 1l-1-1 1-1v-1c1-1 1-1 1-2l-1-1c-1-1-1-1-1-2v-1-3l3-6v-2c1-1 2-2 2-4 0 0 0-1 1-2v-4-1c1-1 1-2 1-3v-2h0c0-1 0-2-1-3 0-1 1-4 1-5v-4z" class="J"></path><path d="M400 458c1 2 0 3 0 4l-1 2v-2h0c0-1 0-2-1-3l2-1z" class="P"></path><path d="M399 450l2 2v2c1 0 1 0 2-1l1-1c-1 2-1 3 0 4v5h-1v2h0l-1-1v-2h1c-1-2-1-3-1-4h-1c0 1 0 1-1 2l-2 1c0-1 1-4 1-5v-4z" class="f"></path><path d="M392 490h1c0-2 0-2 1-3l2-6c0-1 1-1 1-2v-1c0-1 1-3 2-4h0v3c-1 1 0 2-1 3v2h0v1l-1 1v-1h-1s-1 1-1 2v2c0 1 0 1-1 2v1h1s0-1 1-1c0-1 0-2 1-3h0v1c0 1-1 1-1 1v2 2c0 1 1 1 0 3 1 1 1 5 1 7h0c-1 2-1 3-2 4l-1-1v-1-1c-1-1-1-3 0-4v-2c-1 0-1 1-1 1l-1-1 1-1v-1c1-1 1-1 1-2l-1-1c-1-1-1-1-1-2z" class="C"></path><path d="M396 495c1 1 1 5 1 7h0c-1 2-1 3-2 4l-1-1c1-4 2-7 2-10z" class="H"></path><path d="M373 279l1-1v-1h1c1 0 1 1 2 2h1 0c1 1 2 1 3 1h0 1c1 0 2 1 3 2h1l-1-2-1-1h1 1c1 0 1 0 2-1h1l-1 1 2 2 2 3c1 1 1 2 2 3h-1s-1 0-1 1v1c-1 0-1 0-2-1v1h0v1c3 1 7 2 9 5v1h0c-2-1-3-2-4-3-2-1-5-1-6-2v-1-1c-1 1-2 2-3 2h-1-2c-1 1-4 1-5 0h0l1-1c0-1-1-2-2-3l-2-1v2c-2-1-3-2-4-4 0-1-2-1-3-2h2v-2h1c1 0 1 0 1-1h1z" class="Q"></path><path d="M390 285l2-1c1 1 1 2 2 3h-1s-1 0-1 1l-2-3z" class="O"></path><path d="M390 281l2 3-2 1-2-3c1 0 1 0 2-1z" class="h"></path><path d="M385 280l-1-1h1 1c1 0 1 0 2-1h1l-1 1 2 2c-1 1-1 1-2 1l-3-3v1z" class="L"></path><path d="M378 283l1-1 2 2v-1h1c2 1 3 2 4 3v1h-1l-1 2h0c0-2-2-3-4-4 0-1-1-1-2-2z" class="T"></path><path d="M373 279l1-1v-1h1c1 0 1 1 2 2h1 0c1 1 2 1 3 1h0 0l6 6h-1c-1-1-2-2-4-3h-1v1l-2-2c-2 0-2-1-4-2-1 0-1-1-2-1z" class="C"></path><path d="M368 282h2v-2h1c1 0 1 0 1-1h1c1 0 1 1 2 1 2 1 2 2 4 2l-1 1c1 1 2 1 2 2h0-2 0l2 3v1c-1-1-2-2-3-2l-2-1v2c-2-1-3-2-4-4 0-1-2-1-3-2z" class="M"></path><path d="M378 285l-1 1-3-3h0l1-1c1 0 2 1 3 1 1 1 2 1 2 2h0-2 0z" class="C"></path><path d="M475 253l-1-1-1-1c1-1 1-1 2-1v1c0 1 1 1 2 1h0 3 2c-1 1-1 7-2 8v1l-2 3c0 1-1 3 0 5 0 1-1 5-1 6-1 0-1 0-3 1h0l1-2-1-1h0c0-1 0-1-1-1h-2c-1 1-2 1-4 1 0 0-3 1-4 0 1-1 2-1 2-1 1 0 1-1 2-1h-2-5c1-1 2-1 3-1l4-1h-7v-1c1-1 3 0 5-1h1 0-4c2-1 5-1 7-1v-1h0s0-1-1-1h2c1-1 2-2 3-2v-1c-2 0-3 1-5 1l2-2h0v-1c0-1 0-1 1-1h0 1l1-1v-4h2z" class="Q"></path><path d="M470 264c2 0 3-2 5-1-1 2-4 2-6 3v-1h0s0-1-1-1h2z" class="T"></path><path d="M479 259h1v1 1l-2 3-1-1v-3l1-1h1z" class="b"></path><path d="M477 263l1 1c0 1-1 3 0 5 0 1-1 5-1 6-1 0-1 0-3 1h0l1-2-1-1h0l1-1v1h1v-1-2s-1 0-1-1c2-2 1-3 2-5v-1z" class="a"></path><path d="M475 253l-1-1-1-1c1-1 1-1 2-1v1c0 1 1 1 2 1h0 3 2c-1 1-1 7-2 8v-1h-1-1-4c0 1-1 1-2 1h-2 0v-1c0-1 0-1 1-1h0 1l1-1v-4h2z" class="N"></path><path d="M475 253l-1-1-1-1c1-1 1-1 2-1v1c0 1 1 1 2 1h0 3v1h-1-4z" class="f"></path><path d="M482 252c-1 1-1 7-2 8v-1h-1v-1-1c-1 1-2 1-2 1h-3c0-1 1-1 1-1h0c0-1 0-1 1-1 0-1 0-1 1-2h-1v-1h3 0 1v-1h2z" class="k"></path><path d="M412 268c1 1 2 2 2 3v2l1-1h1l1 1v3h1l1 5c0-1-1-1-2-1v-1h0 1v-1l-1-1h0c0-1 0-1-1-2v-2l-1 1v1c-1 0-1 1-1 1h1v1c-1 1-1 1-1 2h0v2 2 1 1c1-1 1-1 1-2h0 1 2l2 2s1 1 0 2v3h0l1 1v1l-1 2h0-1v1l-1 2h0v-2h-1c-1 0-1 3-2 3-1 1-1 1-1 2 0-1 0-1-1-2h0v-2l-2-2v2h-1v-4h0c-1-1-1-2-1-3v-1-3c0-1-1-2-1-2h1v-1c1 0 1-1 1-1-1 0-1 0-1-1-1 0-2-1-2-2h0v-1c0-2 0-3 1-5 1 0 1-2 2-3h1c0 1 0 1 1 1v-2z" class="b"></path><path d="M416 284c1 1 1 3 2 4v1c0 1 0 2-1 3 0-1 0-1-1-2 0-2-1-4 0-6z" class="Q"></path><path d="M416 283h2l2 2h-1c0 1-1 3 0 4h-1v-1c-1-1-1-3-2-4v-1z" class="O"></path><path d="M411 294c1-1 2-1 3-2 0 2 1 4 1 6-1 1-1 1-1 2 0-1 0-1-1-2h0v-2l-2-2zm-1-12l1-1c1 1 2 1 2 3v1c0 1 1 2 1 3s-1 2-1 3l-1-1c-1-3-2-5-2-8z" class="Z"></path><path d="M413 285c0 1 1 2 1 3h-2 0c0-2 0-2 1-3zm7 0s1 1 0 2v3h0l1 1v1l-1 2h0-1v1c0-1 0-2-2-3 1-1 1-2 1-3h1c-1-1 0-3 0-4h1z" class="K"></path><path d="M418 289h1c0 1 1 2 2 3l-1 2h0-1v1c0-1 0-2-2-3 1-1 1-2 1-3z" class="C"></path><defs><linearGradient id="D" x1="411.854" y1="276.008" x2="408.573" y2="271.101" xlink:href="#B"><stop offset="0" stop-color="#676567"></stop><stop offset="1" stop-color="#7e7d7d"></stop></linearGradient></defs><path fill="url(#D)" d="M412 268c1 1 2 2 2 3v2 2 2l-2 1c0 1 1 2 1 4v2c0-2-1-2-2-3l-1 1h0v-1c-1 0-1 0-1-1-1 0-2-1-2-2h0v-1c0-2 0-3 1-5 1 0 1-2 2-3h1c0 1 0 1 1 1v-2z"></path><path d="M414 277h-2l-1-1c1-1 1-1 2-1h1v2z" class="N"></path><path d="M412 268c1 1 2 2 2 3v2l-1 1-2-1c0-1 1-1 1-2v-1-2z" class="f"></path><path d="M408 272c1 1 1 1 1 2l1 1v4h-1v-3h-1c0 1 0 1-1 2h0v-1c0-2 0-3 1-5z" class="N"></path><path d="M327 362c7-5 15-8 23-10l19-6 7-1h2l-52 22c0-2 1-4 1-5z" class="X"></path><path d="M507 537v-1l2-2c2-2 4-2 6-3 1-1 4 0 5-1h0c1 1 2 0 3 1 1 0 1 0 2-1v-1h-1-1v-1h1 1v1c2 1 6 2 8 4 1 0 2 0 2 1 1 0 2 2 3 2h0c-1-3-3-5-6-5v-1c2 0 4 1 5 3 1 0 2 1 2 2 2 2 3 5 4 7 1 3 1 6 3 8h-2v1 1h-1l-1-2-2 1v3c-1 0-1-1-2-2s-2-2-2-3l1-1c0 1 1 1 1 2s0 1 1 1v-1h-1v-3-1c0-1-4-6-5-7l-3-2c-1 0-2-1-3-2-3-1-6-1-9-1h0c-3 1-6 2-9 4v-1c-1-1-1-1-2 0z" class="C"></path><path d="M537 538v-1h1c1 1 1 2 1 3l-2-2z" class="c"></path><path d="M518 534h-3-1l1-1v-1h1 4v1h0c-1 1-1 1-2 1h0z" class="b"></path><path d="M533 533c1 0 2 0 2 1 1 0 2 2 3 2v1h-1v1h0-1l-6-4v-1c1 1 3 2 4 3l1-1c-1 0-2-1-2-2z" class="M"></path><path d="M542 550c-1-1-2-3-3-5l-3-3c-1-2-4-5-5-5-2-1-3-2-4-4 2 1 3 2 5 3l5 4c1 1 1 2 2 3v-1c1 0 1 0 1 1h1c0 1 0 1 1 1 1 3 1 5 2 7v1h-1l-1-2z" class="U"></path><path d="M346 360l12-5c2 1 4 0 4 2h1l-11 8c-2 2-4 4-6 5-1 1-2 1-2 1h-3 0c1 1 2 1 4 1-2 2-4 4-7 6l-1-1v-2l1-1h-1-4 0l1-1h-1 0-2c0-1 1-1 1-2s2 0 3-1l1-1h0v-1h-1c-1 0-2-2-3-2 4-3 10-4 14-6z" class="c"></path><path d="M342 363h0 2c1 0 1 1 1 1v1h-2 0l1 1c0 1-2 2-3 3h-2v-1c1 0 1 0 1-1h-1l-1-3c1-1 2-1 4-1z" class="f"></path><path d="M346 360l12-5c2 1 4 0 4 2-2 0-5 0-7 2h-1c-1 1-2 2-4 3v-1c-1-1-2-1-2-1h-2z" class="L"></path><path d="M388 293c1-1 3 0 5 0 3 1 5 3 6 6 0 1 1 3 0 4-2 4-4 5-7 6-2 0-4 0-6-1s-5-3-6-6c0-2 0-4 1-6s4-3 7-3z" class="D"></path><path d="M404 461v5h1v2c0 1 1 1 1 2 1 1 2 5 3 5v1c0 1 1 4 1 4 1 1 3 3 3 4 1 0 0 2 0 3-1 1-1 2-2 2l-1 1-1-1c-1 0-1 0-1 1l1 3-1-1v2h1c0 1-1 3 0 3l-2 2v-3h-1c-1 0-2 1-3 2 0-1 0-1-1-2h0-2l1-1v-4h-1v-6h-1c2-3 2-6 2-9 0-2 0-5 2-7h0v-5l-1 5h-1c0-2 0-4 1-7l1 1h0v-2h1z" class="a"></path><path d="M411 489c-1-1-1-1-1-2h1 1 1c-1 1-1 2-2 2z" class="h"></path><path d="M407 490c0-2-1-5-1-7 1 1 2 2 2 3v4l1 3-1-1c0-1-1-2-1-2z" class="S"></path><path d="M412 487c0-1-1-2-1-3-1-1-1-3-1-4 1 1 3 3 3 4 1 0 0 2 0 3h-1z" class="K"></path><path d="M404 491h0c0-2-1-3-1-6h0l2-4c0 4 1 8 1 12l-2-2z" class="V"></path><path d="M404 466h1v2c0 1 1 1 1 2s0 2-1 4v2 4h-1v-2h-1c0-2 0-2 1-2v-2c-1-3 0-5 0-8z" class="X"></path><path d="M407 490s1 1 1 2v2h1c0 1-1 3 0 3l-2 2v-3h-1c-1 0-2 1-3 2 0-1 0-1-1-2l1-1c0-1 1-1 1-2v-2l2 2v-1c0-1 0-2 1-2z" class="M"></path><path d="M404 474v2c-1 0-1 0-1 2h1v2c0 1 0 1 1 1l-2 4h0c0 3 1 4 1 6h0v2c0 1-1 1-1 2l-1 1h0-2l1-1h0l1-1c0-3 1-5 1-8s-1-6 0-9v-1c0-1 0-1 1-2z" class="R"></path><path d="M404 461v5c0 3-1 5 0 8-1 1-1 1-1 2v1c-1 3 0 6 0 9s-1 5-1 8l-1 1h0v-4h-1v-6h-1c2-3 2-6 2-9 0-2 0-5 2-7h0v-5l-1 5h-1c0-2 0-4 1-7l1 1h0v-2h1z" class="g"></path><path d="M402 494h-1v-8c0-1 0-1 1-1l1 1c0 3-1 5-1 8z" class="B"></path><path d="M456 362c0-1 1-1 1-1 5 5 10 11 13 19 2 2 2 5 3 8h0c-1 1-2 1-2 2h1 0c1 0 1 1 2 2l-1 3-1 1 1 1h-1-1-2-1v-1h-1v-1h-1c-1-2-2-3-3-3l-1 1c1 0 2 1 2 2h0-2-1l1 1h-1l-1-1c-1 0-1 0-1 1h-1c0-1 0-1-1-1 0-1 2-1 2-3 0 0 0-1-1-1v-2h1c0 1 0 1 1 2h1c0-1 0-1 1-1-1-2-3-3-3-4v-2h1v-2c1 0 1 0 2-1v-1h-1c1 0 1-1 2-1-1-1-1-3-2-5l-1-1c-1-2-2-2-4-3 0-1 1-1 2-2v-1l1-1c-1-1-2-3-3-4z" class="P"></path><path d="M462 380l2 2c0 1 1 1 2 3h0s-1 0-2 1 0 1-1 1l-1-1h-1c1 1 1 1 1 3v1c-1-2-3-3-3-4v-2h1v-2c1 0 1 0 2-1v-1z" class="J"></path><path d="M462 380l2 2h-1 0c1 1 1 1 1 2l-2 2-1-1c1-1 1-2 1-4v-1z" class="K"></path><path d="M459 366c2 2 2 5 3 7l4 11v1h0c-1-2-2-2-2-3l-2-2h-1c1 0 1-1 2-1-1-1-1-3-2-5l-1-1c-1-2-2-2-4-3 0-1 1-1 2-2v-1l1-1z" class="T"></path><path d="M346 453h-1v-3l2-2c1-1 1 0 2 1 0-1 0-1 1-1 0-1 0-1 1-1 0 1 0 1-1 2h0l1 1 1-1v1 2 1c1-1 2-1 2-1v1h2v1h1l1-1h0v1 2h1 0c0-1 1-1 1-1v1l1 1 1-2c1 1 1 1 1 2 1 0 1-1 2-1l1 1h0c1 0 2 0 2 1h1l1-2h1v1l1 1c1-1 1-2 2-3v-3h0l1-1c1 1 1 2 1 3 1 1 2 3 3 4v-2h1c1 0 1-1 1-2l1 1c1 0 1-1 1-2 1 1 1 1 2 1v-1c1 1 1 1 2 0h0v-1l3 2c1-1 0-2 1-3 0 1 1 2 2 3v-1h1c1 3-1 5-1 8h-1v-1h-2v-1h-1l-1 1h-1v3l-1-1v-1h-1 0c-1 1-1 1-1 2 0 0-1-2-2-2h0v2c-1 0-1-1-1-1-1 1-1 2-1 3h0l-1-1h-1v2h-1v-2h-1v3h-1v-3h-1c-1 1 0 2 0 3h-1v-1c0-1 0-1-1-1 0-1-1-2-1-3v1h-3v1h0l1 1v1 1c-1 0-1 0-1-1-1-1-1-2-1-3h-1 0l-1 1c0-1-1-1-1-2h-2-1c0 1 0 1-1 1v-1l-1-1v-1h-1c-1 1-1 1-2 1v-2h-1v1h-1v-1h0c-1-1-1 0-2 1v-2l-3-1c1-1 2-1 2-2l-2-1h-1l-1-1h-1z" class="X"></path><path d="M374 455v-3h0c1 2 2 3 2 5l-1 1c-1-1-1-2-1-3z" class="G"></path><path d="M486 232h0v-1c1-1 1-2 1-3h0 0v3l-2 11-3 10h-2-3 0c-1 0-2 0-2-1v-1c-1 0-1 0-2 1l1 1 1 1h-2-1-5c-1 0-3 0-4-1-2 0-4 0-5-1h4c-1-1-1-1-2-1s-1 0-1-1c-1 0-2 0-2 1-1 1-1 1-2 1h-1c-1 1-1 1-2 1 2-2 4-4 6-5 2-2 4-5 6-7 2-1 5-1 7-2h0 0v-1c3-1 8-1 10-1l1-1h-4c1-1 2-1 4-1h0v-1h3l1-1z" class="K"></path><path d="M481 236h4v1c-1 0-2 0-3 1h-7c-1 1-3 0-4 0h0v-1c3-1 8-1 10-1z" class="B"></path><path d="M477 243l-12 1h0c2-2 5-2 8-2 1-1 6-2 7-1l-1 1-2 1z" class="G"></path><path d="M474 239c1 0 5-1 7 0-2 2-8 2-10 2l-7 1c3-2 7-3 10-3z" class="F"></path><path d="M466 247h-4-1 0c2-1 5-2 6-2l1-1h11 5l-13 2h-5v1z" class="i"></path><path d="M486 232h0v-1c1-1 1-2 1-3h0 0v3l-2 11-3 10h-2-3 0c-1 0-2 0-2-1v-1s1 0 1-1l-1-1h2l5-1v-1c-3 0-7 0-10 1h-1v-1l13-2h-5l-2-1h0l2-1 2 1c1-1 2-1 3-2v-1c0-1 0-1 1-2h-3c1-1 2-1 3-1v-1h-4l1-1h-4c1-1 2-1 4-1h0v-1h3l1-1z" class="J"></path><path d="M482 234h3v1h-3-4c1-1 2-1 4-1h0z" class="V"></path><path d="M475 250s1 0 1-1l-1-1h2 6l-6 2h6c-2 1-3 1-5 1l-1 1h0c-1 0-2 0-2-1v-1z" class="N"></path><path d="M472 247c3-1 7-1 10-1v1l-5 1h-2l1 1c0 1-1 1-1 1-1 0-1 0-2 1l1 1 1 1h-2-1-5c-1 0-3 0-4-1-2 0-4 0-5-1h4c-1-1-1-1-2-1s-1 0-1-1h3c2-1 3-1 5-2h-1v-1h5v1h1z" class="H"></path><path d="M459 249h3v1c1 0 2 0 3 1h-3c-1-1-1-1-2-1s-1 0-1-1z" class="S"></path><path d="M471 247h1 3c-1 1-5 1-7 1h0c1-1 2-1 3-1z" class="F"></path><path d="M465 251h0c2 0 4-1 6-1 1 1 1 1 1 3h0-5c-1 0-3 0-4-1-2 0-4 0-5-1h4 3z" class="W"></path><path d="M436 347c1-1 1-1 2-1 0 1 1 1 2 2v-1h1c1 1 2 2 3 2 0 1 1 1 1 2 1 0 1 0 2 1h1c0 1 1 1 2 1h0 1l6 8s-1 0-1 1c1 1 2 3 3 4l-1 1v1c-1 1-2 1-2 2 2 1 3 1 4 3-1 0-1-1-2-1l-4 1v1h1c1 0 1 0 1 1h-2c0 1-1 2-2 2h-2 0v1h-1v-1c-1-1-1-1-1-2h0v-2h1l-1-1s-1 0-1-1l-1 1h0-1c-1-1-1-1-2-1v-2h0c0-2-1-3-2-4v-1c-1 0-1-1-1-1-1-1 0-1-1-1 0-1-1-2-1-3-1 0-1-1-2-2h0c-1-2-2-4-3-5 0-1 0-1-1-1 0-1 0-2-1-2 0-1-1-1-1-2h1c1 1 3 2 3 3v1h1v-2c-1 0-1-1-1-1 1 0 4 3 6 4h0c-1-2-4-4-5-6l1 1z" class="P"></path><path d="M454 362c1 1 2 2 3 4h0-2-1-1 0c0-2 0-3 1-4z" class="c"></path><path d="M450 377h-1c1-2 2-5 4-6l1 1v-1c1 0 2 0 4 1l-4 1v1h1c1 0 1 0 1 1h-2c0 1-1 2-2 2h-2 0z" class="R"></path><path d="M441 365c1-1 0-1 0-2h1c1 1 1 3 2 3v-2l-1-1v-1c0-1-1-1-1-2h0c1 1 2 1 2 2s2 2 2 3c0 2 1 2 1 4h-1l1 1c-1 0-1 0-1 1-1 0 0 0-1 1h0c-1-1-1-1-2-1v-2h0c0-2-1-3-2-4z" class="X"></path><path d="M436 347c1-1 1-1 2-1 0 1 1 1 2 2v-1h1c1 1 2 2 3 2 0 1 1 1 1 2 1 0 1 0 2 1h1c0 1 1 1 2 1h0 1l6 8s-1 0-1 1c1 1 2 3 3 4l-1 1c0-1-1-1-1-1h0c-1-2-2-3-3-4l-2-2c0 1 0 1-1 1 0 0-1 0-1-1-1-2-2-2-3-3 0-1 0-1-1-2h0l-1-1c-1-1-2-1-2-2-2-1-3-2-4-3s-2-1-3-2z" class="C"></path><path d="M452 360c-1-1-3-3-4-5 2 1 7 7 8 7 1 1 2 3 3 4l-1 1c0-1-1-1-1-1h0c-1-2-2-3-3-4l-2-2z" class="B"></path><path d="M349 233c0-5 1-11 4-16h0c1 1 2 1 3 1v-1h3 0c-2-1-3-1-4-1v-1c2 0 3 0 5 1 1 1 1 2 2 3 4 6 6 12 9 18 1 1 3 3 3 4v1 1c-2 0-4-1-6-1s-4-1-6 0h0v1l1 1c-2 1-3-1-4 1h-2-1c-1-1-3-1-5-1h-1v1 2l-1-3v-11z" class="G"></path><path d="M365 231c1 0 2 0 2 1 1 0 2 3 3 4l1 2c-1-1-3-1-3-1-2-2-4-2-6-3h4v-1c-1 0-1 0-2-1h1v-1z" class="U"></path><path d="M365 241c1 0 2 1 3 0l-1-1c-1 0-2 0-2-1l-1-1h1c1-1 1-1 2 0 1 2 5 3 7 4v1c-2 0-4-1-6-1s-4-1-6 0h0v1h-1c-1 0-1-1-2-1 2-1 4 0 6-1h0z" class="D"></path><path d="M353 233h-1c0-1 0-4 1-5 1 0 1 0 2-1-1 0-3-1-4-1v-1h3v1c1 1 4 1 6 1h0l2 1v1s0-1-1-1c-1-1-3-1-5 0v1h2c1 1 2 1 3 2h1v1h-1-3 0c0 1 1 1 1 1v1h-2v1h2c-1 1-1 1-2 1v1h-1c-1-1-3-1-4-2v-1c1 0 2 1 3 0-1 0-1-1-2-1z" class="f"></path><path d="M361 231h-5l-1-1 1-1h0v-1 1h2c1 1 2 1 3 2z" class="B"></path><path d="M349 233h4c1 0 1 1 2 1-1 1-2 0-3 0v1c1 1 3 1 4 2h1v-1c1 0 1 0 2-1 2 1 4 1 5 2h-3v1h1v1h-2 0c1 2 3 1 5 2h0c-2 1-4 0-6 1 1 0 1 1 2 1h1l1 1c-2 1-3-1-4 1h-2-1c-1-1-3-1-5-1h-1v1 2l-1-3v-11z" class="H"></path><path d="M355 239h3v1h-2v1c1 0 1 0 2 1h-2v1h2c-1 1-1 1-1 2h-1c-1-1-3-1-5-1h-1v1 2l-1-3 1-1v-1h1c1-1 2-1 3-1v-1h-2v-1h3z" class="g"></path><path d="M351 242h3v1h-1s-1 0-2 1h0-1v1 2l-1-3 1-1v-1h1z" class="e"></path><path d="M349 233h4c1 0 1 1 2 1-1 1-2 0-3 0v1c1 1 3 1 4 2h1l1 1c-1 0-5-1-7-1 1 0 1 0 1 1 1 0 2 0 3 1h-3v1h2v1c-1 0-2 0-3 1h-1v1l-1 1v-11z" class="R"></path><path d="M349 233c0-5 1-11 4-16h0c1 1 2 1 3 1v-1h3 0c-2-1-3-1-4-1v-1c2 0 3 0 5 1 1 1 1 2 2 3 4 6 6 12 9 18 1 1 3 3 3 4v1c-2-1-6-2-7-4l4 1v-1h0l-1-2c-1-1-2-4-3-4 0-1-1-1-2-1h-1l2-2h-1v-2c-2 0-4-1-6-2h-1 1l5 1h0l-2-2h-2c-3-1-6-1-9 0 1 0 4 0 5 1h-2-3v1c1 0 3 1 4 1-1 1-1 1-2 1-1 1-1 4-1 5h1-4z" class="J"></path><path d="M360 224v-1c-1 0-3 0-4-1-2 0-3 0-5-1l7-1c-2 0-5 0-7-1 3 0 5-2 7-1l2 1h0c1 0 1 0 2 1h0c0 2 0 2 1 3v1h-1-2z" class="D"></path><path d="M362 243v-1h0c2-1 4 0 6 0s4 1 6 1h1l7 9c1 1 2 2 3 4h-3l-1-1c-3 0-7-1-10 0 0 0 1 1 2 1 2 0 5 0 6 1h-5v1h-1-1c2 1 4 1 5 3h1v1c-1 0-2 1-3 1-1 1-3 1-5 2h3v1h-6v1h2v1h-5-5c-3-1-6-3-8-4h1c2 1 4 2 6 1h-1c-2-1-3-1-5-3 2 0 2 1 4 0-1 0-2-1-3-2 1-1 1-1 2-1 0 1 1 2 2 2v-2h-1c-2-1-4-1-5-1v-1-3c0-1-1-3-1-5v-2-2-1h1c2 0 4 0 5 1h1 2c1-2 2 0 4-1l-1-1z" class="G"></path><path d="M355 253h0 6 0c0-1-1-1-2-1v-1h1c1 0 2 0 3 1h1l1 1c-2 0-3-1-3 0s0 1 1 1h0c-1 1-2 1-3 1-1-1-2-1-4-1h0c-1 0-2 0-3-1h0 2z" class="D"></path><path d="M377 261h1v1c-1 0-2 1-3 1-1 1-3 1-5 2h0-7c0-1 0-1-1-1 0 0-1 0-1-1 2 1 4 2 7 1 1-1 2 0 3-1 0-1-1-1-2-1h-1l1-1h2c2 1 5 1 6 0z" class="k"></path><path d="M373 252h1l1-1 4 1h3c1 1 2 2 3 4h-3l-1-1h0c-1-2-5-2-6-2-2-1-4-1-5-1v-1c2 0 2 0 3 1z" class="C"></path><path d="M350 249c1-1 1-1 2-1l6 2h0c-2 1-4 0-6 1 1 1 4 1 6 1-1 1-2 1-3 1h-2 0c1 1 2 1 3 1h1c0 1 0 1 1 1h-4v1c1 0 2 1 3 1h0-6v-3c0-1-1-3-1-5z" class="M"></path><path d="M363 258c3-1 4 1 6 0h0c0-1-1-1-1-1-1 0-1 0-1-1h0 2c1 0 2 1 3 1h2v1h-1-1c2 1 4 1 5 3-1 1-4 1-6 0h-2l-1 1h1l-1 1h0-3l-3-1v-1h-1-1c0-1-1-1-1-1v-1c1 0 1 0 1-1h1 2 0z" class="f"></path><path d="M361 261l1-1h2c1 1 2 1 3 1v1c-2 0-3 0-4-1h-1-1z" class="B"></path><path d="M363 258c2 1 4 1 6 2h0l1 1h-2 0c-1-1-2-1-3-1h-1-2l-1 1h-1c0-1-1-1-1-1v-1c1 0 1 0 1-1h1 2 0z" class="V"></path><path d="M362 243v-1h0c2-1 4 0 6 0s4 1 6 1h1l7 9h-3l-4-1-1 1h-1c-1-1-1-1-3-1v1c-2 0-4-1-6-1l-3-1h-1-2l-6-2c-1 0-1 0-2 1v-2-2-1h1c2 0 4 0 5 1h1 2c1-2 2 0 4-1l-1-1z" class="h"></path><path d="M360 248c-3 0-6-1-9-2v-1c2 0 4 1 6 1s4 0 6 1c-1 0-2 0-3 1z" class="G"></path><g class="D"><path d="M352 248c2-1 9 0 11 1h1l-3 1h0-1-2l-6-2z"></path><path d="M364 249l3 1h3c2 0 2 0 3 1v1c-1-1-1-1-3-1v1c-2 0-4-1-6-1l-3-1h0l3-1z"></path></g><path d="M364 249l3 1v1h-3l-3-1h0l3-1z" class="B"></path><path d="M363 247h2c2 0 4 1 6 1s6 2 8 3v1l-4-1-1 1h-1v-1c-1-1-1-1-3-1h0c-1-1-6-1-8-2h-2c1-1 2-1 3-1z" class="G"></path><path d="M363 247h2v1h-3-2c1-1 2-1 3-1z" class="D"></path><path d="M362 243v-1h0c2-1 4 0 6 0s4 1 6 1h1l7 9h-3v-1c-2-1-6-3-8-3s-4-1-6-1h-2c-2-1-4-1-6-1v-1h-1 1 2c1-2 2 0 4-1l-1-1z" class="B"></path><path d="M368 242c2 0 4 1 6 1h1l7 9h-3v-1c-2-1-6-3-8-3s-4-1-6-1h-2c-2-1-4-1-6-1v-1h-1 1 2 1c4 2 9 1 13 2 1 1 3 2 4 2 0 0 0-1 1-1-2-2-5-2-7-3h6c-3-1-6-1-9-3z" class="k"></path><path d="M315 351l3 1-1 1c1 0 1 0 1 1 1 4-1 9 0 13 1 2 0 4 0 6v2 4h-1l-2 1c1 1 1 3 1 4 1 1 3 2 3 3-1 1-3 1-4 2 0 1 0 2 1 2-1 2-2 2-2 3l1 1-1 1v1 3c-1 1 0 3 1 4l2 1h0c-2 2-6 4-7 7 0 1 0 2 1 3l2 2c0 1-1 1-1 1-1 2 0 4 0 6s1 5 1 8c-1 2-1 3-1 6l-2-3v-1l-2-10h0v-2-3c1-2 1-3 0-5-1-3 3-6 3-9 1-1 0-2 0-3 0-3 1-6 1-9 0-4-1-8-3-11-2-4-5-7-8-10l-3-4-3-2h-1v-1-1l-2-1v-1-1c3 0 6 2 9 3l-1-2h2 1c2 0 5-1 7-2 1-1 2-2 2-3 0-3 1-3 2-4l1-2z" class="W"></path><path d="M294 364l4 2v2l-3-2h-1v-1-1z" class="D"></path><path d="M298 366h0s1 1 2 1c0 1 1 3 1 5l-3-4v-2zm2-4h2 1c2 0 5-1 7-2 1-1 2-2 2-3 0-3 1-3 2-4h2v1c-1 0-1 1-1 1-2 1-2 3-2 5-1 1-2 3-3 4 0-2 1-3 2-5v-1c0 2-4 3-5 5v1h-6 0 0l-1-2z" class="B"></path><path d="M317 370l1 1v2 2 4h-1l-2 1c-1-3-2-5-5-8h3c2-1 3-1 4-2z" class="X"></path><path d="M315 351l3 1-1 1c1 0 1 0 1 1 1 4-1 9 0 13 1 2 0 4 0 6v-2l-1-1c-1 1-2 1-4 2h-3l-3-4-3-1v-1h3c1 0 2-1 3-2s2-3 3-4c0-2 0-4 2-5 0 0 0-1 1-1v-1h-2l1-2z" class="L"></path><path d="M312 368l1-2h0c1 1 1 2 1 3h1s0-1 1-1h0c1 1 1 1 1 2-1 1-2 1-4 2h-3l-3-4h5z" class="F"></path><path d="M307 368h5c-1 1-2 1-2 3 1 0 2 0 3 1h-3l-3-4z" class="J"></path><path d="M315 351l3 1-1 1c1 0 1 0 1 1 1 4-1 9 0 13 1 2 0 4 0 6v-2l-1-1c0-1 0-1-1-2h0c-1 0-1 1-1 1h-1l1-1c-1-1-1-3-1-4 1-3 0-6 1-9 0 0 0-1 1-1v-1h-2l1-2z" class="E"></path><path d="M316 368c0-1 1-2 1-3v-11h1c1 4-1 9 0 13 1 2 0 4 0 6v-2l-1-1c0-1 0-1-1-2z" class="e"></path><path d="M304 429l2-2v-1l2-2 2 10v1c0 2 0 3 1 4v1h-1c-1 0-1 1-1 2h3v2c-1 1-1 1-1 2 1 0 1 0 2-1l1 1v5 5c-1-1-2-1-3-1v3 1c-1 3-2 7-4 10-1 1-3 2-5 3l1-2h-2v-1c1-2 1-3 1-4-1-2-3-2-4-3-1-2-2-3-2-5l-2-2c-1 0-2 0-3 1h0c-1 0-2 0-2-1l3-1c1-2 0-3-1-4v-1c2-1 2-2 3-3-1 0 0-1 0-2h0l1-1c-1 0-2 0-3-1v-2-1l-3 1c0-1 0-2 1-4l1-2c2 0 1 1 3 2h1c2-1 2 0 4 1h0 2v-3c1 0 1 0 2-1h0v-1-1-1c1 1 1 1 2 1v-1c0-1-1-1-1-1z" class="i"></path><path d="M308 460v1c-1 2-1 4-2 6l1 2c-1 1-3 2-5 3l1-2 1-2c1-1 2-4 1-6v-1c1 0 2 0 3-1z" class="H"></path><path d="M308 458c1 0 2 0 3 1-1 3-2 7-4 10l-1-2c1-2 1-4 2-6v-1-2z" class="E"></path><path d="M291 450l3 1h2c-1 1-1 1-1 2 1 1 3 2 3 3l-2 1-2-2c-1 0-2 0-3 1h0c-1 0-2 0-2-1l3-1c1-2 0-3-1-4z" class="a"></path><path d="M294 444h0v1l2 1v-1c1 1 2 2 3 2l1-1h0v2s1 1 2 1v3c-1-1-2-1-3-1v1h0l-1-1-1-1-1 1h-2l-3-1v-1c2-1 2-2 3-3-1 0 0-1 0-2z" class="d"></path><path d="M294 446c1 1 1 1 1 3h1c1 0 1 0 2 1l1 1h-1l-1-1-1 1h-2l-3-1v-1c2-1 2-2 3-3z" class="M"></path><path d="M300 446l2-1c0 1 1 3 2 4l3 1h0l1 1v-3l1 1v2c-1 1-2 3-2 5h0l1 1c-1 0-2 1-3 1h0c-1-2-1-5-3-6v-3c-1 0-2-1-2-1v-2z" class="D"></path><path d="M304 449l3 1h0v2l-2 1-1-4z" class="F"></path><path d="M300 446l2-1c0 1 1 3 2 4l1 4v5c-1-2-1-5-3-6v-3c-1 0-2-1-2-1v-2z" class="e"></path><path d="M307 449c-1-2-1-5-2-7h-1l1-1c1 1 2 1 4 1h3v2c-1 1-1 1-1 2 1 0 1 0 2-1l1 1v5 5c-1-1-2-1-3-1v3 1c-1-1-2-1-3-1v-1l-1-1h0c0-2 1-4 2-5v-2l-1-1v3l-1-1h0v-1z" class="C"></path><path d="M314 446v5 5c-1-1-2-1-3-1v3 1c-1-1-2-1-3-1v-1l-1-1c1 0 2-1 3-1v-1l1-1c1 0 1 0 2-1v-6h1z" class="I"></path><path d="M310 455c1 1 1 2 1 3v1c-1-1-2-1-3-1v-1l-1-1c1 0 2-1 3-1z" class="S"></path><path d="M307 449c-1-2-1-5-2-7h-1l1-1c1 1 2 1 4 1h3v2c-1 1-1 1-1 2 1 0 1 0 2-1l1 1h-1c-1 1-1 3-2 4 0-2 0-3-1-4l-1 5v-2l-1-1v3l-1-1h0v-1z" class="K"></path><path d="M304 429l2-2v-1l2-2 2 10v1c0 2 0 3 1 4v1h-1c-1 0-1 1-1 2-2 0-3 0-4-1l-1 1h1c1 2 1 5 2 7v1l-3-1c-1-1-2-3-2-4l-2 1h0l-1 1c-1 0-2-1-3-2v1l-2-1v-1l1-1c-1 0-2 0-3-1v-2-1l-3 1c0-1 0-2 1-4l1-2c2 0 1 1 3 2h1c2-1 2 0 4 1h0 2v-3c1 0 1 0 2-1h0v-1-1-1c1 1 1 1 2 1v-1c0-1-1-1-1-1z" class="Y"></path><path d="M308 434h0s1 0 1 1l1-1v1c0 2 0 3 1 4v1h-1c-1-2-2-3-4-4h0 0 2 1l-1-2z" class="R"></path><path d="M301 443c0-1 0-4 1-4h0v4c1 1 2 1 2 3 1 1 1 2 2 3h1v1l-3-1c-1-1-2-3-2-4-1-1-1-1-1-2z" class="D"></path><path d="M295 443c1-1 1-2 3-2 1 0 2 1 3 2h0c0 1 0 1 1 2l-2 1h0l-1 1c-1 0-2-1-3-2v1l-2-1v-1l1-1z" class="J"></path><path d="M296 445l2-1c1 0 1 1 2 2l-1 1c-1 0-2-1-3-2z" class="G"></path><path d="M304 429l2-2v-1l2-2 2 10-1 1c0-1-1-1-1-1h0-1s-1-1-1-2h1c1-1 0-2 0-4h-1v1 3c-1 1-1 1-3 0v-1-1c1 1 1 1 2 1v-1c0-1-1-1-1-1z" class="J"></path><path d="M437 294h0c1-1 3-1 4-2 2 0 3 1 4-1l1 1 2 2h2v-2h1c0 1 0 2 1 2l3 1h1c1 1 2 1 3 2l1 2v1l1 2h2v1c-1 0-1 0-1 1s0 1 1 1c0 0 1 1 1 2h-2c1 0 1 1 1 1l-1 1c1 1 1 1 2 1v1h-2v1h1c0 1-1 2-1 3 2 0 3-1 5-2-1 2-3 2-5 3v1h2l-1 1v1h0 2 0c-1 1-1 2-2 2l-1 1h2c0 1-1 1-2 2h1 2 0c0 1-1 2-1 2-1 1-3 1-5 1 1-1 1-1 1-2-1 0-1 0-2-1 0 0 0-1 1-1l-2-1c0-1 1-2 1-3-1 1-2 1-2 2v1h-1v2h0c-1 1-2 3-3 4h0c0 1-1 1-2 1-1-1 0-2 0-3l-1-1c0-1-1 0-1-1h-1l1-1h0-1c0-1-1-1-1-2h2v-1h2v-1c-1-1-1-1-2-1h-2 0l-1-1h-2c-1 0-2-1-3-1h0c-1-2-4-2-6-4v-1h3c1 1 1 0 2-1h1c-2 0-4-1-6-3-3-2-3-5-3-8h0c2-2 4-3 6-5z" class="c"></path><path d="M434 312c1 0 2 0 3 1 5 2 11-1 15 1l2 1c-1 1-2 0-3 0-2 0-8-2-10 0h0l10 1 1 1v1h0c-1-1-2-1-3-1-3 0-5-1-9-1-1-2-4-2-6-4z" class="d"></path><path d="M449 296l2 1h0l4 4 1 1c-1 1-1 1-1 2h1l1 1h-2c0 1 1 1 1 2v1h-1 0l1 1-1 1-2-1 2 2v1l-3-1s0 1 1 1 1 1 2 1c-1 1-2 1-3 1-4-2-10 1-15-1-1-1-2-1-3-1v-1h3c1 1 1 0 2-1h1c2 0 5-1 6-3 1-1 2-1 2-2 2-3 2-5 1-9z" class="C"></path><path d="M451 297l4 4 1 1c-1 1-1 1-1 2h1l1 1h-2c0 1 1 1 1 2v1h-1 0l1 1-1 1-2-1 2 2v1l-3-1c-2-1-4 0-5-1v-1h4l-2-1h-1c1-1 2-1 3-1h0v-1h-1v-1h2c0-1-1-1-1-2h0c1-1 2-1 3-1l1-1h-3c-1-2-1-2-1-4z" class="U"></path><path d="M437 294c3-1 5-1 8 0l1 1c1 0 3 1 3 1 1 4 1 6-1 9 0 1-1 1-2 2-1 2-4 3-6 3s-4-1-6-3c-3-2-3-5-3-8h0c2-2 4-3 6-5z" class="D"></path><path d="M446 295c1 0 3 1 3 1 1 4 1 6-1 9 0 1-1 1-2 2v-1c0-1 0-2 1-3v-1c1 0 1-1 1-1h0v-1c0-2-1-4-2-5z" class="W"></path><path d="M363 357c2 0 5-2 7-3 4-3 8-4 12-6l6-3h0c1-1 2-1 3-1h0l1 1-1 1h-1l-1 1v1h0 1c1-1 3-3 5-3v1h-1v1c-2 1-4 2-5 4h0l-1 2 1 1-4 4h-1c0 1 0 1 1 2l-5 7-5 6-2 6c0 1-1 2-1 2h-1v-1c-2-1-2 0-3 0v-1l-1-1h0-1-3c0-1 1-1 0-2l-1 1c0 1 0 0-1 1v-1h-1 0-1c0-1 1-1 1-2v-1l-1 1h0c-1 0-2 0-3 1h0v-1 1c-1 0-2 0-2 1l-1-1c-1 1-2 2-2 3s-1 2-1 4c-1 0-1 1-1 1 0 1-1 2-1 2l-1-1h0l-1-1v-1l-1-3h-3l-1-3c-1 3-3 4-5 6h-1l1-3 2-2c3-2 5-4 7-6-2 0-3 0-4-1h0 3s1 0 2-1c2-1 4-3 6-5l11-8z" class="c"></path><path d="M378 363v-1c-1-1-1-1-2-1v-1c1 0 2-1 2-1v-1h1c1-1 1-1 2-1v2h0c-1 1-2 3-3 4z" class="b"></path><path d="M381 357l2-1h0v1c1 0 1 1 1 1 0 1 0 1 1 2l-5 7v-2h0l-1-1h0-1 0v-1c1-1 2-3 3-4h0v-2z" class="C"></path><path d="M375 373c-1 0-1 1-1 1l-1-1 2-3s1-1 1-2h-1v1c-1 1-2 2-3 2v-1l4-4c0-1 1-2 2-2h0 1 0l1 1h0v2l-5 6z" class="b"></path><path d="M353 376c1-1 3-4 4-5v-3c1-1 2-1 3-1s2-2 3-3c1 0 2-1 3-2 2-1 5-3 7-5h0c1-1 2-1 2-1h1l-1 1c-1 1-2 1-3 2h-1c0 1-1 1-1 2-1 0-1 1-2 1s-1 1-2 2c-1 0 0-1-1 0l-1 1v1h-2v1l-1 1c0 1 1 0 0 0l-2 2c-1 2-2 3-3 5h0v1c-1 0-2 0-2 1l-1-1z" class="C"></path><path d="M363 357c2 0 5-2 7-3 4-3 8-4 12-6l6-3h0c1-1 2-1 3-1h0c-3 1-6 4-9 5-10 6-22 11-31 19-2 1-3 4-5 5 0 1-2 1-3 2 0 1-1 2-2 2-1 3-3 4-5 6h-1l1-3 2-2c3-2 5-4 7-6-2 0-3 0-4-1h0 3s1 0 2-1c2-1 4-3 6-5l11-8z" class="d"></path><path d="M511 598v1c0 3 0 8-1 11h0c1-1 1-2 2-2h0c0 1-1 2-1 4h1 0 1v2h1c1 0 1-2 2-3h1c0 1 0 1-1 2l1 1h0c1-1 2-3 3-4v3h0c-1 0-1 1-1 1l-1 1v1h1c0-1 0-1 1-1v1h2l1 1 3-1c1 0 2 0 2 1l2-1h1c1-1 2-1 4-2l1-1h1c1-1 2-1 3-1h0c-1 1-3 2-3 3h1c0 1-1 2-2 3-2 2-5 4-8 6-2 1-5 3-8 4h-1-1c0 1-1 1-2 1-1 1-2 2-4 2-1 1-2 2-4 3h0v1h-1-1l-1 1h-2 0c0 1-1 1-1 1h-1c1 1 2 1 3 1l-7 3h-5-1 0-1l-1 1h-1-1c-1 0-2 0-3 1-1 0-2 1-3 1l-1-1c2-1 4-2 5-3l1-1v-1c-1-1-2-1-2-2v-1h0l-4 4c1-2 2-4 2-6h0c0-1 0-2 1-3v-1-3l2-2c0 1-1 2 0 3h0c1-2 2-5 3-7v-1h1v3h1v2h1c1 0 1 0 1-1s0-1 1-2h0 0v3h-1v2h1s1-1 1-2l1 1h0v-2h1c0-1 1-1 1-2h0c1-1 1-3 2-4 0-1 1-1 1-2 1-2 1-3 2-4h0v-1-3h1c1 1 0 2 0 3v2c-1 0-1 1-1 1v2c1 0 2-1 3-2s1-3 2-4v-1c0 1 0 1 1 0 1-2 2-5 2-8v-1l1-1z" class="c"></path><path d="M523 617l3-1c1 0 2 0 2 1-3 2-7 2-10 3l5-3z" class="G"></path><path d="M521 624h1 1l1-1h2c1-1 3-2 4-2-1 1-1 1-2 1v1c-1 0-2 1-4 2h0l-6 3c0 1-1 1-2 1-1 1-2 2-4 2-1 1-2 2-4 3h-1-1l-1 1h0c-1 0-1 0-2-1h0c1 0 3-1 3-1l1-1h-2-1-2 0l1-1c1 0 3-1 4-1 0 0 1 0 1-1 2 0 3-1 5-1l5-3c1-1 2-1 3-1z" class="C"></path><path d="M531 616c1-1 2-1 4-2l1-1h1c1-1 2-1 3-1h0c-1 1-3 2-3 3h1c0 1-1 2-2 3-2 2-5 4-8 6-2 1-5 3-8 4h-1-1l6-3h0c2-1 3-2 4-2v-1c1 0 1 0 2-1-1 0-3 1-4 2h-2l-1 1h-1-1l1-1c1 0 1 0 2-1h-2c0-1 2-2 4-3 1 0 1-1 2-1l1-1 2-1z" class="I"></path><path d="M502 610v-3h1c1 1 0 2 0 3v2c-1 0-1 1-1 1v2l-1 1s0 1-1 1l1 1c1 0 1 0 1 1h1 1c-1 2-3 3-4 5 1 0 1 0 2 1 0-1 0-1 1-1v1h3v-1l1 1h0c-1 1-1 1-1 2 1 1 2 0 4 0h0c-1 1-2 2-4 2l-2 1c-2 0-4 1-7 1l-1 1v-1h0c0-1 1-1 1-2l-1-1c-1 0-1 0-2 1l-1-1c1-1 1-2 2-3h0v-2h1c0-1 1-1 1-2h0c1-1 1-3 2-4 0-1 1-1 1-2 1-2 1-3 2-4h0v-1z" class="R"></path><path d="M282 582c1-1 1-1 3-1 1 0 2 1 3 2-1 1-1 2-1 3l4 4-5-1c-1 0-1 0-1 1v2c2 3 6 6 6 9h-1l-2 2c1 2 2 3 3 5 0 3 0 7 1 10 0 2 0 3 1 5s2 6 4 7c1 0 1 1 2 1 3 1 5 3 7 4s6 1 8 1l-3-5c-1 0-1 0-2-1h0c-2 0-3-2-5-3 1 0 1-1 1-1 0-1 1-1 2-1h0c1 1 2 1 2 2h0c1 0 1-1 1-1 2 0 4 1 5 2v-2h1l2 1s1 0 1-1l5 2c1-1 1-1 1-2v-1c1-1 1-1 2-1l1-2 3-1h5c2-1 3-2 4-3l1-1c-1-1-1-1-1-3h1s1 0 1 1c1 0 1 0 1 1l16 9 1-1v-2-2h2l2-2 1-1v2c1-1 1-1 2-1v1l1 1v-1h1v1h0v1c1-1 2-1 2-2h0 1s0-1 1-1v1h1l1 1v2h0 1v-1h1v1h1l1-1h1v1h0c1 0 1 0 1-1 1 0 1 0 2 1h1v-1h1c1 0 1-1 1-1h2v-1c2 0 2 1 3 2 1 0 2 1 3 2h0-3v1 1h-1v1c-1 0-1 0-1-1l-1 1h-1v2c0 1 1 1 2 1 1 1 1 2 2 2 1 1 1 1 0 2l2 2h0-2v1s1 1 1 2h-1c1 0 1 0 2 1 0 0 1 0 2 1 0 0 1 0 1 1h1v1l-1 1c-1 0-2 1-2 1l-2-2-1 1c1 1 2 2 2 3h-1-1c0 1 1 2 2 3h-1 0-2c-1 0 0 0-1-1h-4 0c1 1 2 1 3 1l2 2c1 1 2 2 3 4-3 0-3-4-6-4h0v1l-1-1h-1v2l-3-4c0 2 1 4 1 6l-1-1c0-1 0-1-1-2h0v2h-1c0-1 0-1-1-2l-1 1v1h0c-1 0-1-1-1-2v-1l-1-1c0 1-1 1-1 1h-1v1h-1c0-1 0-1-1-2-1 1-2 3-3 4v-1-1c1-1 2-2 2-3-1 1-2 2-3 2 0-1 0-1-1-2 0 1-1 2-1 3s-1 1-2 2h0v-1h0l-1-1 1-1v-1h-1c-2 0-2 1-3 2-1-1 0-2 0-3v-2-3c-1 2 0 2-2 3-1-2-1-4-1-6h-13c2-1 4 0 5 0v-1h-15c-1 0-6 1-7 0 2-1 4 0 5 0 4 0 23 0 25-1v-1c0-1-1-1-1-1-1-1-3 0-3 0-6-1-12 0-18 0h-27-28v-1h3c1-1 1-2 1-3h0c1 1 1 2 2 3h2c2-1 5-2 6-4h0-6l-1-5c-2-6-5-10-10-14h0l3-6h0c1-2 3-6 2-9 0-1 1-4 1-5 0-2 1-7 1-9l-2-2z" class="X"></path><path d="M371 639h1v1c-1 2-2 3-4 4h-1c0-2 3-4 4-5z" class="U"></path><path d="M372 619s0-1 1-1v1c1 1 1 3 1 5l-2-2c-1-1-1-1 0-3z" class="T"></path><path d="M368 626h0c0 1-1 2-1 3h-2 0l-1 2h0c-1 0-1-1-1-1 1-2 4-4 5-4z" class="Z"></path><path d="M368 630c0-3 3-5 4-8h0c0 2-1 5-1 7h-1s-1 1-2 1z" class="K"></path><path d="M288 603h0c-1-2-3-4-4-6 2 1 5 3 6 4l-2 2z" class="E"></path><path d="M368 638h0l1-1 1 1c-1 2-3 3-4 5-1 1-1 2-2 2-1 2 0 4-1 5-2 0-2 1-3 2-1-1 0-2 0-3v-2-3l1-1v1l1-1c0-1 2-2 3-3h0l1-1c1 0 1 0 2-1z" class="N"></path><path d="M361 643v1l1 1-1 1-1 1v-3l1-1z" class="U"></path><path d="M307 625h0c1 1 2 1 2 2h0c1 0 1-1 1-1 2 0 4 1 5 2 0 2 1 4 1 6h-1c-1-1-3-2-4-3-1 0-1 0-2-1h0c-2 0-3-2-5-3 1 0 1-1 1-1 0-1 1-1 2-1z" class="F"></path><path d="M361 643h0c0-1-1-1-1-2l2-2-1-1c0 1 0 1-1 0v-2s0-1 1-2h-2 0c0-1 1-1 1-2 0-2 0-2 1-3h1v1c-1 1 0 2 0 2h2 0l-2 3h1l1-1v1c0 1-1 2-1 3l3-3h1v2h-1v1c0-1 0-1 1-1l1 1c-1 1-1 1-2 1l-1 1h0c-1 1-3 2-3 3l-1 1v-1z" class="g"></path><path d="M391 643h0c-1-1-1 0-2 0-1-1-2-2-2-3-1 0-1-1-2-2v-2h1v-1h3c0 1 2 2 2 3 1 0 1 0 2 1 0 0 1 0 2 1 0 0 1 0 1 1h1v1l-1 1c-1 0-2 1-2 1l-2-2-1 1z" class="M"></path><path d="M340 618l1-1c-1-1-1-1-1-3h1s1 0 1 1c1 0 1 0 1 1h0c1 1 3 2 4 3 3 3 8 6 12 7h0c0 1-1 1-2 1-3 0-5-2-8-3 0 0-1-1-2-1h-1c-1 0-1-1-1-1-1-2-2-3-5-4z" class="F"></path><path d="M340 618c3 1 4 2 5 4 0 0 0 1 1 1h1c1 2 1 2 3 3v2c-1 0-2 1-2 2h0c2 0 3-1 4-2l-1-1h1c1 0 3 0 3 1-4 5-9 8-16 8-8 0-15-4-21-9 0 0 1 0 1-1l5 2c1-1 1-1 1-2v-1c1-1 1-1 2-1l1-2 3-1h5c2-1 3-2 4-3z" class="D"></path><path d="M331 621h5v5h-1l-3 3c-3 0-6 0-8-1 1-1 1-1 1-2v-1c1-1 1-1 2-1l1-2 3-1z" class="O"></path><path d="M331 621h5v5h-1c-1-2 0-3-1-5h-3z" class="B"></path><path d="M325 625h1c2 0 3-1 5-1h1c0 1-1 1-1 2-2 1-4 1-5 0h-1v-1z" class="W"></path><path d="M340 618c3 1 4 2 5 4 0 0 0 1 1 1-1 3-1 5-3 6-1 1-3 1-4 1-2-1-2-3-3-4v-5c2-1 3-2 4-3z" class="M"></path><path d="M341 621h1c1 0 1 0 2 1h0c0 2-2 3-3 4l-1 1-2-2c0-2 2-2 3-4z" class="E"></path><path d="M303 473c2-1 6-3 7-6v1l1 1h0c1 1 1 1 1 2h1 1l2 2h0 2l1 1 1 7c0 1-1 2-1 3 2 3 4 5 5 8 2 4 1 7 1 11 1 4 2 8 2 12 3 29-4 61 12 87 3 5 7 10 12 13 3 2 6 4 9 7v2l-1 1-16-9c0-1 0-1-1-1 0-1-1-1-1-1h-1c0 2 0 2 1 3l-1 1c-1 1-2 2-4 3h-5l-3 1-1 2c-1 0-1 0-2 1v1c0 1 0 1-1 2l-5-2c0 1-1 1-1 1l-2-1h-1v2c-1-1-3-2-5-2 0 0 0 1-1 1h0c0-1-1-1-2-2h0c-1 0-2 0-2 1 0 0 0 1-1 1 2 1 3 3 5 3h0c1 1 1 1 2 1l3 5c-2 0-6 0-8-1s-4-3-7-4c-1 0-1-1-2-1-2-1-3-5-4-7s-1-3-1-5c-1-3-1-7-1-10-1-2-2-3-3-5l2-2h1c0-3-4-6-6-9v-2c0-1 0-1 1-1l5 1-4-4c0-1 0-2 1-3-1-1-2-2-3-2-2 0-2 0-3 1l2 2c0 2-1 7-1 9 0 1-1 4-1 5 1 3-1 7-2 9-1 0-3-1-5-2v-1l-1-1c-2-1-3-3-4-5h-1v-1h0v-1c-1-1-1-2-1-3 0 0 0-1-1-1h0v-5c0-2-1-3 0-5v-1h-1v-3l-1 1-1-1c-1 0-1 0-2-1v-1c1 0 1-1 1-1v-1l1-1v-1c0-1 0-2 1-3v-3c-1-2-3-3-4-4h0c0-1 1-2 1-2l1-1 2-3c1-2 3-5 3-8 0-1-1-1-1-1 0-1 0-2 1-3l1 1v-1c1 0 1-1 2-1v-2h2l-1-1s0-1 1-2c0-1 0-3 1-4v-6h0v-3c0-1-1-4 0-5h1c1-2 1-5 1-7 0-4 0-10 1-13h0c1-2 2-4 2-6 1-2 1 0 2-2 0 0 0-1-1-1 1-1 2-3 2-4l3-5c2-1 3-2 4-4h1c1 0 2 0 3 1l2-1s0 1 1 1c0-1 3-3 4-4s2-1 3-2z" class="W"></path><path d="M312 544l3 3c-1 0-2 1-3 0v-3z" class="B"></path><path d="M318 575h0c1 1 1 4 1 5h-1c0-1-1-1-2-1v-1l2-3z" class="b"></path><path d="M293 611v6 1h0c-1 1 0 3 0 5-1-2-1-3-1-5 0-3-1-4 1-7z" class="F"></path><path d="M305 543c2 1 3 2 4 3l1 1h0-3l-2-2c-1-1 0-1 0-2z" class="L"></path><path d="M291 608h2v3c-2 3-1 4-1 7-1-3-1-7-1-10z" class="E"></path><path d="M327 605c3 1 4 3 6 4v1l-2-2h0l-1 3c-1-1-1-1-1-2h-1v1h-1c1-1 1-1 1-2v-1c-1 0-1-1-1-2h0z" class="R"></path><path d="M298 582c1 0 2 1 3 2 0 2-2 4-3 7h0c0-3 1-4 0-6v-3z" class="J"></path><path d="M306 563c2 0 4 1 5 2v2c-3-2-5-3-9-3h0c1-1 1-1 3-1h1z" class="I"></path><path d="M309 535s-1 0-1-1l3-1c1 0 4-1 4-1 1-1 2-3 3-4l-1 6h0 0-1l-1-1c-2 0-4 1-6 2z" class="j"></path><path d="M315 481l1 1 1-1 1 1c0 2-1 3 0 5-1-1-2-1-4-1 1-1 0-3 0-5h1z" class="E"></path><path d="M294 535c1 0 2 1 3 1h2 1c1 0 2 1 2 2h1c-2 0-6 1-8 0-1-1-1-2-1-3z" class="J"></path><path d="M322 593c0 2 1 5 1 7-1 2-2 3-2 5-1 1 0 2 0 3-1-1 0-3-1-4h0c1-1 1-2 1-4v-1-1c0-2-1-3-3-4h1c1 0 2 0 3-1z" class="R"></path><path d="M309 559l1 1c1 1 1 4 2 4 1 1 1 2 1 3s-1 1-1 2c0-1-1-1-1-2v-2c-1-1-3-2-5-2l2-2c0-1 0-2 1-2z" class="E"></path><path d="M320 509v-11c0-3 0-7 1-10 1 6 1 12 1 19-1-1 0-3-2-4 0 2 1 4 0 6z" class="L"></path><path d="M296 568l1 2-1 1v2c0 1 1 3 1 4v3l-1-3v3c-1-1-1-1-2-1v-2l-1-5h2c0-1 0-2 1-4z" class="D"></path><path d="M293 572h2c0 2 0 4 1 5v3c-1-1-1-1-2-1v-2l-1-5z" class="e"></path><path d="M298 598h0l1 1 1 1v1h-1c0 1 0 1 1 1v4 2h-1v-2c-1 0-1 1-2 1h0v-5c1-2 0-3 1-4z" class="D"></path><path d="M296 577l1 3s1 1 1 2v3h-2 0 0-1v-1l-2-2v-2l1-1c1 0 1 0 2 1v-3z" class="L"></path><path d="M296 577l1 3s1 1 1 2v3h-2v-5-3z" class="S"></path><path d="M325 605h1 1 0c0 1 0 2 1 2v1c0 1 0 1-1 2h1l2 2h-1c-1 0-2 1-4 0-1-1-1-2-1-3s0-2 1-4z" class="L"></path><path d="M325 605h1 1 0c0 1 0 2 1 2v1c0 1 0 1-1 2v2c-1-1-1-5-2-6v-1z" class="C"></path><path d="M293 582l2 2v1h1 0 0 2c1 2 0 3 0 6h0v1c-1 0-1-1-2-1 0 1 0 2-1 2-1-1-1-4 0-5-1-2-2-4-2-6z" class="T"></path><path d="M293 582l2 2v1h1 0c1 2 1 3 0 4v1c-1-1-1-1-1-2-1-2-2-4-2-6z" class="J"></path><path d="M288 526h0c3 4 9 7 14 9-1 0-1 1-2 1h-1-2c-1 0-2-1-3-1 0 0 0-1-1-1 0 0-1-1-2-1h1 3v-1h-2c-3-1-4-3-6-4l1-2z" class="I"></path><path d="M298 612c0-1 0-1 1-2l1 1v5 2c1-2 1-2 0-3l1-1h1c-1 2-1 4 0 5 1 2 3 4 5 6-1 0-2 0-2 1 0 0 0 1-1 1l-1-1c-1 0-2-1-2-2-1-1-2-2-2-3v-1-1c-1-2-1-4-1-7z" class="B"></path><path d="M298 598c0-5 3-10 5-14l11-4c1 1 2 1 3 2 4 3 4 7 5 11-1 1-2 1-3 1 1-1 2-2 2-3-1 0-2 0-3-1 1-1 0-2 0-4-2-2-3-3-6-4l-4 1c-5 1-6 6-8 9-1 1-1 4-2 6h0z" class="I"></path><path d="M314 486c2 0 3 0 4 1 0 1-1 3-1 4h0-1c-1 0-1 1-2 1 1 1 2 1 3 2v6h0c-1-1-1-4-1-5-2 4-2 8-6 11-2 2-5 6-8 6v-1h2c1-1 3-2 4-4l-1-1v-1l1-1c2-1 5-3 6-5v-1h0c1-2 1-3 0-5-1 0-2 0-2-1h0v-1c2 0 4-2 4-3v-1h-1l-1-1h0z" class="e"></path><path d="M285 530c1-1 1-2 1-3l1 1c2 1 3 3 6 4h2v1h-3-1c1 0 2 1 2 1 1 0 1 1 1 1 0 1 0 2 1 3-1 0-1 1-2 1-1-1-2-1-3-2h0l-1-1c0 1 0 1-1 2l-1-1c-1-1-2-2-2-3v-2c1-1 0-1 0-2z" class="C"></path><path d="M289 536h0v-3h2c1 0 2 1 2 1-1 1-1 2-3 2v1h0l-1-1z" class="H"></path><path d="M293 534c1 0 1 1 1 1 0 1 0 2 1 3-1 0-1 1-2 1-1-1-2-1-3-2v-1c2 0 2-1 3-2z" class="l"></path><path d="M280 510c1 1 1 1 1 3l1 1v1c0 1 1 1 1 2l1 2h1c1-1 1-1 1-3 0 2 1 3 3 4 0 1 1 3 3 3h1c-1 1-1 1-1 2l1 1s1 1 2 1c0 1 0 1 1 1-1 0-2 0-2-1h-1l-2-2c-1 0-1 0-1-1h-2-1c0 1 1 1 1 2h0l-1 2-1-1c0 1 0 2-1 3h0v-1-3-2c-1-1-1-1-1-2h0v-1c-3-2-4-5-5-8l1-1v-2z" class="B"></path><path d="M284 522l4 4-1 2-1-1c0 1 0 2-1 3h0v-1-3-2c-1-1-1-1-1-2z" class="M"></path><path d="M311 552c0-1 0-1 1-2 2 6 4 11 2 17-1 2-1 3-2 5v-3c0-1 1-1 1-2s0-2-1-3c-1 0-1-3-2-4l-1-1-1-1h-1c-2 0-2 0-4-1h0c2-1 3-1 4-1l1-1 3-3z" class="X"></path><path d="M303 557c2-1 3-1 4-1l1-1c1 1 2 1 2 3 1 1 2 1 2 3v3c-1 0-1-3-2-4l-1-1-1-1h-1c-2 0-2 0-4-1h0z" class="I"></path><path d="M315 533l1 1h1 0 0v17c-1-1-1-3-2-4l-3-3c-2-2-5-5-9-6h-1c0-1-1-2-2-2 1 0 1-1 2-1 5 2 9 4 13 7 0-1 1-5 0-6h-1c-1 0-2 1-3 0-1 0-1-1-2-1 2-1 4-2 6-2z" class="C"></path><path d="M309 535c2-1 4-2 6-2 0 1 0 2-1 3h-3c-1 0-1-1-2-1z" class="B"></path><path d="M320 509c1-2 0-4 0-6 2 1 1 3 2 4v32c0 17-1 35 4 52 1 3 3 7 5 10-2 0-4-7-5-9 0 4 1 8 5 10 1 1 2 3 2 4-4-4-7-6-7-12-1-7-4-14-5-21-1-12-1-25-1-37v-27z" class="c"></path><path d="M299 543c2 0 4-1 6 0 0 1-1 1 0 2l2 2h3c0 1 1 2 2 3-1 1-1 1-1 2l-3 3-1 1c-1 0-2 0-4 1v-1c-1 1-2 1-3 1v-1l-1-1c0-2 0-3-1-5v-1l-2-2v1 1-1c-1-1-1-1 0-2v-1c0-1 1-2 3-2z" class="l"></path><path d="M298 550h3v2h-1v4l-1-1c0-2 0-3-1-5zm7-5l2 2 1 2c1 0 2 0 3 1-2 0-3 0-5-1l-1 1v-1c-1 0 0-1-1-2l1-2z" class="Y"></path><path d="M310 547c0 1 1 2 2 3-1 1-1 1-1 2v-2c-1-1-2-1-3-1l-1-2h3z" class="D"></path><path d="M299 543c2 0 4-1 6 0 0 1-1 1 0 2l-1 2c1 1 0 2 1 2v1c-1 1-2 2-4 2v-2h-3v-1l-2-2v1 1-1c-1-1-1-1 0-2v-1c0-1 1-2 3-2z" class="B"></path><path d="M299 543c2 0 4-1 6 0 0 1-1 1 0 2l-1 2h-2c0-1 0-2-1-3s-1-1-2 0c-1 0-1 0-2 1 1 2 2 3 1 4l-2-2v1 1-1c-1-1-1-1 0-2v-1c0-1 1-2 3-2z" class="O"></path><path d="M287 559l3-1c1 0 1 1 1 1l3 1h2c0 1 0 1 1 2l1-1c0 1 1 2 1 3h3 0c-2 1-4 2-6 4h0c-1 2-1 3-1 4h-2l1 5c-1 0-2 0-2-1 0 1-1 1-1 1 0 1 0 1-1 1v1l1 1v1c-1-1-2-1-2-2h-1c0 1 1 2 0 3v1c-1-1-2-2-3-2h0v-1c0-3-1-4-2-6l-1-4c0-1 0-2-1-2 1-1 1-1 1-2v-2l1-2v2 1h1c1-1 2-1 3-1 1-1 2-1 3-2l-1-1-2-2z" class="D"></path><path d="M289 568c2 0 3-1 5-1h1l1 1h0c-1 2-1 3-1 4h-2-2c0-1 1-2 1-3h0l-4 1h0l1-2z" class="C"></path><path d="M290 579c0-1 0 0-1-1s-1-1-1-3h1l1 1h0c1-1-2-3-2-4h0 2 1 2l1 5c-1 0-2 0-2-1 0 1-1 1-1 1 0 1 0 1-1 1v1z" class="F"></path><path d="M283 562v2 1h1c1-1 2-1 3-1v1h1l2-1v1l-3 2c0 1 0 2-1 3h0l-1-1h-1c0 3 1 7 3 10h1c0 1 1 2 0 3v1c-1-1-2-2-3-2h0v-1c0-3-1-4-2-6l-1-4c0-1 0-2-1-2 1-1 1-1 1-2v-2l1-2z" class="Q"></path><path d="M287 565h1l2-1v1l-3 2c0 1 0 2-1 3v-2h-1v-2l2-1z" class="S"></path><path d="M283 562v2 1h1v3l-1 1c0 1 1 2 0 3l-1-2c0-1 0-2-1-2 1-1 1-1 1-2v-2l1-2z" class="O"></path><path d="M287 559l3-1c1 0 1 1 1 1l3 1h2c0 1 0 1 1 2l1-1c0 1 1 2 1 3h3 0c-2 1-4 2-6 4l-1-1h-1c-2 0-3 1-5 1-1 1-1 1-2 0v-1l3-2v-1l-2 1h-1v-1c1-1 2-1 3-2l-1-1-2-2z" class="l"></path><path d="M290 565v1h2c1-1 0-2 1-3 1 1 0 1 1 3l1 1h-1c-2 0-3 1-5 1-1 1-1 1-2 0v-1l3-2z" class="Y"></path><path d="M294 560h2c0 1 0 1 1 2l1-1c0 1 1 2 1 3h3 0c-2 1-4 2-6 4l-1-1h-1 1v-1c1 0 1-1 2-2l-3-4z" class="L"></path><path d="M309 603c1 2 2 3 4 4 1 0 2 0 3-1 1 0 2-1 4-2 1 1 0 3 1 4v1c1 2 1 4 3 5l2 1h0c3 1 8 1 11 0h1c-1-2-3-2-4-3s-1-1-1-2v-1c2 2 5 4 6 6l-1 1c-2 1-5 0-7 2-1 1-1 1-2 1v1h-3c-1 0-1 0-1 1 1 1 1 0 2 1h1l-1 2c-1 0-1 0-2 1v1c0 1 0 1-1 2l-5-2s-1-1-2-1l1-3h-1c0-1-2-3-3-4l1-1c-1-1-1-1-1-2-1 0-2 0-3-1v-1c-1-2-2-3-3-4l1-2h-1 0v-3l1-1z" class="j"></path><path d="M309 603c1 2 2 3 4 4h-1v1 1l-1-1h-1s-1 0-1-1h-1 0v-3l1-1z" class="C"></path><path d="M311 613c1-1 1-2 2-2h0c1 1 1 1 2 1l1 1h0l1 1-1 1h-2 0c-1 0-2 0-3-1v-1z" class="B"></path><path d="M316 613l1-1c1 0 2 1 2 2 3 5 7 4 12 4-1 1-1 1-2 1v1h-3c-1 0-1 0-1 1 1 1 1 0 2 1h1l-1 2c-1 0-1 0-2 1v1c0 1 0 1-1 2l-5-2s-1-1-2-1l1-3h-1c0-1-2-3-3-4l1-1c-1-1-1-1-1-2h0 2l1-1-1-1z" class="G"></path><path d="M317 614l1 1-1 2s0 1 1 1h0v4h-1c0-1-2-3-3-4l1-1c-1-1-1-1-1-2h0 2l1-1z" class="T"></path><path d="M318 618c1 1 2 2 4 2 0 1 2 1 2 1l3 3c-1 0-1 0-2 1v1c0 1 0 1-1 2l-5-2s-1-1-2-1l1-3v-4z" class="c"></path><path d="M302 602s1-1 2-1h2l3 2-1 1v3h0 1l-1 2c1 1 2 2 3 4v1c1 1 2 1 3 1 0 1 0 1 1 2l-1 1c1 1 3 3 3 4h1l-1 3c1 0 2 1 2 1 0 1-1 1-1 1l-2-1h-1v2c-1-1-3-2-5-2 0 0 0 1-1 1h0c0-1-1-1-2-2h0c-2-2-4-4-5-6-1-1-1-3 0-5h-1l-1 1c1 1 1 1 0 3v-2-5l-1-1c-1 1-1 1-1 2v-3c-2 1-1 4-3 6h0v-1-1c0-1 1-1 1-1v-1-1-1l1-2h0c1 0 1-1 2-1v2h1v-2-4-1l2 2v-1z" class="E"></path><path d="M304 611c2 1 3 3 5 4s3 2 5 3c1 1 3 3 3 4h-1c-2 0-3-1-4-2-2-2-8-6-8-9z" class="j"></path><path d="M302 614c3 3 5 6 9 9 1 0 2 1 3 2 1 0 2 0 2 1h-1v2c-1-1-3-2-5-2 0 0 0 1-1 1h0c0-1-1-1-2-2h0c-2-2-4-4-5-6-1-1-1-3 0-5z" class="C"></path><path d="M302 602s1-1 2-1h2l3 2-1 1v3h0 1l-1 2c1 1 2 2 3 4v1c1 1 2 1 3 1 0 1 0 1 1 2l-1 1c-2-1-3-2-5-3s-3-3-5-4l-1-1v-1l-1-1c0-1-1-1-2-2v-4-1l2 2v-1z" class="F"></path><path d="M302 602s1-1 2-1h2l3 2-1 1v1c-1 0-1 1-2 1v-1c-1 0-2 0-2-1-1 0-2-1-2-1v-1z" class="P"></path><path d="M298 598c1-2 1-5 2-6 2-3 3-8 8-9l4-1c3 1 4 2 6 4 0 2 1 3 0 4 1 1 2 1 3 1 0 1-1 2-2 3h-1c2 1 3 2 3 4v1 1c0 2 0 3-1 4h0c-2 1-3 2-4 2-1 1-2 1-3 1-2-1-3-2-4-4l-3-2h-2c-1 0-2 1-2 1v1l-2-2v1c-1 0-1 0-1-1h1v-1l-1-1-1-1z" class="E"></path><path d="M309 599l1-2h0l1 1h0l1-1c1 0 1 0 1 1 1 1 1 2 1 3h0c-1 0-1 0-2-1-1 0-2-1-3-1z" class="a"></path><path d="M318 590c1 1 2 1 3 1 0 1-1 2-2 3h-1c-1-1-2-1-3-2 2 0 2 0 3-2z" class="C"></path><path d="M315 602c1-1 0-1 0-3 0 0 0-1 1-2 1 1 1 2 2 2l1 1h2 0c0 2 0 3-1 4h0c-2 1-3 2-4 2h-1-1l-1-1c1-1 2-2 2-3z" class="W"></path><path d="M315 606c0-1 1-1 2-2 0-1 0-1 1-2h0c1 1 1 1 1 2h1 0c-2 1-3 2-4 2h-1z" class="E"></path><path d="M302 602v-1c-1-4 1-8 3-11 2 1 4 3 6 4h-4c-1 1-2 1-2 2-1 2 0 3 0 4h0l1 1h-2c-1 0-2 1-2 1z" class="L"></path><path d="M310 585c1 0 2 0 3 1 1 0 2 2 2 3s-1 1-1 3h-1c-1 0-3 0-4-1-1 0-1-1-2-1v-3c1-1 1-2 3-2z" class="C"></path><path d="M308 597l1-1v1 1 1c1 0 2 1 3 1 1 1 1 1 2 1h0c1 0 1 0 1 1s-1 2-2 3l1 1h1 1c-1 1-2 1-3 1-2-1-3-2-4-4l-3-2-1-1c1 0 1-1 2-2l1-1z" class="B"></path><path d="M308 597l1-1v1 1 1c1 0 2 1 3 1 1 1 1 1 2 1 0 1 0 1-1 2-1 0-2 0-4-1l-1-1v-4z" class="F"></path><path d="M313 507c1-1 2-1 2-2 0 0 1-1 2-1v1s0 1-1 1l-2 1c1 1 2 1 3 2 1 0 0 0 0 1s-1 1-1 1v1 2h1c1 2 0 5 0 6v2c-1 2-1 3-1 4-1 3-4 4-6 5h-2 0c-1 0-3 1-5 0h-1-1-1c-1-1-1-1-2-1-1-1-2-1-2-2-1 0-1 0-1-1-1 0-2-1-2-1l-1-1c0-1 0-1 1-2h-1c-2 0-3-2-3-3v-1c1 1 1 2 2 3 2 0 3 0 5-1v-2h1c1 1 4 1 5 1 1-1 2-2 3-2l1-1h0l-1-1c-1 0-2-2-3-2v-2c3 0 6-4 8-6l1 1h0 2z" class="E"></path><path d="M313 507c1-1 2-1 2-2 0 0 1-1 2-1v1s0 1-1 1l-2 1c0 2-3 3-3 4l-1 1-1-1v-1c1-2 3-2 4-3z" class="D"></path><path d="M311 515c1 0 2 1 2 0l-1-1v-1h2c2 2 2 4 2 7h-1l-1-1-1-1h-3v-1h1 1l-1-2z" class="Y"></path><path d="M305 516l1-1 1 1h0c1-1 1-1 2-1h2l1 2h-1l-3-1v1l3 4h-1c-1-1-3-1-5 0-1 1 0 3 0 4-2-2-4-4-7-3h-1c0 1 0 1-1 1v-2-2h1c1 1 4 1 5 1 1-1 2-2 3-2l1-1h0l-1-1z" class="h"></path><path d="M289 519c1 1 1 2 2 3 2 0 3 0 5-1v2c1 0 1 0 1-1 0 1 0 1 1 2s3 2 5 2c1 0 3 0 4 1h-1 0 2s0-1 1-1c2 0 3-1 5-2h1c0 1 0 2-1 3 0 1-1 1-1 1l-2 1h-1c0 1-1 1-1 1-1 0-1 1-1 1h0c-1 0-3 1-5 0h-1-1-1c-1-1-1-1-2-1-1-1-2-1-2-2-1 0-1 0-1-1-1 0-2-1-2-1l-1-1c0-1 0-1 1-2h-1c-2 0-3-2-3-3v-1z" class="F"></path><path d="M289 519c1 1 1 2 2 3 2 0 3 0 5-1v2c1 0 1 0 1-1 0 1 0 1 1 2s3 2 5 2c1 0 3 0 4 1h-1 0-3-1c-3-1-6-3-9-4h0-1c-2 0-3-2-3-3v-1z" class="J"></path><path d="M276 513c0 1 0 4 1 6v-1l1-1 1-4c1 3 2 6 5 8v1h0c0 1 0 1 1 2v2 3 1h0c0 1 1 1 0 2v2c0 1 1 2 2 3l1 1c1-1 1-1 1-2l1 1h0c1 1 2 1 3 2l-1 2c1 0 2 1 2 1h2c1 1 2 1 3 1-2 0-3 1-3 2v1c-1 1-1 1 0 2v1-1-1l2 2v1c1 2 1 3 1 5l1 1v1c1 0 2 0 3-1v1h0c2 1 2 1 4 1h1l1 1c-1 0-1 1-1 2l-2 2h-1c-2 0-2 0-3 1h-3c0-1-1-2-1-3l-1 1c-1-1-1-1-1-2h-2l-3-1s0-1-1-1l-3 1 2 2 1 1c-1 1-2 1-3 2-1 0-2 0-3 1h-1v-1-2h0c-1-1-1-2-1-3s-1-2-1-2c-1-3-1-5-1-8v-2c0-1 0-2 1-2h0l-1-3-2 1c-1 0-1-1-1-2l-1-1v-2-2l-2 2h-1c0-1 0-3 1-4v-6h0v-3c0-1-1-4 0-5h1c1-2 1-5 1-7z" class="F"></path><path d="M288 543l2 1c1 1 1 1 2 1v2h0-1c-1-1-2-2-3-4h0z" class="R"></path><path d="M277 529c0 2 0 3 1 4h1c0 1 1 2 1 2 0 1-1 2-1 2l-2-2v-6z" class="B"></path><path d="M285 552c1-1 2-2 2-3h0 1s0 1 1 1h0c0 1-1 2-1 3v1c-1 0-2-1-3-2h0z" class="e"></path><path d="M296 549v-1-1l2 2v1c1 2 1 3 1 5l-3-3h-1v-2l1-1h0z" class="l"></path><path d="M277 535l2 2c0 1 1 2 1 3v1 1l-2 1c-1 0-1-1-1-2 1-1 0-4 0-6z" class="V"></path><path d="M284 521v1h0c0 1 0 1 1 2v2h0l-1-1h-1c0 2-2 3-2 5-1 1-1 1-1 2-2-2 2-5 2-7l-2-1 2-1 2-2z" class="G"></path><path d="M289 550l5-1 1 1c-1 2-2 3-4 4h-2-1v-1c0-1 1-2 1-3z" class="L"></path><path d="M285 526h0v3 1h0c0 1 1 1 0 2v2c0 1 1 2 2 3-1 0-1 0-2 1h0l-1 1c-1-1-1-2-1-3v-1c-1-3 1-5 2-8v-1z" class="I"></path><path d="M285 538c-1-1-2-3-2-4s0-1 1-2h1v2c0 1 1 2 2 3-1 0-1 0-2 1h0z" class="E"></path><path d="M296 552l3 3 1 1v1c1 0 2 0 3-1v1h0c-1 1-1 1-2 1-1-1-1-1-2 0h-2c0 1 0 2 1 3l-1 1c-1-1-1-1-1-2h-2l-3-1c2-2 4-5 5-7z" class="O"></path><path d="M289 536l1 1h0c1 1 2 1 3 2l-1 2c1 0 2 1 2 1 0 1-1 2-2 3-1 0-1 0-2-1l-2-1h0-1c-2-1-2-2-3-4l1-1h0c1-1 1-1 2-1l1 1c1-1 1-1 1-2z" class="S"></path><path d="M289 540c1 2 1 2 1 4l-2-1c0-1-1-1-1-2l2-1z" class="B"></path><path d="M289 540l1-1 2 2c1 0 2 1 2 1 0 1-1 2-2 3-1 0-1 0-2-1 0-2 0-2-1-4z" class="L"></path><path d="M303 557c2 1 2 1 4 1h1l1 1c-1 0-1 1-1 2l-2 2h-1c-2 0-2 0-3 1h-3c0-1-1-2-1-3-1-1-1-2-1-3h2c1-1 1-1 2 0 1 0 1 0 2-1z" class="F"></path><path d="M308 558l1 1c-1 0-1 1-1 2l-2 2h-1c-1 0-1-1-1-1 0-1 1-1 1-1l1-1v-1c1-1 1 0 2-1z" class="i"></path><path d="M298 561c-1-1-1-2-1-3h2l-1 1c2 1 3 1 4 1v1c-1 0-2 0-3 2v1c0-1-1-2-1-3z" class="B"></path><defs><linearGradient id="E" x1="284.024" y1="565.105" x2="284.093" y2="548.959" xlink:href="#B"><stop offset="0" stop-color="#151415"></stop><stop offset="1" stop-color="#333231"></stop></linearGradient></defs><path fill="url(#E)" d="M280 541c1 1 2 3 3 4l3 2c0 2-1 2-1 3v2h0l-1 1v1c1 2 2 3 3 5l2 2 1 1c-1 1-2 1-3 2-1 0-2 0-3 1h-1v-1-2h0c-1-1-1-2-1-3s-1-2-1-2c-1-3-1-5-1-8v-2c0-1 0-2 1-2h0l-1-3v-1z"></path><path d="M284 554h0c1 2 2 3 3 5l2 2c-2-1-3-1-4-1-1-1-1-4-1-6z" class="e"></path><path d="M284 554l-1-2c-1-1-1-3 0-4 1 0 1 0 1 1h0l1 1v2h0l-1 1v1h0z" class="D"></path><path d="M280 541c1 1 2 3 3 4v2h1v1h-4v-1c0-1 0-2 1-2h0l-1-3v-1z" class="C"></path><path d="M276 513c0 1 0 4 1 6v-1l1-1 1-4c1 3 2 6 5 8l-2 2-2 1c-1 1-2 2-3 4v1 6c0 2 1 5 0 6l-1-1v-2-2l-2 2h-1c0-1 0-3 1-4v-6h0v-3c0-1-1-4 0-5h1c1-2 1-5 1-7z" class="G"></path><path d="M274 528h0c1 0 1-1 1-2v4c1 1 0 3 1 4v4-2l-2 2h-1c0-1 0-3 1-4v-6z" class="D"></path><path d="M276 513c0 1 0 4 1 6v-1l1-1c0 2-1 3-1 5v1l-2-1v4c0 1 0 2-1 2h0 0v-3c0-1-1-4 0-5h1c1-2 1-5 1-7z" class="E"></path><path d="M279 513c1 3 2 6 5 8l-2 2-2 1c-1 1-2 2-3 4v-5-1c0-2 1-3 1-5l1-4z" class="M"></path><path d="M277 523l2-1v-2c1-1 1 0 2 0 1 1 1 2 1 3l-2 1c-1 1-2 2-3 4v-5z" class="C"></path><path d="M273 538h1l2-2v2 2l1 1c0 1 0 2 1 2l2-1 1 3h0c-1 0-1 1-1 2v2c0 3 0 5 1 8 0 0 1 1 1 2s0 2 1 3h0l-1 2v2c0 1 0 1-1 2 1 0 1 1 1 2l1 4c1 2 2 3 2 6v1h0c-2 0-2 0-3 1l2 2c0 2-1 7-1 9 0 1-1 4-1 5 1 3-1 7-2 9-1 0-3-1-5-2v-1l-1-1c-2-1-3-3-4-5h-1v-1h0v-1c-1-1-1-2-1-3 0 0 0-1-1-1h0v-5c0-2-1-3 0-5v-1h-1v-3l-1 1-1-1c-1 0-1 0-2-1v-1c1 0 1-1 1-1v-1l1-1v-1c0-1 0-2 1-3v-3c-1-2-3-3-4-4h0c0-1 1-2 1-2l1-1 2-3c1-2 3-5 3-8 0-1-1-1-1-1 0-1 0-2 1-3l1 1v-1c1 0 1-1 2-1v-2h2l-1-1s0-1 1-2z" class="F"></path><path d="M281 557s1 1 1 2 0 2 1 3h0l-1 2v-1c-1-2-1-4-1-6z" class="h"></path><path d="M270 545h2v1h0c-1 2 0 4 0 6v1c0 1-1 1-1 2s0 1-1 2v1h0v-2h0c1-3 1-8 0-11z" class="D"></path><path d="M280 570c-2-2-2-4-2-6-1-3-2-5-3-7 0-1-1-3-1-4h-1c0-1 0-1 1-2 1 1 1 3 1 4l5 11v1s0 1 1 1h0 0c0-2 0-3 1-4v2c0 1 0 1-1 2 1 0 1 1 1 2l1 4c-1-1-2-1-2-2s-1-2-1-2z" class="G"></path><path d="M268 548c0-1-1-1-1-1 0-1 0-2 1-3l1 1h1c1 3 1 8 0 11h0v2l-2 9v1h0l-2 4c-1 0-1 1-1 0v-3-3c-1-2-3-3-4-4h0c0-1 1-2 1-2l1-1 2-3c1-2 3-5 3-8z" class="C"></path><path d="M268 548c0-1-1-1-1-1 0-1 0-2 1-3l1 1h1c1 3 1 8 0 11h0l-1-1h0v-1-2c0-1 1-2 0-3 0 2-2 6-4 7 1-2 3-5 3-8z" class="O"></path><path d="M272 561h-1l1-2h0v-2-1c1 0 1 0 1-1 1 1 1 2 1 3v1 3 1l2 4 2 5h1c0-1 0-2 1-2 0 0 1 1 1 2s1 1 2 2c1 2 2 3 2 6v1h0c-2 0-2 0-3 1 0 0-1-1-1-2h-1c-1 0-2 1-3 1-1-2 1-6-1-8l-1 1h0v1c0 1 0 1-1 1v2h-1l-1 7c-1-1-2-3-4-3 0-1-1 0-1 0v-1h-1v-3l-1 1-1-1c-1 0-1 0-2-1v-1c1 0 1-1 1-1v-1l1-1v-1c0-1 0-2 1-3v3c0 1 0 0 1 0l2-4h0v-1h0c1-1 1-2 2-3h0c1-1 1-2 2-3z" class="d"></path><path d="M265 579l-1-1c-1 0-1 0-2-1v-1c1 0 1-1 1-1v-1l1 1c1 0 2 0 3-1v1s0 1-1 1v2h0l-1 1z" class="D"></path><path d="M272 561h-1l1-2h0v-2-1c1 0 1 0 1-1 1 1 1 2 1 3v1 3 1c0 1 0 2-1 4 0 1 0 2-1 3 0 2-1 8-3 9-1-1 0-1-1-2 0-2 1-4 1-6v-1c-1 2-1 2-2 3l-1-1 2-4h0v-1h0c1-1 1-2 2-3h0c1-1 1-2 2-3z" class="E"></path><path d="M268 567h0c1-1 1-2 2-3h0c1-1 1-2 2-3 0 2-1 3-1 4-1 1-1 2-1 4l-1 2h0v-1c-1 2-1 2-2 3l-1-1 2-4h0v-1z" class="G"></path><path d="M272 570c1-1 1-2 1-3 1-2 1-3 1-4l2 4 2 5h1c0-1 0-2 1-2 0 0 1 1 1 2s1 1 2 2c1 2 2 3 2 6v1h0c-2 0-2 0-3 1 0 0-1-1-1-2h-1c-1 0-2 1-3 1-1-2 1-6-1-8l-1 1h0v1c0 1 0 1-1 1v2h-1 0c-1 1-1 2-2 3h-1v-1c1-2 2-3 2-5l1-1c0-2 0-3-1-4z" class="V"></path><path d="M280 570s1 1 1 2 1 1 2 2v1h-1v1s0 1-1 1h0-1c0-1 1-2 0-3 0-1 0-2-1-2 0-1 0-2 1-2z" class="i"></path><path d="M283 574c1 2 2 3 2 6v1h0c-2 0-2 0-3 1 0 0-1-1-1-2l-1-3h1 0c1 0 1-1 1-1v-1h1v-1z" class="V"></path><path d="M280 577h1c1 2 2 3 4 4h0c-2 0-2 0-3 1 0 0-1-1-1-2l-1-3z" class="k"></path><path d="M275 574l1-1c2 2 0 6 1 8 1 0 2-1 3-1h1c0 1 1 2 1 2l2 2c0 2-1 7-1 9 0 1-1 4-1 5 1 3-1 7-2 9-1 0-3-1-5-2v-1l-1-1c-2-1-3-3-4-5h-1v-1h0v-1c-1-1-1-2-1-3 0 0 0-1-1-1h0v-5c0-2-1-3 0-5 0 0 1-1 1 0 2 0 3 2 4 3l1-7h1v-2c1 0 1 0 1-1v-1h0z" class="Y"></path><path d="M275 574l1-1c2 2 0 6 1 8 1 1 0 2 0 2 0 2 0 3-1 4 0 2 0 5-1 7-1 1 0 3 0 4l-1 1c-1-2-2-3-3-5 0 0-1-1-1-2h0c2 1 2 3 3 4v-1-4-2c-1-1-1-2-1-2l1-1c1 1 0 2 1 4 1-1 1-2 1-2v-1c1-1 1-2 1-3l-1-1c-1 0-2 1-3 2l1-7h1v-2c1 0 1 0 1-1v-1h0z" class="E"></path><path d="M275 574h0c0 1 0 2 1 2v2c-1 0-1-1-2 0v-2c1 0 1 0 1-1v-1h0z" class="D"></path><path d="M303 473c2-1 6-3 7-6v1l1 1h0c1 1 1 1 1 2h1 1l2 2h0c-1 2-1 3-1 5h0 1 1c-1 1-2 2-2 3h-1c0 2 1 4 0 5h0l1 1h1v1c0 1-2 3-4 3v1h0c0 1 1 1 2 1 1 2 1 3 0 5h0v1c-1 2-4 4-6 5l-1 1v1l1 1c-1 2-3 3-4 4h-2v1 2c1 0 2 2 3 2l1 1h0l-1 1c-1 0-2 1-3 2-1 0-4 0-5-1h-1v2c-2 1-3 1-5 1-1-1-1-2-2-3v1c-2-1-3-2-3-4 0 2 0 2-1 3h-1l-1-2c0-1-1-1-1-2v-1l-1-1c0-2 0-2-1-3v2l-1 1-1 4-1 1v1c-1-2-1-5-1-6 0-4 0-10 1-13h0c1-2 2-4 2-6 1-2 1 0 2-2 0 0 0-1-1-1 1-1 2-3 2-4l3-5c2-1 3-2 4-4h1c1 0 2 0 3 1l2-1s0 1 1 1c0-1 3-3 4-4s2-1 3-2z" class="Y"></path><path d="M300 494l1 1c0 1-1 1-2 2l-1-2 1-1h1z" class="V"></path><path d="M302 491l-2-1v-1-1c1 0 2 1 3 1s1 0 2 1 1 2 1 3h0c-1-1-2-2-4-3v1z" class="L"></path><path d="M291 503c2 1 4 2 6 2 1 0 1-1 2-1v1c-1 0-1 0-2 2h0c-2 1-4-1-6-1v-1c-1-1-2 0-3 0l2-1 1-1z" class="I"></path><path d="M302 501c1 0 2 0 3 1l1 2c-1 0-1 1-2 1h-5v-1c1-1 2-1 2-3h1z" class="b"></path><path d="M298 487h1c1-1 3-1 4-2h0c1 1 1 1 2 1v1c-1 0-1 0-1 1l1 1v1c-1-1-1-1-2-1s-2-1-3-1v1 1l2 1-1 1h0l-1 1v1h-1l-1 1c0-2-1-4-2-5h-1v-1c1 0 1-1 2-1 0-1 1-1 1-1z" class="R"></path><path d="M295 489c1 0 1-1 2-1 0-1 1-1 1-1 0 2 0 2 1 4h0c1 0 1 1 2 1h0l-1 1v1h-1l-1 1c0-2-1-4-2-5h-1v-1z" class="B"></path><path d="M299 491c1 0 1 1 2 1h0l-1 1v1h-1c-1-1-1-1-1-2l1-1z" class="G"></path><path d="M297 499c1 1 1 1 2 1l2 1c0 2-1 2-2 3-1 0-1 1-2 1-2 0-4-1-6-2l-1 1v-2c0-1 1-1 1-1 2-1 5-1 6-2z" class="E"></path><path d="M297 499c1 1 1 1 2 1-2 0-2 1-3 2-2 0-3 1-4 0l-1 1-1 1v-2c0-1 1-1 1-1 2-1 5-1 6-2z" class="R"></path><path d="M291 491h1v-2c0-1 1-1 1-1h2v1 1h1c1 1 2 3 2 5l1 2c1 2 1 3 3 4h-1l-2-1c-1 0-1 0-2-1l-2-2c-1-1-1-1-3-1v-1l1-1c-1 0-1-1-1-2l-1-1z" class="M"></path><path d="M291 491h1v-2c0-1 1-1 1-1h2v1 1h1c-1 1 0 2-1 3l-2 1c-1 0-1-1-1-2l-1-1z" class="E"></path><path d="M292 492c0 1 0 2 1 2l-1 1v1c2 0 2 0 3 1l2 2c-1 1-4 1-6 2 0 0-1 0-1 1v2l-2 1c-1 0-1-1-2-1v-2h-1c1-1 1-3 2-4l2-1 1-2c1-1 1-1 2-3z" class="V"></path><path d="M286 502c3 2 1 0 4-1v1 2l-2 1c-1 0-1-1-2-1v-2z" class="B"></path><path d="M291 501c-1-1-1-1-2-1v-1c2-3 3-1 5-1l-1-1h2l2 2c-1 1-4 1-6 2z" class="Y"></path><path d="M305 480h5 3l1 1h0c0 2 1 4 0 5h0-1c0-1 0-1-1-1v-1c1 2 0 3 0 5v1h-1l-1 2h-1v-1c-1-1-3-2-4-2l-1-1c0-1 0-1 1-1v-1c-1 0-1 0-2-1h0c-1 1-3 1-4 2h-1s-1 0-1 1c-1 0-1 1-2 1v-1h-2c1-1 1-2 2-3h-1c1-1 1-2 2-2v2h0l1-1c1-2 5-3 8-4z" class="D"></path><path d="M295 488s1 0 1-1h0c1 0 2-2 3-2 1-1 2-1 3-2v-1 1 1c1 0 1 1 2 1h-1c-1 1-3 1-4 2h-1s-1 0-1 1c-1 0-1 1-2 1v-1z" class="V"></path><path d="M305 485l-1-1h1 0l-1-1 1-1c0 1 0 1 1 1 1 1 2 1 3 0 1 1 2 1 3 1 1 2 0 3 0 5-2-2-3-3-5-4h-2z" class="E"></path><path d="M309 483c-1 0-2 0-2-1l2-2c2 1 3 1 5 1h0c0 2 1 4 0 5h0-1c0-1 0-1-1-1v-1c-1 0-2 0-3-1z" class="W"></path><path d="M305 485h2c2 1 3 2 5 4v1h-1l-1 2h-1v-1c-1-1-3-2-4-2l-1-1c0-1 0-1 1-1v-1c-1 0-1 0-2-1h0 1 1z" class="L"></path><path d="M307 485c2 1 3 2 5 4v1h-1v-1l-2-1c-2-1-2-1-2-3z" class="e"></path><path d="M285 502h1v2c1 0 1 1 2 1s2-1 3 0v1c-1 3 0 5 0 8 1 0 1 1 1 1 1 1 1 1 1 2 1 0 2 1 2 2-1 0-1-1-2-1h-1c-1 1-2 0-3 0v1 1c-2-1-3-2-3-4 0 2 0 2-1 3h-1l-1-2c0-1-1-1-1-2v-1l-1-1c0-1 0-4 1-5h0v-4h1c0 1 1 1 2 1l-1-2 1-1z" class="b"></path><path d="M281 513c0-1 0-4 1-5h0v-4h1c0 1 1 1 2 1 0 1 0 2 1 3 0 1-1 3 0 4h-1l-1-1c0 1 1 3 1 4l1 1c0 2 0 2-1 3h-1l-1-2c0-1-1-1-1-2v-1l-1-1z" class="F"></path><path d="M303 473c2-1 6-3 7-6v1l1 1h0c1 1 1 1 1 2h1 1l2 2h0c-1 2-1 3-1 5h0 1 1c-1 1-2 2-2 3h-1 0l-1-1h-3-5c-3 1-7 2-8 4l-1 1h0v-2-1-3c0-1 3-3 4-4s2-1 3-2z" class="V"></path><path d="M313 474c-1 0-2 1-3 0v-5h1 0c1 1 1 1 1 2h1 1c0 1 0 2-1 3z" class="W"></path><path d="M314 471l2 2h0c-1 2-1 3-1 5h0 1 1c-1 1-2 2-2 3h-1 0l-1-1h-3-5l1-1 2-1 1-2h1c0 1-1 2 0 3l4 1v-1-1c0-1-1-2-1-3v-1c1-1 1-2 1-3z" class="M"></path><path d="M301 476v-1h3c1 1 2 2 4 3l-2 1-1 1c-3 1-7 2-8 4l-1 1h0v-2-1-3c0-1 3-3 4-4l1 1z" class="J"></path><path d="M301 476v-1h3l1 4h0-3c-1 0-1-1-2-1l1-2z" class="M"></path><path d="M300 475l1 1-1 2c1 0 1 1 2 1-1 2-4 2-5 3h-1v-3c0-1 3-3 4-4z" class="E"></path><path d="M289 478h1c1 0 2 0 3 1l2-1s0 1 1 1v3 1c-1 0-1 1-2 2h1c-1 1-1 2-2 3 0 0-1 0-1 1v2h-1l1 1c-1 2-1 2-2 3l-1 2-2 1c-1 1-1 3-2 4l-1 1 1 2c-1 0-2 0-2-1h-1v4h0c-1 1-1 4-1 5 0-2 0-2-1-3v2l-1 1-1 4-1 1v1c-1-2-1-5-1-6 0-4 0-10 1-13h0c1-2 2-4 2-6 1-2 1 0 2-2 0 0 0-1-1-1 1-1 2-3 2-4l3-5c2-1 3-2 4-4z" class="D"></path><path d="M282 487h1 3c-2 2-3 3-3 5-1 1-1 2-2 3l-2-1c1-2 1 0 2-2 0 0 0-1-1-1 1-1 2-3 2-4z" class="R"></path><path d="M288 490c1 0 2 1 3 1l1 1c-1 2-1 2-2 3v-2h-1l-1 2h-2-1v-3c1-1 2-1 3-2z" class="I"></path><path d="M287 486v-1c1-1 1-1 3-2 1 1 2 1 3 2h0 1 1c-1 1-1 2-2 3 0 0-1 0-1 1v2h-1c-1 0-2-1-3-1h-2c0-2 0-3 1-4h0z" class="H"></path><path d="M289 478h1c1 0 2 0 3 1l2-1s0 1 1 1v3 1c-1 0-1 1-2 2h-1 0c-1-1-2-1-3-2-2 1-2 1-3 2v1l-1 1h-3-1l3-5c2-1 3-2 4-4z" class="C"></path><path d="M288 495l1-2h1v2l-1 2-2 1c-1 1-1 3-2 4l-1 1 1 2c-1 0-2 0-2-1h-1v4h0c-1 1-1 4-1 5 0-2 0-2-1-3v2l-1 1-1 4-1 1v1c-1-2-1-5-1-6 0-4 0-10 1-13h0c1-2 2-4 2-6l2 1h1l1 1c1 1 1 0 2 0l1 1-1 1v1h0c2-1 2-3 3-4z" class="H"></path><path d="M282 495l1 1v1c0 1 0 1-1 1 0 0-1 1-1 2h-1c0-2 0-3 1-4l1-1zm-5 5c1 0 1 0 2-1v2c1 2 0 8 1 9v2l-1 1-1 4-1 1v1c-1-2-1-5-1-6 0-4 0-10 1-13z" class="i"></path><path d="M358 647h0c-1 1-3 1-4 1l-91-1H118v-31l26 3c22 0 45-3 62-18 10-10 13-23 15-37 1-7 1-14 1-20v-26-87-285c-17 0-34-1-50 2-11 3-23 8-32 15-16 11-28 28-35 47-4 12-7 24-10 36l-5 24-32-27L91 95h400l14 102-38 27c-4-18-11-38-24-51-12-13-29-21-46-25-22-4-46-2-69-3-1 10 0 20 0 30v57 90c0 1 2 1 3 1v1h-1-2c-1 1 0 3 0 4v1c8 2 17 3 26 3l2 1h5 4v1h0c-1 0-2 0-3 1l23 1c3 1 8 1 11 1h1l2 1 1 1h-1l-2 1 1 1c-2 1-3 0-4 1h-1c-1-1-3-1-4 0h-3-3l-3 1c-1 0-5 1-5 1l1 1-7 1-19 6c-8 2-16 5-23 10h-1v-1l-1-1h-3v-1c0-3 0-6-1-9 1-1 0-2 1-3v-2c0-1-1-4 0-5 0-1 0-2-1-3h0c0-1 0-1 1-2v-2c0-1 0-2-1-3v-1l-1-1c1-1 1-2 2-3v-1c0-2 0-2-1-3h1v-2c-1-4 0-9 0-13v-29l-1-116v-13-6h-1v-5h26 21c4 0 9 0 13 1h5l15 3c3 0 6 1 9 2 6 2 13 4 18 8h0 1c11 6 23 16 30 27 6 10 11 22 14 33 2-2 5-4 8-6 5-4 10-8 16-11v-1l-3-27-3-16-4-32c-1-4-1-10-3-15h-1-11-8-67-8-25-51-26-4-11-72-6-15-65-12-4L66 240l19 16c1-3 2-7 3-11l5-21 2-7c1-4 1-7 3-11 2-6 5-12 9-18 1-3 4-6 6-9 5-7 11-13 17-19 15-12 32-19 51-21l49-2v297 81 27c0 8 0 15-1 23-2 14-6 29-16 40-7 6-15 12-24 15-14 6-31 7-46 7-2 0-4 0-5-1l-12-1v15h201c1 1 6 0 7 0h15v1c-1 0-3-1-5 0h13c0 2 0 4 1 6z" class="D"></path><path d="M380 138h5l-3 1v1l-15-1h0v-1c2 0 3 1 5 0h0 8z" class="O"></path><path d="M470 101h11l-1 1h1s1 0 1 1h-11c0-1-4 0-5-1h-1l5-1z" class="c"></path><path d="M101 101h20s1 0 2 1h-10v1h-12l1-1h4 0c-1 0-4 0-5-1z" class="O"></path><path d="M383 341h0c5-2 11-1 16-2l-2 1 1 1c-2 1-3 0-4 1h-1c-1-1-3-1-4 0h-3-3l1-1h-1z" class="M"></path><path d="M385 138l15 3-1 1-17-2v-1l3-1z" class="P"></path><path d="M385 336c3 1 8 1 11 1h1l2 1-18 2c-1-1-2-1-3-1 1 0 2 0 3-1h-3c2 0 4 0 5-1l2-1z" class="C"></path><path d="M400 141c3 0 6 1 9 2 6 2 13 4 18 8h0-1l1 1c-5-2-11-5-17-7-3-1-7-2-11-3l1-1z" class="j"></path><path d="M346 137h21c4 0 9 0 13 1h-8 0c-2 1-3 0-5 0v1h0-34c5-1 11 0 17 0l-1-1h1c-1 0-2 0-4-1h0z" class="M"></path><path d="M445 101h17 8l-5 1h1c1 1 5 0 5 1h-8c-1-1-21-1-24-1h-19c2 0 4 0 6-1h1 18zm-125 41v-5h26 0c2 1 3 1 4 1h-1l1 1c-6 0-12-1-17 0h-11v15c0 2 0 4-1 7v-13-6h-1z" class="O"></path><path d="M332 337l46 1h3c-1 1-2 1-3 1-1-1-20 0-23 0h-27c2-1 24-1 28-1h0-15-9v-1z" class="T"></path><path d="M378 101h49-1c-2 1-4 1-6 1h19c3 0 23 0 24 1h-67c1-1 2-1 3-1h0c-7-1-14 0-21-1zm-257 0h59 18 9 0l-13 1c2 0 5 0 5 1h-6-15-65v-1h10c-1-1-2-1-2-1z" class="I"></path><path d="M98 206c2-6 5-12 9-18 1-3 4-6 6-9 5-7 11-13 17-19 15-12 32-19 51-21h2 0c1 1 0 1 1 1-23 2-48 13-63 31-6 7-11 15-16 23-2 4-4 9-6 13l-1-1zm230 133h27c3 0 22-1 23 0 1 0 2 0 3 1l-13 1c-11 1-22 2-33 4-2 1-5 2-7 2 0-2-1-5 0-8z" class="c"></path><path d="M328 329c8 2 17 3 26 3l2 1h5 4v1h0c-1 0-2 0-3 1l23 1-2 1c-1 1-3 1-5 1l-46-1h-3c-2-1-1-3-2-4h0v-2c1-1 1-1 1-2h0z" class="P"></path><path d="M361 333h4v1h0c-1 0-2 0-3 1-2 0-4 0-6-1h4l1-1z" class="S"></path><path d="M354 332l2 1h5l-1 1h-4-5c2 0 4 0 5-1l-2-1z" class="V"></path><path d="M328 329c8 2 17 3 26 3l2 1c-1 1-3 1-5 1-5 0-11-1-16-2-1 0-6-2-7-1l-1 2v-2c1-1 1-1 1-2h0z" class="B"></path><path d="M481 101h4l13 93h-1l-15 12c-4 2-8 5-11 8-2-7-4-14-7-20-7-18-20-32-37-42l-1-1h1 1c11 6 23 16 30 27 6 10 11 22 14 33 2-2 5-4 8-6 5-4 10-8 16-11v-1l-3-27-3-16-4-32c-1-4-1-10-3-15h-1c0-1-1-1-1-1h-1l1-1z" class="P"></path><path d="M207 101h39 77 41 14c7 1 14 0 21 1h0c-1 0-2 0-3 1h-8-25-51-26-4-11-72c0-1-3-1-5-1l13-1h0z" class="b"></path><path d="M271 103h-1c-1-1-2-1-4-1h1 33c-1 1-3 0-4 0-3 0-7 0-10 1h-4-11z" class="P"></path><path d="M99 207c-4 11-7 23-9 34-2 6-3 12-4 18l-21-19 31-139h5c1 1 4 1 5 1h0-4l-1 1h-4L66 240l19 16c1-3 2-7 3-11l5-21 2-7c1-4 1-7 3-11l1 1z" class="b"></path><path d="M344 641H124v-17l17 1h0c2 1 4 1 6 1h0-9l-12-1v15h201c1 1 6 0 7 0h15v1c-1 0-3-1-5 0z" class="P"></path><path d="M328 350c1-2 6-3 8-3 7-2 15-3 22-3l25-3h1l-1 1-3 1c-1 0-5 1-5 1l1 1-7 1-19 6c-8 2-16 5-23 10h-1v-1l-1-1c-1 0-1 0-1-1v-1h-2v-1c1 0 2 0 2-1 1-1 1-1 3-2 0-1 0-3 1-4z" class="c"></path><path d="M375 344l1 1-7 1-19 6c-8 2-16 5-23 10h-1v-1l-1-1c-1 0-1 0-1-1v-1h-2v-1c1 0 2 0 2-1 1-1 1-1 3-2 0-1 0-3 1-4v10c6-5 13-7 21-9l15-5c4-1 8-1 11-2z" class="Y"></path><path d="M324 356c1-1 1-1 3-2v3c-1 0-2 0-3-1z" class="H"></path><path d="M181 139l49-2v297 81 27c0 8 0 15-1 23-2 14-6 29-16 40-7 6-15 12-24 15-14 6-31 7-46 7-2 0-4 0-5-1h9 0c-2 0-4 0-6-1h0c20 0 41-1 59-12 4-2 8-6 12-9 10-10 14-25 16-38 1-8 0-17 0-25l1-28-1-81V139l-44 1c-1 0 0 0-1-1h0-2z" class="O"></path><path d="M101 103h12 65 15 6 72 11 4 26 51 25 8 67 8 11 1c2 5 2 11 3 15l4 32 3 16 3 27v1c-6 3-11 7-16 11-3 2-6 4-8 6-3-11-8-23-14-33-7-11-19-21-30-27h-1 0c-5-4-12-6-18-8-3-1-6-2-9-2l-15-3h-5c-4-1-9-1-13-1h-21-26v5h1v6 13l1 116v29c0 4-1 9 0 13v2h-1c1 1 1 1 1 3v1c-1 1-1 2-2 3l1 1v1c1 1 1 2 1 3v2c-1 1-1 1-1 2h0c1 1 1 2 1 3-1 1 0 4 0 5v2c-1 1 0 2-1 3 0 1 0 1-1 2 0 1 1 1 0 2v1c1 1 1 2 1 3-1 3 0 6-1 9h-2c-1-4 1-9 0-13 0-1 0-1-1-1l1-1-3-1-1 2c-1 1-2 1-2 4 0 1-1 2-2 3-2 1-5 2-7 2h-1-2l1 2c-3-1-6-3-9-3v1 1l2 1v1 1h1l3 2 3 4c3 3 6 6 8 10 2 3 3 7 3 11 0 3-1 6-1 9 0 1 1 2 0 3 0 3-4 6-3 9 1 2 1 3 0 5v3 2h0l-2 2v1l-2 2s1 0 1 1v1c-1 0-1 0-2-1v1 1 1h0c-1 1-1 1-2 1v3h-2 0c-2-1-2-2-4-1h-1c-2-1-1-2-3-2l-1 2c-1 2-1 3-1 4l3-1v1 2c1 1 2 1 3 1l-1 1h0c0 1-1 2 0 2-1 1-1 2-3 3v1c1 1 2 2 1 4l-3 1c0 1 1 1 2 1h0c1-1 2-1 3-1l2 2c0 2 1 3 2 5 1 1 3 1 4 3 0 1 0 2-1 4v1h2l-1 2 1 1c-1 1-2 1-3 2s-4 3-4 4c-1 0-1-1-1-1l-2 1c-1-1-2-1-3-1h-1c-1 2-2 3-4 4l-3 5c0 1-1 3-2 4 1 0 1 1 1 1-1 2-1 0-2 2 0 2-1 4-2 6h0c-1 3-1 9-1 13 0 2 0 5-1 7h-1c-1 1 0 4 0 5v3h0v6c-1 1-1 3-1 4-1 1-1 2-1 2l1 1h-2v2c-1 0-1 1-2 1v1l-1-1c-1 1-1 2-1 3 0 0 1 0 1 1 0 3-2 6-3 8l-2 3-1 1s-1 1-1 2h0c1 1 3 2 4 4v3c-1 1-1 2-1 3v1l-1 1v1s0 1-1 1v1c1 1 1 1 2 1l1 1 1-1v3h1v1c-1 2 0 3 0 5v5h0c1 0 1 1 1 1 0 1 0 2 1 3v1h0v1h1c1 2 2 4 4 5l1 1v1c2 1 4 2 5 2h0l-3 6h0c5 4 8 8 10 14l1 5h6 0c-1 2-4 3-6 4h-2c-1-1-1-2-2-3h0c0 1 0 2-1 3h-3v1h28 27c6 0 12-1 18 0 0 0 2-1 3 0 0 0 1 0 1 1v1c-2 1-21 1-25 1-1 0-3-1-5 0H126v-15l12 1c1 1 3 1 5 1 15 0 32-1 46-7 9-3 17-9 24-15 10-11 14-26 16-40 1-8 1-15 1-23v-27-81-297l-49 2c-19 2-36 9-51 21-6 6-12 12-17 19-2 3-5 6-6 9-4 6-7 12-9 18-2 4-2 7-3 11l-2 7-5 21c-1 4-2 8-3 11l-19-16 31-137h4z" class="l"></path><path d="M241 325c0-1 1-1 2-1v1 1s0 1-1 1l-1-2z" class="D"></path><path d="M252 441v-1l-1-1 1-1c1 1 2 1 3 2l-2 1h-1z" class="B"></path><path d="M239 200c1 2 2 3 3 5h-2l-1-1v-4z" class="L"></path><path d="M310 125h1l1 1c0 1 1 1 1 1-2 0-4 1-5 0h0 1l1-2zm-34 86c1-1 1-1 2 0l-1 2c-1 1-1 1-2 1 0 0 0-1-1-1h0-1l2-1s0-1 1-1z" class="F"></path><path d="M206 132c1 0 1 1 2 1l1 1h1-4-2c1 0 1-1 1-2h1zm114 196v-6h0c1 1 1 1 2 3-1 1-1 2-2 3z" class="O"></path><path d="M312 125l1 1c2 0 3 1 5 2h0c-2 0-4 0-5-1 0 0-1 0-1-1l-1-1h1z" class="B"></path><path d="M294 416c2 0 3 1 4 3h0c-1-1-2-1-4-2h-2 0-1v-1h3z" class="P"></path><path d="M317 308c1 3 1 8 0 11 0-2-1-4-1-6h0c1-2 1-3 1-5z" class="I"></path><path d="M240 428c2 1 4 2 5 3v1c-1 0-3-1-4-1h0l-1-3z" class="M"></path><path d="M240 442s0-2 1-2c0 0 2-1 3-1 0 1 1 2 1 2h1 0 0c-2 1-4 0-6 1z" class="Y"></path><path d="M310 130c1 0 3-1 4 0h1c1 1 1 1 2 1h0c-1 1-4 1-5 0h-2v-1z" class="F"></path><path d="M281 124l3-1c0 2 0 4 1 6h-1c-1-1-2-3-3-5z" class="I"></path><path d="M278 440v-2h-1c1-2 3-3 4-5 1 1 1 1 2 1-2 1-4 4-5 6z" class="B"></path><path d="M98 185l2 1 1-1h0c0 2-2 3-2 4v4h-1v-7-1z" class="H"></path><path d="M291 171l1 1v1h0c0 1 0 1-1 1 0 1-1 2-2 3l-1-2c1-1 2-3 3-4z" class="I"></path><path d="M302 124c0 1 1 3 1 4-1 1-2 1-3 1 1-1 1-1 2-1-1-2-2-3-4-4h1c1-1 0 0 1 1h1l1-1z" class="Y"></path><path d="M143 135h-2c0-2-1-4 0-5 0-1 0-1 1-1h0v3h0l2 2h0l-1 1z" class="D"></path><path d="M131 124s1-1 1-2c1-1 2-1 4-1 0 1 0 1 1 2h-2-1c-1 1-1 2-1 2l-2-1z" class="H"></path><path d="M237 202l-2-9c1 2 2 5 4 7h0v4c0-1-1-2-2-3v1z" class="b"></path><path d="M291 171c2-2 4-2 6-3l1 1-3 3v-1h-1v-1c-1 1-2 1-2 2l-1-1z" class="a"></path><path d="M456 166v3c0 2 2 2 2 3v1l-5-5 1-2h2 0z" class="I"></path><path d="M277 634c2 1 2 2 5 2h1-3v1h-8l1-1h2c0-1 0-1 1-1s1-1 1-1z" class="C"></path><path d="M306 134c1 0 3-1 4 0l3 3h0l-1 1c0-1-1-2-2-2h-1l-1 2c-1-2-1-3-2-4z" class="E"></path><path d="M258 133l1-1 1 1h0 2 0l-5 4v-2l1-2z" class="L"></path><path d="M159 114h4c-1 1-1 2-2 2 0 1 1 1 0 2l-4-1v-1l2-2z" class="X"></path><path d="M439 138c0-1 0-2 1-3v1h3c-1 1-1 2-1 3l1 1v1l-2-1c0-1-1-2-2-2z" class="a"></path><path d="M282 139c-1 0-2 0-3-1v-3 1c2 0 4 0 5 1l1 1s0 1 1 1h0l-1-1c-1 0-2 0-3 1z" class="Y"></path><path d="M336 122c0-4-2-7-5-10 4 2 6 5 7 9-1 0-1 1-2 1z" class="P"></path><path d="M313 321l1 1h1c0-1-1-1-1-2 1 0 2 1 3 2h0c0 1 1 3 0 4-1-1-3-3-4-3v-2z" class="R"></path><path d="M295 253c1 0 2 0 3 1 0 0 1 0 1 1h2v1c-1 2-1 3-2 5h1 1c-1 1-2 0-3 0l1-2v-3c-1-1-3-2-4-3h0z" class="E"></path><path d="M443 135c1 1 3 1 4 2v1l-4 2-1-1c0-1 0-2 1-3v-1z" class="b"></path><path d="M136 139c1 1 1 2 1 3v3c0 1 0 1-1 2h0 0v-3l-1-1-1-1c0-1 1-2 1-2l1-1z" class="V"></path><path d="M315 243v-1h1v-1c0-2-1-2 0-3v-1c1 1 1 3 0 4h0v4 1c0 1 1 2 1 3v1l-1-1h0c0-1 0-2-1-3-1 0-1-1-2-1 1-1 1-2 2-2z" class="c"></path><path d="M278 402l2-2 1 2-1 2-2 5h0c-1-1-1-1-1-2 1-1 1-1 1-2v-3z" class="H"></path><path d="M104 177l1 1h0c0 1-1 4-1 5v1c-1 1-1 1-3 1h0v-1c1-1 3-6 3-7z" class="B"></path><path d="M291 139s-1 0-1-1c-2-2-3-2-5-3l1-1 2 2c1 0 1 0 2-1-1 0-1-1-1-2v-1c1 3 4 4 4 6-1 0-2 1-2 1z" class="Y"></path><path d="M352 131v-3c1-1 1-1 2-1 0-1 1-1 1-1v-2h0 1 2 0l-2 2h0v2c-2 0-3 2-4 3z" class="D"></path><path d="M284 233l1 2h-1l2 1c0 1 1 3 1 4h-1c-1-1-2-1-4-2v-1l1-1v-1c1 0 1-1 1-2z" class="R"></path><path d="M97 197h1c-1 2-2 5-3 6h-2c-1 0-2 0-2-1s1-1 2-2c2 0 3-1 4-3z" class="O"></path><path d="M451 136l2 1v1c-2 0-3 1-6 1 0 1-1 2-2 3-1-1-1-1-2-1v-1l4-2c1-1 3-1 4-2z" class="E"></path><path d="M410 126l-1-2 1-1c0 1 1 1 1 2 1 1 3 1 5 1 0 1 1 1 1 1 1-1 1-1 1-2l2 1-2 2h-1l-1-1c-1 1-3 0-4 0s-1 0-2-1z" class="D"></path><path d="M295 253c1 1 3 2 4 3v3c-2-1-1 0-2 0h-2l1-2c-1-1-2-2-2-3h0l1-1z" class="L"></path><path d="M312 321h1v2s0 2 1 2c0 1 2 1 3 2v1 1c-3-2-4-2-7-3 0-2 1-3 2-5z" class="a"></path><path d="M245 320v-2c1 0 1 1 1 2l2 3h0c-1 2-1 3-1 4s-1 2-1 2c-1-1-1-4-1-6 0-1-1-1 0-3z" class="h"></path><path d="M289 234l2 1c1 1 2 2 3 4h0l-1 1v1h0c-1 0-2-1-3-2v-1h-1v-1h-1v-1h1v-2z" class="Y"></path><path d="M314 332h2v1c0 1 0 0 1 1 0 1 0 2-1 3h0c-2-1-3-1-6-1l1-1h1c1-1 1-2 2-3z" class="H"></path><path d="M97 187l1-1v7 4h-1s-1-1-2-1c0-2 1-4 2-6v-3z" class="X"></path><path d="M245 431l1 1h0-3c0 1-1 1-2 2 0 0 0 1 1 1 0 2-1 2-2 3h-1c0-2 2-1 2-3-2-1-2-1-4-1h0v-1c1-1 2-1 4-2 1 0 3 1 4 1v-1z" class="G"></path><path d="M78 241v1c0 1-1 2 0 3s2 2 3 2h2c0-1 1-1 1-2 0 0 1 0 1 1s-1 3-2 4c-2-1-5-3-7-6 0-1 1 0 1-1 1 0 1-1 1-2z" class="P"></path><path d="M314 306h1c2-2 1-5 2-7 0 2 1 7 0 9 0 2 0 3-1 5l-1-3h0c0-2 1-3-1-4z" class="M"></path><path d="M78 202v-3c2 0 4 1 6 1h1l1 1h1 0v1c-1 0-1 0-1 1h-1c-1-2-5-1-7-1z" class="E"></path><path d="M190 623c1-1 1 0 2 0 0 1 2 2 2 3l-4 1c-1 0-2-1-3-1v-2c1-1 2-1 3-1z" class="X"></path><path d="M107 175c1 0 1 0 2-1 0 0-1-1-1-2h0l2 1c1 1 2 1 4 0h1l-9 10c1-2 3-6 3-8h0-2z" class="P"></path><path d="M289 226c0 1 0 1 1 2 0 0 1-1 2-1v1l-2 2v1 1h0v1l1 2-2-1c-1-1-2-1-3 0v2l-2-1h1v-2c2 0 2-1 3-2v-4l1-1z" class="G"></path><path d="M141 629c2 0 3 0 4 1h1c0 1 1 2 2 3h2c0 1 0 2-1 2l-1 1c-3-1-4-5-6-7h-1z" class="b"></path><path d="M237 202v-1c1 1 2 2 2 3l1 1v1 1l-1 1c-1 0-2 1-3 0s-1-2-1-3v-1l1 1h1v-3z" class="P"></path><path d="M248 323l1 2c1 1 2 2 2 3v1h1-1c-1 0-1 1-2 1-1 1-2 1-2 3l-1-1v-3s1-1 1-2 0-2 1-4z" class="C"></path><path d="M98 123c-1 1-1 2-1 3 1 2 0 7-1 9h0v-5c0-2-1-4-1-5s1-2 1-3v-4c0 2 0 3 2 5z" class="D"></path><path d="M239 488v-1c1 0 2 0 2-1h0 2s0-1 1-1h0c1 0 1 1 2 2h0v1h0c-1 1-2 1-3 2h-2 0l-2-2z" class="M"></path><path d="M414 117h0 2l4-1 1 1s1 1 1 2h-3c-1-1-1-1-2-1-1 1-1 2-2 3-1 0-1-1-2-1h-3l2-1v-1l2-1z" class="F"></path><path d="M279 396v-1c1 0 1-1 1-1h1v1h1 1l-1 3v2h-2l-2 2v-1h-1v-2l1-1v1c0-1 1-2 1-3z" class="Y"></path><path d="M291 235l-1-2v-1h0v-1c1 1 1 1 2 1 1 2 3 2 5 3v-1c1 1 1 2 0 2h0c-1 0-1 1-2 1h-1v2c-1-2-2-3-3-4z" class="H"></path><path d="M241 406h1c0 2 1 3 2 4 1 0 2 0 2 1h2c1 1 1 1 2 1 1-1 1-1 2-1h2c-3 1-5 2-6 4l-1-1h-1c-2-2-4-4-5-8z" class="a"></path><path d="M214 106h1 1 0c1 1 1 1 1 3-1 1-1 2-3 2h0c-1 0-1 0-2-1-1 0-1-1-1-2 1-1 1-2 3-2z" class="B"></path><path d="M165 631v1c0 1 0 1-1 2l1 1v-1c1 1 1 1 1 2h-10c-1 0-2 1-3 0 0-1 1-1 2-2 1 2 2 0 4 1h3c1-1 1-1 1-2s1-2 2-2z" class="P"></path><path d="M119 153c1 1 1 2 3 2 1-1 0-1 0-2 2 0 1 0 2 1 1-1 2-2 3-2 2 1 2 1 4 1h-1 0-1c-1 1-2 2-3 2-3 2-5 2-8 1l-1-1h2v-2z" class="E"></path><path d="M277 209v-1-1-1c2 0 2-1 3-2 2-2 4-2 6-3 1-1 2-1 3-1 0 1 0 1 1 1l1 1c-5 0-7 1-11 5l-2 2h-1z" class="H"></path><path d="M278 409l2-5v1h1c0 1 0 3-1 4l-1 4h1l1 1h0c-2 1-3 3-5 5 1-3 2-7 2-10z" class="e"></path><path d="M80 193h2l-1 1c0 2 1 3 2 4h3c1 0 2-1 3-2h1c-2 2-3 3-5 4h-1c-2 0-4-1-6-1 1-2 1-3 1-5l1-1z" class="c"></path><path d="M199 623c0 1 0 1 1 2v-1l1 1c0 1 1 4 2 5h0l1 1h0-3l-1 1c0-1-1-1-1-2-1-1-1-4-2-6l2-1z" class="P"></path><path d="M251 431c1 1 2 1 3 1h0l4 1c1 1 1 1 1 2h1v1l-1 1c0-1-1-1-1-1l-1 1h-1 0l-1-1c-1-1-3-2-3-3h-1v-2z" class="B"></path><path d="M277 209h1l2-2c2 0 2 1 3 2 1 0 1 1 1 2-2 1-2 1-4 1-1 0-1-1-2-2v1c-1-1-1-1-2 0l-1-1s-1 0-1-1c1-1 2 0 3 0z" class="D"></path><path d="M247 415c1 1 1 2 2 3h1v1c0 1 1 2 2 2 1 1 1 2 1 3l1 1c0 1 0 1-1 1h-1c-2-1-4-3-4-5-1-2-1-4-1-6z" class="Y"></path><path d="M297 303c3 1 6 2 9 2-1 1-2 1-2 2-1 0-1 1-2 1s-1 0-1 1c0 0 1 0 1 1 1 0 1-1 2 0h3-6-1 0-1c1-2-2-5-2-7z" class="B"></path><path d="M193 133h1c0-1 0-1 1-1-1 1-1 1 0 1 0 1 1 1 1 0v-1h0c2 1 3 1 5 1v1h3 2l-21 1h2c0-1 0-1 1-1h4 0l1-1z" class="L"></path><path d="M402 115v-1h1c1 1 1 1 2 1l-1 1 1 1s2 0 2 1h5l2-1-2 1v1l-2 1-1-1h0v2h-1c0-1-1-2-2-2h-1v1c-1 0-1-1-2-1l-1 1c-1 1-2-1-3-1v-1h2c2 0 2 0 3-1 0-1-1-1-2-2z" class="f"></path><path d="M78 242c2-1 2-1 4-1l2 2v2c0 1-1 1-1 2h-2c-1 0-2-1-3-2s0-2 0-3z" class="W"></path><path d="M471 106c3 0 8-1 10 0l-1 1h-3-2c-1 1-1 2-1 3 0 0 0 1 1 1 0 1 0 1 1 1l-1 1c-2 0-4 0-6-1v-1l3-1c0-1 1-2 1-3-1-1-1-1-2 0v-1z" class="P"></path><path d="M183 626h1v1c-1 2-3 3-5 4l-2-2c-1-1-2-1-3-2h0 0l9-1z" class="j"></path><path d="M160 127c1-2 0-2 1-3 0 0 1 1 2 1s1 0 2-1h1c1 0 3 0 4 1h-2l-3 6c-2-2-3-4-5-4z" class="F"></path><path d="M240 323s1 1 1 2l1 2v4c0 1-1 1-1 2h-1v-2c-1 1-2 1-3 1h-1c1-2 1-3 2-4h-1l2-1c1-1 1-3 1-4z" class="B"></path><path d="M238 328l2 1v1h-1l-2 2h0-1c1-2 1-3 2-4z" class="E"></path><path d="M199 623v-2c1-1 5-2 7-2 1 0 1 0 1 1v1l-3 3c-1 0-1-1-1-1-1 0-2 1-2 2l-1-1v1c-1-1-1-1-1-2z" class="X"></path><path d="M147 112c0 1-1 1 0 2s1 1 2 1h1c1 0 2 0 3-1l1 1v-1h1 1 3l-2 2v1l-9-1c-2 0-4 0-5 1h-1v-1h1l2-2c0-1 1-1 2-2z" class="C"></path><path d="M466 172h0c-2 3-2 5-3 8l2 3-1 1c-2-4-4-8-6-11v-1c1 1 3 2 4 2 2 0 3-1 4-2z" class="P"></path><path d="M132 117c1 0 1 1 1 1 1 0 1-1 2 0-1 2-4 2-5 5 0 0-1 0-1 1-1-1-2-1-3-1h0c0 1 0 1-1 1h-1-1v-1l9-6h0z" class="B"></path><path d="M236 188h1c1 1 2 1 4 1 1 1 1 2 1 3h-1c0 1 0 2-1 2h0c1 1 1 2 2 3v1c1 1 4 4 4 5-1-1-2-2-4-3-2-3-6-8-6-12z" class="P"></path><path d="M476 112c-1 0-1 0-1-1-1 0-1-1-1-1 0-1 0-2 1-3h2c1 0 2 1 3 1 1 1 1 3 1 5h-1c-2 0-3 0-4-1z" class="l"></path><path d="M122 106c2-1 7 0 9 0h-1c2 1 3 2 4 4-1 0-2-1-3-1v2c0 1 1 1 1 2l-1 1c0-1-3-5-4-6-2-1-3-1-4-2h-1z" class="D"></path><path d="M130 106c2 1 3 2 4 4-1 0-2-1-3-1h0c-1-1-3-1-3-1v-1l2-1z" class="G"></path><path d="M148 633c1-1 0-2 0-3 2-1 7-1 9-1-1 1-2 2-2 3-1 0-1 1-2 2s-2 2-4 2h-1l1-1c1 0 1-1 1-2h-2z" class="C"></path><path d="M153 634s-1 0-1-1 0-1 1-2h1l1 1c-1 0-1 1-2 2z" class="j"></path><path d="M259 405l2 3c1 0 3 1 4 2l5 4 2 2c-3-2-5-4-8-5-2-1-5-1-8-1h-1c0-2 3-4 4-5z" class="I"></path><path d="M216 623c1 0 1 0 1-1 1 1 1 1 2 1 2 1 3 2 5 2l1 1v2h-1c-1 1-4 1-5 1-2-2-3-3-3-5h0v-1z" class="M"></path><path d="M216 623c1 0 1 0 1-1 1 1 1 1 2 1 2 1 3 2 5 2l1 1-2 1c-2 0-4-1-6-3h-1 0v-1z" class="D"></path><path d="M314 307v-1c2 1 1 2 1 4h0l1 3h0l-1-1h-1v1c1 1 1 2 1 3v1c1 1 1 2 2 3 1 0 0 0 1 1-1 0-1 0-1-1h-1s-1 0-1-1h-1c0-1 0-3-1-4h0s-1-1-1-2-1-1-2-1l-1-1h0c1-1 2-2 4-2h0l1-2z" class="T"></path><path d="M117 155l1 1c3 1 5 1 8-1 1 0 2-1 3-2h1c-2 2-3 3-5 4-1 0-3 1-4 1 0 2 0 3 1 4v-1c2 0 2-1 4-1l1 1c-1 1-2 1-3 1-2 1-3 3-4 5h-1l1-2v-1-5c-1 0-1-1-2-2 0-1-2-2-4-3 1 0 3 1 3 1z" class="j"></path><path d="M189 634c1 0 2 0 2-1l-1-3c0-1 4-1 5-3 1 0 1 1 1 2l-1 1 1 1c1-1 2 0 3 0v1c-4 2-6 3-11 3l1-1z" class="X"></path><path d="M294 239v-2h1c1 0 1-1 2-1h0l2 2h0l2 2h1c2 1 2 1 4 1v-1l7-1h0c-1 0-2 1-2 1v1h-1-1-1c-3 2-6 0-9-1l-1 1v1c-1 0-1 1-2 1-1-1-1-2-2-4h0z" class="C"></path><path d="M317 206c1 7 1 15-2 21 1-5 1-10 0-15l-3 3h0c0-2-1-3-1-4 1 0 1 0 2 1 1-1 2-1 2-1v-1c1-2 1-3 2-4z" class="M"></path><path d="M238 215c-1-1-2-1-3-2 0 1-1 1-1 1v2 7c1 1 1 2 1 3h-1c-3-5-1-18-1-24h1v2 1c-1 1-1 6 0 7h1 2l2-2c0 2-1 2-1 4v1z" class="X"></path><path d="M282 224c1 0 2 1 3 1 1 1 1 2 3 2v4c-1 1-1 2-3 2v2l-1-2h-1c0-2 0-3-1-4h1c0-1-1-1-1-2h0 1c0-1 0-2-1-3z" class="J"></path><path d="M283 233v-3c2 0 2 0 4 1v1c-1 0-1 1-2 1v2l-1-2h-1z" class="I"></path><path d="M224 593v-1h1c0 1 0 1 1 2h-1 0v2 1 1 1 1 1l1 1v1h-1c-1 0-1 0-2-1h-1c0 1-1 1-1 1l-1 1v1c1 0 1 0 2-1h1c1 1 2 1 3 2v-1h0v-2-1-1-2l1-1v3s0 1 1 0c0 1 0 2 1 2v1 1h-2 0c1 1 1 2 1 3h-1c-1 0-1-1-2-2s-4-1-5 0l-1-1v-1c1-1 2-3 4-4l1 1c-1-2 1-5 0-8z" class="F"></path><path d="M291 223l3-2c0 1 0 2-1 2 0 1 1 3 1 4h-1c-1 2 1 0 0 1l-2 2v1h2v-1h0c1 0 0 0 1-1v-1h0v-1c1-2 1-4 4-5h0 0c-1 1-2 2-2 4h0c0 1 0 1 1 2-2 1-3 2-5 4-1 0-1 0-2-1v-1l2-2v-1c-1 0-2 1-2 1-1-1-1-1-1-2l2-3z" class="E"></path><path d="M291 223l3-2c0 1 0 2-1 2l-1 1v1 1 1c-1 0-2 1-2 1-1-1-1-1-1-2l2-3z" class="F"></path><path d="M257 353l1 1 1 1c0 2 1 3 2 4l-3 3h1l-1 1h-1v-2h0 0c-1 0-2 1-3 1h-3c1-1 2-1 4-2l-1-3h-1l1-1 2-2 1 1v-2z" class="E"></path><path d="M253 357l1-1 2-2 1 1c0 2 0 2-1 3l-1 2-1-3h-1z" class="R"></path><path d="M314 205h3v1c-1 1-1 2-2 4v1s-1 0-2 1c-1-1-1-1-2-1 0 1 1 2 1 4h-1c-2-3-4-3-5-7 2 0 6 1 7 0s2-2 1-3z" class="G"></path><path d="M298 241l1-1c3 1 6 3 9 1h1 1 1l4 2c-1 0-1 1-2 2 1 0 1 1 2 1l-3 1v1c-1-1-2-3-3-4s-2-1-3-1h-5 0c-1-1-2-1-3-2z" class="E"></path><path d="M311 241l4 2c-1 0-1 1-2 2-1-2-2-2-3-4h1z" class="b"></path><path d="M481 106c3 7 2 15 3 22h-1l-1-8-2 2-1-2c1-1 1-4 1-5l1-1-1-1h1c0-2 0-4-1-5-1 0-2-1-3-1h3l1-1z" class="a"></path><path d="M480 115c2 1 2 4 2 5l-2 2-1-2c1-1 1-4 1-5z" class="F"></path><path d="M310 251c3 5 5 10 6 16l-1-1v-1c-1 0 0-1 0-1h-1l-1 1v-1c-1-3-2-6-4-8l-2-3h1c0-1 1-2 2-2z" class="E"></path><path d="M314 307l-1 2h0c-2 0-3 1-4 2h0l1 1c1 0 2 0 2 1s1 2 1 2l-1 1c-1 0-2 1-2 1h-2-1l1-2c-2-3-5-3-7-5h-1 1 6c2 0 5-2 7-3z" class="J"></path><path d="M308 315v-2c1 0 2 0 3 1s1 1 1 2c-1 0-2 1-2 1h-2-1l1-2z" class="H"></path><path d="M465 183c1 2 3 6 5 8h0 4v2l-1 1s-1 1-1 2c1 2 1 4 0 6-2-4-3-8-5-12 0-2-2-4-3-6l1-1z" class="b"></path><path d="M470 191h4v2l-1 1s-1 1-1 2h0l-2-5z" class="D"></path><path d="M241 116h3 1v-1h-1c-1-1 0-1 0-2l-1-1-1 1c-1 0-2 0-3 1h0-1v-1c1-2 1-2 3-3 2-2 5-1 8 0-1 1-1 2-1 2l-4 1c2 0 2-1 3 0 0 1-1 1-1 2-1 1-1 3-1 4-2-1-3-2-4-3z" class="j"></path><path d="M220 606c1-1 4-1 5 0s1 2 2 2c-1 2-1 3-2 4s-2 0-3 0h-1c-1-2-2-2-3-4 1-1 1-2 2-2z" class="M"></path><path d="M278 246c1 0 1 0 2 1h1 1l1 1h1c1 0 3 0 4 2 1 0 3-1 3 0 1 0 1 1 1 2l1 1c-2 0-3 0-4 1 0 0-1 0-1 1v2h-1c-1-1-1-2-1-3 1-2 2-3 3-4h-6 0-3c-1-1-2-1-3-2v-1l1-1z" class="F"></path><path d="M242 198c1 0 2-1 3-1 0 1 0 1 1 1 1 1 2 3 4 3 0 0 1-1 2-1l1 1v1c0 1-1 1-2 1 1 1 2 1 2 2-1 0-1 0-1 1h-1 0v1l-1 1c-1-2-3-3-4-5 0-1-3-4-4-5z" class="B"></path><path d="M251 206c-1-1-1-1-1-2l1-1c1 1 2 1 2 2-1 0-1 0-1 1h-1z" class="C"></path><path d="M138 115c0-1 0-1 1-1l1-2c2-2 6-4 8-5l2 1-1 1c-1 0-1 0-2 1l-1 1 1 1c-1 1-2 1-2 2l-2 2h-1v1h0-2-1 0c-1 0-1-1-2-1l1-1z" class="B"></path><path d="M305 182c1 0 2 0 3 1h0l2 3v1c1 1 1 2 3 2 1-1 1-2 2-3 0 1 0 3-1 4 0 1-1 2-2 3-1 3-2 6-5 8l-3 3h-1 0c3-2 5-4 6-7 0-2 0-2-1-3 1 0 1-1 1-2 1-1 1-3 0-4v-2l-2-2-2-2z" class="L"></path><path d="M274 115c-4-4-9-8-15-10l13 1c1 3 1 5 3 7-1 1-1 1-1 2z" class="P"></path><path d="M298 242v-1c1 1 2 1 3 2h0 5l-2 1 6 6v1h0c-1 0-2 1-2 2h-1c-1-1-3-3-4-5-2-2-2-4-5-6z" class="h"></path><path d="M308 253c-2-3-4-6-5-9h1l6 6v1h0c-1 0-2 1-2 2z" class="D"></path><path d="M275 113h0c0-1 1-2 1-3 1-2 2-3 3-4l12-1-4 2c-4 1-9 5-12 9l-1-1c0-1 0-1 1-2z" class="c"></path><path d="M233 249c1-5 0-9 0-13 1 1 3 2 4 3h3c1 0 2 1 4 1h0c1 0 1 0 1 1-1 0-6-1-7-2v2h2v1c-2 2-4 5-7 7z" class="I"></path><path d="M238 241c-2 0-3 2-4 4v-7h0c1 1 2 1 3 1h1v2z" class="W"></path><path d="M426 115c1 0 1 0 1 1h1 3 1l1-1h-5v-1c0-1 0-1 2-1h1s1-1 2-1l2 3c0 1-1 1-1 2s0 1-1 1h0c0 1 0 2-1 2s0-1-1-3l-1 1s-1 1-2 1h0l-1-1c-2 0-3 0-4 1h-1c0-1-1-2-1-2l-1-1c2 0 4 0 6-1z" class="B"></path><path d="M284 211v1l2-1c0-1 0-2 1-3h0l1 2v1c-1 2-2 3-3 4s-3 2-4 2l-1 1h-1l-1-1c-1-2-1-3-1-4l1-2v-1c1 1 1 2 2 2 2 0 2 0 4-1z" class="M"></path><path d="M288 255c0-1 1-1 1-1 1-1 2-1 4-1h2 0l-1 1h0c0 1 1 2 2 3l-1 2-1 1c-1 1-3 1-5 1-1-1-2-2-2-4h1v-2z" class="E"></path><path d="M288 255c0-1 1-1 1-1 1-1 2-1 4-1h2 0l-1 1h0-1c-1 1-2 1-3 2v1h-1c-1-1-1-1-1-2z" class="I"></path><path d="M408 106c1 0 3-1 4 0 3 1 4 2 6 4-1 0-3 0-4 1v1l-6-3-5-1c-1-1-3-2-4-2h9z" class="b"></path><path d="M408 106c1 0 3-1 4 0 3 1 4 2 6 4-1 0-3 0-4 1-1-1-3-4-4-5h-2z" class="B"></path><path d="M97 115c1-1 2-1 2-1h0v1 1l2 2v1c1 0 2 0 2-1l1 1v1c-1 2-2 7-3 8-1-2-2-3-3-5-2-2-2-3-2-5l1-3z" class="C"></path><path d="M252 159c1 0 2 0 3 1h1c2 1 3 1 4 2l2 3-1 1h0c0 1-1 1-1 1l-7-4c-2 1-5-2-8-2v-1c1 0 2 0 3 1h1v-1c1-1 2-1 3-1z" class="V"></path><path d="M256 160c2 1 3 1 4 2l2 3-1 1c-1-2-3-3-4-3 0-1-1-2-1-3z" class="j"></path><path d="M252 159c1 0 2 0 3 1h1c0 1 1 2 1 3l-3-1c-1 0-3-1-5-1v-1c1-1 2-1 3-1z" class="X"></path><path d="M345 106l1-1c1 0 1 0 2 1h5c1 0 2 1 3 1h1c-1 0-1 0-1 1-1 0-2 1-3 2h3c-2 1-4 1-6 2h0c-2 0-3-1-5-2 0-1-1-2-1-3l1-1h0z" class="F"></path><path d="M348 106h5c-1 1-2 1-3 2v1h2c-1 1-1 1-2 1s-2-1-3-2c0-1 0-1 1-2z" class="P"></path><path d="M306 206c3-2 6-5 7-8 1-2 1-4 2-6h0c0 4 0 8-1 12v1c1 1 0 2-1 3s-5 0-7 0h-2c1-1 2-1 2-2z" class="C"></path><path d="M308 328c1-1 1-1 2-1 0 3 1 3 3 4v1h1c-1 1-1 2-2 3h-1l-1 1h-2l-1-1h-1c0 1 0 2-1 2h0 0v-1c-1-3-1-6 0-9v1c2 0 2-1 3 0z" class="B"></path><path d="M308 328c1-1 1-1 2-1 0 3 1 3 3 4v1c-1 0-1 0-2 1-1-1-2-1-3-2h0v-3z" class="J"></path><path d="M305 328c2 0 2-1 3 0v3h-1-1 0-1v-3z" class="Y"></path><path d="M483 128h1l3 18h0-2c-1-2-3-4-3-5h1c0 1 1 2 2 3l1-1c0-1-1-3-1-5h-1l-1 1v-1c0-1 1-2 1-3l-1-1c-1 2-2 5-2 7v3c-1 2-1 4-2 6v-1-2c0-2 0-5 1-6 1-5 3-9 3-13z" class="P"></path><path d="M453 168l-7-8c2 0 4 1 6 2h0 1v-2l1-1h-1c0-1-2-1-2-1-2-1-6-3-7-5 2 1 3 2 5 3 5 1 8-1 12-3-1 2-3 3-4 4s-1 3-1 4c-1 2-1 3 0 5h0 0-2l-1 2z" class="C"></path><path d="M88 150c1-2 2-4 2-5l3-12c1 1 1 3 1 3 1 3 1 6 2 9l-1 8v-5l-1 1c-1 0-1 1-2 0v1l-1-1c-1 0-2 0-3 1z" class="h"></path><path d="M92 149l-2-2 3-7c1 3 2 6 1 9-1 0-1 1-2 0z" class="Y"></path><path d="M352 131c1-1 2-3 4-3-1 2-1 3 0 4v1c2 0 2 0 2-1l1-2h1c1 2 1 2 3 3h1v-2c1 0 2 1 3 1h0 2c0 1 1 1 1 2v1l-24-1 6-3z" class="I"></path><path d="M364 131c1 0 2 1 3 1h0 2c0 1 1 1 1 2-2 0-5 0-7-1h1v-2z" class="F"></path><path d="M84 216v1 2c1 1 3 1 4 1l2 1c0 2-1 5-1 7 0-1-1-1-1-1-3 1-4 0-7 0l2-2-1-1h-1-1c1-3 2-5 4-8z" class="M"></path><path d="M81 224h1c0-1 0-1 1-2 1 0 1 1 2 1h0 2s1 0 1 1v2 1c-3 1-4 0-7 0l2-2-1-1h-1z" class="F"></path><path d="M233 155h0v-4h1l1 2v5h0c1 0 1 1 2 1l1 1h2 0 5v1c-2 1-4 1-5 2s-2 2-3 2-2-2-2-2h-1c1 4 0 7-1 10-1-6 0-12 0-18z" class="P"></path><path d="M235 158h0c1 0 1 1 2 1l1 1h2 0 5v1c-2 1-4 1-5 2h-3v1l-2-3c-1-1-1-2 0-3z" class="T"></path><path d="M281 217c1 0 3-1 4-2 1 1 1 3 2 3h1v1l1 2 2 2-2 3-1 1c-2 0-2-1-3-2-1 0-2-1-3-1 0-1 0-2 1-2-1-1-1-2-1-3h0c-1 0-1-1-2-1h0l1-1z" class="R"></path><path d="M287 218l1 1 1 2c0 1 0 2-2 3-1-2-1-3-3-4l1-1s1 0 2-1z" class="D"></path><path d="M289 221l2 2-2 3-1 1c-2 0-2-1-3-2 0-1 1-1 2-1 2-1 2-2 2-3z" class="e"></path><path d="M281 217c1 0 3-1 4-2 1 1 1 3 2 3h1v1l-1-1c-1 1-2 1-2 1l-1 1-1 2c-1-1-1-2-1-3h0c-1 0-1-1-2-1h0l1-1z" class="E"></path><path d="M281 217c1 0 3-1 4-2 1 1 1 3 2 3h1v1l-1-1h-2-2l-2-1z" class="H"></path><path d="M429 134l2-2 1 6 1 6 3 8c-3-2-7-4-11-7h1l-1-1c1 0 2 0 3-1l-1-1c1-1 2-1 4-1h0c-1-1-2-1-2-2h0c1 0 1-1 1-2v-1-1l-1-1z" class="P"></path><path d="M428 143c1-1 2-1 3-1l1 1-1 1c0 1-1 1-2 2-1 0-2-1-3-1l-1-1c1 0 2 0 3-1z" class="i"></path><path d="M429 134l2-2 1 6 1 6h-2l1-1-1-1c-1 0-2 0-3 1l-1-1c1-1 2-1 4-1h0c-1-1-2-1-2-2h0c1 0 1-1 1-2v-1-1l-1-1z" class="e"></path><path d="M233 464v4c1 1 1 2 1 3h0v2h1s0-1 1-1v1 3c1 0 1-1 2-1s3 2 3 2c1 1 2 1 3 0l-1 1h-2c1 1 1 1 1 2h0c-4 0-7 3-9 5h0v-21z" class="R"></path><path d="M237 477h2c1 0 2 1 2 1v1l-2-1-1 2c-1 0-1 1-2 1s-1-1-1-1v-2c1-1 1-1 2-1z" class="S"></path><path d="M420 126h1 0c1-1 2-1 3-1v-1h2l1 1s1 0 1-1c2 0 4 1 6 1l1 1h0c-1 1-2 1-2 1v1 1c0 1 0 0-1 1s0 6 0 8l-1-6v-6c-2 0-4 1-6 2-1-1-1-1-3-1s-4 2-5 4c-1 1-2 2-3 4-1-1-1-1-3-1 2-3 3-5 6-6h1l2-2z" class="H"></path><path d="M207 620c1-1 2-2 4-2s3 1 5 2v3 1l-2 1v2c-1 1-1 2-2 2h-3c-1 0-2-1-2-2s0-1 1-2h2 1v-2c-1-1-2-2-4-2v-1z" class="C"></path><path d="M143 135c1 1 2 1 3 2 1 0 3 0 4 2h1l1 1-2 1v1c-2 0-2 1-4 1-1 0-1 0-1 1l-2 1h1 3c-3 1-6 3-8 5h0v-2c2-1 2-2 2-4 1-3 2-6 2-8v-1z" class="j"></path><path d="M145 144l-2-1c0-1 0-1 1-2 0-1 0-1 1-1l3 1 2-2v1 1h0v1c-2 0-2 1-4 1-1 0-1 0-1 1z" class="B"></path><path d="M294 163l2-1v1c-2 3-7 5-9 8 0 1-1 1-2 2l-4 10c-1 1-1 1-1 2l-1 2v2l-1-1v-2l1-1c0-2 1-5 2-7v-1c1-1 2-2 3-4h0l2-2-1-1-1-3 4-4 2 1-2 2c2 0 4-2 6-3z" class="G"></path><path d="M288 163l2 1-2 2c-1 0-1 0-2 2h1v1c0 1-1 1-1 2l-1-1-1-3 4-4z" class="E"></path><path d="M466 172l1 1v4h0l-1 1c0 1 1 1 1 2 1 2 2 4 5 5h1c-1 1-1 2-1 3 1 1 2 2 2 3h-4 0c-2-2-4-6-5-8l-2-3c1-3 1-5 3-8z" class="H"></path><path d="M470 191v-3c-1-2-3-5-4-8h1c1 2 2 4 5 5h1c-1 1-1 2-1 3 1 1 2 2 2 3h-4 0z" class="l"></path><path d="M224 593c1 3-1 6 0 8l-1-1c-2 1-3 3-4 4v1l1 1c-1 0-1 1-2 2 1 2 2 2 3 4l-1 1-2-1h0c-2 1-4-1-6-1-1 0-3 3-4 3h-1c2-3 5-5 8-8 4-3 7-8 9-13z" class="P"></path><path d="M218 612c-1-1-3-1-5-2 1-1 3-2 3-3 1-1 2-1 3-2l1 1c-1 0-1 1-2 2 1 2 2 2 3 4l-1 1-2-1z" class="B"></path><path d="M84 216c2-5 7-7 12-10 0 1-4 11-5 12l-1-2c-1 1-1 2-2 4-1 0-3 0-4-1v-2-1z" class="b"></path><path d="M84 217c1-1 2-2 3-2s2 0 3 1c-1 1-1 2-2 4-1 0-3 0-4-1v-2z" class="E"></path><path d="M275 189c0-1 1-3 1-5 2-6 4-12 8-17l1 3 1 1-2 2h0c-1 2-2 3-3 4v1c-1 2-2 5-2 7l-1 1v2l1 1c0 1-1 3-1 4s0 1-1 2c-1-1 0-5 0-6h0c-1 1-1 1-1 2l1 1h-1v-3c0 1 0 2-1 2h0v-2z" class="B"></path><path d="M242 127c2-1 5-4 5-7 0 2 1 2 2 3l-2 2c1 0 2 0 3 1h0l1 1h0l1-1h0l1-1c0 1 0 1 1 2-1 1-1 1-1 2v1h-2l-1 1v-1c-1 0-1 0-1 1-1 0-2 0-2 1-1 0-2 1-3 2-1 0-1-1-2-1-1-1-1-1-2-1v-1c1 0 2-1 3-2v-1c-1 0-1 1-2 1l1-2z" class="C"></path><path d="M253 125c0 1 0 1 1 2-1 1-1 1-1 2v1h-2 0v-1c-1-1-3-1-5-2l1-2c1 0 2 0 3 1h0l1 1h0l1-1h0l1-1z" class="Y"></path><path d="M258 439c1 1 4 3 6 2 1 0 2-1 2-1v-1-1h-1v-1h0 1v-1-2c0-1 0-1 1 0 2 0 6 5 7 6h-1-3 0l1 1-3 1v2h-1c-1-1-3-1-4 0-1 0-2 1-2 1-1 2-2 4-2 6l-1-1 1-2v-3h0c0-2 0-3-1-5h-1l1-1z" class="I"></path><path d="M233 453v-2l1 1c0-1 0-1 1-1l1 1h0l1-1c0 1 1 1 0 3v2h1v1h0c1 2 2 3 2 4s0 1-1 1c-1 2-2 3-4 4-1 0 0 0-1 1l-1 1v-4-11z" class="O"></path><path d="M233 453v-2l1 1c0-1 0-1 1-1l1 1v6c1 2 1 4 1 5l-1 1c-1-1-1 0-1-1-1-1-1-3-1-4v-3c-1-1 0-2-1-3z" class="Y"></path><path d="M142 629c-1 1-2 2-2 3s-1 2 0 3h2 0c-1 1-5 0-6 1h-7v-8h0c3 0 5 1 6 2h1v-1h5 1z" class="P"></path><path d="M134 633c0-1-3-1-4-2v-1h0c3 0 4 0 5 2l-1 1z" class="G"></path><path d="M135 632l2 1c0-1 0-1-1-2 1-1 1-1 2-1l1 1-1 4h0-8v-3h0 1c1 1 2 1 3 1l1-1z" class="Y"></path><path d="M217 622c1 0 1 0 2-1l7 2 6-5v1c0 1 1 2 1 2h2v1h1l1 1c-1 0-3 0-3 1l1 1h-1l-9 3v-2l-1-1c-2 0-3-1-5-2-1 0-1 0-2-1z" class="C"></path><path d="M232 619c0 1 1 2 1 2h2v1h1l1 1c-1 0-3 0-3 1-1 0-2 0-3-1 0-1 0-2 1-4z" class="V"></path><path d="M307 195l1-1c1 1 1 1 1 3-1 3-3 5-6 7h0l-1 1 1 1h2 0 1c0 1-1 1-2 2-2 2-3 4-5 6v-1c0-1-1-2-1-3l-1-1c1-1 1-2 2-3 1 0 1-1 2-1-1-1-3-1-3-3 4-1 6-2 9-5l-3-2h1 2z" class="F"></path><path d="M296 201h1v1h1c0 2 2 2 3 3-1 0-1 1-2 1-1 1-1 2-2 3l1 1c-1 2-2 2-4 3h0c1-1 2-3 3-4-1-3-1-4-4-6-1 0-3 0-4 1l-2 2c0 1 1 1 1 1l-1 1h0c-1 1-1 2-1 3l-2 1v-1c0-1 0-2-1-2-1-1-1-2-3-2 4-4 6-5 11-5h4 1v-1z" class="O"></path><path d="M94 149l1-1v5c1 1 1 2 1 2v2l-1 1-2 3h-1-1-2v2l-2 1c1-4 1-6 3-10h0v-1h-1v1l-2-2c0-1 0-1 1-2s2-1 3-1l1 1v-1c1 1 1 0 2 0z" class="F"></path><path d="M91 160h-1c-1-1-1-2-1-3l2-2 1 2s0 1-1 1v2z" class="X"></path><path d="M94 149l1-1v5c1 1 1 2 1 2-1 0-1-1-2-2s-2-1-2-3v-1c1 1 1 0 2 0zm-2 8h4l-1 1-2 3h-1-1v-1-2c1 0 1-1 1-1z" class="P"></path><path d="M277 388v-5-1c1 1 1 1 3 1v1h1c1 0 2 0 3 1l1 2v2h0-1v1h1c0 2-1 3-2 5h-1-1v-1h-1s0 1-1 1v1c0 1-1 2-1 3v-1c0-3-1-7-1-10z" class="J"></path><path d="M280 384h1v1l-1 2h-2c0-1 1-2 1-3h1z" class="R"></path><path d="M281 384c1 0 2 0 3 1-1 2-3 4-4 6l-1-2c0-1 0-1 1-2l1-2v-1z" class="k"></path><path d="M284 385l1 2v2h0-1v1h1c0 2-1 3-2 5h-1-1v-1h-1s0 1-1 1v1l1-5c1-2 3-4 4-6z" class="H"></path><path d="M276 235c0-2-1-6 0-8 0-1 1-1 2-2 1 1 2 1 2 2l1-1 1 1h0c0 1 1 1 1 2h-1c1 1 1 2 1 4h1c0 1 0 2-1 2v1l-1 1v1c-1 0-1 0-2 1 0 1 0 1 1 2h-1l-1 1v-1-1c0-2-1-2-2-3s-1-1-1-2z" class="F"></path><path d="M280 227l1-1 1 1h0c0 1 1 1 1 2h-1l-1 3h0l-1-1c-1-2 0-3 0-4z" class="R"></path><path d="M277 237l2-1v-2h0c-1-1-2-4-2-5h0c1 0 1 0 1 1s1 4 3 5v1h1 1l-1 1v1c-1 0-1 0-2 1 0 1 0 1 1 2h-1l-1 1v-1-1c0-2-1-2-2-3z" class="S"></path><path d="M218 124h0c-2-5-6-9-10-13-2-1-5-3-8-4 1-1 2 0 3 0l7-1c0 1-1 1-1 1-1 1-2 1-3 1v1h2l1 1c1 2 4 3 6 4 1-1 2-1 2-2 1-1 3-4 3-5h-2v-1c3 1 5 0 8 0-2 1-4 2-5 4-2 4-3 8-2 13h0c0 1-1 1-1 1z" class="j"></path><path d="M232 133c2 1 3 1 4 2 1 2 1 6 3 7h3l3 1h0l-2 1c-2 1-7 2-8 4v5l-1-2h-1v4h0v-12-8l-1-2z" class="c"></path><path d="M278 217l1 1h1 0c1 0 1 1 2 1h0c0 1 0 2 1 3-1 0-1 1-1 2 1 1 1 2 1 3h-1l-1-1-1 1c0-1-1-1-2-2-1 1-2 1-2 2-1 2 0 6 0 8l-1-2c-1-1 0-5 0-6-1-1-2-1-3-1v-1-2l1-1-1-1h0c1-1 1-1 1-2s0-1 1-1c1 1 1 1 1 2 1 0 1-1 3-1v-2z" class="G"></path><path d="M414 111c1-1 3-1 4-1 1 1 1 2 3 2h0l1-1 3 3 1 1c-2 1-4 1-6 1l-4 1h-2 0l-2 1h-5c0-1-2-1-2-1l-1-1 1-1h4 0l2-1h2c0-2 0-2 1-2v-1z" class="O"></path><path d="M405 115h4 1v2l-3 1c0-1-2-1-2-1l-1-1 1-1z" class="C"></path><path d="M414 111c1-1 3-1 4-1 1 1 1 2 3 2h0l2 1v1c-1 1-3 1-5 0h-2v-1h-1c-1 1-1 1-2 1 0-2 0-2 1-2v-1z" class="H"></path><path d="M233 440l-1-22 2 2c1 3 4 6 6 8l1 3h0c-2 1-3 1-4 2v1h-1v3c-1 1-2 2-2 3h-1zM131 106c6-1 13-1 19 0v1h-2c-2 1-6 3-8 5l-1 2c-1 0-1 0-1 1-1-2-3-4-4-5-1-2-2-3-4-4h1z" class="j"></path><path d="M425 106h26-1c-2 1-5 0-6 0l-9 9-2-3c-2-3-5-4-8-6z" class="c"></path><path d="M316 111c2 3 2 4 2 7h0v1l1 1-1 1h0c-2 1-4 3-6 4h0-1-1c-1 0-2-1-3-2-1-2-1-3-2-5 1-1 1-1 2-1l-1-1h3l1 1h0 1c1 0 2 1 3 1 1-2 1-4 2-6v-1z" class="I"></path><path d="M316 111c2 3 2 4 2 7h0v1l1 1-1 1h-1c0-1-1-1-1-1v-3-1c1-2 1-2 0-4v-1z" class="V"></path><path d="M314 118c0 2 0 3-2 4h-2c-1-1-2-2-4-3v-1c1 0 2-1 4-1h0 1c1 0 2 1 3 1z" class="Y"></path><path d="M283 434c1 0 2 0 2 1v3l-3 3c2 0 5 0 7-1l3-1v1 2c1 1 2 1 3 1l-1 1h0c0 1-1 2 0 2-1 1-1 2-3 3v-1l-1-1-1-2c-1-1-2-2-3-2s-2 1-3 2c-1-1-2-2-3-2l-1-1c0-1-1-1-1-1v-1h0c1-2 3-5 5-6z" class="h"></path><path d="M291 447v-2c0-1 0-2 1-3 1 1 2 1 3 1l-1 1h0-1c-2 1-2 1-2 3z" class="D"></path><path d="M291 447c0-2 0-2 2-3h1c0 1-1 2 0 2-1 1-1 2-3 3v-1-1z" class="G"></path><path d="M257 135v2c1 2 1 2 2 3 2 1 3 1 4 2l2-1-1-2c1-1 2-1 2-1 1 0 3 0 3 1h0v1h0v1 4l-1-1-3 1c-6-3-13-3-20-2h0l-3-1h7c2-1 6-5 8-7z" class="C"></path><path d="M266 138c1 0 3 0 3 1h0v1h0v1 4l-1-1-3-4h0c0 1 0 1-1 2h-1l2-1-1-2c1-1 2-1 2-1z" class="S"></path><path d="M281 124l-3-5c5-4 10-8 16-10-1 1-4 2-5 4h0c-1 1-1 1-1 2-1 1 0 3 0 4 1 1 2 2 3 2h1v1c-2 0-2 1-3 2l-1-1v-1h-2 0l1 4-2-3h-1l-3 1z" class="c"></path><path d="M283 445c1-1 2-2 3-2s2 1 3 2l1 2 1 1v1 1c1 1 2 2 1 4l-3 1h-2c-1 0-2-1-3-2 0-2-2-3-3-5-1 0-1-1-1-1 1-1 2-1 2-2h1z" class="l"></path><path d="M283 445c1-1 2-2 3-2s2 1 3 2l1 2c-1 0-2 0-3-1-1 1 0 2-2 2 0 0-1-1-2-1 0 0-1 0-1 1s3 2 3 3l-1 2c0-2-2-3-3-5-1 0-1-1-1-1 1-1 2-1 2-2h1zm-39-212l2 2c1 1 2 1 4 2h2l-2 2v1h-1 0-1v1s1 0 1 1h0l-1 1h-4-2c-1 0-1 2-2 3-1 2-2 4-3 5l-1 2c-1 0-2-1-3-2h0v-2c3-2 5-5 7-7v-1h-2v-2c1 1 6 2 7 2 0-1 0-1-1-1 1 0 1 0 2-1 0-1-3-3-4-5 1 0 1 0 2-1z" class="H"></path><path d="M233 251l1-1c1 0 2 0 2 1h1l-1 2c-1 0-2-1-3-2z" class="F"></path><path d="M236 188c2-1 4-3 5-5v-2c-1-1-1-1-2-1s-2 1-2 2h0-1c1-3 2-6 4-9 1-3 3-4 7-5v1c-1 0-1 1-2 2 0 2 0 6 1 7s1 2 2 2c1 1 2 1 2 1h0-5v1c-1 1-3 2-4 3-1 2-2 3-4 3h-1z" class="c"></path><path d="M261 117h1c1-1 1-1 0-2-2-3-5-5-8-7 7 2 12 7 18 11-2 4-5 7-6 11l-1-5h0c0-2-3-4-4-5h0v-3z" class="X"></path><path d="M403 108l5 1 6 3c-1 0-1 0-1 2h-2l-2 1h0-4c-1 0-1 0-2-1h-1v1l-1 1h-4 0c-1-1-1 0-1 0h-2l-1-1c0-1 0-1 1-1h3 1v-1l-1-1h-1c-1 1-1 0-2 0h0v-1-1l1-1 1 1v1h1v-2c4 0 7 2 11 2-1-1-3-2-5-2h0v-1z" class="V"></path><path d="M409 115l-1-1v-2h1l2 2-2 1z" class="Y"></path><path d="M235 354l1 1 4 8c0 1 0 2 1 2 0 1 0 1 1 1h5c-2 2-5 3-6 5-2 2-5 4-6 7-1 2-2 5-2 8 0-6-1-12 0-17 0 0 1 0 1 1h0c2 1 5 2 7 0v-1c-1-1-4-1-5-2v-1c1-2 0-9-1-11v-1z" class="b"></path><path d="M423 106h2c3 2 6 3 8 6-1 0-2 1-2 1h-1c-2 0-2 0-2 1v1h5l-1 1h-1-3-1c0-1 0-1-1-1l-1-1-3-3-1 1h0c-2 0-2-1-3-2-2-2-3-3-6-4h11z" class="V"></path><path d="M412 106h11 0c1 2 3 3 4 5v1c-2-1-3-5-5-4 0 0-1 0-1 1 1 1 1 1 1 2l-1 1h0c-2 0-2-1-3-2-2-2-3-3-6-4z" class="C"></path><path d="M281 402v-1h1c2 0 3 1 5 1l3 3 2 2c0 1 0 2-1 2-4 1-6 3-10 5l-1-1h-1l1-4c1-1 1-3 1-4h-1v-1l1-2z" class="G"></path><path d="M281 402v-1h1l1 1h1l-3 3h-1v-1l1-2z" class="I"></path><path d="M282 401c2 0 3 1 5 1l3 3h0c-1 0-1 0-2-1v1l1 1-5-4h-1l-1-1z" class="e"></path><path d="M280 409h2 3 0c0 1-1 1-1 2l-4 2h-1l1-4z" class="F"></path><path d="M289 406l-1-1v-1c1 1 1 1 2 1h0l2 2c0 1 0 2-1 2-4 1-6 3-10 5l-1-1 4-2c1-1 4-2 5-3v-2z" class="M"></path><path d="M307 169c4 4 5 7 7 12v2c-1 0-1 0-1-1-1 1-1 2-1 4-1 0-1 0-2 1v-1l-2-3h0c-1-1-2-1-3-1s-2 0-4-1h0l3-3c2-2 2-4 2-7l1-2z" class="b"></path><path d="M301 181c3 0 6-1 8-1v1c1 0 2-1 3-1 0 1 1 1 1 2l1-1v2c-1 0-1 0-1-1-1 1-1 2-1 4-1 0-1 0-2 1v-1l-2-3h0c-1-1-2-1-3-1s-2 0-4-1z" class="H"></path><path d="M308 183c1 0 1-1 2 0 1 0 1 1 2 3-1 0-1 0-2 1v-1l-2-3z" class="E"></path><path d="M306 243c1 0 2 0 3 1s2 3 3 4v-1l3-1c1 1 1 2 1 3h0l1 1v-1c1 1 0 3 0 4v1 17c-1-1-1-3-1-4-1-6-3-11-6-16h0v-1l-6-6 2-1z" class="L"></path><path d="M315 246c1 1 1 2 1 3h0l1 1v-1c1 1 0 3 0 4v1-1l-1 2h-1l-3-7h0v-1l3-1z" class="H"></path><path d="M315 246c1 1 1 2 1 3l-4-1h0v-1l3-1z" class="i"></path><path d="M288 207s-1 0-1-1l2-2c1-1 3-1 4-1 3 2 3 3 4 6-1 1-2 3-3 4 0 1-1 1-1 1v1h0c-1 0-2 1-2 2h-1-1l-1 1h-1c-1 0-1-2-2-3 1-1 2-2 3-4v-1l-1-2 1-1z" class="F"></path><path d="M288 207s-1 0-1-1l2-2c1-1 3-1 4-1v1c0 1 1 2 2 4h-1 1-1c-1 0-2 0-3 1l-1-1s-1 0-2-1z" class="W"></path><path d="M285 215c1-1 2-2 3-4v2c1 0 2-1 3-1s1 0 1 1l1 1v1h0c-1 0-2 1-2 2h-1-1l-1 1h-1c-1 0-1-2-2-3z" class="B"></path><path d="M292 213l1 1v1h0c-1 0-2 1-2 2h-1-1c0-2 1-3 3-4z" class="J"></path><path d="M437 123c1 0 2 1 3 2 1 0 2 0 3 1h1c0 1 0 2-1 3v1c-2 1-3 3-3 5-1 1-1 2-1 3v4 6c-1-2-3-4-3-6-1-3-1-9 0-12v-2c0-2 1-4 1-5z" class="b"></path><path d="M437 123c1 0 2 1 3 2 1 0 2 0 3 1-1 0-2 1-3 1-1 1-1 1-3 1l-1 2v-2c0-2 1-4 1-5z" class="M"></path><path d="M437 123c1 0 2 1 3 2l-4 3c0-2 1-4 1-5z" class="N"></path><path d="M161 106h0 2 2c-3 2-5 3-7 6v2h-2-1-1v1l-1-1c-1 1-2 1-3 1h-1c-1 0-1 0-2-1s0-1 0-2l-1-1 1-1c1-1 1-1 2-1l1-1-2-1h2v-1h11z" class="Y"></path><path d="M150 106h11-1c-2 2-4 5-6 6-1 1-3 0-4 0v-1c0-2 2-3 3-4-1-1-2 0-3 0v-1z" class="C"></path><path d="M165 631c-1-1-2-1-2-2 1-1 3 0 4 0h2c1 1 3 1 4 1 2 1 4 2 5 2 0 0 1 1 2 1s4-3 5-2c2 1 2 3 4 3l-1 1c-3 0-6 1-8 0l-14 1c0-1 0-1-1-2v1l-1-1c1-1 1-1 1-2v-1h0z" class="P"></path><path d="M165 631h0 1c1-1 2-1 2-1l1 1c0 1-1 1-1 2 1 0 2-1 4 0l-1 1c-1 1-3 1-5 1 0-1 0-1-1-1v1l-1-1c1-1 1-1 1-2v-1h0z" class="H"></path><path d="M172 633v1l2-2v1h2 1c1 1 1 1 2 1l1 1-14 1c0-1 0-1-1-2 1 0 1 0 1 1 2 0 4 0 5-1l1-1z" class="X"></path><path d="M289 132h3 0c1 2 4 3 6 3v2c2 0 2 1 3 2h-2c-1-1-1-1-2-1l1 1 1 1c2 1 3 2 5 3h0c-6-2-13-1-18 2l-1 1c-2 1-4 2-6 5 1-5 4-8 6-13l1 1v1c-1 1-2 1-2 2l1 1c2-1 5-2 6-4 0 0 1-1 2-1 0-2-3-3-4-6z" class="C"></path><path d="M299 140c-2-1-5-2-6-4h1l4 1c2 0 2 1 3 2h-2c-1-1-1-1-2-1l1 1 1 1z" class="B"></path><path d="M240 205h2l8 7h0c0 2-1 3 0 4h1v1h-4c-4 1-5 3-8 6h-1v-2c0-2 2-3 3-5 0 0-1 0-2-1h-1v-1c0-2 1-2 1-4h2 0v1h2 0c1-1 1-1 0-2 0-1-2-2-3-2v-1-1z" class="b"></path><path d="M241 214c1 0 1 0 2 1-1 0-1 1-2 1 0 0-1 0-2-1l2-1z" class="E"></path><path d="M239 210h2 0v1 3l-2 1h-1v-1c0-2 1-2 1-4z" class="D"></path><path d="M259 405c2-1 5-4 8-4h1 1c1 2 2 3 2 4 1 2 1 4 2 5 1 3 1 6 1 9h0l-1-1c-1-1-1-1-1-2l-2-2-5-4c-1-1-3-2-4-2l-2-3z" class="B"></path><path d="M265 410v-1-2h0l1-1c1 1 3 4 4 5v3l-5-4z" class="V"></path><path d="M259 405c2-1 5-4 8-4h1 1c1 2 2 3 2 4 1 2 1 4 2 5 1 3 1 6 1 9h0l-1-1v-1c-1-4-3-11-6-14h-3c-1 1-3 3-3 5l-2-3z" class="R"></path><path d="M88 227s1 0 1 1l-4 18c0-1-1-1-1-1v-2l-2-2c-2 0-2 0-4 1v-1c2-3-2-6-1-9 1 0 3 1 4 2h3v-3c-1 0-2-1-2-1-1-1-1-2-1-3 3 0 4 1 7 0zm228-116c-1-2-2-3-5-5 5 1 11 3 14 7 2 2 2 6 2 9 0 1-1 3-3 4 0 0-1 0-2 1v-1c1-2 2-4 1-6l-1-1c-2 1-3 1-4 2h0l1-1-1-1v-1h0c0-3 0-4-2-7z" class="c"></path><path d="M255 440l1-1-1-1h1 0c1 0 1 0 2 1l-1 1h1c1 2 1 3 1 5h0v3l-1 2c-1 0-1 1-2 2h-1-2-1v1h-1l-1-1c-1 1-1 2-2 3v-2c-1-4 0-7 2-10l2-2h1l2-1z" class="a"></path><path d="M255 440l1-1-1-1h1 0c1 0 1 0 2 1l-1 1h1c1 2 1 3 1 5h0v3l-1 2c-1 0-1 1-2 2 0-2 0-2 1-4v-2-1l-4-4 2-1z" class="D"></path><path d="M257 448v-3h2v3l-1 2c-1 0-1 1-2 2 0-2 0-2 1-4zm-7-1c0-2 1-4 3-5l3 3v2c-1 2 0 3-3 5h-1c-2-2-2-3-2-5z" class="G"></path><path d="M250 447h2v2c1 1 1 2 1 3h-1c-2-2-2-3-2-5z" class="F"></path><path d="M253 130v-1c0-1 0-1 1-2 1 1 2 3 2 4l1 2h0 1l-1 2c-2 2-6 6-8 7l-1-1h-3c-1 0-2-1-3-1h0c0-1 1-2 1-2h1v-4c1-1 2-2 3-2 0-1 1-1 2-1 0-1 0-1 1-1v1l1-1h2z" class="j"></path><path d="M253 130v-1c0-1 0-1 1-2 1 1 2 3 2 4l1 2h0 1l-1 2c-2 2-6 6-8 7l-1-1h-3c-1 0-2-1-3-1h0 5c3-1 5-3 7-5 0-2-1-2-2-3l1-2z" class="H"></path><path d="M235 351c-1-1-1-1-1-3l1 1c0 1 1 2 2 3l3 2c1 0 2 1 3 2s2 1 3 1h1 4 2 1l1 3c-2 1-3 1-4 2h-1l-3 1c-2 1-4 1-5 3-1 0-1 0-1-1-1 0-1-1-1-2l-4-8-1-1v-3z" class="C"></path><path d="M235 351v1c3 2 4 8 8 10h3 4l-3 1c-2 1-4 1-5 3-1 0-1 0-1-1-1 0-1-1-1-2l-4-8-1-1v-3z" class="H"></path><path d="M78 202c2 0 6-1 7 1h1v2c1 1 2 1 3 1-1 1-2 1-3 2s-4 3-5 4c-2 3-4 7-5 10-1 2-2 4-4 5l6-25z" class="X"></path><defs><linearGradient id="F" x1="299.147" y1="136.291" x2="306.564" y2="123.012" xlink:href="#B"><stop offset="0" stop-color="#100f0f"></stop><stop offset="1" stop-color="#2a2a2a"></stop></linearGradient></defs><path fill="url(#F)" d="M303 119c1 2 1 3 2 5s3 5 5 6v1c-1 0-2 0-4 1v2h0c1 1 1 2 2 4 0 1-1 1-2 2-2 0-3 0-4-1h-1c-1-1-1-2-3-2v-2l-1-4 2-2h1c1 0 2 0 3-1 0-1-1-3-1-4-1-2-2-3-3-5l3 1c1 0 1 0 1-1z"></path><path d="M229 127h-1c0-1-1-1-2-1-2-2-2-3-3-5 0-3 1-6 3-9 3-4 9-5 14-5-3 1-4 3-6 6-2 2-2 7-3 8h-1-1c0 1 0 1-1 2v1l1 3z" class="c"></path><path d="M228 124c-1-1-1-3-1-5h1c1 0 1 1 2 2h-1c0 1 0 1-1 2v1z" class="B"></path><path d="M243 466l1-1v-4h3c-1 0-2 1-2 2v3c1 1 1 1 1 2h-2c1 1 1 2 2 2-1 1-1 2-1 3v4h-1c-1 1-2 1-3 0 0 0-2-2-3-2s-1 1-2 1v-3-1c-1 0-1 1-1 1h-1v-2h0c0-1 0-2-1-3l1-1c1-1 0-1 1-1 2-1 3-2 4-4 1 0 1 0 1-1l2 4 1 1z" class="i"></path><path d="M239 462l1 1c0 1 0 3-1 4 0 0-1 1-2 1h0c-1 0 0 1-1 2h-1v-3h-1c1-1 0-1 1-1 2-1 3-2 4-4z" class="E"></path><path d="M240 461l2 4c0 1 0 2 1 3h0c-1 1-2 1-2 2h-1c-1 0-2-1-3-2 1 0 2-1 2-1 1-1 1-3 1-4l-1-1c1 0 1 0 1-1z" class="L"></path><path d="M243 466l1-1v-4h3c-1 0-2 1-2 2v3c1 1 1 1 1 2h-2c1 1 1 2 2 2-1 1-1 2-1 3l-2 2-1-1c0-1 0-1 1-1 0-1 1-1 1-2s0-2-1-3h0c-1-1-1-2-1-3l1 1z" class="H"></path><path d="M283 250h0 6c-1 1-2 2-3 4 0 1 0 2 1 3 0 2 1 3 2 4 2 0 4 0 5-1l1-1h2c1 0 0-1 2 0l-1 2c0 1-1 1-1 2s2 2 3 3h0c-1 1 0 1-1 2l-1 1-1 1h-4c-2-1-2-1-3-3v-1c0-1 0-1-1-1v-1-1h-1-1c-1-1-2-3-3-4v-1l-1 1-1-1c-1-1 1-4 1-5l1-2h0-4v-1h3zM89 154v-1h1v1h0c-2 4-2 6-3 10 0 1 1 3 1 4 1 2 1 3 2 4v6h-1c-1 1 0 2 0 3-2 0-4 1-5 2 2 1 3 0 4 1v2l1 1-1 1c-1-1-2-2-3-1-2 0-4 2-4 4l-1 2-1 1 1-6 9-34z" class="C"></path><path d="M249 110l5 2c2 1 5 2 6 5h1v3h0v2c1 1 2 1 1 2h0l-1-1h-1s-1 0-1-1l-1 1h0-1c-1 0-2 0-3-1v-4h0 0c1-1 3-1 3-2l-2-1h0c0 1-1 1-1 1-1 1-2 3-2 3v1h0s-1 1 0 2l1 3-1 1h0l-1 1h0l-1-1h0c-1-1-2-1-3-1l2-2c-1-1-2-1-2-3 1-1 1-3 1-5v-3s0-1 1-2z" class="E"></path><path d="M250 126v-1c-1 0-1-1-1-2h1c1 1 1 2 2 3l-1 1h0l-1-1h0z" class="D"></path><path d="M249 110l5 2c2 1 5 2 6 5h-2c0-2-1-3-3-3h-1c-2 2-3 6-5 9-1-1-2-1-2-3 1-1 1-3 1-5v-3s0-1 1-2z" class="j"></path><path d="M249 110l5 2c-1 0-3 1-4 1l-2 2v-3s0-1 1-2z" class="C"></path><path d="M252 452h1 2c0 2-1 3-2 3 0 1 0 1 1 2h0c1 1 1 1 0 2-1 2-4 3-5 5v3c0 1 0 1 1 3h-1 0c-1-1-1-1-1-2v-1c-1-1-2-1-3-1v-3c0-1 1-2 2-2h-3v4l-1 1-1-1-2-4c0-1-1-2-2-4h0 1v-1-1l2 1h1v-2h0c1 0 1 0 2 1v1c0 1 1 1 1 2h0l2-2 1-1c1-1 1-2 2-3l1 1h1v-1z" class="W"></path><path d="M252 452h1 2c0 2-1 3-2 3 0 1 0 1 1 2-2 0-3-1-4-1v-1l1-2h1v-1z" class="G"></path><path d="M242 454c1 0 1 0 2 1v1h-1c-1 1-1 3 0 4v1c0 2-1 2 0 5l-1-1-2-4c0-1-1-2-2-4h0 1v-1-1l2 1h1v-2h0z" class="E"></path><path d="M345 106h0l-1 1c0 1 1 2 1 3 2 1 3 2 5 2h0c2-1 4-1 6-2h4 0c-1 1-3 1-5 2h-1l-1 1c-1 0-2 0-2 1l-1 1c-3 2-5 6-8 9 0-1 0-1-1-1 1-1 1-2 2-3 0 0 1-1 0-2l-2-1c0-1-1-2-2-3 0 0-1-1-1-2h-1c0-1 0-1-1-1v-1l1-1v-1c-2 0-5-1-7-2h15 0z" class="X"></path><path d="M336 110h1c1-1 3-3 5-3h1c-1 1-1 2-2 3 0 1 0 1 1 2v1l-1 1h-1 0c0-1-1-1-1-2h-1-1c0-1 0-1-1-1v-1z" class="B"></path><path d="M356 110h4 0c-1 1-3 1-5 2h-1l-1 1c-1 0-2 0-2 1l-1-1c-2 1-3 3-5 4l-1-1h-2v-1c0-1 1-1 1-2h1c-1-1-1-1-1-2h1c1 1 1 1 3 1v1l-2 2 1 1c0-1 1-1 2-2 0-1 1-1 2-2h0c2-1 4-1 6-2z" class="H"></path><path d="M281 200l2-2c4-4 11-8 17-7 1 0 3 2 4 3v1l3 2c-3 3-5 4-9 5h-1v-1h-1c-1-2-3-3-5-3-4-1-7 0-10 2z" class="c"></path><path d="M160 127c2 0 3 2 5 4l3-6h2c1 0 1 1 2 2-1 1-2 2-3 4v1h0c-1 1-1 2 0 3s2 1 3 1v-1c0-1-1-1-1-2h1c0 1 1 2 1 2h1c1 0 1 0 1-1l1 1c1-1 1-1 2 0 1 0 2 0 2 1-6 0-12 1-17 3l-1-1c-1 0-1 1-2 0v-3c0-2-2-5-4-6-1 0-2-1-3-1h0l1-1v-1h1c2 0 3 1 5 1z" class="C"></path><path d="M153 128l1-1v-1h1 0c1 1 1 1 2 1 2 1 4 3 5 4 1 2 1 5 1 7h-1c-1 0-1 1-2 0v-3c0-2-2-5-4-6-1 0-2-1-3-1h0z" class="G"></path><path d="M305 337h0 0c1 0 1-1 1-2h1l1 1h2c3 0 4 0 6 1v1c0 1 1 2 0 3h0v2c0 2-2 1-2 3h1c0 1 0 1-1 2l-1-1c0 1-1 1-2 2 0-1-1-1-2-2-1 1-3 3-4 3h-1 0v-2-1l-2 2c1-1 2-2 2-4v-2c1-1 1-1 1-2v-4z" class="Z"></path><path d="M306 344v-1-4h1v4s1 0 1 1l2-1h1c-1 1-1 2-2 2s-1 1-2 0c0-1 0-1-1-1h0z" class="O"></path><path d="M305 341c0-1 0-2 1-2 0 1-1 3 0 5h0c0 2-1 4-2 6h0v-2-1l-2 2c1-1 2-2 2-4v-2c1-1 1-1 1-2z" class="E"></path><path d="M310 343h1v1h0c1 1 1 0 1 1l-3 2c-1 1-3 3-4 3h-1c1-2 2-4 2-6 1 0 1 0 1 1 1 1 1 0 2 0s1-1 2-2h-1 0z" class="h"></path><path d="M316 338c0 1 1 2 0 3h0v2c0 2-2 1-2 3h1c0 1 0 1-1 2l-1-1c0 1-1 1-2 2 0-1-1-1-2-2l3-2c0-1 0 0-1-1h0v-1h-1v-1h-1 0c1-1 3-2 4-3h1 0c1-1 1 1 2 1v-2z" class="V"></path><path d="M310 342c1 0 2-1 4-1v1c0 1-1 2-2 3 0-1 0 0-1-1h0v-1h-1v-1z" class="R"></path><path d="M245 305c3 0 6-1 8-1h0l1 2h1c0 1-1 2-1 3-1 0-1 1-2 1 0 1 1 1 2 2v1c-1 1-2 1-2 2-1 2-1 4-1 6h0c1 1 2 1 2 2s0 1 1 2h1c0 2 0 2-1 3-1 0-1 0-2 1h0-1v-1c0-1-1-2-2-3l-1-2h0l-2-3c0-1 0-2-1-2v2h-2l-1-1v-1-1h0l-1-1 2-2h2s1-1 1-2l2-1c-1 0-1 0-1-1h0 1 0v-2h1 1l-1-1c-1-1-2-2-4-2z" class="D"></path><path d="M249 325h1c2 1 3 0 5 0 0 2 0 2-1 3-1 0-1 0-2 1h0-1v-1c0-1-1-2-2-3z" class="I"></path><path d="M253 304h0l1 2h1c0 1-1 2-1 3-1 0-1 1-2 1h-1c-1-1-1-1-1-2l2-1c1-1 1-2 1-3z" class="J"></path><path d="M246 312c2 1 1 1 2 2v1c0 1-1 1-1 1 0 1 1 2-1 2s-2-1-3-1h-1l-1-1 2-2h2s1-1 1-2zm5-2h1c0 1 1 1 2 2v1c-1 1-2 1-2 2-1 2-1 4-1 6h0v1c1 1 1 2 0 3h-1v-2-3c-1-1-1-2-2-3 0-1 1-1 2-2-1-1-1-1-1-2l-1-1h2c0-1 1-1 1-2h0z" class="W"></path><path d="M133 125s0-1 1-2h1 2s0 1 1 1c2 6 2 15-1 21v-3c0-1 0-2-1-3s-1-1-1-2c-1-1-1-1-2-1h0v-1c-2 1-5 1-7 0h-1c0-1 1-1 1-2l1-1v-1-2l1-2c-1 0-2-1-3-2h6v-1l2 1z" class="j"></path><path d="M133 125s0-1 1-2h1 2s0 1 1 1l-1 3c-1 0-3-1-4-2z" class="G"></path><path d="M125 125h6c0 1 0 2 1 3 1 2 2 3 2 5h0c-1-2-4-4-6-6-1 0-2-1-3-2z" class="L"></path><path d="M128 127c2 2 5 4 6 6l-1 2c-2 1-5 1-7 0h-1c0-1 1-1 1-2l1-1v-1-2l1-2z" class="l"></path><defs><linearGradient id="G" x1="301.247" y1="211.136" x2="299.8" y2="222.547" xlink:href="#B"><stop offset="0" stop-color="#171718"></stop><stop offset="1" stop-color="#333233"></stop></linearGradient></defs><path fill="url(#G)" d="M299 214c2-2 3-4 5-6h2c1 4 3 4 5 7-2-1-3-1-5-1v1s1 0 1 1c1 1 4 2 5 3s1 4 1 6h0c-2-2-4-5-6-6s-5-2-8-1c-2 0-4 2-5 3l-3 2-2-2-1-2v-1l1-1h1 1c0-1 1-2 2-2h0v-1s1 0 1-1h0c2-1 3-1 4-3 0 1 1 2 1 3v1z"></path><path d="M299 214c2-2 3-4 5-6h2c1 4 3 4 5 7-2-1-3-1-5-1v1l-1-1h0c1-1 2 0 3-1l-1-1h0c-1 0-2 0-3-1-2 2-3 3-3 4l-1 1v-1-1l-3 3h-1c0 1-1 1-2 1l-4-1h1c0-1 1-2 2-2h0v-1s1 0 1-1h0c2-1 3-1 4-3 0 1 1 2 1 3v1z" class="M"></path><path d="M298 210c0 1 1 2 1 3v1c-1 1-3 3-5 4l-4-1h1c0-1 1-2 2-2h0v-1s1 0 1-1h0c2-1 3-1 4-3z" class="D"></path><path d="M299 119c-2-1-2-2-2-5h-1l-6 3h-1v-1c3-3 8-5 13-7 3-1 6-1 8 1 2 1 3 1 4 3l-3 3v1h-1 0l-1-1h-3l1 1c-1 0-1 0-2 1 1 2 1 3 2 5-1 0-1 0-2 1-1-2-1-3-2-5 0 1 0 1-1 1l-3-1z" class="C"></path><path d="M305 112h4c2 0 4 1 5 1l-3 3v-1c-1-1-2-2-4-2 0 0-1-1-2-1z" class="B"></path><path d="M303 119c0-1-1-4 0-5 0-1 1-2 2-2s2 1 2 1c2 0 3 1 4 2v1 1h-1 0l-1-1h-3l1 1c-1 0-1 0-2 1 1 2 1 3 2 5-1 0-1 0-2 1-1-2-1-3-2-5z" class="H"></path><path d="M307 113c2 0 3 1 4 2v1 1h-1 0l-1-1h-3-1v-2l2-1z" class="M"></path><path d="M451 106h1 1c-1 1-3 1-5 2-1 0-3 2-4 3 0 1 0 2-1 3 0 1-1 2-2 3 1 1 2 2 4 2 2 1 4 3 6 5-3 2-4 2-7 2h-1c-1-1-2-1-3-1-1-1-2-2-3-2h0l-1-2v-1l-2-2h-1 0c1 0 1 0 1-1s1-1 1-2l9-9c1 0 4 1 6 0h1z" class="Y"></path><path d="M445 119c2 1 4 3 6 5-3 2-4 2-7 2h-1c-1-1-2-1-3-1-1-1-2-2-3-2h0l-1-2 1-1c2 1 3 2 4 3h1 1c0-1 1-1 2-1l-1-1-1-1 2-1z" class="B"></path><path d="M445 122c2 0 3 1 5 2-2 0-5 0-7-1h0c0-1 1-1 2-1z" class="D"></path><path d="M260 202l5 1c2 0 3 1 4 2h2c2 3 1 4 1 7l1 1h1 0c1 0 1 1 1 1 1 0 1 0 2-1 0 1 0 2 1 4v2c-2 0-2 1-3 1 0-1 0-1-1-2-1 0-1 0-1 1s0 1-1 2h0l1 1-1 1c-1-1-1-3-3-4-1-2-4-3-5-5l-2-2 1-1 1-1c0-2-1-2-3-4 1-1 1-1 1-2-1 0-1-1-2-1v-1z" class="C"></path><path d="M269 205h2c2 3 1 4 1 7 0-1 0-2-1-2v2c0 1 0 1-1 1h-2c-1-1-1-2-1-4l1-1v1-2l1-2z" class="D"></path><path d="M274 213h0c1 0 1 1 1 1 1 0 1 0 2-1 0 1 0 2 1 4v2c-2 0-2 1-3 1 0-1 0-1-1-2-1 0-1 0-1 1s0 1-1 2h0c-1-1-1-2-2-3v-1l2-1c1-1 2-1 2-3z" class="E"></path><path d="M260 202l5 1c2 0 3 1 4 2l-1 2v2-1l-1 1c0 1-1 2 0 3-1 1-2 1-3 1v-3c0-2-1-2-3-4 1-1 1-1 1-2-1 0-1-1-2-1v-1z" class="j"></path><path d="M265 203c2 0 3 1 4 2l-1 2v2-1c0-1-2-2-3-3v-2z" class="R"></path><defs><linearGradient id="H" x1="247.723" y1="170.67" x2="260.207" y2="175.45" xlink:href="#B"><stop offset="0" stop-color="#1b1b1b"></stop><stop offset="1" stop-color="#3a3a3b"></stop></linearGradient></defs><path fill="url(#H)" d="M248 169c0-1 0-1 2-2 3 0 6 1 8 3l4 4 1 2c-1 1 0 2-1 4s-2 3-3 5l-2-2h-1v-1-1h-4c0 1-1 0-2 0h0s-1 0-2-1h8l-1-1h0c-2 0-5-1-6-3-2-2-2-4-1-7z"></path><path d="M256 178c-1 0-2 0-3-1-2-1-2-2-2-4 0-1 0-2 1-3h2c1 1 1 1 1 2l-1 1c0 1 0 1 1 1h1l1-1c1 0 2 1 3 2h0l-4 3z" class="F"></path><path d="M260 175h1c1 2 1 4 1 5-1 2-2 3-3 5l-2-2h-1v-1-1h-4c0 1-1 0-2 0h0s-1 0-2-1h8l-1-1 1-1 4-3h0z" class="E"></path><path d="M260 175c0 1 1 2 0 3 0 1-1 2-3 3h-1v-1l-1-1 1-1 4-3z" class="M"></path><path d="M90 185c1-1 1-1 1-2 1 0 1 1 1 1 1 2 3 5 5 6-1 2-2 4-2 6 1 0 2 1 2 1-1 2-2 3-4 3v-1c-1-1-2-2-2-3h-1-1c-1 1-2 2-3 2h-3c-1-1-2-2-2-4l1-1h-2l1-2c0-2 2-4 4-4 1-1 2 0 3 1l1-1-1-1v-2l2 1z" class="E"></path><path d="M85 190c1 1 2 1 3 2v2h-2v-1-1h0c0 1-1 2-1 2-1-1-1-1-3-1v-1c1-1 2-1 3-2z" class="P"></path><path d="M88 184l2 1c2 3 2 5 2 9-1-2-2-4-4-6l1-1-1-1v-2z" class="O"></path><path d="M248 112v3c0 2 0 4-1 5 0 3-3 6-5 7-1 0-2-2-3-2 0 1 0 1-1 1 0 1-3 1-4 1h-1c-2 1-3 1-4 0l-1-3v-1c1-1 1-1 1-2h1 1c1-1 1-6 3-8v1c1 1 1 3 2 4l4-1 1-1c1 1 2 2 4 3 0-1 0-3 1-4 0-1 1-1 1-2-1-1-1 0-3 0l4-1z" class="Y"></path><path d="M243 119c1 1 1 1 1 2-1 2-2 2-3 3-1 0-1 0-2-1-1 0-2 1-3 1s-1 0-1-1v-2c1 0 1 1 1 1h1c1 0 1 1 2 1 0-1 1-2 2-2 0-1 1 0 1-1 0 0 1 0 1-1z" class="P"></path><path d="M240 117c0 1 1 1 1 1 1 1 2 1 2 1 0 1-1 1-1 1 0 1-1 0-1 1-1 0-2 1-2 2-1 0-1-1-2-1 0-1 0-3-1-4l4-1z" class="E"></path><path d="M234 113v1c1 1 1 3 2 4s1 3 1 4h-1s0-1-1-1v2c0 1 0 1 1 1 0 0-1 0-2 1-1-1-1-2-2-3 0 0-1 0-1-1 1-1 1-6 3-8z" class="B"></path><path d="M235 123h-1v-1-3c-1-1-1-4 0-5 1 1 1 3 2 4s1 3 1 4h-1s0-1-1-1v2z" class="X"></path><path d="M283 158h1c0 2 3 4 4 5l-4 4c-4 5-6 11-8 17 0 2-1 4-1 5 0-2-1-4-2-6-1-5-2-10-5-14l3-3c1 1 2 1 3 2v1c2 0 3-2 5-2h0 1c0-1 3-3 3-3 1-1 0-3 0-4v-2z" class="h"></path><path d="M274 172h-1c0-1-1-2 0-3h1c1 1 1 1 2 1l1-1v2 2h0-1s-1-1-2-1z" class="F"></path><path d="M274 172c1 0 2 1 2 1h1 0c-1 4-1 8-3 12 0-4 1-8 0-12v-1z" class="B"></path><path d="M264 164l1-1c1 0 2 1 4 2l2 1-3 3c3 4 4 9 5 14h-1v6c1 2 1 6 1 8h-2c-2-3-3-8-4-12v-4-2l-1-1h1 0c0-1-1-1-1-2-1-2-3-3-4-5l-1-1h0l1-1h0l-2-2s1 0 1-1h0l1-1h0l2-1z" class="N"></path><path d="M261 166c2 1 4 4 5 6v1c-2-1-2-4-4-4l-2-2s1 0 1-1z" class="B"></path><path d="M262 169l4 6h1l3 9-1 1h0c-1-1-1-2-2-4v-2l-1-1h1 0c0-1-1-1-1-2-1-2-3-3-4-5l-1-1h0l1-1z" class="E"></path><path d="M267 181c1 2 1 3 2 4h0l1-1c0 4 1 8 2 12l-1 1c-2-3-3-8-4-12v-4z" class="D"></path><path d="M264 164l1-1c1 0 2 1 4 2l2 1-3 3c3 4 4 9 5 14h-1v6c0-2-1-5-1-7-1-2-2-5-2-7-1-1-1-2-1-3-2-2-4-5-6-7l2-1z" class="G"></path><path d="M264 164l1-1c1 0 2 1 4 2l2 1-3 3c-1-2-3-3-4-5z" class="O"></path><path d="M271 240h0c2 1 3 0 4 1 1 0 1-1 2 0h2v1l1 1c-1 0-1 1-2 1h-1v1l1 1-1 1v1c1 1 2 1 3 2h3-3v1h4 0l-1 2h-3v1h-1 0v-1c-1 1-1 2-2 3v2h-1 0l-1-1-1-1c0 2 0 2-1 3-1 0 0 0-1-1v-1c-1-1-1-2-1-2 0-1 0-1-1-2v-1c0-1-1-1-2-2-2 0-5 0-6 1h-1c1-1 1 0 2-1l2-2h1c0-1 0-1 1-1s2 0 3-1c-1 0-1 0-1-1 0 0 1-1 1-2h-2l3-3z" class="B"></path><path d="M270 246l2 2h1l-1 1h-6v-1h0c0-1 0-1 1-1s2 0 3-1z" class="D"></path><path d="M268 250c1 0 3 0 4 1s1 2 2 3l1 1v2l-1-1c0 2 0 2-1 3-1 0 0 0-1-1v-1c-1-1-1-2-1-2 0-1 0-1-1-2v-1c0-1-1-1-2-2z" class="H"></path><path d="M272 257v-3h1c0 1 1 1 1 2 0 2 0 2-1 3-1 0 0 0-1-1v-1z" class="B"></path><path d="M280 250h3-3v1h4 0l-1 2h-3v1h-1 0v-1c-1 1-1 2-2 3v2h-1c0-2 1-4 2-5-1 0-1-1-2-1l1-1v-1h1s1 0 1 1h0l1-1h0z" class="V"></path><path d="M271 243h0 2c1 0 1 0 1 1 1 0 1 1 2 2 0-1 1-1 1-1l1 1-1 1h-1v2h-1 0c-1-1-1-1-2-1h-1l-2-2c-1 0-1 0-1-1 0 0 1-1 1-2h1z" class="I"></path><path d="M270 243h1c1 1 2 1 3 3v1c-1 0-1 0-2 1l-2-2c-1 0-1 0-1-1 0 0 1-1 1-2z" class="F"></path><path d="M271 240h0c2 1 3 0 4 1 1 0 1-1 2 0h2v1l1 1c-1 0-1 1-2 1h-1v1s-1 0-1 1c-1-1-1-2-2-2 0-1 0-1-1-1h-2 0-1-2l3-3z" class="C"></path><path d="M233 293l1 8c1 1 1 3 1 3 1 2 3 3 4 4 2 1 3 2 4 2h1c1 1 2 0 3 0 0 1 0 1 1 1l-2 1c0 1-1 2-1 2h-2l-2 2 1 1h0l-3 1h-1l-1 1c0 1 0 1 1 2 1 0 1 1 2 2h0c0 1 0 3-1 4l-2 1c0-1-2-1-2-2v-1h-1v2h0c0 1 0 3-1 4-1-3 0-33 0-38z" class="X"></path><path d="M235 305c1 1 1 2 2 3l-1 1-1 2c-1-2 0-5 0-6z" class="G"></path><path d="M233 304h1c1 1 0 8 0 10l-1 1v-11z" class="P"></path><path d="M238 318h0l-3 2v-7l3 2 1 2h-1c1 0 1 0 1 1h-1z" class="V"></path><path d="M239 308c2 1 3 2 4 2h1c1 1 2 0 3 0 0 1 0 1 1 1l-2 1c0 1-1 2-1 2h-2l-2 2 1 1h0l-3 1c0-1 0-1-1-1h1l-1-2h0l-2-2h-1v-1l2-4h2z" class="H"></path><path d="M233 440h1v3 2h0l2-2c0-1-1-2 0-2 1 1 0 2 2 3 1-1 0-1 1-1l1-1c2-1 4 0 6-1h0v2c1 1 2 0 4 0h0c-2 3-3 6-2 10v2l-1 1-2 2h0c0-1-1-1-1-2v-1c-1-1-1-1-2-1h0v2h-1l-2-1v1 1h-1v-1h-1v-2c1-2 0-2 0-3l-1 1h0l-1-1c-1 0-1 0-1 1l-1-1v-11z" class="E"></path><path d="M246 441v1 4h0c-1 1-1 2-1 4l-2-2 1-1v-1l-1 1h0c-1-1-2-1-2-2v-1c-1 0-1-1-2-1l1-1c2-1 4 0 6-1z" class="P"></path><path d="M246 441h0v2c1 1 2 0 4 0h0c-2 3-3 6-2 10v2l-1 1-2 2h0c0-1-1-1-1-2v-1c-1-1-1-1-2-1 0-1 1-1 1-1l1 1c1-1 1-2 1-4h0c0-2 0-3 1-4h0v-4-1z" class="D"></path><path d="M245 450c1 2 1 4 2 6l-2 2h0c0-1-1-1-1-2v-1c-1-1-1-1-2-1 0-1 1-1 1-1l1 1c1-1 1-2 1-4z" class="J"></path><path d="M235 451c1-2 0-5 0-7l5 3 3 6s-1 0-1 1h0v2h-1l-2-1v1 1h-1v-1h-1v-2c1-2 0-2 0-3l-1 1h0l-1-1z" class="P"></path><path d="M240 447l3 6s-1 0-1 1h0v2h-1v-2h0c0-1-1-2-1-3-1-2-1-2 0-4z" class="j"></path><path d="M144 125l9 3h0c1 0 2 1 3 1 2 1 4 4 4 6v3c1 1 1 0 2 0l1 1c-2 1-5 2-7 2h-2c-1 1-2 1-3 2s-3 2-4 2h-3-1l2-1c0-1 0-1 1-1 2 0 2-1 4-1v-1l2-1-1-1h-1c-1-2-3-2-4-2-1-1-2-1-3-2l1-1h0c-1-3-1-7 0-9z" class="H"></path><path d="M144 125l9 3h0c1 0 2 1 3 1 2 1 4 4 4 6v3c1 1 1 0 2 0l1 1c-2 1-5 2-7 2h-2c-1 1-2 1-3 2 0-1 1-1 1-2h2c1-1 4-5 5-6 0-1-1-1-1-2-1-1-1 0-2 0l-1-2h-3-2 0c-3 0-4 2-6 3h0 0c-1-3-1-7 0-9z" class="b"></path><path d="M451 124c1 0 1 0 2 1h7l3 3h-2l-3 1h0c0 1 0 1 1 2 1 0 3 0 3 1v1h-1c0-1-1-1-2-1-1 1 0 2-1 2v2l-4-1v1h-3 0c-1 1-3 1-4 2v-1c-1-1-3-1-4-2v1h-3v-1c0-2 1-4 3-5v-1c1-1 1-2 1-3 3 0 4 0 7-2z" class="G"></path><path d="M454 130l1-1c1 1 1 1 1 2s-1 1-2 1c-1-1-1-1 0-2z" class="i"></path><path d="M447 137l1-1c0-1-1-1 0-2 2-1 4 0 6 1v1h-3 0c-1 1-3 1-4 2v-1z" class="C"></path><path d="M451 124c1 0 1 0 2 1-4 1-7 3-10 5h0v-1c1-1 1-2 1-3 3 0 4 0 7-2z" class="L"></path><path d="M454 130c-2 1-5 1-7 1 5-3 8-4 13-4l1 1-3 1h-3l-1 1z" class="a"></path><path d="M443 130h0c1 0 1 1 2 1s2-1 3 0c-1 1-4 4-5 4v1h-3v-1c0-2 1-4 3-5z" class="W"></path><path d="M288 175l1 2c2 2 4 3 6 5-1 0-3-1-4 0l-2 2c1 1 2 1 3 2s4 2 4 4h1 3v1c-6-1-13 3-17 7l-2 2c0 1 0 1-1 1h0v-1c-1 0-1 1-2 1v-1l-1 1h0c0-2 1-3 2-5s1-4 2-5c2-6 4-11 7-16z" class="c"></path><path d="M296 190h1 3v1c-6-1-13 3-17 7l-2 2c0 1 0 1-1 1h0v-1c-1 0-1 1-2 1v-1l1-2c3-6 12-7 17-8zm6 34l-1-2c1-1 2-3 3-3l1 1c-1 0-1 0-2 1 3 0 3-1 5 1v1h-3l-1-1h-1v1 1c1-1 1-1 2 0l3 3v-2-1h0c1 0 2 1 2 2 0 2 0 4 1 6 0 2-2 4-3 5h-1c-2 1-3 1-4 1h-1c0 1 1 1 0 2h-1l-2-2h0l-2-2c1 0 1-1 0-2v1c-2-1-4-1-5-3 2-2 3-3 5-4-1-1-1-1-1-2l1 1c1 0 1-1 2-1 0-1 1-1 1-1l2-1z" class="F"></path><path d="M308 225v-1h0c1 0 2 1 2 2l-1 3h0c0-2-1-3-1-4z" class="B"></path><path d="M297 228l1 1c1 2 0 4-1 5v1c-2-1-4-1-5-3 2-2 3-3 5-4zm5-4c2 0 3 1 4 2s2 3 1 4c0 2 0 3-1 4-2 1-4 2-7 3 1-3 0-5 3-7 0-1 2-2 3-3-2 0-4 0-5-2l2-1z" class="O"></path><path d="M252 200l3 1c1 1 2 1 4 1h1 0v1c1 0 1 1 2 1 0 1 0 1-1 2 2 2 3 2 3 4l-1 1-1 1 2 2h-4c1 1 1 2 2 3v1 3c-1 0-2 1-3 1l-2-1c-3-3-6-4-10-4h4v-1h-1c-1-1 0-2 0-4h0c0-1 1-1 1-2l1-1h0c1-2 0-2 0-3s0-1 1-1c0-1-1-1-2-2 1 0 2 0 2-1v-1l-1-1z" class="L"></path><path d="M254 210c2 1 2 1 3 3h0l3 1c1 1 1 2 2 3h-2v1h-1c-2 0-4-1-5-2s-1 0-2-1c1 0 2-1 3-2h0 1c0-1-1-2-2-3z" class="D"></path><path d="M257 213l3 1c1 1 1 2 2 3h-2v1l-1-2c-1 0-1-1-2-2v-1z" class="a"></path><path d="M252 200l3 1c1 1 2 1 4 1-2 1-5 2-5 5v3c1 1 2 2 2 3h-1 0c-1 1-2 2-3 2s-1-2-2-3h0c0-1 1-1 1-2l1-1h0c1-2 0-2 0-3s0-1 1-1c0-1-1-1-2-2 1 0 2 0 2-1v-1l-1-1z" class="M"></path><path d="M251 210c2 0 2 0 3 2l1 1c-1 1-2 2-3 2s-1-2-2-3h0c0-1 1-1 1-2z" class="B"></path><path d="M259 202h1 0v1c1 0 1 1 2 1 0 1 0 1-1 2 2 2 3 2 3 4l-1 1-1 1 2 2h-4l-3-1h0c-1-2-1-2-3-3v-3c0-3 3-4 5-5z" class="F"></path><path d="M263 211h-1c-2 0-3-1-5-3v-1l1 1v-1h0c1 1 1 1 2 0v-1h1c2 2 3 2 3 4l-1 1z" class="D"></path><path d="M98 111c0-2 0-4 1-5h15v2c-1 1-2 3-2 4h1 5v1 3h0c-1 0-1 0-1 1v-1h-1l-3-1c-2 1-2 1-3 3l-1 1c-1 0-2 1-3 1 0 0-1 0-1-1l-1 1v-1l-1-1c0 1-1 1-2 1v-1l-2-2v-1-1h0s-1 0-2 1v-3l1-1z" class="M"></path><path d="M98 111l3-1c0-1-1-1-1-2h2l1 1c0 1-1 2-2 3l-2 1-2-1 1-1zm12 4h3 0c-2 1-2 1-3 3l-1 1c-1 0-2 1-3 1 0 0-1 0-1-1 1-2 3-3 5-4z" class="G"></path><path d="M99 116l3-3c2 0 3 0 4 1v1c-1 2-3 3-5 3h0l-2-2z" class="F"></path><path d="M110 115c-1 0-1-1-2-1-1-1-3-2-4-2h-1c1-2 2-3 3-4 3-1 2 0 3 2 1 0 2 1 2 1l1 1h1 5v1 3h0c-1 0-1 0-1 1v-1h-1l-3-1h0-3z" class="W"></path><path d="M228 601l1 1h0c0 1 1 2 2 2 1 2 3 2 5 3h2l3-3c0 1 1 2 1 3s-1 1-1 2v2h-1c-1 0-1 0-2 1l-1 1-1-1h0l-1 1c0 1-1 1-1 1-1 0-2 2-2 2h-1c-3 0-4 2-7 2-2 0-6-1-8-2-1 0-1-1-2-1-1-1-2 0-3 0s-2-1-3-1c1 0 3-3 4-3 2 0 4 2 6 1h0l2 1 1-1h1c1 0 2 1 3 0s1-2 2-4h1c0-1 0-2-1-3h0 2v-1-1c-1 0-1-1-1-2z" class="C"></path><path d="M231 616c-1 0-1 0-1-1 1-2 1-2 3-3 1-1 2 0 3 0l-1 1c0 1-1 1-1 1-1 0-2 2-2 2h-1z" class="E"></path><defs><linearGradient id="I" x1="215.514" y1="610.7" x2="217.76" y2="615.898" xlink:href="#B"><stop offset="0" stop-color="#b3b9b6"></stop><stop offset="1" stop-color="#ccc6c8"></stop></linearGradient></defs><path fill="url(#I)" d="M208 614c1 0 3-3 4-3 2 0 4 2 6 1h0l2 1c2 1 4 1 6 1-2 1-4 2-6 2s-5-2-7-2c-1 0-1-1-2 0h0v1c-1 0-2-1-3-1z"></path><path d="M229 604c1 2 2 4 1 7-1 1-2 3-4 3s-4 0-6-1l1-1h1c1 0 2 1 3 0s1-2 2-4h1c0-1 0-2-1-3h0 2v-1z" class="D"></path><path d="M229 589c1 0 1-1 1-1l1 1c1 2 1 3 3 3 1 0 1 0 1 1 1-1 2-1 3-1h5c-1 1-1 1-2 1s-1 1-1 1l2 1c1 0 2 1 3 2v1l1 1-1 1h0l-3 1c0 1-1 2-1 3l-3 3h-2c-2-1-4-1-5-3-1 0-2-1-2-2h0l-1-1c-1 1-1 0-1 0v-3s0-2 1-3c0-2 0-4 1-6z" class="Y"></path><path d="M230 595l2 1v2h1v2l-1-1c-1-1-2-1-3 0 0-2 0-3 1-4z" class="l"></path><path d="M229 599c1-1 2-1 3 0l1 1c0 1 1 4 1 4 1 1 2 2 2 3-2-1-4-1-5-3-1 0-2-1-2-2h0l-1-1s0-1 1-1v-1z" class="B"></path><path d="M229 600c1-1 1-1 3-1v5h-1c-1 0-2-1-2-2h0l-1-1s0-1 1-1z" class="i"></path><path d="M229 589c1 0 1-1 1-1l1 1c1 2 1 3 3 3 1 0 1 0 1 1l-5 2c-1 1-1 2-1 4v1c-1 0-1 1-1 1-1 1-1 0-1 0v-3s0-2 1-3c0-2 0-4 1-6z" class="O"></path><path d="M240 594l2 1c1 0 2 1 3 2v1l1 1-1 1h0l-3 1c0 1-1 2-1 3l-3 3c-1-2 0-2-2-4v-1-1h-1 0l1-2c1-2 1-2 2-3l2-2z" class="E"></path><path d="M242 595c1 0 2 1 3 2v1l1 1-1 1h0 0l-1-1c-1 0-1-1-2-1v-3z" class="G"></path><path d="M92 161h1l2-3c1 1 0 2 1 3s1 5 1 6c0 5 0 9 1 13v5 1l-1 1v3c-2-1-4-4-5-6 0 0 0-1-1-1 0 1 0 1-1 2l-2-1c-1-1-2 0-4-1 1-1 3-2 5-2 0-1-1-2 0-3h1v-6c-1-1-1-2-2-4 0-1-1-3-1-4l2-1v-2h2 1z" class="i"></path><path d="M92 161h1l2-3c1 1 0 2 1 3s1 5 1 6c0 5 0 9 1 13v5 1l-1 1h0c-1-2-1-5-1-8-1 0-1-1-1-2v-2l-1-1c-1 0-1 0-2-1 0 0 0-1 1-1-1-1-1-2-2-3h0c0-1 0-2 1-3 0-1-1-3 1-4l-1-1z" class="j"></path><path d="M96 165v6c-1 2 0 6 0 8-1 0-1-1-1-2v-2l-1-1c-1 0-1 0-2-1 0 0 0-1 1-1l2-2-1-2h0 1l1-3z" class="E"></path><path d="M93 162c1 1 2 1 3 3l-1 3h-1 0l1 2-2 2c-1-1-1-2-2-3h0c0-1 0-2 1-3 0-1-1-3 1-4z" class="l"></path><path d="M256 394h0 0v-1l1 1 1-1c1 2 2 4 3 5 1-1 1-2 1-2h2c1 0 1-1 2-1v2l2 2h0c1 0 1 0 1 1 1 0 2 1 2 1 1 2 0 4 2 6 1-1 1-1 1-2 1 1 1 1 0 2v1c0 1 0 1-1 2-1-1-1-3-2-5 0-1-1-2-2-4h-1-1c-3 0-6 3-8 4-1 1-4 3-4 5h1c-1 0-1 1-2 1h-2c-1 0-1 0-2 1-1 0-1 0-2-1h-2c0-1-1-1-2-1-1-1-2-2-2-4h1c1-1 2-1 4-2 1 0 3 0 4-1v-1-1s0-1 1-1c0 0 0-1 1-1v-2c-1-1-1-1-1-2 1-1 1-1 2-1h2z" class="H"></path><path d="M251 409c2-1 2-1 3 0h1v1h1c-1 0-1 1-2 1h-2l-1-2z" class="Y"></path><path d="M266 395v2h0c0 1-1 2-1 3h0-1v-1l-3-1c1-1 1-2 1-2h2c1 0 1-1 2-1z" class="i"></path><path d="M243 406c2 0 4 0 6-1h1 5c0 1 0 1-1 2-1 0-2 0-3 1v1l1 2c-1 0-1 0-2 1-1 0-1 0-2-1h-2c0-1-1-1-2-1-1-1-2-2-2-4h1z" class="W"></path><path d="M256 394h0 0v-1l1 1 1-1c1 2 2 4 3 5l3 1-3 1h0c-2 1-4 3-6 4h0c-3 1-6 0-8 0 1 0 3 0 4-1v-1-1s0-1 1-1c0 0 0-1 1-1v-2c-1-1-1-1-1-2 1-1 1-1 2-1h2z" class="L"></path><path d="M256 394h0 0v-1l1 1 1-1c1 2 2 4 3 5l3 1-3 1v-2c-1 0-2-1-3-1v1c-1-1-2-2-2-4z" class="e"></path><path d="M251 402v-1s0-1 1-1c0 0 0-1 1-1v-2c-1-1-1-1-1-2 1-1 1-1 2-1h2c0 2 1 3 2 4 0 2 0 2-1 3s-3 2-4 2-1-1-2-1z" class="l"></path><path d="M133 135v1h0c1 0 1 0 2 1 0 1 0 1 1 2l-1 1s-1 1-1 2l1 1h-1c-1 0-2 0-3-1-1 2 0 4 0 5l-3 3c-1 0-1 0-1-1-2 1-4 2-6 2h-3v-2l-2-2c-2-1-2-3-2-5l1-1c0-2 3-4 5-5h4 1v-1h1c2 1 5 1 7 0z" class="E"></path><path d="M124 142s0-1 1-1h1 1v1c0 1 1 3 0 4l-1 2-3-1v-2c0-1 1-1 1-1v-2z" class="Y"></path><path d="M127 142c1 1 2 1 2 3v2l-2 2c-2 1-4 2-6 2h-3v-2h1l2 1c2 0 3-1 5-2l1-2c1-1 0-3 0-4z" class="e"></path><path d="M120 141c1-1 2-1 3-1v1l1 1v2s-1 0-1 1l-1 1h-2c-1 0-2-1-2-2s1-2 2-3z" class="R"></path><path d="M120 141c1-1 2-1 3-1v1 1l-2 2h-1v-3zm13-6v1h0c1 0 1 0 2 1 0 1 0 1 1 2l-1 1s-1 1-1 2c-2-3-7-5-10-6h1v-1h1c2 1 5 1 7 0z" class="O"></path><path d="M135 140h-1-1l-3-3h5c0 1 0 1 1 2l-1 1z" class="T"></path><path d="M476 112c1 1 2 1 4 1l1 1-1 1c0 1 0 4-1 5l1 2 2-2 1 8c0 4-2 8-3 13-1 1-1 4-1 6h0c-1 1 0 4-1 6l-1-1v-2c0-1 1-1 1-2l-1-1v-1-2c0-1-1-1-1-2v-1c0-1-1-2-1-3v-2c-1-1-1-2-1-3h-1c0-3-1-5-1-7v-2l-1-3v-2l1-2c1 1 2 1 3 0s1-1 1-3c0-1 0-1-1-1l1-1z" class="D"></path><path d="M479 120l1 2c-1 1-1 1-1 2l-1 3c-1 1-1 3-1 4h1l-1 1h0c0-1-1-1 0-2l-1-1v-2c-1 0-1-1-2-1l5-6z" class="Y"></path><path d="M476 112c1 1 2 1 4 1l1 1-1 1c0 1 0 4-1 5l-5 6c-1 1-1 4-1 6l1 1h-1c0-3-1-5-1-7v-2l-1-3v-2l1-2c1 1 2 1 3 0s1-1 1-3c0-1 0-1-1-1l1-1z" class="b"></path><path d="M304 143c3 0 5 0 7-2 1-1 2-2 2-3s0-1 1-2v-2h1c5-1 10-2 13-7 2-3 3-7 3-10l-1-2v-1c3 4 2 11 5 15 1 2 2 4 4 5h-22v1c1 4 0 9 0 13v24c-1-3-1-5-4-8h1 1v-3h-1l-1 1c-1 0-3-2-4-2h6l1-11c0-1 0-2-1-3h-1c-1 2 1 5 1 7-1-1-1-2-1-3-2-4-6-6-10-7h0z" class="c"></path><path d="M114 106h6 0 2 1c1 1 2 1 4 2 1 1 4 5 4 6l1 3h0l-9 6v1h1l1 1-6-1h-2v-2c-2 1-2 1-4 0 0-1-1-2-1-3 1-1 3-1 4-2v-1h1v1c0-1 0-1 1-1h0v-3-1h-5-1c0-1 1-3 2-4v-2z" class="O"></path><path d="M114 106h6 0-2 0c-1 2-5 4-5 6h-1c0-1 1-3 2-4v-2z" class="D"></path><path d="M118 112h0c2 0 3-3 6-1 1 1 2 3 3 5-1 1-2 1-3 2h-1c-1-1-1-1-1-2 1-1 1-2 1-3-1 0-1 0-1 1-1 1-2 2-2 4v1c0 1-2 2-3 3h0c-2 1-2 1-4 0 0-1-1-2-1-3 1-1 3-1 4-2v-1h1v1c0-1 0-1 1-1h0v-3-1zm172 198c1-1 1-2 2-3l3 2 2 2 2-2v1h1 0 1c2 2 5 2 7 5l-1 2h1 2s1-1 2-1l1-1h0c1 1 1 3 1 4h1c0 1 1 1 1 1h1c0 1 0 1 1 1-1 0-1 0-1 1h0c-1-1-2-2-3-2 0 1 1 1 1 2h-1l-1-1h-1c-1 2-2 3-2 5v1c-1 0-1 0-2 1-1-1-1 0-3 0v-1-1l-1-3s-1-1-2-1h0c0 1 0 1-1 2-1 0-2-1-2-1l-1-1v-1h-3-1v1c0 1-1 1-1 1v1h-1v-2h1v-2c-1-2-1-5-1-7 0-1-1-1-1-2h2l-1-1h-2z" class="E"></path><path d="M297 318c0 1 0 2 1 3 1 0 2 0 3 1h1c0 1 0 1-1 2-1 0-2-1-2-1l-1-1v-1h-3-1 0v-2l1-1c1 1 1 2 2 2v-2z" class="F"></path><path d="M295 318c1 0 0-1 1-1l-1-1c-1-1 0-2 0-3v-2h0 2c0 1 1 2 2 3v1l-1 1-1-1v-1c-1 0-1 0-1 1 1 1 1 2 1 3v2c-1 0-1-1-2-2z" class="l"></path><path d="M295 309l2 2h0-2 0v2c0 1-1 2 0 3l1 1c-1 0 0 1-1 1l-1 1v2c-1-1 0-2 0-3-1-3-1-5-1-7l2-2z" class="G"></path><path d="M290 310c1-1 1-2 2-3l3 2-2 2c0 2 0 4 1 7 0 1-1 2 0 3h0v1c0 1-1 1-1 1v1h-1v-2h1v-2c-1-2-1-5-1-7 0-1-1-1-1-2h2l-1-1h-2z" class="J"></path><path d="M300 315c1 0 1 0 2 1h3c1 0 1 1 1 1l1 1c-1 1-2 2-3 2h-1 0l-1 1h-1c-1 0-2-1-2-3h0c0-1 0-2 1-3z" class="W"></path><path d="M304 323h1c1 0 2 0 3-1s2-1 2-2v-1h1l1 1v1c-1 2-2 3-2 5v1c-1 0-1 0-2 1-1-1-1 0-3 0v-1-1l-1-3z" class="F"></path><path d="M305 326h0 2 1v2c-1-1-1 0-3 0v-1-1z" class="W"></path><path d="M299 309v1h1 0 1c2 2 5 2 7 5l-1 2v1h0l-1-1s0-1-1-1h-3c-1-1-1-1-2-1 0-1-1-1-1-2v1c-1-1-2-2-2-3h0l2-2z" class="F"></path><path d="M320 142h1v6 13l1 116v29c0 4-1 9 0 13l-1 1h0c-1-2-1-34-1-39v-88-51z" class="P"></path><path d="M245 466c1 0 2 0 3 1v1c0 1 0 1 1 2h0 1c-1-2-1-2-1-3h1c1 3 2 4 5 5l1 1c2 0 3 1 5 1s5 0 6 1h1c0 1-1 1-2 2v2l1 1h-2l2 2-1 1h-1c0 1 0 2-1 3h-1c0-1 0-2-1-2-3-2-4 0-7 1-1-1-1-2-2-2h-1 0l-1-1-2-1-1 1h-1c-1-1-3 0-4 0v-2h-1c0-1 0-1-1-2h2l1-1h1v-4c0-1 0-2 1-3-1 0-1-1-2-2h2c0-1 0-1-1-2z" class="a"></path><path d="M246 470l3 3c-1 1-1 1-2 1h-1v1s0 1-1 2v-4c0-1 0-2 1-3z" class="F"></path><path d="M249 473c2 1 3 3 5 4h0c-1 0-1 1-1 1-1 1-1 1-2 1 0-1-1-1-1-1h0v-2c-1-1-2-2-3-2 1 0 1 0 2-1z" class="G"></path><path d="M247 474c1 0 2 1 3 2v2h0s1 0 1 1l-1 1s1 1 1 2h0l-2-1-1 1h-1c-1-1-3 0-4 0v-2h-1c0-1 0-1-1-2h2l1-1h1c1-1 1-2 1-2v-1h1z" class="M"></path><path d="M243 480l6 1-1 1h-1c-1-1-3 0-4 0v-2z" class="F"></path><path d="M245 466c1 0 2 0 3 1v1c0 1 0 1 1 2h0 1c-1-2-1-2-1-3h1c1 3 2 4 5 5l1 1c2 0 3 1 5 1s5 0 6 1h1c0 1-1 1-2 2v2h-1c-2 0-6-2-8-3-4-2-9-4-11-8 0-1 0-1-1-2z" class="B"></path><path d="M266 477h-1c-1 0-2 0-3-1v-1h5 1c0 1-1 1-2 2z" class="E"></path><path d="M254 477l3 1h0v-1h1l3 3h1c1 0 2 0 2 1l1-1 2 2-1 1h-1c0 1 0 2-1 3h-1c0-1 0-2-1-2-3-2-4 0-7 1-1-1-1-2-2-2h-1 0l-1-1h0c0-1-1-2-1-2l1-1c1 0 1 0 2-1 0 0 0-1 1-1h0z" class="O"></path><path d="M254 477l3 1-5 5h0l-1-1h0c0-1-1-2-1-2l1-1c1 0 1 0 2-1 0 0 0-1 1-1h0z" class="B"></path><path d="M268 134s1-2 2-2c2-3 3-8 5-11l2 6c1 1 2 3 3 4l2 3h0c-1 0-2 0-3 1v3c1 1 2 1 3 1 1-1 2-1 3-1-2 5-5 8-6 13l-1 1c0 1-1 1-1 2l-1 1-1-1c-1-4-3-10-6-14h0v-1h0c0-1-2-1-3-1 1-1 2-1 3-2h0c0-1 0-1-1-1v-1z" class="j"></path><path d="M277 127c1 1 2 3 3 4h-2l-2-1c0-2 0-2 1-3z" class="C"></path><path d="M268 134c1 1 3 0 4 1v3c-1 1-1 1-2 1h-1 0c0-1-2-1-3-1 1-1 2-1 3-2h0c0-1 0-1-1-1v-1z" class="F"></path><path d="M282 139c1-1 2-1 3-1-2 5-5 8-6 13l-1 1c0 1-1 1-1 2-1 0-1-1-1-1 0-4 4-11 6-14h0 0z" class="E"></path><path d="M247 217c4 0 7 1 10 4v1c0 1 1 1 1 1v2l1 2-1 1c1 0 2 1 3 2h0l1 1-1 1h0l-1 1c-1 0-1 0-2 1l-2 1h-1v-1l1-1-1-1c-1 0-1 0-2 1 1 1 1 1 1 2l-2 2h-2c-2-1-3-1-4-2l-2-2c-1 1-1 1-2 1l-1-2h-1v-5h0c1-1 1-3 2-3l1-1h-2-2c3-3 4-5 8-6z" class="Y"></path><path d="M246 229v-2l1 1h2c1 1 2 1 3 1h1 0-2v1c-1 0-1 0-2 1-1-2-1-2-3-2z" class="V"></path><path d="M252 224v-1-1c2 0 5 2 6 3l1 2-1 1-3-2h-1c0-1-1-1-2-2z" class="D"></path><path d="M242 230c0-1 0-2 1-3 0-1 2-2 3-3 2 0 3 0 4 1v1c-2-1-2 0-4 1v2 2h-1 1v1h-1v-1l-1 1c-1-1-1-2-1-2h-1z" class="e"></path><path d="M245 231c-1-1-1-2-1-3l1-1v2h1v2h-1 1v1h-1v-1z" class="O"></path><path d="M246 229c2 0 2 0 3 2 1-1 1-1 2-1 1 1 2 2 2 3 1 1 1 1 1 2l-2 2h-2c-2-1-3-1-4-2l-2-2c-1-1-1-2-2-3h1s0 1 1 2l1-1v1h1v-1h-1 1v-2z" class="L"></path><path d="M242 230h1s0 1 1 2l1-1v1l2 1-1 2-2-2c-1-1-1-2-2-3z" class="R"></path><g class="B"><path d="M249 231c1-1 1-1 2-1 1 1 2 2 2 3 1 1 1 1 1 2l-2 2h-2c0-1 1-2 1-3 0-2-1-2-2-3z"></path><path d="M252 224c1 1 2 1 2 2h1l3 2c1 0 2 1 3 2h0l1 1-1 1h0l-1 1c-1 0-1 0-2 1l-2 1h-1v-1l1-1-1-1c-1 0-1 0-2 1 0-1-1-2-2-3v-1h2 0c0-1 0-2-1-3v-2z"></path></g><path d="M253 229c2 0 3 1 4 2 0 1 1 2 1 3l-2 1h-1v-1l1-1-1-1c-1 0-1 0-2 1 0-1-1-2-2-3v-1h2 0z" class="R"></path><path d="M247 429c-2-3-4-5-6-7l-3-6c-2-6-3-11-3-17 2 4 3 9 6 12 1 2 3 3 5 3h0 1l1 1h-1c0 2 0 4 1 6 0 2 2 4 4 5h1 0c2 1 5 1 7 0 1-1 3-2 3-4v-2c-2-2-3-2-5-2-2-1-3 0-4 0h-1-1c1-2 2-2 4-2 3-1 6 0 9 2 4 3 8 10 9 16h0c-3-3-5-5-9-5-3 0-8 0-10 2l-1 1h0c-1 0-2 0-3-1v2h0c-1 0-1 0-2-1 0-1-1-2-2-3z" class="c"></path><path d="M247 429h0l-3-6s-1-1-1-2c1-1 1-1 2-1v1c1 2 3 6 5 8 0 1 1 2 1 2v2h0c-1 0-1 0-2-1 0-1-1-2-2-3z" class="G"></path><path d="M271 441l-1-1h0 3 1v1c1 0 2 0 3-1h1v1s1 0 1 1l1 1c1 0 2 1 3 2h-1c0 1-1 1-2 2 0 0 0 1 1 1 1 2 3 3 3 5 1 1 2 2 3 2l-3 3h-2c0 2 1 2 2 3l-2 1h0c-1-1-2-1-3 0l-2 1c-1 0-2 1-2 2v1c-1-1-2-2-2-3-2-3-3-3-6-3l-2-1c1-2 3-3 3-5h0c-2 0-2 0-3-1l5-5v-2c-2-1-2-1-3 0v-1-1h1v-2l3-1z" class="C"></path><path d="M275 455v3h0l1 1c0 1 1 2 2 2l-1 2c-1 0-2 1-2 2-1-1-2-3-2-5-1 0 0 0-1-1v-1c1-1 1-1 1-2v-1h2z" class="F"></path><path d="M271 441l-1-1h0 3 1v1c1 0 2 0 3-1h1v1s1 0 1 1l1 1c1 0 2 1 3 2h-1c0 1-1 1-2 2 0 0 0 1 1 1h-3l-1 2h-2-2c0-2 1-4 0-7 0-1-1-1-2-2z" class="E"></path><path d="M278 448l-1-1c0-1 1-1 1-2s-1-2-1-3c1 0 2 1 3 1s2 1 3 2h-1c0 1-1 1-2 2 0 0 0 1 1 1h-3z" class="O"></path><path d="M278 448h3c1 2 3 3 3 5 1 1 2 2 3 2l-3 3h-2c0 2 1 2 2 3l-2 1h0c-1-1-2-1-3 0l-2 1 1-2c-1 0-2-1-2-2l-1-1h0v-3c0-2-1-3-2-5h2 2l1-2z" class="C"></path><path d="M276 459c1-1 3-1 5-1l-3 3c-1 0-2-1-2-2z" class="B"></path><path d="M281 458h1c0 2 1 2 2 3l-2 1h0c-1-1-2-1-3 0l-2 1 1-2 3-3z" class="L"></path><path d="M273 450h2 2l1 1c-1 1-2 2-2 4l1 1c1 1 1 1 0 2h-2 0v-3c0-2-1-3-2-5z" class="i"></path><path d="M294 321h1 3v1l1 1s1 1 2 1c1-1 1-1 1-2h0c1 0 2 1 2 1l1 3v1c-1 3-1 6 0 9v1 4c0 1 0 1-1 2v2c0 2-1 3-2 4h-1-2-4l-1-2h-1v-1l1-1v-3l-1 1c-1-1 0-1-1-2l-1-3-1-2c0-1-1-2-1-3h0c0-1-1-1-1-2v-1c1-1 3 0 4 0l-1-2c1 0 1-1 2-2v-2-1s1 0 1-1v-1z" class="W"></path><path d="M288 330c2 1 2 2 3 4h0v4l-1-2c0-1-1-2-1-3h0c0-1-1-1-1-2v-1z" class="D"></path><path d="M291 334c1 1 4 4 4 6h-2v2h1c1 1 1 2 1 4l-1 1h-1v-1l1-1v-3l-1 1c-1-1 0-1-1-2l-1-3v-4z" class="E"></path><path d="M302 322h0c1 0 2 1 2 1l1 3v1c-1 3-1 6 0 9v1 4c0 1 0 1-1 2v2-3c1-2 0-7-1-9 0-2-1-3-2-3 1-1 2-1 3-2l-1-3c-1 0-1 0-2-1h0c1-1 1-1 1-2z" class="R"></path><path d="M295 346l3-2s0-1 1-2v-1l1 2c1-1 2-1 4-1v3c0 2-1 3-2 4h-1-2-4l-1-2 1-1z" class="C"></path><path d="M300 343c1-1 2-1 4-1v3c0 2-1 3-2 4h-1-2l3-3v-2c-1 0-1-1-2-1h0z" class="L"></path><path d="M294 321h1 3v1l1 1s1 1 2 1h0c1 1 1 1 2 1l1 3c-1 1-2 1-3 2-3-1-5-2-7-4h-1v-2-1s1 0 1-1v-1z" class="C"></path><path d="M299 323s1 1 2 1h0c-1 1-1 2-2 2h-1v-1h0c0-1 0-1 1-2z" class="G"></path><path d="M294 321h1 3v1l1 1c-1 1-1 1-1 2h0v1c-1 0-2-1-3-1l-1 1h-1v-2-1s1 0 1-1v-1z" class="B"></path><path d="M297 168c4-1 7-1 10 1l-1 2c0 3 0 5-2 7l-3 3h0c2 1 3 1 4 1l2 2 2 2v2c1 1 1 3 0 4 0 1 0 2-1 2l-1 1h-2-1v-1c-1-1-3-3-4-3v-1h-3-1c0-2-3-3-4-4s-2-1-3-2l2-2c1-1 3 0 4 0-2-2-4-3-6-5 1-1 2-2 2-3 1 0 1 0 1-1h0v-1c0-1 1-1 2-2v1h1v1l3-3-1-1z" class="l"></path><path d="M300 190c2 1 4 1 6 3 0 0 1 1 1 2h-2-1v-1c-1-1-3-3-4-3v-1z" class="J"></path><path d="M292 186c-1-1-2-1-3-2l2-2v1c3 1 5 1 7 2-2 0-4 0-6 1z" class="G"></path><path d="M299 173c1 0 3 1 3 1-1 2-2 4-4 4-1 0-1 0-2-1v-1-1l2 2v-1l1-3z" class="a"></path><path d="M298 169c1 1 1 2 1 4l-1 3v1l-2-2c0-1-1-2-1-3l3-3z" class="F"></path><path d="M292 172c0-1 1-1 2-2v1h1v1c0 1 1 2 1 3v1 1c-2 0-3-1-5-3 1 0 1 0 1-1h0v-1z" class="Z"></path><path d="M294 171h1v1c0 1 1 2 1 3v1c-1 0-1 0-2-1v-4z" class="I"></path><path d="M297 168c4-1 7-1 10 1l-1 2c-1-1-1-2-2-2-1 2-1 3-2 5 0 0-2-1-3-1 0-2 0-3-1-4l-1-1z" class="M"></path><path d="M410 126c1 1 1 1 2 1s3 1 4 0l1 1c-3 1-4 3-6 6 2 0 2 0 3 1 1-2 2-3 3-4 1-2 3-4 5-4s2 0 3 1c2-1 4-2 6-2v6l-2 2 1 1v1 1c0 1 0 2-1 2h0c0 1 1 1 2 2h0c-2 0-3 0-4 1l1 1c-1 1-2 1-3 1l1 1h-1c-4-2-8-3-13-5-3-1-6-2-9-2l2-2v-3l-1-2h1v-1l1-1c1-1 2-1 2-2h2v-1z" class="j"></path><path d="M425 140c1 1 1 1 2 1v1l1 1c-1 1-2 1-3 1l-2-2v-1h2v-1z" class="F"></path><path d="M410 126c1 1 1 1 2 1v1c-1 1-2 2-3 2s-2 0-3-1c1-1 2-1 2-2h2v-1z" class="E"></path><path d="M414 135v4c-1 1-2 0-3 0-1-1 0-3 0-5 2 0 2 0 3 1z" class="W"></path><path d="M418 131h4c1 0 1 1 2 2h0c1 0 1-1 1-2 1 0 3 2 4 3l1 1v1 1c0 1 0 2-1 2h0c0 1 1 1 2 2h0c-2 0-3 0-4 1v-1c-1 0-1 0-2-1h-1c-1-1-1-1-2-1l-1 1c-1 1-2 0-2 0-1 0-2 0-3-1-1-2 0-2 0-4s1-3 2-4z" class="i"></path><path d="M424 138l6-2v1c0 1 0 2-1 2h0-3c-1 1 0 1-1 0l-1-1z" class="O"></path><path d="M425 140h-1c-1-1-1-1-2-1l-1 1c-1 1-2 0-2 0-1 0-2 0-3-1-1-2 0-2 0-4s1-3 2-4v2l1 1h1 1 0v1h0c-1 0-1 0-2 1h-1c2 1 2 0 4 2h0 2l1 1c1 1 0 1 1 0h3c0 1 1 1 2 2h0c-2 0-3 0-4 1v-1c-1 0-1 0-2-1z" class="E"></path><path d="M244 336c1-1 1-3 2-4l1 1v2c0 1 1 2 1 3 1 0 1 1 2 1v2c1 1 2 3 3 4h2c1 0 1 1 2 1v1h-2c0 1 0 1 1 1h0 1v1h3v3h-1v1l-1 1-1-1v2l-1-1-2 2-1 1h-2-4-1c-1 0-2 0-3-1s-2-2-3-2l-3-2c1-1 2-1 3-2l-1-1c-1 0-2-1-2-1-1-1-1-2-1-2 0-3 1-3 3-4 0 0 1 0 2 1v-2c-1-1-1-1-2-1-1 1-2 1-4 1v-4-1h1c2 0 4 1 5-1h1 1v1h1z" class="B"></path><path d="M255 352h0 4v1l-1 1-1-1c-1 0-1 0-2-1z" class="I"></path><path d="M250 356c0-2 0-2-2-4h1 3c0 2-1 2-2 4z" class="k"></path><path d="M240 343l2 2c1 0 1 0 1 1h1l-1 1c-1 0-3-1-4-2 1-1 1-1 1-2z" class="L"></path><path d="M252 352c1 1 1 2 2 3v-1s0-1 1-1v-1c1 1 1 1 2 1v2l-1-1-2 2-1 1h-2c0-1 0-1-1-1 1-2 2-2 2-4z" class="G"></path><path d="M240 354l1-1c0-1 0-1 1-2h0 2 1c0 1 0 1 1 1h0v1c0 1 0 2 1 2h0l1 1c-1 0-1 0-1 1h0-1c-1 0-2 0-3-1s-2-2-3-2z" class="D"></path><path d="M244 336c1-1 1-3 2-4l1 1v2c0 1 1 2 1 3 1 0 1 1 2 1v2l-3 3h0c-1-1-2-2-2-3-1-2-1-3-1-5z" class="M"></path><path d="M247 335c0 1 1 2 1 3 1 0 1 1 2 1v2l-3 3c-1-2-1-6 0-9z" class="B"></path><path d="M247 344l3-3c1 1 2 3 3 4h2c1 0 1 1 2 1v1h-2c0 1 0 1 1 1-2 2-5 1-7 1-1-1-2-3-2-5h0z" class="O"></path><path d="M242 335v1l-1 1c0 1 0 1 1 2s1 3 2 4c1 2 2 4 2 5v1c-2-1-2-1-3-2l1-1h-1c0-1 0-1-1-1l-2-2h1v-2c-1-1-1-1-2-1-1 1-2 1-4 1v-4-1h1c2 0 4 1 5-1h1z" class="M"></path><path d="M172 119l3 1v-2h1v1l1 1c2 1 2 1 4 1h1 0c4 1 7 2 10 3l2-2 2-1 2 1c1 0 2 0 2 1 1 1 1 2 1 3h0c0 1-1 2 0 2v4 1c-2 0-3 0-5-1h0v1c0 1-1 1-1 0-1 0-1 0 0-1-1 0-1 0-1 1h-1l-1 1h0-4c-1 0-1 0-1 1h-2l-3 1h-2c0-1-1-1-2-1-1-1-1-1-2 0l-1-1c0 1 0 1-1 1h-1s-1-1-1-2h-1c0 1 1 1 1 2v1c-1 0-2 0-3-1s-1-2 0-3h0v-1c1-2 2-3 3-4h1v-2-1h1s-1-1-1-2c-1 0-2-1-3-2h0c1 0 1 0 2-1z" class="X"></path><path d="M180 130h1-1v-3h1l3 3c0 1 1 1 0 1 0 1-1 1-1 2-1-1-1-1-2-1 0-1 0-1-1-1v-1z" class="E"></path><path d="M178 135c0-2 1-3 2-5v1c1 0 1 0 1 1 1 0 1 0 2 1v1l-1 2h-2c0-1-1-1-2-1z" class="H"></path><path d="M182 121c4 1 7 2 10 3 2 0 3 1 5 2l-1 1h0c-1-1-2 0-3 0-1 1-1 4-1 6v-3l-1-1c-1 0 0 0-1-1l2-2c1 0 2 0 2-1h-4-1l-1 2-3-3-3-3z" class="M"></path><path d="M181 121h1 0l3 3 3 3v3c0 1-1 1-1 1l-1-1c-1-3-4-4-7-5v-1h1c0-1 1-1 1-3z" class="E"></path><path d="M196 121l2 1c1 0 2 0 2 1 1 1 1 2 1 3h0c0 1-1 2 0 2v4 1c-2 0-3 0-5-1h0v1c0 1-1 1-1 0-1 0-1 0 0-1-1 0-1 0-1 1h-1-1c0-2 0-5 1-6 1 0 2-1 3 0h0l1-1c-2-1-3-2-5-2l2-2 2-1z" class="W"></path><path d="M172 119l3 1v-2h1v1l1 1c2 1 2 1 4 1 0 2-1 2-1 3h-1l-7-3c3 2 5 3 5 7 0 2-1 4-2 6h0c0 1 0 1-1 1h-1s-1-1-1-2h-1c0 1 1 1 1 2v1c-1 0-2 0-3-1s-1-2 0-3h0v-1c1-2 2-3 3-4h1v-2-1h1s-1-1-1-2c-1 0-2-1-3-2h0c1 0 1 0 2-1z" class="i"></path><path d="M171 133h-1l1-1s0-1 1-1c0 0 0-1 1-1 1 1 1 2 2 4h0c0 1 0 1-1 1h-1s-1-1-1-2h-1z" class="F"></path><path d="M354 116l2-1c5-2 10-4 16-5 5-1 10-1 15-2 6 0 11-1 16 1h0c2 0 4 1 5 2-4 0-7-2-11-2v2h-1v-1l-1-1-1 1v1 1c-1-1-1-2-1-3-2 0-3 0-5 1v1c-1 1-1 1-2 1l1-2c-2-1-9 1-11 1v3c-1 0-1 1-2 1-1 1-1 1-2 1l-1-1-3 2h1v1c1 0 1 1 2 2h0c-2 1-6 2-8 3-1 2-1 3-3 4-1 0-2-1-4-1h0l2-2h0-2-1l1-1h-1s-1 0-2 1-1 2-2 2c-2 1-3 2-4 3-2 1-6 0-8-1s-2-4-3-6c1 0 1-1 2-1l1 4 2-2h0c1 0 1 0 1 1 3-3 5-7 8-9l1 1h1 2z" class="b"></path><path d="M369 112l4-1c0 1-1 2-2 3h-1l-1-2z" class="B"></path><path d="M356 118c0-1 1-2 2-2h1c1 0 1 0 1 1l-2 2h-1-1 1-1v-1h0z" class="Y"></path><path d="M357 119c-1 1-1 2-2 3l-1-1c-1-1-1-1-1-2 1-1 2-1 3-1h0v1h1-1 1z" class="H"></path><path d="M351 126l-1-1c0-1-1-2 0-3 0-1 1-1 1-1 2 1 2 1 2 3-1 1-1 2-2 2z" class="Y"></path><path d="M358 119l2-2v1s1 1 1 2-1 2-3 3h-1-1-1l3-3v-1z" class="F"></path><path d="M368 117l-3 2v-1-2l-2-3 6-1 1 2c-1 0-2 1-3 2 1 0 1 1 1 1z" class="D"></path><path d="M373 111h3v3c-1 0-1 1-2 1-1 1-1 1-2 1l-1-1-3 2h0s0-1-1-1c1-1 2-2 3-2h1c1-1 2-2 2-3z" class="G"></path><path d="M342 124c3-3 5-7 8-9l1 1h1 2l-5 4c-2 2-5 6-7 6-1 0-1-1-1-1l1-1z" class="F"></path><path d="M280 254v-1h3c0 1-2 4-1 5l1 1 1-1v1c1 1 2 3 3 4h1 1v1 1c1 0 1 0 1 1v1c1 2 1 2 3 3h4l1-1 2 1h1v3l1 1-1 4c-1 3-3 3-6 4h0c-1 0-1 0-2 1h-1v2c-2-1-4-1-6-2h-1c-1-1-2-3-2-4-1-2-2-3-2-4-2-3-2-7-3-10v-3h-2v-4h0 1v-2c1-1 1-2 2-3v1h0 1z" class="I"></path><path d="M276 258h1v-2c1-1 1-2 2-3v1h0l-1 11v-3h-2v-4h0z" class="D"></path><path d="M280 254v-1h3c0 1-2 4-1 5l1 1-2 2h-2c1-2 1-5 1-7z" class="S"></path><path d="M286 283c-1-2-3-3-3-6-1-1-1-2-2-3l1-1c1 0 2 1 3 1 2 0 3 1 4 1 1 1 2 1 2 2-1 1-2 2-3 2-1-1-2-1-3-1h0l1 2h4 1s1 0 2 1v2h-1v2c-2-1-4-1-6-2z" class="G"></path><path d="M290 280h1s1 0 2 1v2h-1-1s-1-1-2-1l-1-1 2-1z" class="l"></path><path d="M298 269l2 1h1v3l1 1-1 4c-1 3-3 3-6 4h0c-1 0-1 0-2 1v-2c-1-1-2-1-2-1l4-2c0-2-2-4-3-5s-1-2-2-2l-1-1h1 3 4l1-1z" class="F"></path><path d="M292 273h0c2 0 3 1 4 0 1 0 1 0 2 1 1 0 1 0 2 1v1h-1l-1-1h-1c0 1 0 2-1 3h-1c0-2-2-4-3-5z" class="B"></path><path d="M283 259l1-1v1c1 1 2 3 3 4h1 1v1 1c1 0 1 0 1 1v1c1 2 1 2 3 3h-3-1l1 1h-1v2h-1c-2-1-6-1-7-4-2-2-2-5-2-8h2l2-2z" class="i"></path><path d="M288 263h1v1 1c1 0 1 0 1 1v1c1 2 1 2 3 3h-3-1l1 1h-1v2h-1c-2-1-6-1-7-4 0 1 1 1 2 0h0v-1h-1l-1-1s0-1 1-1c0-1 0-1 1-2l1 1h-1v3c1 1 2 1 3 1v-1-3l2-2z" class="B"></path><path d="M288 263h1v1 1c1 0 1 0 1 1v1h-1c-1 1 0 2-1 2s-1 0-2-1v-3l2-2z" class="h"></path><path d="M261 445s1-1 2-1c1-1 3-1 4 0v1 1c1-1 1-1 3 0v2l-5 5c1 1 1 1 3 1h0c0 2-2 3-3 5l2 1c3 0 4 0 6 3 0 1 1 2 2 3-1 0-1 0-2 1h0v1c-2 1-4-1-6-2-1 2-1 1-2 3 1 1 2 1 3 2 1 0 3 1 4 1 0 1 1 2 1 3h0-1-1 0-3-1c-1-1-4-1-6-1s-3-1-5-1l-1-1c-3-1-4-2-5-5h-1v-3c1-2 4-3 5-5 1-1 1-1 0-2h0c-1-1-1-1-1-2 1 0 2-1 2-3h1c1-1 1-2 2-2l1 1c0-2 1-4 2-6z" class="F"></path><path d="M264 461c2 1 3 0 4 1-1 1-1 2-2 3v1c-2-1-2-2-3-3 0-1 0-1 1-2z" class="b"></path><path d="M260 456c0 1 1 1 1 2v1h0c-1 0-1 0-1 1-1 0-1 1-1 1h0c0 1 0 1-1 2 0-1-1-2-1-3v-1c0-1 2-2 3-3z" class="i"></path><path d="M263 463c-1 0-1 0-1 1-1 0-1 0-1-1h0c0-1-1-1 0-2 0-2 1-1 2-1v-2c0-1 0-1 1-1s1 1 1 2l2 1v1h-1-2c-1 1-1 1-1 2z" class="W"></path><path d="M267 460c3 0 4 0 6 3 0 1 1 2 2 3-1 0-1 0-2 1h0v1c-2 1-4-1-6-2h-1v-1c1-1 1-2 2-3-1-1-2 0-4-1h2 1v-1z" class="B"></path><path d="M268 462c1 2 4 3 5 5v1c-2 1-4-1-6-2h-1v-1c1-1 1-2 2-3z" class="C"></path><path d="M261 445s1-1 2-1c1-1 3-1 4 0v1 1c1-1 1-1 3 0v2l-5 5v1c-1 0-1 0-2 1h0v-2-1-1l-1 1c-1-1-1-3 0-4h0c-1-1-1-2-1-3z" class="W"></path><path d="M258 450l1 1c-1 0-1 1-1 1 1 1 3 2 4 3-1 1-1 1-2 1-1 1-3 2-3 3v1c0 1 1 2 1 3l3 3 4 3c1 1 2 1 3 2 1 0 3 1 4 1 0 1 1 2 1 3h0-1-1 0-3-1c-1-1-4-1-6-1s-3-1-5-1l-1-1c-3-1-4-2-5-5h-1v-3c1-2 4-3 5-5 1-1 1-1 0-2h0c-1-1-1-1-1-2 1 0 2-1 2-3h1c1-1 1-2 2-2z" class="j"></path><path d="M257 460c0 1 1 2 1 3l3 3h-2c-1-1-2-2-2-3-1-1 0-2 0-3z" class="C"></path><path d="M250 467s1 0 1 1c1 1 2 2 4 3 0 0 1 1 1 2l-1-1c-3-1-4-2-5-5z" class="I"></path><path d="M453 106h5c1 1 0 3 1 4l1 1 1-2c1-1 0-1 0-3 2 0 5 1 7 0h3v1c1-1 1-1 2 0 0 1-1 2-1 3l-3 1v1c2 1 4 1 6 1 1 0 1 0 1 1 0 2 0 2-1 3s-2 1-3 0l-1 2v2c0 1 0 2-1 2h-1l-2-1h-1l-1-2-2 2v2c-1 0-2 1-3 1h-7c-1-1-1-1-2-1-2-2-4-4-6-5-2 0-3-1-4-2 1-1 2-2 2-3 1-1 1-2 1-3 1-1 3-3 4-3 2-1 4-1 5-2z" class="C"></path><path d="M469 111c0-2 1-2 1-3 1-1 2-1 3-1 0 1-1 2-1 3l-3 1z" class="E"></path><path d="M462 116c-1 0-3-1-5 0 2 1 3 2 4 4h0c-1 1-2 2-3 2-2 0-3-1-4-2-1-2 0-4 0-5v-1h0c-2 1-2 3-3 4l-3-3c1-2 3-4 5-5l3 2c2 1 6 0 8-1s3-3 5-3h0c-1 1-2 2-1 4h1c2 1 4 1 6 1 1 0 1 0 1 1 0 2 0 2-1 3s-2 1-3 0l-1 2v2c0 1 0 2-1 2h-1l-2-1h-1l-1-2v-2c-1-1-2-2-3-2z" class="W"></path><path d="M466 115c1-1 2 0 4 0h0c0 1 1 2 2 2l-1 2c-1-1-2-2-3-2-1-1-2-1-2-2z" class="L"></path><path d="M462 116l2-1c0 1 1 1 1 1l1-1c0 1 1 1 2 2 1 0 2 1 3 2v2c0 1 0 2-1 2h-1l-2-1h-1l-1-2v-2c-1-1-2-2-3-2z" class="F"></path><path d="M113 115l3 1v1c-1 1-3 1-4 2 0 1 1 2 1 3 2 1 2 1 4 0v2h2l6 1c1 1 2 2 3 2l-1 2v2 1l-1 1c0 1-1 1-1 2v1h-1-4l-1-1c-3 1-5 2-8 4-2 1-5 2-7 4-1 1-2 2-3 4h-1l-1 1v1 1 1c-2-3 0-6 0-8v-1l-2 2c0-1 0-1 1-2h0v-2c0-1 1-2 1-3 1-2 0-5 1-6h1l1-1v-1h-1v-1c1-1 2-6 3-8l1-1c0 1 1 1 1 1 1 0 2-1 3-1l1-1c1-2 1-2 3-3z" class="W"></path><path d="M106 130h1l1-2h3 0-1c-1 1-1 3-2 3h0s-1 0-1-1h-1z" class="B"></path><path d="M119 129l4 1c2 1 3 2 4 2l-1 1c0 1-1 1-1 2v1h-1-4l-1-1 2-1-1-2h-3c-2 1-5 2-6 2 1-2 6-2 8-4v-1z" class="D"></path><path d="M121 134c1 0 2 0 4-1h1c0 1-1 1-1 2v1h-1-4l-1-1 2-1z" class="j"></path><path d="M113 124h6l6 1c1 1 2 2 3 2l-1 2v2 1c-1 0-2-1-4-2l-4-1-5-1-2 2c-1 0-1-1-1-2h0l1-1-1-1c1-1 1-2 2-2z" class="E"></path><path d="M114 128h-1c2-1 6-1 7-1 3 0 6 2 7 4v1c-1 0-2-1-4-2l-4-1-5-1z" class="L"></path><path d="M113 115l3 1v1c-1 1-3 1-4 2 0 1 1 2 1 3 2 1 2 1 4 0v2h2-6c-1 0-1 1-2 2l1 1-1 1h-3l-1 2h-1 1c-1 2-2 3-3 4-2 0-1-1-3-1-1 1-1 2-1 3l2 1c1 1 0 1 0 2-1 1-1 0-3 0v3l-2 2c0-1 0-1 1-2h0v-2c0-1 1-2 1-3 1-2 0-5 1-6h1l1-1v-1h-1v-1c1-1 2-6 3-8l1-1c0 1 1 1 1 1 1 0 2-1 3-1l1-1c1-2 1-2 3-3z" class="E"></path><path d="M105 119c0 1 1 1 1 1 1 0 2-1 3-1 0 1 0 3-1 4h-1c-2 1-3 4-4 6h0c0 1 0 1-1 1v-1h-1v-1c1-1 2-6 3-8l1-1z" class="B"></path><path d="M113 115l3 1v1c-1 1-3 1-4 2 0 1 1 2 1 3 2 1 2 1 4 0v2h2-6c-1 0-1 1-2 2l1 1-1 1h-3l-1 2h-1l2-4h0-2c0-1 1-2 1-3h1c1-1 1-3 1-4l1-1c1-2 1-2 3-3z" class="e"></path><path d="M111 126h0l1 1-1 1h-3c1-1 2-2 3-2z" class="G"></path><path d="M111 126l-2-2-1 1v-1l1-1c0-1 0-1 1-2h0l3 3h0c-1 0-1 1-2 2h0z" class="T"></path><path d="M262 174c2 1 2 1 3 2v1 1h0 1l1 1v2 4c1 4 2 9 4 12h2v5h0v-1c-1-3-4-6-7-8-2-1-6-3-9-3v1c3 3 10 5 11 9h0c-3-2-5-2-8-1-2 0-3 1-5 2l-3-1c-1 0-2 1-2 1-2 0-3-2-4-3-1 0-1 0-1-1-1 0-2 1-3 1v-1c-1-1-1-2-2-3h0c1 0 1-1 1-2h1c0-1 0-2-1-3-2 0-3 0-4-1 2 0 3-1 4-3 1-1 3-2 4-3v-1h5c1 0 2 1 2 0h4v1 1h1l2 2c1-2 2-3 3-5s0-3 1-4l-1-2z" class="j"></path><path d="M262 174c2 1 2 1 3 2v1 1h0 1l1 1v2 4c-1-3-2-6-4-9l-1-2z" class="V"></path><path d="M242 197c-1-1-1-2-2-3h0c1 0 1-1 1-2h1v1l1-1h3c-1 1-1 1-1 2v1c-1 2-2 2-3 2z" class="F"></path><path d="M237 188c2 0 3-1 4-3 1-1 3-2 4-3v-1h5c1 0 2 1 2 0h4v1 1h1l2 2c-2 1-5 2-5 4 0 0 1 0 2 1h0c-1 1-3 1-5 1-1-1-3-1-4 0 0 1 0 1-1 1h0-3l-1 1v-1c0-1 0-2-1-3-2 0-3 0-4-1z" class="l"></path><path d="M264 214c1 2 4 3 5 5 2 1 2 3 3 4v2 1c1 0 2 0 3 1 0 1-1 5 0 6l1 2c0 1 0 1 1 2s2 1 2 3v1h-2c-1-1-1 0-2 0-1-1-2 0-4-1h0l-3 3c-1-1-1-1-1-2-1 1-1 2-2 2-2 0-5 1-7 1l-1 1-1 1h-1v-1l-2 1h-2v-1-1l-2-2h0c0-1-1-1-1-1v-1h1 0 1v-1l2-2 2-2c0-1 0-1-1-2 1-1 1-1 2-1l1 1-1 1v1h1l2-1c1-1 1-1 2-1l1-1h0l1-1-1-1h0c-1-1-2-2-3-2l1-1-1-2v-2s-1 0-1-1v-1l2 1c1 0 2-1 3-1v-3-1c-1-1-1-2-2-3h4z" class="F"></path><path d="M264 239h1 1v1s-1 1-1 2h-3c0-1 1-1 2-2v-1z" class="Y"></path><path d="M263 233l1-1h0 1l1 1v2l-1-1c-1 1-1 2-1 3 0-1 0-1-1-2l-1-1 1-1z" class="B"></path><path d="M264 237c0-1 0-2 1-3l1 1 1 1h2c-1 1-2 2-3 2l-1 1h-1v-2z" class="R"></path><path d="M271 240c-1-1-1 0-2-1v-1-1c1-1 2-2 2-4v-1l1 1c0 1 0 2 1 3h1-1c-1 1-1 2-1 3l-1 1h0z" class="V"></path><path d="M262 231v1h0c-1 1-1 1-1 3h0c0 2-1 3-2 4l-1-1h-2v-3l2-1c1-1 1-1 2-1l1-1h0l1-1z" class="G"></path><path d="M275 233l1 2c0 1 0 1 1 2s2 1 2 3v1h-2c-1-1-1 0-2 0-1-1-2 0-4-1l1-1c0-1 0-2 1-3h1c0-1 0-2 1-3z" class="L"></path><path d="M272 239c0-1 0-2 1-3l1 1h1c1 1 1 1 1 2-1 1-3 0-4 0z" class="O"></path><path d="M269 224l2 2 1-1v1 1h-2v1c1 2 1 2 1 4l-1 1c0-1-1-1-1-2-1 0-2 1-3 2h0l-1-1c0-2 0-3-1-4 0-1 0-1 1-2s3 0 4-2z" class="a"></path><path d="M269 224l2 2 1-1v1 1h-2v1h0c-2 0-4 0-5-2 1-1 3 0 4-2z" class="D"></path><path d="M259 222c1 0 2-1 3-1l-1 3 1 1 1 1c0 1 0 1 1 2s1 2 1 4h-1 0l-1 1-1 1-1 1h0c0-2 0-2 1-3h0v-1l-1-1h0c-1-1-2-2-3-2l1-1-1-2v-2s-1 0-1-1v-1l2 1z" class="E"></path><path d="M259 222c1 0 2-1 3-1l-1 3 1 1 1 1h-1l-1 1h-1c0-2 0-3-1-5z" class="J"></path><path d="M262 226h1c0 1 0 1 1 2s1 2 1 4h-1 0l-1 1-1 1-1 1h0c0-2 0-2 1-3h0v-1l-1-1h0c0-1 0-2-1-3h1l1-1z" class="Q"></path><path d="M262 226h1c0 1 0 1 1 2s1 2 1 4h-1 0l-1 1c0-1 0-3-1-5v-2z" class="V"></path><path d="M253 233c1-1 1-1 2-1l1 1-1 1v1h1v3l-1 2v1c1 0 2 0 2-1l1 1c-1 1-3 3-3 4l-2 1h-2v-1-1l-2-2h0c0-1-1-1-1-1v-1h1 0 1v-1l2-2 2-2c0-1 0-1-1-2z" class="L"></path><path d="M250 240c1 1 2 2 2 4 1-1 1-2 2-2l1-1c1 0 2 0 2-1l1 1c-1 1-3 3-3 4l-2 1h-2v-1-1l-2-2h0c0-1-1-1-1-1v-1h1 0 1z" class="D"></path><path d="M264 214c1 2 4 3 5 5 2 1 2 3 3 4v2l-1 1-2-2c-1 2-3 1-4 2s-1 1-1 2c-1-1-1-1-1-2l-1-1-1-1 1-3v-3-1c-1-1-1-2-2-3h4z" class="E"></path><path d="M262 218c1 1 1 1 2 1h0c1 0 1 1 2 1 1 1 2 3 3 4-1 2-3 1-4 2s-1 1-1 2c-1-1-1-1-1-2l-1-1-1-1 1-3v-3z" class="h"></path><path d="M262 218c1 1 1 1 2 1h0c1 0 1 1 2 1-1 1-1 1-1 2l-1-1c-1 1-1 2-2 4l-1-1 1-3v-3z" class="G"></path><path d="M281 341c1-1 3-1 3-3l2-2 1-1 1 1h2l1 2 1 3c1 1 0 1 1 2l1-1v3l-1 1v1h1l1 2h4 2 1l2-2v1 2h0 1c1 0 3-2 4-3 1 1 2 1 2 2 1-1 2-1 2-2l1 1c1-1 1-1 1-2h2c1 2 1 4 1 6l-3-1-1 2c-1 1-2 1-2 4 0 1-1 2-2 3-2 1-5 2-7 2h-1-2l1 2c-3-1-6-3-9-3v1c-2-1-3-3-4-4l-1-2c0-2-1-3-2-5 0 1 0 1-1 1-1-1-2-1-4-1h-2c1-3 2-7 3-10z" class="O"></path><path d="M303 353l2 1c0 1-1 2-1 3h-2v-2l1-2z" class="H"></path><path d="M305 354h1l2-1 2 2c-2 1-4 2-6 2 0-1 1-2 1-3zm-9-1l1 1v3h-3l-2-2 1-1c1 0 2 0 3-1z" class="B"></path><path d="M294 351h1c1 0 1 0 1 1v1c-1 1-2 1-3 1l-1 1c-1-1-1-3-1-5 2 1 2 1 3 1z" class="S"></path><path d="M290 354l1 1c3 4 5 6 9 7l1 2c-3-1-6-3-9-3v1c-2-1-3-3-4-4l-1-2c1 0 1-1 2-1 0-1 1-1 1-1z" class="E"></path><path d="M290 354l1 1c0 1 0 2-1 2l-2 1-1-2c1 0 1-1 2-1 0-1 1-1 1-1z" class="F"></path><path d="M291 345l2 1v1h1l1 2h4 2 1l2-2v1 2l-1 1h-1v1l1 1-1 2c-1 0 0 1-1 2s-1 0-2 0c0-1-1-1-1-2s1-2 1-2l-1-1-1 1v1l-1-1v-1c0-1 0-1-1-1h-1c-1 0-1 0-3-1l1-1c-1-1-1-1-2-3l1-1z" class="D"></path><path d="M291 345l2 1v1h1l1 2-2-1h-1v1l2 2c-1 0-1 0-3-1l1-1c-1-1-1-1-2-3l1-1z" class="R"></path><path d="M302 349l2-2v1 2l-1 1h-1l-2 1c-1 0-2 0-3-1l1-1 3-1h1z" class="H"></path><path d="M315 346h2c1 2 1 4 1 6l-3-1h-1l-2 3-2 1-2-2-2 1h-1l-2-1-1-1v-1h1l1-1h0 1c1 0 3-2 4-3 1 1 2 1 2 2 1-1 2-1 2-2l1 1c1-1 1-1 1-2z" class="d"></path><path d="M309 347c1 1 2 1 2 2h0l-1 1c-1 1-2 1-4 2-1 0 0 1-1 0h-2-1v-1h1l1-1h0 1c1 0 3-2 4-3z" class="S"></path><path d="M315 346h2c1 2 1 4 1 6l-3-1h-1l-2 3-2-2 2-2 2-2h0c1-1 1-1 1-2z" class="B"></path><path d="M281 341c1-1 3-1 3-3l2-2 1-1 1 1h2l1 2 1 3c1 1 0 1 1 2l1-1v3l-1 1-2-1-1 1-1 1v1c0 2 0 4 1 6 0 0-1 0-1 1-1 0-1 1-2 1 0-2-1-3-2-5 0 1 0 1-1 1-1-1-2-1-4-1h-2c1-3 2-7 3-10z" class="C"></path><path d="M285 351v-2c1-3 1-9 3-11 2 0 2 1 3 1 0 1 0 2 1 2h0c1 1 0 1 1 2l1-1v3l-1 1-2-1-1 1-1 1v1c0 2 0 4 1 6 0 0-1 0-1 1-1 0-1 1-2 1 0-2-1-3-2-5z" class="D"></path><path d="M289 348c-1-1-1-4-1-5 2 0 3 1 3 2l-1 1-1 1v1z" class="h"></path><path d="M285 351c1 2 2 3 2 5l1 2c1 1 2 3 4 4v1h0c-1 0-2-1-3-1s-1 1-2 2c1 0 1 1 1 1-1 1-4 1-5 1v1h2v1c0 1-1 2-2 3s-2 1-3 2h0c-1 2-4 5-4 7l1 1v1h1c1 0 1 0 2 1h0c-2 0-2 0-3-1v1 5l-2-2c-1 1-1 1-1 3h2v1l-3 2v4l-1-1v-1c-1-1-2-4-2-5l-3-3-1-2c1 0 2 1 3 0 1 0 3-1 4-2h0c-1-3-4-5-7-7 1-1 1-2 2-2v-2c2 0 2 0 3 1h0l1 1 1-1-3-3h-3v-1l-1-1h0c-1-1-3-1-4-2h2c1 0 1 0 2-1-1-1-1-2-1-4h-1l1-1v-2c1 1 2 0 3 0h0c1-1 2-1 3-1h0l1 1s1 0 1 1 1 1 1 2l1-1c-1-1-1-1 0-2h1l1-1c1-1 1-1 1-2h-1l1-3h2c2 0 3 0 4 1 1 0 1 0 1-1z" class="H"></path><path d="M269 363v-2c1 0 2 1 3 1l-1 1h-2z" class="B"></path><path d="M269 363h2l1 1v1h-1-1v1h-1v-3z" class="V"></path><path d="M278 354h-1l1-3h2c1 2 1 2 2 3h0l-1 2c-1-1-1-2-2-4h0l-1 2z" class="T"></path><path d="M272 362h9v1c-1 0-3 1-4 1h-5l-1-1 1-1z" class="k"></path><path d="M270 366v-1h1c1 1 2 1 3 2v2h0c-1 2-1 4-1 6h0c-1-1-1-2-2-3l1 1 1-1-3-3h-3v-1c2 1 1 0 2-1h1l1 1 1 1h0l1-1c-1-1-2-1-3-2z" class="Q"></path><path d="M272 364h5l-1 1h-1v3l2 1v4c0 1-1 2-2 2 0-1 1-3 1-4-1-1-1-2-2-2v-2c-1-1-2-1-3-2h1v-1zm-1-8h0l1 1v2h-2s-1 1-2 1c-1-1 0-1-1 0v1l-1 2v1c-1-1-1-2-1-4h-1l1-1v-2c1 1 2 0 3 0h0c1-1 2-1 3-1z" class="T"></path><path d="M271 356h0v2h-3c-1 0-2 0-3 1v-2c1 1 2 0 3 0h0c1-1 2-1 3-1z" class="J"></path><path d="M285 351c1 2 2 3 2 5l1 2c1 1 2 3 4 4v1h0c-1 0-2-1-3-1s-1 1-2 2c1 0 1 1 1 1-1 1-4 1-5 1v1h2v1c0 1-1 2-2 3s-2 1-3 2h0c0-2-1-2-3-4l-2-1v-3h1l1-1c1 0 3-1 4-1v-1-1s-1 0-1-1v-2h0c0-1 1-2 1-2l1-2h0c-1-1-1-1-2-3 2 0 3 0 4 1 1 0 1 0 1-1z" class="F"></path><path d="M282 363c0-1 0-1 1-2h1v1 2h-1c-1 0-1 0-1-1z" class="W"></path><path d="M280 358c2 1 4 1 6 3h1l-2 1h-1v-1h-1c-1 1-1 1-1 2h-1v-1-1s-1 0-1-1v-2z" class="D"></path><path d="M277 369l-2-1v-3h1l7 6c-1 1-2 1-3 2h0c0-2-1-2-3-4z" class="J"></path><path d="M285 351c1 2 2 3 2 5l1 2c1 1 2 3 4 4v1h0c-1 0-2-1-3-1l-2-1h-1c-2-2-4-2-6-3h0c0-1 1-2 1-2l1-2h0c-1-1-1-1-2-3 2 0 3 0 4 1 1 0 1 0 1-1z" class="L"></path><path d="M284 356v-1c1 0 1 1 2 1v1h-2 0v-1z" class="M"></path><path d="M280 351c2 0 3 0 4 1 0 1 1 2 0 3v1l-2-2h0c-1-1-1-1-2-3zm-6 18c1 0 1 1 2 2 0 1-1 3-1 4 1 0 2-1 2-2v-4c2 2 3 2 3 4-1 2-4 5-4 7l1 1v1h1c1 0 1 0 2 1h0c-2 0-2 0-3-1v1 5l-2-2c-1 1-1 1-1 3h2v1l-3 2v4l-1-1v-1c-1-1-2-4-2-5l-3-3-1-2c1 0 2 1 3 0 1 0 3-1 4-2h0c-1-3-4-5-7-7 1-1 1-2 2-2v-2c2 0 2 0 3 1h0c1 1 1 2 2 3h0c0-2 0-4 1-6h0z" class="V"></path><path d="M268 373l3 3c0 1 2 2 2 3 1 1 0 3 0 5v-2h0c-1-3-4-5-7-7 1-1 1-2 2-2z" class="R"></path><path d="M267 386l-1-2c1 0 2 1 3 0 1 0 3-1 4-2v2 4c0 2-1 3 0 4 0 1-1 1-1 2h0c-1-1-2-4-2-5l-3-3z" class="a"></path><path d="M277 369c2 2 3 2 3 4-1 2-4 5-4 7l1 1v1h1c1 0 1 0 2 1h0c-2 0-2 0-3-1v1 5l-2-2 1-1c-1 0-1-1-1-2s0-1-1-1v-3l1-1v-1h-2c2 0 1-1 2-2 1 0 2-1 2-2v-4z" class="E"></path><path d="M480 151h1c1-3 1-5 1-8 1 1 1 5 1 5 1 2 4 2 4 4 3 8 3 18 4 27 1 4 3 9 3 13l-11 7v-1l-1 1-3-3c-2-3-2-5-2-9l1-6 1-2c1-4 0-8 0-13 0-1 0-2 1-3h0c-1-1-1-2-1-3 1-1 2-2 3-2-1-1-1-1-1-2-1-1-1-3 0-4l-1-1z" class="P"></path><path d="M486 154v-2c0 1 0 1 1 2v3c1 3 1 5 0 8v-2s-1 0-1-1c-1 0-1-1-2-1 1-1 2-2 2-3 1-1 0-3 0-4z" class="F"></path><path d="M481 152l1 2h1v-2h2s-1-1 1-1v1h0v2l-4 4c-1-1-1-1-1-2-1-1-1-3 0-4z" class="D"></path><path d="M486 154c0 1 1 3 0 4 0 1-1 2-2 3s-2 2-4 2h0c-1-1-1-2-1-3 1-1 2-2 3-2l4-4z" class="b"></path><path d="M483 194v-3h0c1-1 2-3 4-4 0 0 1 1 2 1 1 1 1 2 1 3l-1 1s-1-1-2-1c0 1-1 1-1 2-1 1 0 1-2 1h-1z" class="F"></path><path d="M480 163c2 0 3-1 4-2 1 0 1 1 2 1 0 1 1 1 1 1v2c-1 2-2 5-3 8h1v1 1l-1-1h0c0 1 0 1 1 1-1 0-1 1-1 1 0 1 0 2-1 2h2 0v1c0 1-1 2-1 3h1c1 0 2 0 3 1l1 1h-2c-2 1-3 1-5 3 0 1-1 3-1 4s1 2 2 3h1c2 0 1 0 2-1 0-1 1-1 1-2 1 0 2 1 2 1-1 3-4 4-6 6l-1 1-3-3c-2-3-2-5-2-9l1-6 1-2c1-4 0-8 0-13 0-1 0-2 1-3z" class="W"></path><path d="M478 181l1-2c0 2-1 3 0 4 1 0 1 0 2-1v1h0c-1 1-2 2-2 3-1 1-1 1-2 1l1-6z" class="H"></path><defs><linearGradient id="J" x1="306.341" y1="378.166" x2="286.209" y2="387.97" xlink:href="#B"><stop offset="0" stop-color="#171616"></stop><stop offset="1" stop-color="#3b3b3a"></stop></linearGradient></defs><path fill="url(#J)" d="M289 362c1 0 2 1 3 1h0l2 1v1 1h1l3 2 3 4c3 3 6 6 8 10 2 3 3 7 3 11 0 3-1 6-1 9v-2h-2s-1-1-1-2c-1 0-1-1-1-1l-2-1 1-1h1 0c0-1-1-2-1-3v-2l-3 1-3-3c-1 1-1 2-2 2-1 1-2 1-3 0-1 0-2-1-3-2v-1h-1l-2 1c-1 1-2 2-4 2h-1v-1h1 0v-2l-1-2c-1-1-2-1-3-1h-1v-1h0c-1-1-1-1-2-1h-1v-1l-1-1c0-2 3-5 4-7h0c1-1 2-1 3-2s2-2 2-3v-1h-2v-1c1 0 4 0 5-1 0 0 0-1-1-1 1-1 1-2 2-2z"></path><path d="M283 374h2l2 3 1 1h-3l-1 1h0-1l1-1 1-1v-2l-2-1z" class="B"></path><path d="M282 375l1-1 2 1v2l-1 1-1-1c-1 1-2 1-4 1l3-3z" class="H"></path><path d="M280 373h0v2h1c0-1 0 0 1 0l-3 3h0c-1 2-1 2-3 2 0-2 3-5 4-7z" class="R"></path><path d="M289 362c1 0 2 1 3 1h0l2 1v1 1h1c-2-1-4-1-7-1 0 0 0-1-1-1 1-1 1-2 2-2z" class="G"></path><path d="M307 388c1 4 3 8 2 12 0 0-1-1-1-2-1 0-1-1-1-1l-2-1 1-1h1 0c0-1-1-2-1-3v-2-1l1-1z" class="H"></path><path d="M288 376c-1-2-2-3-1-5 1-1 2-3 4-3s4 1 5 2 1 2 1 3c1 2 0 3-1 5 0 0 1 0 2 1h-3-1c-2 0-4 0-6-1l-1-1 1-1z" class="W"></path><path d="M288 376l2 2h6 0s1 0 2 1h-3-1c-2 0-4 0-6-1l-1-1 1-1z" class="T"></path><path d="M279 378c2 0 3 0 4-1l1 1-1 1h1 0l1-1h3c2 1 4 1 6 1h1 3c3 0 6 2 7 5l2 4-1 1v1l-3 1-3-3c-1 1-1 2-2 2-1 1-2 1-3 0-1 0-2-1-3-2v-1h-1l-2 1c-1 1-2 2-4 2h-1v-1h1 0v-2l-1-2c-1-1-2-1-3-1h-1v-1h0c-1-1-1-1-2-1h-1v-1l-1-1c2 0 2 0 3-2h0z" class="Y"></path><path d="M276 380c2 0 2 0 3-2 0 2 1 2 2 3-1 0-2 1-3 1l-1-1-1-1z" class="D"></path><path d="M291 387c-1-1-1-2-2-3h2c1 1 1 1 2 1l1 1-2 2v-1h-1z" class="B"></path><path d="M279 378c2 0 3 0 4-1l1 1-1 1-2 2c-1-1-2-1-2-3h0z" class="l"></path><path d="M285 389l2-1v-3h1c0 1 0 2 1 3h0c-1 1-2 2-4 2h-1v-1h1 0z" class="B"></path><path d="M300 388l-1-2h2c2 0 3 1 5 3v1l-3 1-3-3z" class="I"></path><path d="M284 379l1-1h3c2 1 4 1 6 1h1c2 0 4 1 6 2-1 1-1 2-1 3-1 0-2 0-2-1h-1-5c-2 1-2 1-3 0h-3c0-1-1-1-2-1l-1-1 1-2h0z" class="D"></path><path d="M284 379l1-1h3c2 1 4 1 6 1-1 1-3 0-4 1-1 0-2 0-2 1h-2l-1-2h-1z" class="F"></path><path d="M269 140c3 4 5 10 6 14h-1v1l1 3h1c0 1-1 2-1 3h-1v1l1 2c0 1-1 1-1 2v2c-1-1-2-1-3-2l-2-1c-2-1-3-2-4-2l-1 1-2 1h0l-2-3c-1-1-2-1-4-2h-1c-1-1-2-1-3-1s-2 0-3 1v1h-1c-1-1-2-1-3-1h-5 0-2l-1-1c-1 0-1-1-2-1h0v-5-5c1-2 6-3 8-4l2-1c7-1 14-1 20 2l3-1 1 1v-4-1z" class="W"></path><path d="M268 144l1 1c1 1 1 3 2 4 0 1 1 2 1 2l-1-1c-2-2-4-3-6-5l3-1z" class="L"></path><path d="M255 156l2 3v1h-2c-1-1-2-1-3-1l-1-2 1-1h0c1 1 2 1 3 1v-1z" class="G"></path><path d="M255 156v-3c0-1 1-2 2-3 3-1 5 0 7 1l2 1h-1v1c-1-1-1-2-3-2-1 0-3 2-4 3s-1 2-1 2c0 1 0 1 1 2l-1 1-2-3z" class="J"></path><path d="M235 153v-5c1-2 6-3 8-4-1 1-1 2-2 3v1c-2 1-4 2-4 4h-1l-1 1v5h0v-5z" class="H"></path><path d="M247 153c0-1 1-2 2-2 1 1 0 3 1 4 1 0 1 1 2 1l-1 1v-1l-2 2c-1 0-2 0-4-1l-2-1v-2l1-1h0 3z" class="S"></path><path d="M244 153h0 3c0 1 0 2-1 3-1 0-2-1-2-2h0v-1z" class="V"></path><path d="M243 154v2l2 1c2 1 3 1 4 1l2-2v1l1 2c-1 0-2 0-3 1v1h-1c-1-1-2-1-3-1h-5v-2c0-1 2-3 3-4z" class="j"></path><path d="M269 140c3 4 5 10 6 14h-1v1l1 3h1c0 1-1 2-1 3h-1v1l1 2c0 1-1 1-1 2v2c-1-1-2-1-3-2l-2-1c-2-1-3-2-4-2l-1 1-2 1h0l-2-3c-1-1-2-1-4-2h-1 2v-1l1-1c-1-1-1-1-1-2 0 0 0-1 1-2s3-3 4-3c2 0 2 1 3 2v-1h1l-2-1 2-1h0c1 1 2 1 3 1s1 0 2-1l1 1s-1-1-1-2c-1-1-1-3-2-4v-4-1z" class="h"></path><path d="M264 151l2-1h0c1 1 2 1 3 1 1 1 1 2 2 4-2-1-3-2-5-3l-2-1z" class="G"></path><path d="M271 150l1 1v1 2c1 1 1 2 1 3l-1 1-1-3c-1-2-1-3-2-4 1 0 1 0 2-1z" class="F"></path><path d="M263 161h0 2 0l-1-2c0-1 1-2 1-2 1-1 2-1 3 0 1 0 2 1 2 2 1 1 1 2 1 3v1h0c-1 1-2 1-2 2-2-1-3-2-4-2l-1 1-2 1h0l-2-3c2 0 2 0 3-1z" class="T"></path><path d="M268 157c1 0 2 1 2 2 1 1 1 2 1 3h0c-1 0-1 0-2-1-1 0-2-1-2-2l-1-1v-1h2z" class="G"></path><path d="M258 158c-1-1-1-1-1-2 0 0 0-1 1-2s3-3 4-3c2 0 2 1 3 2-1 2-2 4-2 6h-1l-1-1v1c1 1 2 1 2 2-1 1-1 1-3 1-1-1-2-1-4-2h-1 2v-1l1-1z" class="B"></path><path d="M258 158l1 1c1-1 0-3 2-4 1 1 1 1 1 2v2l-1-1v1c1 1 2 1 2 2-1 1-1 1-3 1-1-1-2-1-4-2h-1 2v-1l1-1z" class="H"></path><path d="M269 140c3 4 5 10 6 14h-1v1l1 3h1c0 1-1 2-1 3h-1v1l1 2c0 1-1 1-1 2v2c-1-1-2-1-3-2l-2-1c0-1 1-1 2-2l2 2v-1l-1-6 1-1c0-1 0-2-1-3v-2-1s-1-1-1-2c-1-1-1-3-2-4v-4-1z" class="B"></path><path d="M285 146l1-1c5-3 12-4 18-2 4 1 8 3 10 7 0 1 0 2 1 3 0-2-2-5-1-7h1c1 1 1 2 1 3l-1 11h-6c1 0 3 2 4 2l1-1h1v3h-1-1-1l-1-1c-3-2-7-3-11-2-1 1-2 1-4 1l-2 1c-2 1-4 3-6 3l2-2-2-1c-1-1-4-3-4-5h-1v2c0 1 1 3 0 4 0 0-3 2-3 3h-1 0c-2 0-3 2-5 2v-1-2c0-1 1-1 1-2l-1-2v-1h1c0-1 1-2 1-3h-1l-1-3v-1h1l1 1 1-1c0-1 1-1 1-2l1-1c2-3 4-4 6-5z" class="i"></path><path d="M304 158h1c1-2 1-2 1-4 2 1 3 2 4 4 0 1 0 1-1 2h-2v-1h-1-1l-1-1h0z" class="b"></path><path d="M298 159c1-1 2-2 3-2s1 1 2 2l1-1 1 1h1 1v1h-1 0l2 1c2 0 4 2 6 3h-1-1l-1-1c-3-2-7-3-11-2h-2v-2z" class="L"></path><path d="M285 146l-2 2c-2 1-3 3-4 5 0-1 1-1 2-2h1c1-1 2-1 3-2l2 1h1l1 2h-3v2s2 2 2 3v1h-4-1v2c0 1 1 3 0 4 0 0-3 2-3 3h-1 0c-2 0-3 2-5 2v-1-2c0-1 1-1 1-2l-1-2v-1h1c0-1 1-2 1-3h-1l-1-3v-1h1l1 1 1-1c0-1 1-1 1-2l1-1c2-3 4-4 6-5z" class="R"></path><path d="M282 151c1-1 2-1 3-2l2 1c-4 2-6 3-8 6v-2l3-3z" class="B"></path><path d="M283 156l-1-1 3-2 1 1s2 2 2 3v1h-4-1v-2z" class="L"></path><path d="M278 163l2-1-1-2s0-1 1-1c0-2 1-2 3-3v2 2c0 1 1 3 0 4 0 0-3 2-3 3h-1v-3l-1-1z" class="V"></path><path d="M285 146l-2 2c-2 1-3 3-4 5 0-1 1-1 2-2h1l-3 3v2c-1 2-2 4-2 6 0 1 1 1 1 1l1 1v3h0c-2 0-3 2-5 2v-1-2c0-1 1-1 1-2l-1-2v-1h1c0-1 1-2 1-3h-1l-1-3v-1h1l1 1 1-1c0-1 1-1 1-2l1-1c2-3 4-4 6-5z" class="F"></path><path d="M290 149l1-1c1-1 2 0 2 0h2c2 0 4 1 5 2 2 2 3 4 3 6 0 1 1 1 1 2h0l-1 1c-1-1-1-2-2-2s-2 1-3 2v2h2c-1 1-2 1-4 1l-2 1c-2 1-4 3-6 3l2-2-2-1c-1-1-4-3-4-5h4v-1c0-1-2-3-2-3v-2h3l-1-2 2-1z" class="E"></path><path d="M294 161h0 0l3-3h0c1 0 1 1 1 1v2h2c-1 1-2 1-4 1l-2 1h-1c0-1 1-1 1-2z" class="a"></path><path d="M290 149c2 1 4 1 5 3h0v1h0c0 2-1 3-2 5h0v-1c-1-2-1-3-2-4s-1-1-2-1l-1-2 2-1z" class="I"></path><path d="M288 157h1v2h1c1 0 2 0 3 1v1h1c0 1-1 1-1 2h1c-2 1-4 3-6 3l2-2-2-1c-1-1-4-3-4-5h4v-1z" class="D"></path><path d="M290 159c1 0 2 0 3 1v1h1c0 1-1 1-1 2h1c-2 1-4 3-6 3l2-2c1 0 1-1 2-1h0l-3-3 1-1z" class="J"></path><path d="M300 266h0c-1-1-3-2-3-3s1-1 1-2c1 0 2 1 3 0 2 0 3-1 5 0 1 1 1 1 1 2 0 0 0 1-1 1l1 1c1 1 2 1 3 3 0 1 2 2 3 3h0c0 1 0 2 1 4 0 3 1 7-1 10 1 4 1 11-1 15-2 3-4 4-6 5h0c-3 0-6-1-9-2h0c-1-1-1-1-2-1 0-1-1-2-2-3h0l1-1-3-3h0l-2-1c0-1-1-3-2-4v-1c0-1-1-2-2-3l1-1-1-2h1c2 1 4 1 6 2v-2h1c1-1 1-1 2-1h0c3-1 5-1 6-4l1-4-1-1v-3h-1l-2-1 1-1c1-1 0-1 1-2z" class="Y"></path><path d="M297 289c1 1 1 2 1 4h-1l-1 1c0 1 1 1 1 1 1 0 2 0 2 1h1l1 1c0 1-1 1-1 1v1l1 2c-1 0-3-1-4-3-1-1-2-4-2-6h0c1-2 1-2 2-3z" class="B"></path><path d="M303 273c1 0 1 1 2 1 0 2 0 2 1 3l1-1v-1c2 1 2 2 3 3s1 1 1 2c-1 1-3 0-4 2h0c-1 1-3 1-5 0h0-2c0 1 0 2-1 2-1 1-2 0-2 2-1 0-4-1-5-1v-2h1c1-1 1-1 2-1h0c3-1 5-1 6-4l1-4 1-1z" class="b"></path><path d="M302 274l1-1h0c0 2 1 3 0 4 0 1-1 1-2 1l1-4z" class="L"></path><path d="M295 282c2 1 3 0 5 0h7c-1 1-3 1-5 0h0-2c0 1 0 2-1 2-1 1-2 0-2 2-1 0-4-1-5-1v-2h1c1-1 1-1 2-1z" class="S"></path><path d="M300 266h0c-1-1-3-2-3-3s1-1 1-2c1 0 2 1 3 0 2 0 3-1 5 0 1 1 1 1 1 2 0 0 0 1-1 1l1 1v1c0 1 0 1-1 2 1 2 4 3 5 6 1 1 1 1 1 2l-2 2c-1-1-1-2-3-3v1l-1 1c-1-1-1-1-1-3-1 0-1-1-2-1l-1 1-1-1v-3h-1l-2-1 1-1c1-1 0-1 1-2z" class="L"></path><path d="M307 275l-1-1 1-1h1c1 0 1 0 2 1h0 1c1 1 1 1 1 2l-2 2c-1-1-1-2-3-3z" class="D"></path><path d="M300 270l3-3h1 1c1 1 1 1 0 3-1 1-1 2-2 3l-1 1-1-1v-3h-1z" class="B"></path><path d="M300 266v-2s0-1 1-1 2 2 3 2c0 1 1 2 1 2h-1-1l-3 3-2-1 1-1c1-1 0-1 1-2z" class="D"></path><path d="M301 301h0c1 1 3 1 4 0 2-1 4-4 5-7v-1c1-1 1-2 1-3 0-4-2-6-4-9 3 1 4 1 6 4 1 4 1 11-1 15-2 3-4 4-6 5h0c-3 0-6-1-9-2h0c-1-1-1-1-2-1 0-1-1-2-2-3h0l1-1-3-3h0l-2-1c0-1-1-3-2-4v-1c0-1-1-2-2-3l1-1-1-2h1c2 1 4 1 6 2 1 0 4 1 5 1l3 1c-1 1-2 1-3 2s-1 1-2 3h0c0 2 1 5 2 6 1 2 3 3 4 3z" class="C"></path><path d="M286 285c2 2 4 5 6 6h0c0 1 1 2 1 3h1v1h-2v-1c-1 0-1-1-2-1s0 0-1 1c0-1-1-3-2-4v-1c0-1-1-2-2-3l1-1z" class="S"></path><path d="M100 147h1c1-2 2-3 3-4 2-2 5-3 7-4 3-2 5-3 8-4l1 1c-2 1-5 3-5 5l-1 1c0 2 0 4 2 5l2 2v2h3l-1 1-1 1v2h-2s-2-1-3-1c2 1 4 2 4 3 1 1 1 2 2 2v5 1l-1 2h1 0c-1 1-2 1-2 2l-1 1h1v1c-1 1-2 1-3 2h-1c-2 1-3 1-4 0l-2-1h0c0 1 1 2 1 2-1 1-1 1-2 1v1c-2 0-2-2-4-1 1 0 1 1 1 2s-2 6-3 7v1l-1 1-2-1v-5c-1-4-1-8-1-13 0-1 0-5-1-6s0-2-1-3l1-1v-2s0-1-1-2l1-8v2-1l1-1v-1l2-2v1c0 2-2 5 0 8v-1-1-1l1-1z" class="i"></path><path d="M111 156c3 0 5 2 6 4 1 1 1 1 1 2l-1 1h0c0-1-1-2-2-3s-2-1-4-1l2-2c-1 0-1 0-2-1z" class="C"></path><path d="M97 167h0v2h1 0l1 1c-1 1-1 2-1 3s1 2 1 2l1 1c-1 1-2 1-2 2v2c-1-4-1-8-1-13z" class="E"></path><path d="M113 142h1c0 2 0 4 2 5l2 2v2c-3 0-6 0-8-1h-1c0 1 0 1-1 2v-2h-2v-1c2 0 4 0 6-1 1-2 1-4 1-6z" class="I"></path><path d="M117 163h0l1-1v3h0c-1 2-1 4-2 4s-1 0-1 1c-2-1-3-3-4-4l-4 4v1h-1c-1 0-1 1-2 1 0 2 1 2 2 3h1v1c-2 0-2-2-4-1 1 0 1 1 1 2s-2 6-3 7c1-4 1-11 4-14 1-2 5-4 7-4 1 0 1 0 2 1 1-2 2-3 3-4z" class="P"></path><path d="M118 165h2l-1 2h1 0c-1 1-2 1-2 2l-1 1h1v1c-1 1-2 1-3 2h-1c-2 1-3 1-4 0l-2-1h0c0 1 1 2 1 2-1 1-1 1-2 1h-1c-1-1-2-1-2-3 1 0 1-1 2-1h1v-1l4-4c1 1 2 3 4 4 0-1 0-1 1-1s1-2 2-4h0z" class="E"></path><path d="M103 155c2-1 3-2 4-2 2 1 3 1 4 2v1c1 1 1 1 2 1l-2 2h-1c-1 0-2 1-3 1-3 1-6 4-8 6 0-4 0-6 2-9l2-2z" class="c"></path><path d="M101 157l2-2v1h1c0 1 0 1-1 1h-2zm-1-10h1c1-2 2-3 3-4 2-2 5-3 7-4 3-2 5-3 8-4l1 1c-2 1-5 3-5 5l-1 1h-1c0 2 0 4-1 6-2 1-4 1-6 1v1c-1 1-3 1-4 1-1 1-2 3-3 4v-1c0-2 1-4 1-7z" class="C"></path><path d="M106 149c-1 0-2-1-3-2 1-1 1-1 1-2 1-1 7-2 9-3 0 2 0 4-1 6-2 1-4 1-6 1z" class="F"></path><path d="M287 455h2c0 1 1 1 2 1h0c1-1 2-1 3-1l2 2c0 2 1 3 2 5 1 1 3 1 4 3 0 1 0 2-1 4v1h2l-1 2 1 1c-1 1-2 1-3 2s-4 3-4 4c-1 0-1-1-1-1l-2 1c-1-1-2-1-3-1h-1c-1 2-2 3-4 4l-3 5c0 1-1 3-2 4l-3-2h0c-1-1-1-1-1-2 0 0-1 0-1-1l-2 2-3 3c-1 0-1-1-2-2h-1s-1 0-1-1c-1 0-1-1-2-1h0v-1c1-1 1-2 1-3h1l1-1-2-2h2l-1-1v-2c1-1 2-1 2-2h3 0 1 1 0c0-1-1-2-1-3-1 0-3-1-4-1-1-1-2-1-3-2 1-2 1-1 2-3 2 1 4 3 6 2v-1h0c1-1 1-1 2-1v-1c0-1 1-2 2-2l2-1c1-1 2-1 3 0h0l2-1c-1-1-2-1-2-3h2l3-3z" class="Y"></path><path d="M274 477c2-2 0-5 1-6v-1l1 1c0 1-1 4 1 5 0-2-1-4 0-5 0 2 0 8 1 9h0c-1 1-1 2-2 2l-2-2v-3z" class="D"></path><path d="M277 471h4v3c1 1 2 0 4 0l-2 1v2h-1-1s0 1-1 2c0-1 0-2-1-2s-1 2-1 3c-1-1-1-7-1-9z" class="C"></path><path d="M282 458h2 3c2 1 3 2 4 3v1c-2 1-2 2-4 4l-2 2c-1 0-1 0-2-1h0c2-2 4-3 5-5l-4-1c-1-1-2-1-2-3z" class="W"></path><path d="M282 458h2c1 1 1 2 3 2v1 1h1l-4-1c-1-1-2-1-2-3z" class="F"></path><path d="M284 461l4 1c-1 2-3 3-5 5-1 0-3 1-4 1l-2 1c-1 0-1 1-2 1v1c-1 1 1 4-1 6 0-1 0-3-1-4 0-1-1-1-1-1-1 0-3-1-4-1-1-1-2-1-3-2 1-2 1-1 2-3 2 1 4 3 6 2v-1h0c1-1 1-1 2-1v-1c0-1 1-2 2-2l2-1c1-1 2-1 3 0h0l2-1z" class="E"></path><path d="M284 461l4 1c-1 2-3 3-5 5-1 0-3 1-4 1h-2c0-1 4-4 5-6h0 0l2-1z" class="b"></path><path d="M272 472s1 0 1 1c1 1 1 3 1 4v3l2 2c1 0 1-1 2-2h0c0-1 0-3 1-3s1 1 1 2c1-1 1-2 1-2h1c1 1 1 2 2 3l1 1v1l-3 5c0 1-1 3-2 4l-3-2h0c-1-1-1-1-1-2 0 0-1 0-1-1l-2 2-3 3c-1 0-1-1-2-2h-1s-1 0-1-1c-1 0-1-1-2-1h0v-1c1-1 1-2 1-3h1l1-1-2-2h2l-1-1v-2c1-1 2-1 2-2h3 0 1 1 0c0-1-1-2-1-3z" class="W"></path><path d="M271 484h1c0 1-1 3-1 4h0l-1-1c0-1 0-2 1-3z" class="S"></path><path d="M267 482v3h1l-1 4s-1 0-1-1c-1 0-1-1-2-1h0v-1c1-1 1-2 1-3h1l1-1z" class="L"></path><path d="M272 472s1 0 1 1c1 1 1 3 1 4v3c-1-1-2-3-2-4h-1c0 1-1 2-1 2v1c0-2 0-2-1-3-1 1-1 2-2 3v1l-1-1v-2c1-1 2-1 2-2h3 0 1 1 0c0-1-1-2-1-3z" class="h"></path><path d="M277 489c1 0 1-1 2-1v-3l2 1v-1l1-1h1l-1-1c-1 1-1 1-2 1l-1-1c1 0 1 0 1-1 1 0 1 0 2-1h0 3 0v1l-3 5c0 1-1 3-2 4l-3-2z" class="E"></path><path d="M291 456h0c1-1 2-1 3-1l2 2c0 2 1 3 2 5 1 1 3 1 4 3 0 1 0 2-1 4v1h2l-1 2 1 1c-1 1-2 1-3 2s-4 3-4 4c-1 0-1-1-1-1l-2 1c-1-1-2-1-3-1h-1c-1 2-2 3-4 4v-1l-1-1c-1-1-1-2-2-3h1v-2l2-1c-2 0-3 1-4 0v-3c4-1 10-5 11-8 1-2 2-2 2-4l-3-3z" class="j"></path><path d="M301 469v1h2l-1 2 1 1c-1 1-2 1-3 2s-4 3-4 4c-1 0-1-1-1-1l-2 1c-1-1-2-1-3-1h-1c-1 2-2 3-4 4v-1l-1-1c-1-1-1-2-2-3h1v-2l2-1c0 1 2 0 3 0 3-1 7-1 11-3l2-2z" class="H"></path><path d="M290 476h2 0c0-1 0-1-1-1v-1h2 2 0l1 1-6 3v-2z" class="F"></path><path d="M301 469v1h2l-1 2c-2 1-3 3-6 3l-1-1c1 0 2-1 3-1 1-1 1-1 1-2l2-2z" class="D"></path><path d="M302 472l1 1c-1 1-2 1-3 2s-4 3-4 4c-1 0-1-1-1-1l-2 1c-1-1-2-1-3-1h0l6-3c3 0 4-2 6-3z" class="a"></path><path d="M283 477c1-1 2-1 3-1l1-1h2l1 1v2h0-1c-1 2-2 3-4 4v-1l-1-1c-1-1-1-2-2-3h1 0z" class="W"></path><path d="M283 477h0l2 2 4-1c-1 2-2 3-4 4v-1l-1-1c-1-1-1-2-2-3h1z" class="a"></path><path d="M291 387h1v1c1 1 2 2 3 2 1 1 2 1 3 0 1 0 1-1 2-2l3 3 3-1v2c0 1 1 2 1 3h0-1l-1 1 2 1s0 1 1 1c0 1 1 2 1 2h2v2c0 1 1 2 0 3 0 3-4 6-3 9 1 2 1 3 0 5v3 2h0l-2 2v1l-2 2s1 0 1 1v1c-1 0-1 0-2-1v1 1 1h0c-1 1-1 1-2 1v3h-2 0c-2-1-2-2-4-1l1-1c-1-1-1-1-2-1v-1-1c-1 0-1 1-2 0-1 0-1-1-2-1-1-2-4-2-6-2-3 0-6 3-8 5 2-6 4-13 10-17 3-2 5-2 8-1h-3v1h1 0 2c-1 0-3 0-4 1s-2 2-2 3c0 2 1 3 2 4s3 2 5 1c2 0 5-1 7-3 1-1 2-3 2-5-1-3-3-5-5-6h0c-1-1-2-1-3-2l-5-1c1 0 1-1 1-2l-2-2-3-3c-2 0-3-1-5-1h-1v1l-1-2h2v-2l1-3c1-2 2-3 2-5 2 0 3-1 4-2l2-1z" class="b"></path><path d="M284 429c4-1 8 0 11 2l2 3-1 1c-1-1-1-1-2-1v-1-1c-1 0-1 1-2 0-1 0-1-1-2-1-1-2-4-2-6-2z" class="G"></path><path d="M304 429s1 0 1 1v1c-1 0-1 0-2-1v1 1 1h0c-1 1-1 1-2 1v3h-2 0c0-2 0-4 1-6 2 0 3-2 4-2z" class="E"></path><path d="M291 395l1 1v2c0 1 0 2 1 3 1 2 4 2 6 2h-1v1h11c-2 3-3 5-4 8h-6 0c-1-1-2-1-3-2l-5-1c1 0 1-1 1-2l-2-2-3-3s0-1-1-1v-3-2h1c1 1 2 0 3 1l1-2z" class="H"></path><path d="M292 407c2 1 3 2 4 3l-5-1c1 0 1-1 1-2z" class="L"></path><path d="M291 395l1 1v2c0 1 0 2 1 3 1 2 4 2 6 2h-1v1c-4 0-6-3-9-4h-1v-1l2-2 1-2z" class="I"></path><path d="M291 387h1v1c1 1 2 2 3 2 1 1 2 1 3 0 1 0 1-1 2-2l3 3 3-1v2c0 1 1 2 1 3h0-1l-1 1 2 1c-1 1-1 1-3 1 0 0-1-1-2-1-1 2-1 4-3 6-2 0-5 0-6-2-1-1-1-2-1-3v-2l-1-1-1 2c-1-1-2 0-3-1h-1v2 3c1 0 1 1 1 1-2 0-3-1-5-1h-1v1l-1-2h2v-2l1-3c1-2 2-3 2-5 2 0 3-1 4-2l2-1z" class="a"></path><path d="M282 398c1 0 2-3 3-4 1 0 2 0 2-1v-2c2 1 3 1 5 2 0 1 0 1-1 2l-1 2c-1-1-2 0-3-1h-1v2 3c1 0 1 1 1 1-2 0-3-1-5-1h-1v1l-1-2h2v-2z" class="B"></path><path d="M303 391l3-1v2c0 1 1 2 1 3h0-1l-1 1 2 1c-1 1-1 1-3 1 0 0-1-1-2-1-1 2-1 4-3 6-2 0-5 0-6-2-1-1-1-2-1-3h0c1 0 1-1 1-2h0c1-1 2-3 3-3 2 0 3 0 3 1h1 0v-1c0-1 2-1 3-2z" class="W"></path><path d="M174 106h2 0l-1 1c1 1 3 0 4 1 7 1 15 2 22 5 2 1 5 2 7 3 3 3 6 6 10 8 0 0 1 0 1-1 1 3 4 7 7 9 2 1 4 1 6 1l1 2-23-1h-1l-1-1c-1 0-1-1-2-1h-1c0 1 0 2-1 2h-3v-1-1-4c-1 0 0-1 0-2h0c0-1 0-2-1-3 0-1-1-1-2-1l-2-1-2 1-2 2c-3-1-6-2-10-3h0-1c-2 0-2 0-4-1l-1-1v-1h-1v2l-3-1c-1 1-1 1-2 1h0-2c0 1 0 1-1 2h-1v-1l1-1c-1-1-2 0-3-1s-2-1-3-1c1-1 0-1 0-2 1 0 1-1 2-2h-4-3 2v-2c2-3 4-4 7-6h0 9z" class="X"></path><path d="M198 122c1 0 2-1 3 0l1 1h0 0-2c0-1-1-1-2-1z" class="b"></path><path d="M174 106h2 0l-1 1c1 1 3 0 4 1-2 0-5 0-7 1h0-1c1-3 2-2 3-3h0z" class="F"></path><path d="M202 121v-3c1-2 1-3 2-3 1 1 2 1 3 2v2c-1 0-1 0-2 1-1 0-2 1-3 1z" class="i"></path><path d="M207 119v1l1-1 3 3c1 1 1 2 1 4s-1 4-1 5c-1 2-1 2-2 3l-1-1c-1 0-1-1-2-1h-1c0 1 0 2-1 2h-3v-1-1-4c-1 0 0-1 0-2h0c0-1 0-2-1-3h2 0 0 0l1-1-1-1c1 0 2-1 3-1 1-1 1-1 2-1z" class="W"></path><path d="M203 122h0 3s1 1 1 2-1 2-1 2c-1 0-1 1-1 1-2-1-3-1-4-1 0-1 0-2-1-3h2 0 0 0l1-1z" class="C"></path><path d="M207 120l1-1 3 3c1 1 1 2 1 4s-1 4-1 5h0l-1-1h-2v-1h1 1c0-1 1-1 1-2h0c-2 0-2 0-3 1h-1 0l2-2c0-3 0-4-2-6z" class="E"></path><path d="M201 126c1 0 2 0 4 1 0 2 1 3 1 5h-1c0 1 0 2-1 2h-3v-1-1-4c-1 0 0-1 0-2h0z" class="C"></path><path d="M201 126c1 0 2 0 4 1 0 2 1 3 1 5h-1 0s-1 0-1-1c-2-1-3-3-3-5h0z" class="F"></path><path d="M164 111c3-1 5-1 7-1 3 0 6-1 9 0 3-2 8 1 11 2h3c1 1 4 1 6 2-1 0-1 1-2 1 0 1-1 2-1 3v1c-2 0-7-4-8-3v3l7 2-2 1-2 2c-3-1-6-2-10-3h0-1c-2 0-2 0-4-1l-1-1v-1h-1v2l-3-1c-1 1-1 1-2 1h0-2c0 1 0 1-1 2h-1v-1l1-1c-1-1-2 0-3-1s-2-1-3-1c1-1 0-1 0-2 1 0 1-1 2-2h-4-3 2v-2h1c2-1 3-1 5-1z" class="E"></path><path d="M170 120h0c0-1 1-1 1-2s-1-1-1-1c0-1 0-1 1-1h2v2s0 1-1 1h0c-1 1-1 1-2 1z" class="B"></path><path d="M163 114h2v2h0c2-1 3 0 4 0-1 2-3 1-5 3-1-1-2-1-3-1 1-1 0-1 0-2 1 0 1-1 2-2z" class="P"></path><path d="M183 117l2-2 1 2h2l1-1v3l7 2-2 1-2 2c-3-1-6-2-10-3h0-1c-2 0-2 0-4-1h1c-1-1-1-1-1-2l1-1h0 5z" class="F"></path><path d="M183 117l2-2 1 2h2l1-1v3l-6-2z" class="b"></path><path d="M164 111c3-1 5-1 7-1 3 0 6-1 9 0 3-2 8 1 11 2h3c1 1 4 1 6 2-1 0-1 1-2 1 0 1-1 2-1 3v1c-2 0-7-4-8-3l-1 1h-2l-1-2v-1c-1 0-1 0-1-2h-5l-2 2c-1-1-1-1-1-2h-2c-1 0-1 1-1 1h-1c-2-1-2 0-3 0v1c1 1 2 1 3 0v1c1 0 2-1 2-1h1l-1 1h-1c-1 1-3 0-4 0h-1s-1 0-2-1c1 0 1 0 2-1v-1h-1l-2 2h-2l1-3z" class="F"></path><path d="M191 112h3c1 1 4 1 6 2-1 0-1 1-2 1h-1 0-2-2c0-1-1-1-1-1h-2v-1h1v-1z" class="B"></path><path d="M191 112h3l-1 1h-2v-1z" class="E"></path><path d="M262 365c1 1 3 1 4 2h0l1 1v1h3l3 3-1 1-1-1h0c-1-1-1-1-3-1v2c-1 0-1 1-2 2 3 2 6 4 7 7h0c-1 1-3 2-4 2-1 1-2 0-3 0l1 2 3 3c0 1 1 4 2 5v1l1 1c-1 0-1 1-2 1s-1-1-2-2l-1 1v2 1h0l-2-2v-2c-1 0-1 1-2 1h-2s0 1-1 2c-1-1-2-3-3-5l-1 1-1-1v1h0 0-2c-1 0-1 0-2 1 0 1 0 1 1 2v2c-1 0-1 1-1 1-1 0-1 1-1 1v1 1c-1 1-3 1-4 1-2 1-3 1-4 2h-1-1c-1-1-2-3-2-4-1-2 0-4-1-6 0-3 0-6 1-9 1-6 7-14 12-17 3-3 7-4 11-5z" class="W"></path><path d="M251 386h0v1c-2 1-2 1-3 2-1 0-1 1-2 1h-1v-1c0-1 0-1 1-2s3-1 5-1z" class="I"></path><path d="M251 386c1 0 1 0 2 1v4c-1 0-1 0-2-1-1 1-1 2-2 2h0c0-1-1-2-1-3 1-1 1-1 3-2v-1h0z" class="D"></path><path d="M248 389c1-1 1-1 3-2 0 1-1 2 0 3-1 1-1 2-2 2h0c0-1-1-2-1-3z" class="j"></path><path d="M262 365c1 1 3 1 4 2h0l1 1v1h3l3 3-1 1-1-1h0c-1-1-1-1-3-1v2c-1 0-1 1-2 2h-1l-2 3c0-2 1-4 1-6l-3-3c-1-1-3-1-4-1h-1c-1 1-2 2-4 3v-1c1 0 1-1 2-1h0c-1 0-2 0-3 1h0c3-3 7-4 11-5z" class="I"></path><path d="M265 375c0-1 1-2 1-2 1-1 1-2 2-2v2c-1 0-1 1-2 2h-1z" class="J"></path><path d="M246 390c1 0 1-1 2-1 0 1 1 2 1 3h0c2 1 4 1 5 2h0c-1 0-1 0-2 1 0 1 0 1 1 2v2c-1 0-1 1-1 1-1 0-1 1-1 1v1c-2-1-3-2-5-3l-4 1c0-1 0-3 2-4 1-2 1-4 1-6h1z" class="F"></path><path d="M246 390c1 0 1-1 2-1 0 1 1 2 1 3-2 0-2-1-3-2z" class="e"></path><path d="M255 386l1-1h4c2-1 4 2 6 2l1-1 3 3c0 1 1 4 2 5v1l1 1c-1 0-1 1-2 1s-1-1-2-2l-1 1v2 1h0l-2-2v-2c-1 0-1 1-2 1h-2s0 1-1 2c-1-1-2-3-3-5l-1 1-1-1v1h0 0-2 0c-1-1-3-1-5-2 1 0 1-1 2-2 1 1 1 1 2 1s2 0 2-1v-4z" class="I"></path><path d="M255 386h0l1 1c1 0 2 0 3 1-1 1-2 2-3 2h-1v-4z" class="V"></path><path d="M258 393c1 0 3-1 4-1h2c1 1 1 2 2 3-1 0-1 1-2 1h-2s0 1-1 2c-1-1-2-3-3-5z" class="F"></path><path d="M255 386l1-1h4c2-1 4 2 6 2l1-1 3 3c0 1 1 4 2 5v1l1 1c-1 0-1 1-2 1s-1-1-2-2l-1 1v2-1c-1-1-1-3-2-4s-1-1-1-2-1-1-1-1c-3-3-5-4-9-4h0z" class="H"></path><path d="M251 370h0c1-1 2-1 3-1h0c-1 0-1 1-2 1v1c0 3 0 5 2 7l-1 1c-3 0-6 2-7 3-2 3-2 6-3 8s-3 4-3 6h0c1 2 0 4 1 5h1v-1l4-1c2 1 3 2 5 3v1c-1 1-3 1-4 1-2 1-3 1-4 2h-1-1c-1-1-2-3-2-4-1-2 0-4-1-6 0-3 0-6 1-9 1-6 7-14 12-17z" class="c"></path><path d="M376 111c2 0 9-2 11-1l-1 2c1 0 1 0 2-1v-1c2-1 3-1 5-1 0 1 0 2 1 3h0c1 0 1 1 2 0h1l1 1v1h-1-3c-1 0-1 0-1 1l1 1h2s0-1 1 0h0 4l1-1c1 1 2 1 2 2-1 1-1 1-3 1h-2v1c1 0 2 2 3 1l1-1c1 0 1 1 2 1v-1c1 2 0 2 2 4h3l-1 1 1 2v1h-2c0 1-1 1-2 2l-1 1v1h-1l1 2v3l-2 2-17-2-4-1c-3 0-8 1-12 0v-1c0-1-1-1-1-2h-2 0c-1 0-2-1-3-1v2h-1c-2-1-2-1-3-3h-1l-1 2c0 1 0 1-2 1v-1c-1-1-1-2 0-4v-2c2 0 3 1 4 1 2-1 2-2 3-4 2-1 6-2 8-3h0c-1-1-1-2-2-2v-1h-1l3-2 1 1c1 0 1 0 2-1 1 0 1-1 2-1v-3z" class="Y"></path><path d="M373 123c-1 1-1 2-2 3s-2 1-2 2l-2-1c2-2 3-3 6-4z" class="a"></path><path d="M401 129c1 1 2 1 3 2l1 2-1 1h-1-2l1-1c-1-2-2 0-3-2h0 2v-2z" class="E"></path><path d="M364 130c1-2 2-2 3-3l2 1v1c0 1-2 2-2 3h0c-1 0-2-1-3-1v-1z" class="S"></path><path d="M374 116l1 1 2-1c1-1 2-1 4-1v1c0 1 0 2-1 2-2 1-3 0-4 1v-1l-2-1v-1z" class="J"></path><path d="M360 130c1 0 1 0 2-1v-1c0-1 1-1 1-2h0l1 1h0v3h0v1 2h-1c-2-1-2-1-3-3z" class="l"></path><path d="M368 117l3-2 1 1c1 0 1 0 2-1v1 1l2 1v1c-2 0-3 1-5 1h0c-1-1-1-2-2-2v-1h-1z" class="c"></path><path d="M391 127l8-5c-1 2-1 4-2 6l-3 2c-2-1-3-2-3-3z" class="I"></path><path d="M401 129v-1l1-1c0-1 1-2 2-2 2 0 3 1 4 2 0 1-1 1-2 2l-1 1v1h-1c-1-1-2-1-3-2zm-19-5c2-1 4-1 6-1 2-1 3-2 4-1h1c-2 1-5 2-7 4 0 2-1 3-1 5-1 0-1-1-2-1l1-3h-1c0-1-1-1-1-1h-2c1 0 1-1 2-1v-1z" class="O"></path><path d="M399 119v1 1c-1-1-1-1-2-1v1h-1l-1-1c-1-1-1-1-2-1h-1v-1l1-1v-2l1 1h2s0-1 1 0h0 4l1-1c1 1 2 1 2 2-1 1-1 1-3 1h-2v1z" class="D"></path><path d="M385 131c0-2 1-3 1-5 0 1 0 2 1 2h2c0 1-1 3 0 4h1v1l-2 2h0l-2 1-4-1h0c1 0 1 0 1-1s0-1-1-2h0v-1l1-1c1 0 1 1 2 1z" class="j"></path><path d="M385 131c0-2 1-3 1-5 0 1 0 2 1 2h2c0 1-1 3 0 4h1c-1 0-1 2-3 2-1-1-1-1-2-3z" class="S"></path><path d="M389 128h0c1 0 1 0 2-1 0 1 1 2 3 3l3-2v3c1 1 1 3 2 4h1l1-1h2 1l1-1v3l-2 2-17-2 2-1h0l2-2v-1h-1c-1-1 0-3 0-4z" class="a"></path><path d="M405 133v3c-2 1-3 0-5-1l1-1h2 1l1-1z" class="B"></path><path d="M389 128h0c1 0 1 0 2-1 0 1 1 2 3 3 0 1 1 2 1 3s0 2-1 2c-1 1-1 1-2 1-1-1-2-1-3-1h-1l2-2v-1h-1c-1-1 0-3 0-4z" class="E"></path><path d="M373 123c2-1 4-1 5-1l1 1v2h1l2-1v1c-1 0-1 1-2 1h2s1 0 1 1h1l-1 3-1 1v1h0c1 1 1 1 1 2s0 1-1 1h0c-3 0-8 1-12 0v-1c0-1-1-1-1-2h-2c0-1 2-2 2-3v-1c0-1 1-1 2-2s1-2 2-3z" class="b"></path><path d="M380 131h2v1h0c1 1 1 1 1 2s0 1-1 1-2-1-4-1v-1l2-2z" class="G"></path><path d="M376 130h-1c-1-1-1-1-1-2 1-2 2-4 5-5v2h1c-1 2-3 2-3 4l-1 1z" class="H"></path><path d="M380 125l2-1v1c-1 0-1 1-2 1h2s1 0 1 1h1l-1 3-1 1h-2l-4-1 1-1c0-2 2-2 3-4z" class="B"></path><path d="M380 125l2-1v1c-1 0-1 1-2 1h2s1 0 1 1c-1 0-1 0-2 1s-1 1-2 1h-2c0-2 2-2 3-4z" class="L"></path><path d="M266 254h2c1 0 2 0 3 1 0 0 0 1 1 2v1c1 1 0 1 1 1 1-1 1-1 1-3l1 1 1 1v4h2v3c1 3 1 7 3 10 0 1 1 2 2 4 0 1 1 3 2 4l1 2-1 1c1 1 2 2 2 3v1c1 1 2 3 2 4l2 1h0l3 3-1 1h0c1 1 2 2 2 3 1 0 1 0 2 1h0c0 2 3 5 2 7v-1l-2 2-2-2-3-2c-1 1-1 2-2 3-2-1-3-1-4-3 0-1-1-1-1-2l-1-2h0v1c-2 1-3 0-4 1h-1 0c0 1 1 1 1 2l-1 1c0-1 0-2-2-4-1-1-1-2-1-4h-1l-1 3v-1-1l-1 1v2h-1l-1-1c1-2 1-4 2-6h-1c0 1 0 2-1 2 0 2-2 2-3 3l-1 1c0 1 0 3-1 4-1 0-2 1-3 1v1c-1 0-2 0-3 1l-2 2v1c-2 0-2 1-3 2l-1-2v-1c-1-1-2-1-2-2 1 0 1-1 2-1 0-1 1-2 1-3l1-1-1-4v-1c1-1 1-3 2-4l-1-1c1-1 2-2 2-3s0-1 1-1l1-2c1-2 3-2 4-4 1-1 2-3 3-4s0-1 1-1h-1l-1 1c-1 2-4 3-7 4v-2c0-1 1-1 1-1l1-1h0l-3-1c0-1-1-1-2-2 0-2 0-3 1-5l-1-1v-2c1 0 3-2 4-2l2-2c1 0 2 1 2 1h1c0-2-1-4-1-5s1-2 1-2l3-3v-1h-2v-1-1z" class="F"></path><path d="M264 287c6-4 9-10 11-17h0 1c0 1-1 4-2 5l-3 9-2 2h1c-1 0-1 0-1 1v1 1c-1 1-2 1-3 0h-1c-1 1-1 1-2 3 0 1 0 2-1 3l1 2-1 1h0v2l3 1v1l1 1h1c0 1 0 3-1 4-1 0-2 1-3 1v1c-1 0-2 0-3 1l-2 2v1c-2 0-2 1-3 2l-1-2v-1c-1-1-2-1-2-2 1 0 1-1 2-1 0-1 1-2 1-3l1-1-1-4v-1c1-1 1-3 2-4l-1-1c1-1 2-2 2-3s0-1 1-1l1-2c1-2 3-2 4-4v2h0z" class="J"></path><path d="M257 303c1-1 1-2 1-3l1-1h1l1 1h-1l-1 2 1 1c-1 1-1 1-2 1l-1-1z" class="Z"></path><path d="M262 295l1 2-1 1h0v2h-1l-1-1h-1c0-1 1-2 1-2l2-2h0z" class="G"></path><path d="M262 298v2h-1l-1-1v-1h2z" class="B"></path><path d="M269 286h1c-1 0-1 0-1 1v1 1c-1 1-2 1-3 0h-1c-1 1-1 1-2 3-1 0-2 1-2 2h-1c0-1 1-2 2-3 2-2 4-4 7-5z" class="E"></path><path d="M261 300h1l3 1v1l1 1h1c0 1 0 3-1 4-1 0-2 1-3 1v1l-1-1h-1l-1-2c0-1-1-1-2-1h-1l1-1c1 0 1 0 2-1l-1-1 1-2h1z" class="G"></path><path d="M260 306c1-1 2-1 3-2 1 1 0 1 0 2h0v2h-1-1l-1-2z" class="D"></path><path d="M261 300h1l3 1v1 1h-1c-1-1-1-1-2-1l-2 1-1-1 1-2h1z" class="R"></path><path d="M260 289c1-2 3-2 4-4v2h0c-2 3-5 6-6 9 0 1-1 2-1 3v4l1 1-1 1h1c1 0 2 0 2 1l1 2h1l1 1c-1 0-2 0-3 1l-2 2v1c-2 0-2 1-3 2l-1-2v-1c-1-1-2-1-2-2 1 0 1-1 2-1 0-1 1-2 1-3l1-1-1-4v-1c1-1 1-3 2-4l-1-1c1-1 2-2 2-3s0-1 1-1l1-2z" class="f"></path><path d="M257 310h1c1-1 0-2 0-3h1c1 1 1 1 1 3l-2 2s0-1-1-2z" class="H"></path><path d="M255 306l1-1c0 1 0 2 1 2 0 1-1 2 0 3h0c1 1 1 2 1 2v1c-2 0-2 1-3 2l-1-2v-1c-1-1-2-1-2-2 1 0 1-1 2-1 0-1 1-2 1-3z" class="Y"></path><path d="M255 306l1-1c0 1 0 2 1 2 0 1-1 2 0 3h-1v2h-1-1l2-2c-1-1-1-1-2-1 0-1 1-2 1-3z" class="S"></path><path d="M266 254h2c1 0 2 0 3 1 0 0 0 1 1 2v1c1 1 0 1 1 1 1-1 1-1 1-3l1 1 1 1v4c0 1-1 2-2 3-1 0-1 0-2 1 0 3-1 9-4 11h-1c1-1 2-2 2-3-1 0-6 1-8 2-1 0-2 0-3 2l1 1c1 0 4-1 5 0 0 0-1 0-1 1h-5c0-1-1-1-2-2 0-2 0-3 1-5l-1-1v-2c1 0 3-2 4-2l2-2c1 0 2 1 2 1h1c0-2-1-4-1-5s1-2 1-2l3-3v-1h-2v-1-1z" class="e"></path><path d="M265 273l4-2v2l-1 1c-2-1-2-1-3-1z" class="O"></path><path d="M256 270c1 0 3-2 4-2h1l-4 5-1-1v-2z" class="E"></path><path d="M260 268l2-2c1 0 2 1 2 1 0 2-3 4-5 6h0c0-1 1-3 2-4v-1h0-1z" class="D"></path><path d="M264 269c0 1 0 2-1 3h0 1l1-1h1 0c2-2 3-4 5-4-1 1-1 2-2 3v1l-4 2h-4 0c1-2 2-3 3-4z" class="F"></path><path d="M274 256l1 1 1 1v4c0 1-1 2-2 3-1 0-1 0-2 1 0-2 1-5 0-6v-1-1c1 1 0 1 1 1 1-1 1-1 1-3z" class="V"></path><path d="M266 255c1 0 2-1 3 0 0 0 1 1 1 2 1 1 1 2 1 4v5-3c-1-1-2 1-3 1-1-2-1-3-3-4l3-3v-1h-2v-1z" class="T"></path><path d="M270 257c1 1 1 2 1 4-1 0-1 1-2 0-1 0-1-2-1-2 1-2 1-2 2-2z" class="B"></path><path d="M265 260c2 1 2 2 3 4 1 0 2-2 3-1v3 1c-2 0-3 2-5 4h0-1l-1 1h-1 0c1-1 1-2 1-3l1-2c0-2-1-4-1-5s1-2 1-2z" class="W"></path><path d="M277 278c0-1-1-2 0-4h0c0 2 1 5 3 7v1c2 2 4 3 5 4s2 2 2 3v1c1 1 2 3 2 4l2 1h0l3 3-1 1h0c1 1 2 2 2 3 1 0 1 0 2 1h0c0 2 3 5 2 7v-1l-2 2-2-2-3-2c-1 1-1 2-2 3-2-1-3-1-4-3 0-1-1-1-1-2l-1-2h0v1c-2 1-3 0-4 1h-1 0c0 1 1 1 1 2l-1 1c0-1 0-2-2-4-1-1-1-2-1-4h-1l-1 3v-1-1l-1 1v2h-1l-1-1c1-2 1-4 2-6h-1c0 1 0 2-1 2 0 2-2 2-3 3l-1 1h-1l-1-1v-1l-3-1v-2h0l1-1-1-2c1-1 1-2 1-3 1-2 1-2 2-3h1c1 1 2 1 3 0v-1-1c0-1 0-1 1-1h-1l2-2h1 0l1-1v-2l1-1v-1c1 0 1 0 1-1l2 1h0v-1z" class="B"></path><path d="M276 285c1 0 1 0 2 1-1 1-1 2-1 3l-1-1c-1-1 0-2 0-3z" class="F"></path><path d="M276 285c0-1 0-2 1-3 0 1 1 1 2 2s1 1 1 2h-2c-1-1-1-1-2-1z" class="W"></path><path d="M270 286c2 0 2 1 3 2 1 2 1 3 1 5h-1 0c0-2-1-4-3-5h-1v-1c0-1 0-1 1-1z" class="R"></path><path d="M271 284h1 0l1-1v-2l1-1v-1c1 0 1 0 1-1l2 1c-1 1-2 3-2 4s-1 4-2 5c-1-1-1-2-3-2h-1l2-2z" class="W"></path><path d="M264 297v-2c1-1 2-3 4-3 1 0 1 0 2 1 1 0 2 1 2 2l1 2h-1-1l-5 1-2-1z" class="I"></path><path d="M270 293c1 0 2 1 2 2l1 2h-1-1l1-1h-1c-1-1-1-2-1-3z" class="R"></path><path d="M264 297l2 1 5-1h1c0 1 0 2-1 2 0 2-2 2-3 3l-1 1h-1l-1-1v-1l-3-1v-2h0l1-1h1z" class="E"></path><path d="M264 297l2 1v1h-3l-1-1 1-1h1zm1 4v-1h3v2l-1 1h-1l-1-1v-1z" class="F"></path><path d="M277 278c0-1-1-2 0-4h0c0 2 1 5 3 7v1c2 2 4 3 5 4s2 2 2 3v1c-1-1-2-3-4-4-1 0-1 0-2 1v2c-1 0-1 0-2 1 0 1 0 1 1 1v1l-2 2c-1 0-1 0-2 1h-1v-1c0-1 0-1 1-2 0-1 0-2 1-3 0-1 0-2 1-3h2c0-1 0-1-1-2l-2-6z" class="R"></path><path d="M276 292h0l2-3c1-1 1-1 2-1h0l1-1v2c-1 0-1 0-2 1 0 1 0 1 1 1v1l-2 2c-1 0-1 0-2 1h-1v-1c0-1 0-1 1-2z" class="V"></path><path d="M281 287c1-1 1-1 2-1 2 1 3 3 4 4s2 3 2 4l2 1h0l3 3-1 1h0c1 1 2 2 2 3 1 0 1 0 2 1h0c0 2 3 5 2 7v-1l-2 2-2-2-3-2c-1 1-1 2-2 3-2-1-3-1-4-3 0-1-1-1-1-2l-1-2h0v1c-2 1-3 0-4 1h-1c0-3-1-5-1-7v-4l2-2v-1c-1 0-1 0-1-1 1-1 1-1 2-1v-2z" class="F"></path><path d="M280 291c4 1 5 2 8 5-2 0-3 2-5 1h0-1v-1-1c-1-1-1-1-2-1v-2-1z" class="I"></path><path d="M278 294l2-2v2c1 0 1 0 2 1v1 1h1 0l-1 1 2 2-1 1c1 0 1 1 1 2h0v1c-2 1-3 0-4 1h-1c0-3-1-5-1-7v-4z" class="O"></path><path d="M282 297h1 0l-1 1 2 2-1 1c1 0 1 1 1 2h0c-2-1-4-1-5-3v-1c1-1 2-1 2-2h1zm9-2h0l3 3-1 1h0c1 1 2 2 2 3 1 0 1 0 2 1h0c0 2 3 5 2 7v-1l-2 2-2-2-3-2c-1 1-1 2-2 3-2-1-3-1-4-3 0-1-1-1-1-2l1-1c0-1 0-2 1-2h1c1-2 1-2 1-3v-2s0-1 1-1c0-1 0 0 1-1z" class="D"></path><path d="M293 299h0c1 1 2 2 2 3 1 0 1 0 2 1h0c0 2 3 5 2 7v-1c-1 0-2-1-3-2s-1-4-2-5c0-1-1-1-2-2l1-1z" class="O"></path><path d="M465 120l1 2h1l2 1h1c1 0 1-1 1-2l1 3v2c0 2 1 4 1 7h1c0 1 0 2 1 3v2c0 1 1 2 1 3v1c0 1 1 1 1 2v2 1l1 1c0 1-1 1-1 2v2l1 1c1-2 0-5 1-6h0v2 1l1 1h0l1 1c-1 1-1 3 0 4 0 1 0 1 1 2-1 0-2 1-3 2 0 1 0 2 1 3h0c-1 1-1 2-1 3 0 5 1 9 0 13l-1 2-1 6c0 4 0 6 2 9l3 3 1-1v1l-1 1c-1 2-2 2-3 3-2 0-4 3-5 3-1-1-1-2-1-3l-1-1c1-2 1-4 0-6 0-1 1-2 1-2l1-1v-2c0-1-1-2-2-3 0-1 0-2 1-3h-1c-3-1-4-3-5-5 0-1-1-1-1-2l1-1h0v-4l-1-1h0c-1 1-2 2-4 2-1 0-3-1-4-2 0-1-2-1-2-3v-3h0c-1-2-1-3 0-5 0-1 0-3 1-4s3-2 4-4c0 0 0-1 1-1-1 0-1 0-1-1-2-2-7 1-9 0-2 0-4-1-5-3-1-1-1-2 0-3v-1l1-1c1-1 2-2 4-3 1 0 1 0 2-1 0 0-1 0-1-1h0v-1l-2-1h0 3v-1l4 1v-2c1 0 0-1 1-2 1 0 2 0 2 1h1v-1c0-1-2-1-3-1-1-1-1-1-1-2h0l3-1h2l-3-3c1 0 2-1 3-1v-2l2-2z" class="F"></path><path d="M466 155l1-1c1-1 3 1 5 2 1 1 2 2 3 4-2-1-4-2-5-4-1-1-1-1-2-1h-2z" class="M"></path><path d="M473 203l1 1h1v-5c1 2 2 3 4 4-2 0-4 3-5 3-1-1-1-2-1-3z" class="P"></path><path d="M477 180h1v1l-1 6c0 4 0 6 2 9l3 3 1-1v1l-1 1c-2 0-4-1-5-2-3-4 0-13 0-18z" class="b"></path><path d="M479 149v1l1 1h0l1 1c-1 1-1 3 0 4 0 1 0 1 1 2-1 0-2 1-3 2 0 1 0 2 1 3h0c-1 1-1 2-1 3 0 5 1 9 0 13l-1 2v-1h-1v-6l1-11c1-3 1-5 1-8 0-1 1-3 0-4v-2z" class="M"></path><path d="M473 133h1c0 1 0 2 1 3v2c0 1 1 2 1 3v1c0 1 1 1 1 2v2 1l1 1c0 1-1 1-1 2v2l1 1c1-2 0-5 1-6h0v2 2c1 1 0 3 0 4 0 3 0 5-1 8v-4-3c-1 0-1-1-1-2h0c0-1 0-2-1-3v1h0l-1-1h0v1c-1-2-1-5-3-7h0l1-1c-1-2-1-3 0-4l-3-3v-1h1c0 1 1 1 2 2v1h1c1-1-1-4-1-6z" class="G"></path><path d="M466 155h2c1 0 1 0 2 1 1 2 3 3 5 4v6c-2-1-3-3-6-4-2-1-3-2-5-3-3 0-4 0-6 2h0 0 0c0-2 2-4 3-4 1-1 2-1 2-2h3z" class="c"></path><path d="M467 141c2 1 3 2 5 4h0c2 2 2 5 3 7 0 1 0 2 1 3l-1 1-2-3c-1-1-3-2-5-2-1 0-2 0-3 1-1 0 0 0-1-1h-1c-1 0-1 1-1 1h0c-1 0-1 0-1-1-2-2-7 1-9 0-2 0-4-1-5-3-1-1-1-2 0-3 0 2 1 3 3 5h6c2-1 3-3 5-4h0v-1-1l3-1 2 1 1-1v-2z" class="I"></path><path d="M467 141c2 1 3 2 5 4h0l-1 2v1l-2 2c-1 0-1 0-2-1l-1-1h0l-2 1c-1 0-2-1-3-1v-3-1l3-1 2 1 1-1v-2z" class="F"></path><path d="M467 141c2 1 3 2 5 4h0l-1 2v1c0-1 0-1-1-2 0-1-1-1-2-2h-2l1-1v-2z" class="R"></path><path d="M454 135l4 1 9 5v2l-1 1-2-1-3 1v1 1h0c-2 1-3 3-5 4h-6c-2-2-3-3-3-5v-1l1-1c1-1 2-2 4-3 1 0 1 0 2-1 0 0-1 0-1-1h0v-1l-2-1h0 3v-1z" class="E"></path><path d="M455 142c2-1 4 0 5 0v1s-1 1-1 0l-2 2v-1h0l-2-2z" class="F"></path><path d="M451 143h2l1 1h0v1c1 0 1 1 2 2-1 1-1 1-3 1-1 0-2-1-3-2 0-1 0-2 1-3z" class="a"></path><g class="h"><path d="M452 140c1 0 2 1 3 2h0l2 2-1 1-2-1h0l-1-1h-2-1-2 0c1-1 2-2 4-3z"></path><path d="M454 135l4 1 9 5v2l-1 1-2-1-3 1v1 1l-1-3v-1c-1 0-3-1-5 0h0c-1-1-2-2-3-2 1 0 1 0 2-1 0 0-1 0-1-1h0v-1l-2-1h0 3v-1z"></path></g><path d="M460 141c1 0 2 0 3 1 0 1 0 1 1 1l-3 1v1 1l-1-3v-1-1z" class="J"></path><path d="M453 137c2 0 4 1 6 3l1 1v1c-1 0-3-1-5 0h0c-1-1-2-2-3-2 1 0 1 0 2-1 0 0-1 0-1-1h0v-1z" class="B"></path><path d="M458 161h0 0c2-2 3-2 6-2v4h0c0 1 1 1 1 2v1 1c5 3 7 7 8 13v3h1c-1 1-1 1-1 2h-1c-3-1-4-3-5-5 0-1-1-1-1-2l1-1h0v-4l-1-1h0c-1 1-2 2-4 2-1 0-3-1-4-2 0-1-2-1-2-3v-3h0c-1-2-1-3 0-5h1 1z" class="i"></path><path d="M467 171l-2-3h-4c-1 0-2 1-2 2l-1-6v-1c1 1 1 2 2 2h1c1 1 2 1 4 2 5 3 7 7 8 13v3h1c-1 1-1 1-1 2h-1c-3-1-4-3-5-5 0-1-1-1-1-2l1-1h0v-4l-1-1h0l1-1z" class="M"></path><path d="M467 171c1 1 2 2 2 3 1 3 1 5 2 7 0-1-1-4-1-6l3 8h0 1c-1 1-1 1-1 2h-1c-3-1-4-3-5-5 0-1-1-1-1-2l1-1h0v-4l-1-1h0l1-1z" class="G"></path><path d="M465 120l1 2h1l2 1h1c1 0 1-1 1-2l1 3v2c0 2 1 4 1 7 0 2 2 5 1 6h-1v-1c-1-1-2-1-2-2h-1v1l3 3c-1 1-1 2 0 4l-1 1c-2-2-3-3-5-4l-9-5v-2c1 0 0-1 1-2 1 0 2 0 2 1h1v-1c0-1-2-1-3-1-1-1-1-1-1-2h0l3-1h2l-3-3c1 0 2-1 3-1v-2l2-2z" class="W"></path><path d="M465 120l1 2h1l2 1h1c1 0 1-1 1-2l1 3v2c-1 0-3 1-3 2l1 2c-2 0-3-1-4-2h-3l-3-3c1 0 2-1 3-1v-2l2-2z" class="S"></path><path d="M471 121l1 3v2c-1 0-3 1-3 2l1 2c-2 0-3-1-4-2s0-3 0-5h0c1 1 2 3 2 4h1v-1c0-1-1-2-2-3h0v-1l2 1h1c1 0 1-1 1-2z" class="I"></path><path d="M471 121l1 3-1 1h-1l-1-2h1c1 0 1-1 1-2z" class="B"></path><path d="M255 245v1h1l1-1 1-1c2 0 5-1 7-1 1 0 1-1 2-2 0 1 0 1 1 2h2c0 1-1 2-1 2 0 1 0 1 1 1-1 1-2 1-3 1s-1 0-1 1h-1l-2 2c-1 1-1 0-2 1h1c1-1 4-1 6-1 1 1 2 1 2 2v1c1 1 1 1 1 2-1-1-2-1-3-1h-2v1 1h2v1l-3 3s-1 1-1 2 1 3 1 5h-1s-1-1-2-1l-2 2c-1 0-3 2-4 2v2l1 1c-1 2-1 3-1 5 1 1 2 1 2 2l3 1h0l-1 1s-1 0-1 1v2c3-1 6-2 7-4l1-1h1c-1 0 0 0-1 1s-2 3-3 4c-1 2-3 2-4 4l-1 2c-1 0-1 0-1 1s-1 2-2 3l1 1c-1 1-1 3-2 4v1l1 4-1 1h-1l-1-2h0c-2 0-5 1-8 1 2 0 3 1 4 2l1 1h-1-1v2h0-1 0c-1 0-2 1-3 0h-1c-1 0-2-1-4-2-1-1-3-2-4-4 0 0 0-2-1-3l-1-8v-5-11-8-18h0c1 1 2 2 3 2l1-2c1-1 2-3 3-5 1-1 1-3 2-3h2 4l1-1 2 2v1 1h2l2-1z" class="Y"></path><path d="M269 245c0 1 0 1 1 1-1 1-2 1-3 1l-1-1c1-1 2-1 3-1z" class="B"></path><path d="M253 255c2-1 4-3 6-2 1 0 2 1 3 1v1l-1 1h0c0-1 0-1-1-1-1-1-2-1-3 0-1 0-2 1-3 1v2c-1-2 0-2-1-3z" class="h"></path><path d="M249 242l2 2v1 1c-2 1-4 3-6 5-2 4-5 7-8 11 1-1 1-3 2-4 2-4 5-7 6-11 1-1 1-2 1-3h-1c-6 7-9 13-11 22 0 3 0 7-1 10v1-8-18h0c1 1 2 2 3 2l1-2c1-1 2-3 3-5 1-1 1-3 2-3h2 4l1-1z" class="L"></path><path d="M262 251c1-1 4-1 6-1 1 1 2 1 2 2v1c1 1 1 1 1 2-1-1-2-1-3-1h-2v1 1h2v1l-3 3s-1 1-1 2 1 3 1 5h-1s-1-1-2-1l-2 2c-1 0-3 2-4 2s-1 0-1-1h-1c-1 0-1 0-2 1-1-1-1-2-2-2v-4h-1c-1 0-2 1-3 2s-1 1 0 2c-1 0-1 0-2 1h-1c0-1 0-1-1-2h0c-1-1 0-2 0-3 1-1 3-2 4-3h4v2h1c1-1 2-3 1-5 0-1-1-1-1-2l2-1c1 1 0 1 1 3 1 1 2 3 4 3 1 0 3 0 4-1 2-2 2-4 2-7l-2-2z" class="b"></path><path d="M266 254l-1-1 1-1h1c2 0 3 1 3 1 1 1 1 1 1 2-1-1-2-1-3-1h-2z" class="G"></path><path d="M244 269c1-1 1-1 2-1-1-1-1-1 0-2s2-2 3-2h1v4c1 0 1 1 2 2 1-1 1-1 2-1h1c0 1 0 1 1 1v2l1 1c-1 2-1 3-1 5 1 1 2 1 2 2l3 1h0l-1 1s-1 0-1 1v2c3-1 6-2 7-4l1-1h1c-1 0 0 0-1 1s-2 3-3 4c-1 2-3 2-4 4l-1 2c-1 0-1 0-1 1s-1 2-2 3l1 1c-1 1-1 3-2 4v1l1 4-1 1h-1l-1-2h0c-2 0-5 1-8 1l-2-1c-3-2-5-6-6-9s-1-9 1-11c1-2 3-2 4-2l-1-1h-2v-3c1 0 1-1 2-1v-3h0 0-2c1-1 1-3 2-4l2-1h1z" class="i"></path><path d="M256 272l1 1c-1 2-1 3-1 5-2-1-2-2-3-4 1-2 1 0 2-1l1-1z" class="F"></path><path d="M253 293c1 0 1 0 2-1l-1 3c0 2-1 4-3 5l-2 1c1-2 4-5 4-8z" class="B"></path><path d="M247 292h2s1 1 1 2v1c-1 1-1 1-1 2h0c-1 1-2 1-3 0v-2h1-1l1-1v-2zm3-5l2 1 3 3v1h0c-1 1-1 1-2 1 0-3-3-2-5-4h1c1-1 1-1 1-2z" class="E"></path><path d="M252 288h2l2 2c1 0 2-1 3-2v1h1l-1 2c-1 0-1 0-1 1s-1 2-2 3l1 1c-1 1-1 3-2 4v1l-1-2c-1 1-2 1-3 1 2-1 3-3 3-5l1-3h0v-1l-3-3z" class="e"></path><path d="M254 295l2 1c-1 1-2 1-2 3 0 0 1 0 1 1v1l-1-2c-1 1-2 1-3 1 2-1 3-3 3-5z" class="I"></path><path d="M259 285c3-1 6-2 7-4l1-1h1c-1 0 0 0-1 1s-2 3-3 4c-1 2-3 2-4 4h-1v-1c-1 1-2 2-3 2l-2-2h-2l-2-1-1-1c4-1 7-1 10-1z" class="C"></path><path d="M243 269h1l3 2c1 3 1 7 4 9l4 2h1c-4 0-10 0-13-2-1 1-1 1-2 1h-2v-3c1 0 1-1 2-1v-3h0 0-2c1-1 1-3 2-4l2-1z" class="X"></path><path d="M241 274c1-2 2-3 4-3h0v1c-1 2-3 4-4 5v-3h0 0z" class="E"></path><defs><linearGradient id="K" x1="239.28" y1="283.177" x2="249.853" y2="306.168" xlink:href="#B"><stop offset="0" stop-color="#010100"></stop><stop offset="1" stop-color="#2d2c2d"></stop></linearGradient></defs><path fill="url(#K)" d="M242 282v1c-1 2-3 3-2 6 0 3 1 9 3 11 2 1 3 2 5 2l1-1 2-1c1 0 2 0 3-1l1 2 1 4-1 1h-1l-1-2h0c-2 0-5 1-8 1l-2-1c-3-2-5-6-6-9s-1-9 1-11c1-2 3-2 4-2z"></path><path d="M272 297h1c-1 2-1 4-2 6l1 1h1v-2l1-1v1 1l1-3h1c0 2 0 3 1 4 2 2 2 3 2 4l1-1c0-1-1-1-1-2h0 1c1-1 2 0 4-1v-1h0l1 2c0 1 1 1 1 2 1 2 2 2 4 3h2l1 1h-2c0 1 1 1 1 2 0 2 0 5 1 7v2h-1v2h1v2c-1 1-1 2-2 2l1 2c-1 0-3-1-4 0v1c0 1 1 1 1 2h0c0 1 1 2 1 3h-2l-1-1-1 1-2 2c0 2-2 2-3 3-1 3-2 7-3 10l-1 3h1c0 1 0 1-1 2l-1 1h-1c-1 1-1 1 0 2l-1 1c0-1-1-1-1-2s-1-1-1-1l-1-1h0c-1 0-2 0-3 1h0c-1 0-2 1-3 0v2l-1 1c-1 1-3 2-5 2h-1l3-3c-1-1-2-2-2-4l-1-1 1-1v-1h1v-3h-3v-1h-1 0c-1 0-1 0-1-1h2v-1c-1 0-1-1-2-1h-2c-1-1-2-3-3-4v-2c-1 0-1-1-2-1 0-1-1-2-1-3v-2c0-2 1-2 2-3 1 0 1-1 2-1h1 0c1-1 1-1 2-1 1-1 1-1 1-3h-1c-1-1-1-1-1-2s-1-1-2-2h0c0-2 0-4 1-6 0-1 1-1 2-2l1 2c1-1 1-2 3-2v-1l2-2c1-1 2-1 3-1v-1c1 0 2-1 3-1 1-1 1-3 1-4l1-1c1-1 3-1 3-3 1 0 1-1 1-2z" class="F"></path><path d="M252 315c0-1 1-1 2-2l1 2c-2 0-2 1-3 0zm8 21v1c-1 1-1 2-1 3h-1v-3l2-1z" class="E"></path><path d="M251 332h0v2c0 1 1 2 2 3 0 1 0 2-1 3h0v-1c-1 0-1-1-1-1 0-1 0-2-1-3h0c0-1 0-2 1-3z" class="B"></path><path d="M265 329c1 0 3 0 4 1 0 1 0 1 1 1-1 1-1 2-2 2-1-1-3-2-3-4z" class="R"></path><path d="M258 315c1-2 2-3 4-5h0c0 1 1 2 2 3-1 1-2 0-3 0s-1 1-2 1c0 1-1 1-1 1z" class="G"></path><path d="M265 316c2 1 5 3 6 6v2c-1-1-1-2-1-2h-3v-2l-2-2v-2z" class="a"></path><path d="M266 307c0 1 0 1 1 2 1 0 2 0 3-1v1c0 1 0 1 1 2v1 1h0c-2-2-5-3-8-3v-2c1 0 2-1 3-1z" class="I"></path><path d="M263 309v-1 2h-1c-2 2-3 3-4 5 0 2 1 3 1 4l1 2c1 0 1 0 1 1h0-2v-1l-1 1h-2l-1 2h0v-2l2-1v-2h-2 1c0-2 1-4 2-6v-1l2-2c1-1 2-1 3-1z" class="J"></path><path d="M272 297h1c-1 2-1 4-2 6l-2 1c1 1 2 1 2 2 1 0 0 4 0 5-1-1-1-1-1-2v-1c-1 1-2 1-3 1-1-1-1-1-1-2 1-1 1-3 1-4l1-1c1-1 3-1 3-3 1 0 1-1 1-2z" class="k"></path><path d="M263 315h1l1 1v2l2 2v2c0 1-1 2-2 2-2 0-3-1-4-2h0c0-1 0-1-1-1l-1-2h1c1-2 2-3 3-4z" class="T"></path><path d="M263 315l1 1v1c0 1-1 1-1 2l-2 1-1-1c1-2 2-3 3-4z" class="J"></path><path d="M261 322c1 0 2 1 3 0v-2c1 0 2 1 2 2h1v-2 2c0 1-1 2-2 2-2 0-3-1-4-2h0z" class="U"></path><path d="M258 322l1-1v1h2c1 1 2 2 4 2 1 0 2-1 2-2h3s0 1 1 2c-2 3-5 3-8 4l-7 2 3-3c0-2-1-4-1-5z" class="O"></path><path d="M255 345h1l-1-4v-4 3c0 1 1 2 1 3h0c1 0 1 0 2-1h3l1 1v-1-1c-1 0-2 0-3-1h0c1-1 2-2 4-2h0v4h0c1 1 1 2 1 2 1 4 1 9-1 13l-2 2c-1-1-2-2-2-4l-1-1 1-1v-1h1v-3h-3v-1h-1 0c-1 0-1 0-1-1h2v-1c-1 0-1-1-2-1z" class="V"></path><path d="M262 350l1 1c0 2-1 3-2 5h0c-1 0-1-1-2-1 1-2 2-3 3-5z" class="B"></path><path d="M255 345h1l-1-4v-4 3c0 1 1 2 1 3h0l2 1s-1 1-1 2v1h1l2-2 1-2h0c2 3 1 5 1 7-1 2-2 3-3 5h0l-1-1 1-1v-1h1v-3h-3v-1h-1 0c-1 0-1 0-1-1h2v-1c-1 0-1-1-2-1z" class="J"></path><path d="M274 303l1-3h1c0 2 0 3 1 4s1 2 1 2c-1 3-1 5-1 7l1 1c0 2 1 5 0 7s-1 5 0 7h1v2h0c-1 0-1 0-2-1v1 2l-2 2-1-1v-2-3-1l-1 1v2h-1-1-1v-1l3-6v-1l-1-1v-3c1-1 0-2 1-3v-2c0-4 0-8 1-10z" class="l"></path><path d="M260 336c-2-2-2-2-3-2v-1h3l1-2c1 0 1 0 1-1 1-1 1-1 3-1 0 2 2 3 3 4 1 0 1-1 2-2 0 1 2 2 2 4 0 0 0 1 1 1 1-1 1-1 1-3h0l1 1 2-2 2 3-1 1-1 2v3c1 0 2-4 3-5l1 1v1s0 1 1 1l-1 2c-1 3-2 7-3 10l-1 3h1c0 1 0 1-1 2l-1 1h-1c-1 1-1 1 0 2l-1 1c0-1-1-1-1-2s-1-1-1-1l-1-1h0c-1 0-2 0-3 1h0c-1 0-2 1-3 0v2l-1 1c-1 1-3 2-5 2h-1l3-3 2-2c2-4 2-9 1-13 0 0 0-1-1-2h0v-4h0c-2 0-3 1-4 2h0c0-1 0-2 1-3v-1z" class="B"></path><path d="M276 345h1v-1h0v-2h0c1 0 1-1 2 0v2c-1 0-1 1-1 2l-1 1s-1 0-1-1v-1z" class="i"></path><path d="M270 331c0 1 2 2 2 4 0 0 0 1 1 1 1-1 1-1 1-3h0l1 1 2-2 2 3-1 1-1 2h-2v3c0 1 0 3 1 4v1c0 3-1 6 0 9v1h-1c-1-1-1-2-1-4h0l-3-6h0l1-1v-1h0c1-1 1-1 1-2l1 1c-1-4-3-7-6-10 1 0 1-1 2-2z" class="J"></path><path d="M271 346l1-1v-1h0c1-1 1-1 1-2l1 1c1 3 1 6 0 9l-3-6h0z" class="H"></path><path d="M277 332l2 3-1 1-1 2h-2v3l-2-5c1-1 1-1 1-3h0l1 1 2-2z" class="G"></path><path d="M275 338v-2c1-1 2-1 3 0l-1 2h-2z" class="Y"></path><path d="M268 340l3 6h0l3 6h0s-1-1-3-1c-1-1-2 0-3 0v3c1 1 2 1 3 2-1 0-2 0-3 1h0c-1 0-2 1-3 0v2l-1 1c-1 1-3 2-5 2h-1l3-3 2-2c2-4 2-9 1-13 1 1 2 1 3 2h1 1c-1-1 0-1-1-3 0 0-1-1-1-2s1-1 1-1z" class="O"></path><path d="M271 346l3 6h0s-1-1-3-1c-1-1-2 0-3 0v3c1 1 2 1 3 2-1 0-2 0-3 1-1-1-2-1-2-2 1-1 1-1 1-2 1-1 0-2 1-4 1 1 1 1 2 1 0-1 1-2 1-4z" class="Z"></path><path d="M260 336c-2-2-2-2-3-2v-1h3l1-2c1 0 1 0 1-1 1-1 1-1 3-1 0 2 2 3 3 4 3 3 5 6 6 10l-1-1c0 1 0 1-1 2h0v1l-1 1-3-6s-1 0-1 1 1 2 1 2c1 2 0 2 1 3h-1-1c-1-1-2-1-3-2 0 0 0-1-1-2h0v-4h0c-2 0-3 1-4 2h0c0-1 0-2 1-3v-1z" class="B"></path><path d="M260 337l4-5c2 3 3 5 4 8 0 0-1 0-1 1s1 2 1 2c1 2 0 2 1 3h-1-1c-1-1-2-1-3-2 0 0 0-1-1-2h0v-4h0c-2 0-3 1-4 2h0c0-1 0-2 1-3z" class="C"></path><path d="M284 303l1 2c0 1 1 1 1 2 1 2 2 2 4 3h2l1 1h-2c0 1 1 1 1 2 0 2 0 5 1 7v2h-1v2h1v2c-1 1-1 2-2 2l1 2c-1 0-3-1-4 0v1c0 1 1 1 1 2h0c0 1 1 2 1 3h-2l-1-1-1 1-2 2c0 2-2 2-3 3l1-2c-1 0-1-1-1-1v-1l-1-1c-1 1-2 5-3 5v-3l1-2 1-1-2-3v-2-1c1 1 1 1 2 1h0v-2h-1c-1-2-1-5 0-7s0-5 0-7l-1-1c0-2 0-4 1-7 0 0 0-1-1-2 2 2 2 3 2 4l1-1c0-1-1-1-1-2h0 1c1-1 2 0 4-1v-1h0z" class="F"></path><path d="M285 328c-2 0-3 0-4-1s-2-3-2-4c0-2 1-4 2-5l1 1v3 1 3l3 2z" class="e"></path><path d="M279 335c0-2 1-4 2-5h4v1c-2 2-4 3-5 5-1 1-2 5-3 5v-3l1-2 1-1z" class="J"></path><path d="M280 336c1-2 3-3 5-5v2h1 3c0 1 1 2 1 3h-2l-1-1-1 1-2 2c0 2-2 2-3 3l1-2c-1 0-1-1-1-1v-1l-1-1z" class="I"></path><path d="M280 336c1-2 3-3 5-5v2h1c-2 2-3 4-4 6-1 0-1-1-1-1v-1l-1-1z" class="V"></path><path d="M281 318l1-1s1-1 2-1c2-1 4 0 6 1-1 0-1 1-1 1 0 2 1 4 0 5l-1 1h-2c-1-1-2-1-4-1v-1-3l-1-1z" class="J"></path><path d="M287 321c0-1-1-2-1-3h3c0 2 1 4 0 5-1 0-2-1-2-2z" class="G"></path><path d="M282 319l2-2h0c1 1 1 2 0 4h3c0 1 1 2 2 2l-1 1h-2c-1-1-2-1-4-1v-1-3z" class="S"></path><path d="M290 317c1 1 2 3 2 5v2h1v2c-1 1-1 2-2 2l-1 1c-1 0-2-1-3-1h-2l-3-2v-3c2 0 3 0 4 1h2l1-1c1-1 0-3 0-5 0 0 0-1 1-1z" class="O"></path><path d="M288 324c0 1 1 1 0 2h-1v-1l-1-1h2z" class="L"></path><path d="M284 303l1 2c0 1 1 1 1 2 1 2 2 2 4 3h2l1 1h-2c0 1 1 1 1 2l-1 1c-1 0-2 0-3-1h0l-2 2h0-1l-1-1 1-1h0-1-1c-1 1-2 3-4 4v-2c1 0 1-2 1-2v-1c0-1 0-2-1-4l1-1c0-1-1-1-1-2h0 1c1-1 2 0 4-1v-1h0z" class="W"></path><path d="M286 307c1 2 2 2 4 3h2l1 1h-2-1c-2 0-3-1-5-1 0-1 1-2 1-3z" class="R"></path><path d="M284 303l1 2c0 1 1 1 1 2s-1 2-1 3h-1c-2 0-3 1-4 2 0-1 0-2-1-4l1-1c0-1-1-1-1-2h0 1c1-1 2 0 4-1v-1h0z" class="h"></path><path d="M280 307c0-1-1-1-1-2h0 1c1-1 2 0 4-1 0 1-1 3-1 4h-1c-1 0-2 0-2-1h0z" class="S"></path><path d="M249 481l2 1 1 1h0 1c1 0 1 1 2 2 3-1 4-3 7-1 1 0 1 1 1 2h1v1h0c1 0 1 1 2 1 0 1 1 1 1 1h1c1 1 1 2 2 2l3-3 2-2c0 1 1 1 1 1 0 1 0 1 1 2h0l3 2c1 0 1 1 1 1-1 2-1 0-2 2 0 2-1 4-2 6h0c-1 3-1 9-1 13 0 2 0 5-1 7h-1c-1 1 0 4 0 5v3l-1-1c0-1-1-1-2-2 0-1-1-1-1-1-2-1-2-1-3 0l-1-1c-5 6-13 12-21 13h0 0c-1-1-3-1-4-1-2-1-5-2-6-4-1-1-1-2-2-3 1-1 1-1 2-1-1-4-2-7-2-11v-4c-1-2-1-3-1-5v-2c0-1 1-10 1-12v-7c1 1 2 3 3 3v1c1-1 2 0 3-1v-1l2 2h0 2c1-1 2-1 3-2h0v-1h0c0-1 1-1 1-2v-3h1l1-1z" class="B"></path><path d="M265 517h2v2h-1c-1 0-1-1-1-2h0z" class="W"></path><path d="M268 512c1 1 0 2 1 3l2 1-1 2-2-2-1 1h-2c1-2 2-3 3-5z" class="F"></path><path d="M249 519c2 1 4 1 6 1l1 1c1 0 1 0 2 1v1h-1 0-1-1l-2-2-2 3v-1c-1-1-2-1-3-2v-1l1-1z" class="M"></path><path d="M232 507l1 1h2c1 1 0 2 2 2v-1l2 1 1 1h-1c-2 0-2 1-3 2l-1 1 1 1c-1 0-1-1-2 0v2c2 1 2 1 3 2h1v1c-1 0-1 1-2 2l-1 1c0 2 1 4 3 5 1 1 2 1 3 2 3 1 10 0 13-1 1-1 3-1 3-2l2-1c0 1-2 2-2 2-5 3-10 5-15 3-3 0-6-2-7-4-1-4-2-7-2-11v-4c-1-2-1-3-1-5z" class="I"></path><path d="M234 517c2 1 2 1 3 2h1v1c-1 0-1 1-2 2l-1 1-1-6z" class="i"></path><path d="M233 508h2c1 1 0 2 2 2v-1l2 1h-1c-1 0-3 1-4 2v1c-1-2-1-3-1-5z" class="D"></path><path d="M235 523l1-1c1 2 3 4 5 5h2 5l3-1v-2l2-3 2 2c0 1 0 1-1 2 0 1 0 1 1 1h1 1v1c0 1-2 1-3 2-3 1-10 2-13 1-1-1-2-1-3-2-2-1-3-3-3-5z" class="F"></path><path d="M258 505c3 0 5 2 7 3v7h0c1-1 2-3 3-4v1c-1 2-2 3-3 5h0c-1 3-3 4-5 6l-1-1v-1l2-1v-1l-1-1c1-1 1-1 1-2-1 0-2 1-3 2h-1c1-3 4-7 3-9s-1-2-3-3c-1 1-1 1-2 1h0-1c0-1 0-2 1-2s1 0 1 1l2-1z" class="b"></path><path d="M238 520l1-3s1-1 1-2h0 2v2h0 3l1 1h1c1 1 1 1 2 1l-1 1v1c1 1 2 1 3 2v1 2l-3 1h-5-2c-2-1-4-3-5-5 1-1 1-2 2-2z" class="W"></path><path d="M247 518c1 1 1 1 2 1l-1 1v1l1 1-1 1c-1 1-1 1-2 0h-1c-1-1-1-2-1-3h2c1-1 1-1 1-2z" class="I"></path><path d="M246 523h0c0-1 0-1 1-2l2 1-1 1c-1 1-1 1-2 0z" class="O"></path><path d="M242 517h3l1 1h1c0 1 0 1-1 2h-2-2l-2-1v-1h1 1v-1z" class="X"></path><path d="M238 520l1-3s1-1 1-2h0 2v2h0v1h-1-1v1l2 1c-2 0-3 1-3 3 0 1 0 2 1 3 1 0 2 0 3 1h-2c-2-1-4-3-5-5 1-1 1-2 2-2z" class="P"></path><path d="M273 488l2-2c0 1 1 1 1 1 0 1 0 1 1 2h0l3 2c1 0 1 1 1 1-1 2-1 0-2 2 0 2-1 4-2 6h0c-1 3-1 9-1 13 0 2 0 5-1 7h-1c-1 1 0 4 0 5v3l-1-1c0-1-1-1-2-2 0-1-1-1-1-1-2-1-2-1-3 0l-1-1 2-3 2-2 1-2-2-1c-1-1 0-2-1-3v-1c-1 1-2 3-3 4h0v-7c-2-1-4-3-7-3 2-1 5-1 7-2v-3-1-1c1-1 1-1 2-1-1-1-1-3-1-4h2 0c-1-1-1-2-1-2l1-2c1 1 1 2 2 2l3-3z" class="E"></path><path d="M268 511h1s1 0 1 1v2l-1 1c-1-1 0-2-1-3v-1z" class="D"></path><path d="M268 503c1 1 1 2 1 4h0-1v1h-1s-1 0-1 1l-1-1v-2h1l1-1 1-2z" class="i"></path><path d="M265 499c1 1 3 2 3 3v1l-1 2-1 1h-1v2c-2-1-4-3-7-3 2-1 5-1 7-2v-3-1z" class="I"></path><path d="M265 506h0c0-2 0-2 1-4h1v3h0l-1 1h-1z" class="H"></path><path d="M272 511v1c1 0 1 0 1 1 0 0 0 1 1 1h0c1-1 1-2 1-3v8c1 0 1 0 0 1h-1c-1 1 0 4 0 5v3l-1-1c0-1-1-1-2-2 0-1-1-1-1-1-2-1-2-1-3 0l-1-1 2-3 2-2 1-2c0-1 0-3 1-5z" class="D"></path><path d="M271 516c0-1 0-3 1-5l1 8c-1 0-2 0-3 1h-2l2-2 1-2z" class="I"></path><path d="M268 520h2c1-1 2-1 3-1l1 6v3l-1-1c0-1-1-1-2-2 0-1-1-1-1-1-2-1-2-1-3 0l-1-1 2-3z" class="O"></path><path d="M273 488l2-2c0 1 1 1 1 1 0 1 0 1 1 2h0l3 2c1 0 1 1 1 1-1 2-1 0-2 2 0 2-1 4-2 6h0c-1 3-1 9-1 13 0 2 0 5-1 7 1-1 1-1 0-1v-8c0-2-1-4-1-7-1-1-1-3-1-5-1-2-3-4-5-6h0c-1-1-1-2-1-2l1-2c1 1 1 2 2 2l3-3z" class="B"></path><path d="M274 495l1 7c0 1 0 2-1 2-1-1-1-3-1-5h1v-4z" class="R"></path><path d="M276 493c2 1 0 5 1 7h0c-1 3-1 9-1 13 0 2 0 5-1 7 1-1 1-1 0-1v-8c0-2-1-4-1-7 1 0 1-1 1-2v2h1v-11z" class="J"></path><path d="M277 489h0l3 2c1 0 1 1 1 1-1 2-1 0-2 2 0 2-1 4-2 6-1-2 1-6-1-7 0-2 0-3 1-4z" class="a"></path><path d="M273 488c1 1 1 3 1 4v3 4h-1c-1-2-3-4-5-6h0c-1-1-1-2-1-2l1-2c1 1 1 2 2 2l3-3z" class="L"></path><path d="M268 493h2v1l1 1c0-1 1-1 1-1v-1l2-1v3 4h-1c-1-2-3-4-5-6h0z" class="e"></path><path d="M249 481l2 1 1 1h0 1c1 0 1 1 2 2 3-1 4-3 7-1 1 0 1 1 1 2h1v1h0c1 0 1 1 2 1 0 1 1 1 1 1h1l-1 2s0 1 1 2h0-2c0 1 0 3 1 4-1 0-1 0-2 1v1 1 3c-2 1-5 1-7 2l-2 1c0-1 0-1-1-1s-1 1-1 2h1 0c1 0 1 0 2-1 2 1 2 1 3 3s-2 6-3 9v1h1l-1 1-1 1-1-1c-2 0-4 0-6-1-1 0-1 0-2-1h-1l-1-1h-3 0v-2h-2 0c0 1-1 2-1 2l-1 3v-1h-1c-1-1-1-1-3-2v-2c1-1 1 0 2 0l-1-1 1-1c1-1 1-2 3-2h1l-1-1-2-1v1c-2 0-1-1-2-2h-2l-1-1v-2c0-1 1-10 1-12v-7c1 1 2 3 3 3v1c1-1 2 0 3-1v-1l2 2h0 2c1-1 2-1 3-2h0v-1h0c0-1 1-1 1-2v-3h1l1-1z" class="W"></path><path d="M251 492c1-1 1-1 2-1v3c-1 1-2 2-2 4-1-1-1 0-2 0l-1-1c1-1 2-1 3-2 0-1 0-2 1-3h-1z" class="H"></path><path d="M245 502c1 0 2-1 2-2-1 0-1 0-2-1h0-3v-2h1c1 0 2 1 2 1 0 1 1 0 1 0l3 3v1h1 0v-2h1v1 1l-1 1-2-1c-1 1-2 0-3 0z" class="F"></path><path d="M246 490c2-1 3-1 5-1 0 1-1 2-1 3s0 1-1 1h0 0c-1 0-2 1-2 2h0-1v-4-1z" class="Y"></path><path d="M247 485h1 0c2 1 3 1 4 2 0 0 0 1-1 2-2 0-3 0-5 1s-2 1-4 1c-1 0-2 1-3 1 1-1 1-2 2-2h0 2c1-1 2-1 3-2h0v-1h0c0-1 1-1 1-2z" class="e"></path><path d="M232 505c0-1 1-10 1-12v-7c1 1 2 3 3 3v1c1-1 2 0 3-1v-1l2 2c-1 0-1 1-2 2l-3 3c2 5 4 10 9 13v1c-3-1-4-2-6-4-1-1-2-4-3-6h0c0 1-1 3-1 4l1 1c-1 0-1 1-2 1h0v-1c1-2 1-3 1-5v-2h-1c0 2-1 5-1 8h0-1z" class="R"></path><path d="M239 488l2 2c-1 0-1 1-2 2h-1c-1 0-2 1-2 1l-1 1h-1c1-1 1-1 1-2v-1h2 0l-1-1c1-1 2 0 3-1v-1z" class="H"></path><path d="M264 498l1 1v1 3c-2 1-5 1-7 2l-2 1c0-1 0-1-1-1s-1 1-1 2h0c-1-1-3-1-5-2l-1 2h-1l-1-1c0-1-1-2-2-2v-1l1-1c1 0 2 1 3 0l2 1 1-1v-1-1l3 2s0-1 1-1l1 1h0c1 0 2 1 3 0s1-1 1-3h3l1-1z" class="E"></path><path d="M255 493h0c1 0 2-1 4-1l2 1h0c1 2 4 3 4 5v1l-1-1-1 1h-3c0 2 0 2-1 3s-2 0-3 0h0l-1-1c-1 0-1 1-1 1l-3-2h-1l1-2c0-2 1-3 2-4l2-1z" class="h"></path><path d="M260 499l-1 1c-1 1-1 1-2 1l-2-2c-1-1-1-1-1-2 1-1 2-2 3-2h3c2 1 2 1 4 3l-1 1h-3z" class="B"></path><path d="M260 495c2 1 2 1 4 3l-1 1-1-1c-1 0-2 0-2-1-1 0-1 0-2-1l2-1zm-11-14l2 1 1 1h0 1c1 0 1 1 2 2 3-1 4-3 7-1 1 0 1 1 1 2h1v1h0c1 0 1 1 2 1 0 1 1 1 1 1h1l-1 2s0 1 1 2h0-2c0 1 0 3 1 4-1 0-1 0-2 1v1 1-1-1c0-2-3-3-4-5h0l-2-1c-2 0-3 1-4 1h0l-2 1v-3c-1 0-1 0-2 1h-1c0-1 1-2 1-3 1-1 1-2 1-2-1-1-2-1-4-2h0-1v-3h1l1-1z" class="G"></path><path d="M263 486h1v1l-1 1c-1 0-2 0-3-1l1-1h2z" class="F"></path><path d="M252 487l2 1c0 2 0 3 1 5l-2 1v-3c-1 0-1 0-2 1h-1c0-1 1-2 1-3 1-1 1-2 1-2z" class="a"></path><path d="M261 493v-3h3c0 1 2 1 3 1 0 0 0 1 1 2h0-2c0 1 0 3 1 4-1 0-1 0-2 1v1 1-1-1c0-2-3-3-4-5h0z" class="V"></path><path d="M245 536h0c8-1 16-7 21-13l1 1c1-1 1-1 3 0 0 0 1 0 1 1 1 1 2 1 2 2l1 1h0v6c-1 1-1 3-1 4-1 1-1 2-1 2l1 1h-2v2c-1 0-1 1-2 1v1l-1-1c-1 1-1 2-1 3 0 0 1 0 1 1 0 3-2 6-3 8l-2 3-1 1s-1 1-1 2h0c1 1 3 2 4 4v3c-1 1-1 2-1 3v1l-1 1v1s0 1-1 1v1c1 1 1 1 2 1l1 1 1-1v3h1v1c-1 2 0 3 0 5v5h0c1 0 1 1 1 1 0 1 0 2 1 3v1h0v1h1c1 2 2 4 4 5l1 1v1c2 1 4 2 5 2h0l-3 6h0c5 4 8 8 10 14l1 5h6 0c-1 2-4 3-6 4h-2c-1-1-1-2-2-3h0c0 1 0 2-1 3h-1c-3 0-3-1-5-2 0 0 0 1-1 1s-1 0-1 1h-2l-1 1h-8-19-7-18c-2 0-5 1-7 0h1l6-3c5-2 10-4 14-7v-1-1h1l-1-1c0-1 2-1 3-1l-1-1h-1v-1h-2s-1-1-1-2v-1c0-1-1-1-1-2h1s1-2 2-2c0 0 1 0 1-1l1-1h0l1 1 1-1c1-1 1-1 2-1h1v-2c0-1 1-1 1-2s-1-2-1-3 1-2 1-3l3-1h0l1-1-1-1v-1c-1-1-2-2-3-2l-2-1s0-1 1-1 1 0 2-1h-5c-1 0-2 0-3 1 0-1 0-1-1-1-2 0-2-1-3-3l-1-1s0 1-1 1c1-2 2-4 4-5v-3l1-1-1-1v-1c-1 0-1 1-1 1h-1c0 1-1 1-2 1h0v-1-2h1v-1c1-2 0-4 1-6 0-1 1-2 1-3 0-3-1-6 0-10 0-2 1-4 0-6v-11h1c0-4-1-9 0-12 1 1 1 2 2 3 1 2 4 3 6 4 1 0 3 0 4 1h0z" class="Y"></path><path d="M235 626c1 0 2-1 3-2 1 1 1 2 2 2-2 1-4 1-5 2v-2z" class="a"></path><path d="M259 601v-1c1-2 2-3 4-5 0 1 0 2 1 2l1 1c-3 0-4 2-6 3z" class="O"></path><path d="M259 601c2-1 3-3 6-3-1 3-4 5-5 8l-1-1s1 0 1-1-1-2-1-3z" class="E"></path><path d="M259 605l1 1c0 6 0 12-2 17 0-6-1-12 1-18z" class="D"></path><path d="M251 618h0 1v-3h0c1 1 1 3 1 4s1 1 1 2v2c0 1 0 1-1 1l1 1-3 3h-1c2-2 2-3 2-5v-1c-1 1-2 1-2 1v1l-2 2h0s-1 0-1 1h-1 0c1-1 2-3 3-4h0c1-1 1-1 1-2v-1h0c1-1 1-1 1-2z" class="B"></path><path d="M254 610v2l1 1c1 1 0 3 1 5v1 2s0 1-1 1v1c0 1-1 1-1 2l-1-1c1 0 1 0 1-1v-2c0-1-1-1-1-2s0-3-1-4h0v3h-1 0c0-1 0-2-1-2v-2-4c1 0 2 1 3 1l1-1z" class="G"></path><path d="M242 579h2 5l-2 2h-1c1 1 3 1 4 1h1c0 1 0 1 1 2l1 1h1s1 0 1 1h0 1l1-2v3l-2 6c0 1-1 1-1 2v3 1c-1 1-1 2-1 3s1 1 1 1v1 2l1 7-1-1v-2l-1-6-3-11h0l-2-6c-1-2-1-3-3-3-1-1-1-1-2-1h0c-1-1-1-1-2-1v-2l1-1z" class="B"></path><path d="M253 585h1s1 0 1 1h0 1l1-2v3l-2 6h0l-2-7v-1z" class="a"></path><path d="M243 583c2 0 4 0 6 2l1 3c2 6 2 13 4 18l1 7-1-1v-2l-1-6-3-11h0l-2-6c-1-2-1-3-3-3-1-1-1-1-2-1h0z" class="J"></path><path d="M242 579h2 5l-2 2h-1c1 1 3 1 4 1h1c0 1 0 1 1 2l1 1v1l-1 1v1h-2l-1-3c-2-2-4-2-6-2-1-1-1-1-2-1v-2l1-1z" class="F"></path><path d="M241 580l2-1 1 1c0 1-1 1-1 2h-2v-2z" class="i"></path><path d="M240 578l2 1-1 1v2c1 0 1 0 2 1h0c1 0 1 0 2 1 2 0 2 1 3 3l2 6h0c-1 1-1 1-1 2l-1-3v-1c-1 0-1-1-1-2v-2l-2 1-1-1c0 2 0 3-1 4v1h-5c-1 0-2 0-3 1 0-1 0-1-1-1-2 0-2-1-3-3l-1-1s0 1-1 1c1-2 2-4 4-5 2-2 3-2 6-3h-2v-1h3v-1-1z" class="B"></path><path d="M233 584c2-2 3-2 6-3l1 1c-3 0-4 1-7 3-1 1-2 3-2 4l-1-1s0 1-1 1c1-2 2-4 4-5z" class="L"></path><path d="M238 592h-1c-1-1-2-2-2-3s1-2 1-3c1-1 2-1 4-1s3 1 4 2c0 2 0 3-1 4v1h-5z" class="C"></path><path d="M265 566v3c-1 1-1 2-1 3v1l-1 1v1s0 1-1 1v1c1 1 1 1 2 1l1 1c0 1-1 2-1 4l-2 1c1 1 2 0 2 1l-2 3v1h0s-1 0-1 1c-1-2-1-2-3-3h-1v-3l-1 2h-1 0c0-1-1-1-1-1h-1l-1-1c-1-1-1-1-1-2h-1c-1 0-3 0-4-1h1l2-2c1 0 2-2 3-4-1 1-1 1-2 1 1-1 1-2 2-3 0-1 1-1 1-2-1-2-1-3-1-5l2 2 1-1c1 1 1 1 2 1 0-1 1-1 2 0h3 0 0c0-2 2-1 3-2z" class="V"></path><path d="M252 584c1-2 2-3 3-5h0c0 1 0 2-1 3l1 1c0-2 1-3 2-4v1 4l-1 2h-1 0c0-1-1-1-1-1h-1l-1-1zm3-17c1 1 1 1 2 1 0-1 1-1 2 0h3c-1 2-2 2-2 4l-1-2c-2 1-2 1-3 2v1c1 1 0 2 0 2l-1 1c0-3 1-6-1-8l1-1z" class="I"></path><path d="M252 566l2 2c2 2 1 5 1 8h0c-1 2-3 4-4 6h-1c-1 0-3 0-4-1h1l2-2c1 0 2-2 3-4-1 1-1 1-2 1 1-1 1-2 2-3 0-1 1-1 1-2-1-2-1-3-1-5z" class="W"></path><path d="M252 566l2 2c2 2 1 5 1 8h0v-5c-1 2-2 3-3 4h0c-1 1-1 1-2 1 1-1 1-2 2-3 0-1 1-1 1-2-1-2-1-3-1-5z" class="D"></path><path d="M265 566v3c-1 1-1 2-1 3v1l-1 1v1s0 1-1 1v1c1 1 1 1 2 1l1 1c0 1-1 2-1 4l-2 1c1 1 2 0 2 1l-2 3v1h0s-1 0-1 1c-1-2-1-2-3-3h-1v-3-4h0l1-1v-3c1-2 2-3 2-4 0-2 1-2 2-4h0 0c0-2 2-1 3-2z" class="B"></path><path d="M260 572h1v4c-1 0-2 1-3 0h0c1-2 2-3 2-4zm-1 10h1c1 2 2 3 1 4h0c-1 0-2 0-2-1v-3zm-21-16l2-4c1-3 3-4 6-5l1 1c0 1 0 1 1 2h1c-1 1-1 1-1 3h3c1-1 1-1 1-2v-1 1c1 1 1 1 2 1l-1 1c0 2 1 3 2 4l-1 1-2-2c0 2 0 3 1 5 0 1-1 1-1 2-1 1-1 2-2 3 1 0 1 0 2-1-1 2-2 4-3 4h-5-2l-2-1c-1 0-1 0-2-1l2-2-2-1c0-1 0-2-1-3v-1c-1-1 0-3 1-4z" class="F"></path><path d="M247 576h-1c-1 0-1 0-2-1h-1c0-1-1-1-2-2h0 1 1c1 1 2 1 3 1h0l2 1c0 1-1 1-1 1z" class="G"></path><path d="M251 563c1-1 1-1 1-2v-1 1c1 1 1 1 2 1l-1 1c0 2 1 3 2 4l-1 1-2-2c-2-2-5-2-8-2-2 1-4 2-5 5v1l-1-1c0-1 1-3 2-4 3-2 5-2 8-2h3z" class="R"></path><path d="M238 566l2-4c1-3 3-4 6-5l1 1c0 1 0 1 1 2h1c-1 1-1 1-1 3-3 0-5 0-8 2-1 1-2 3-2 4l1 1h0c0 1 1 1 1 2l-2 2c0-1 0-2-1-3v-1c-1-1 0-3 1-4z" class="E"></path><path d="M242 569c1-1 3-2 4-2s4 1 4 2c1 0 2 3 2 4-1 1-1 2-2 3 1 0 1 0 2-1-1 2-2 4-3 4h-5-2l-2-1c-1 0-1 0-2-1l2-2c2 2 4 2 6 2l1-1s1 0 1-1l-2-1h0c0-1-1-1-1-1 0-1 0-1-1-1l-2-2v-1z" class="a"></path><path d="M250 576c1 0 1 0 2-1-1 2-2 4-3 4h-5c1-1 3-1 4-2l2-1zm-8-7h1 1c0-1 0-1 1-1s3 0 4 1c-1 0-2 0-2 1v2l-1 2c0-1-1-1-1-1 0-1 0-1-1-1l-2-2v-1z" class="B"></path><path d="M249 569c1 1 1 1 1 3s0 2-1 3h-1l-2-1h0l1-2v-2c0-1 1-1 2-1z" class="W"></path><path d="M244 587l1 1 2-1v2c0 1 0 2 1 2v1l1 3c0-1 0-1 1-2l3 11 1 6-1 1c-1 0-2-1-3-1v4 2c1 0 1 1 1 2s0 1-1 2h0v1c0 1 0 1-1 2h0l-1-1c-2 2-5 3-8 4-1 0-1-1-2-2-1 1-2 2-3 2h-1v-1h1l-1-1c0-1 2-1 3-1l-1-1h-1v-1h-2s-1-1-1-2v-1c0-1-1-1-1-2h1s1-2 2-2c0 0 1 0 1-1l1-1h0l1 1 1-1c1-1 1-1 2-1h1v-2c0-1 1-1 1-2s-1-2-1-3 1-2 1-3l3-1h0l1-1-1-1v-1c-1-1-2-2-3-2l-2-1s0-1 1-1 1 0 2-1v-1c1-1 1-2 1-4z" class="F"></path><path d="M253 604l1 6-1 1c-1 0-2-1-3-1h0c1 0 1 0 2-1-1-1-2-3-1-5 1 1 1 1 2 0z" class="B"></path><path d="M245 600h0c1 1 2 1 2 2v1c0 1-1 1-1 1l-2 1c1 1 1 1 1 2h0c-1 0-1-1-3 0 0-1-1-2-1-3s1-2 1-3l3-1z" class="L"></path><path d="M241 609c1 1 1 1 1 2 1 0 2 0 3 1 1-1 1-1 1-2 1 0 1-1 2-1h0c0 1 0 1-1 2 0 1-1 1-1 2 0 0-1 0-2 1l-1 1h-1s-1 0-1 1c-1 1-1 1-3 2h0v-1c1-1 1-2 2-2-1-1 0-2 0-3h-2c1-1 1-1 2-1h1v-2z" class="V"></path><path d="M236 612h0l1 1 1-1h2c0 1-1 2 0 3-1 0-1 1-2 2v1h0c2-1 2-1 3-2 0-1 1-1 1-1-1 1-1 2-3 3 0 1 0 1-1 1-1 1-2 2-3 2h-2s-1-1-1-2v-1c0-1-1-1-1-2h1s1-2 2-2c0 0 1 0 1-1l1-1z" class="D"></path><path d="M232 616c0 1 1 2 0 3 2-1 3 0 4-1v-1c0-1 0-2 1-3v1s0 1-1 2l1 1h1 0c2-1 2-1 3-2 0-1 1-1 1-1-1 1-1 2-3 3 0 1 0 1-1 1-1 1-2 2-3 2h-2s-1-1-1-2v-1c0-1-1-1-1-2h1z" class="I"></path><path d="M244 587l1 1 2-1v2c0 1 0 2 1 2v1l1 3c0 1 0 2 1 4 0 1 0 3-1 4h0v4l-1 1c-1-1-1-3-2-4 0 0 1 0 1-1v-1c0-1-1-1-2-2l1-1-1-1v-1c-1-1-2-2-3-2l-2-1s0-1 1-1 1 0 2-1v-1c1-1 1-2 1-4z" class="D"></path><path d="M248 596v7h-1v-1c0-1-1-1-2-2l1-1-1-1c1-1 2-1 3-2z" class="e"></path><path d="M243 591h3c1 1 1 4 2 5-1 1-2 1-3 2v-1c-1-1-2-2-3-2l-2-1s0-1 1-1 1 0 2-1v-1z" class="L"></path><path d="M246 613v3s0-1 1-1h0 2c1 1 0 3 0 5l-1 2c-2 2-5 3-8 4-1 0-1-1-2-2-1 1-2 2-3 2h-1v-1h1l-1-1c0-1 2-1 3-1l-1-1h-1v-1c1 0 2-1 3-2 1 0 1 0 1-1 2-1 2-2 3-3h1l1-1c1-1 2-1 2-1z" class="b"></path><path d="M246 613v3s0-1 1-1h0 2c-4 2-8 7-11 9-1 1-2 2-3 2h-1v-1h1l-1-1c0-1 2-1 3-1l-1-1h-1v-1c1 0 2-1 3-2 1 0 1 0 1-1 2-1 2-2 3-3h1l1-1c1-1 2-1 2-1z" class="E"></path><path d="M266 578v3h1v1c-1 2 0 3 0 5v5h0c1 0 1 1 1 1 0 1 0 2 1 3v1h0v1h1c1 2 2 4 4 5l1 1v1c2 1 4 2 5 2h0l-3 6h0c5 4 8 8 10 14l1 5h6 0c-1 2-4 3-6 4h-2c-1-1-1-2-2-3h0c0 1 0 2-1 3h-1c-3 0-3-1-5-2 0 0 0 1-1 1s-1 0-1 1h-2l-1 1h-8-19-7-18c-2 0-5 1-7 0h1l6-3c5-2 10-4 14-7v-1h1v2c0 2-1 3-1 5l2 2h1s0-2 1-2c0-1 1-2 2-3 0 1-1 2-1 2-1 2-1 3-1 4h4c4-1 6-4 9-6h3c1 1 1 1 2 1 0-3 1-5 2-8 2-5 2-11 2-17 1-3 4-5 5-8l-1-1c-1 0-1-1-1-2 1-1 2-3 2-4 0-2 0-2-1-3l-2 1h0v-1l2-3c0-1-1 0-2-1l2-1c0-2 1-3 1-4l1-1z" class="c"></path><path d="M267 592c1 0 1 1 1 1 0 1 0 2 1 3v1h0v1c0 1 1 2 1 3 1 1 2 2 2 3v1c1 1 1 1 1 2v1c-1-1-1-2-2-2v2c-3-5-3-11-4-16z" class="G"></path><path d="M265 624c0-2 2-5 3-6 2-2 4-4 6-5 1 2 1 3 1 5h0 0l2 2v2l-1 1s0 1 1 1h-1-4c-1-1-1-3-2-4 1-2 3-4 4-5-1 0-2 1-2 1-4 3-5 7-6 11v-2h0c0-1 0-1-1-1z" class="i"></path><path d="M265 624c1 0 1 0 1 1h0v2c1 0 1 2 2 3s4 1 5 2c0 1-1 2-1 3-1 1-2 1-3 1-2 0-1-2-2-3l-1 2c-2 1-3 1-4 1-2-1-4-3-5-4 2 0 4 1 6 0 1-1 2-7 2-8zm4-26h1c1 2 2 4 4 5l1 1v1c2 1 4 2 5 2h0l-3 6h0c5 4 8 8 10 14l1 5h6 0c-1 2-4 3-6 4h-2c-1-1-1-2-2-3h0c0 1 0 2-1 3h-1c-3 0-3-1-5-2v-1c1-1 5-2 6-3 1-2 1-4 1-6-1-3-4-7-6-8-1 0-1-1-2 0 2 1 3 2 4 3s1 2 0 4c-1 0-2 1-3 1h0c-1 0-1-1-1-1l1-1v-2l-2-2h0 0c0-2 0-3-1-5-1-1-2-3-3-5v-2c1 0 1 1 2 2v-1c0-1 0-1-1-2v-1c0-1-1-2-2-3 0-1-1-2-1-3zm-24-62h0c8-1 16-7 21-13l1 1c1-1 1-1 3 0 0 0 1 0 1 1 1 1 2 1 2 2l1 1h0v6c-1 1-1 3-1 4-1 1-1 2-1 2l1 1h-2v2c-1 0-1 1-2 1v1l-1-1c-1 1-1 2-1 3 0 0 1 0 1 1 0 3-2 6-3 8l-2 3-1 1s-1 1-1 2h0c1 1 3 2 4 4-1 1-3 0-3 2h0 0-3c-1-1-2-1-2 0-1 0-1 0-2-1s-2-2-2-4l1-1c-1 0-1 0-2-1v-1 1c0 1 0 1-1 2h-3c0-2 0-2 1-3h-1c-1-1-1-1-1-2l-1-1c-3 1-5 2-6 5l-2 4c-1 1-2 3-1 4v1c1 1 1 2 1 3l2 1-2 2c1 1 1 1 2 1v1 1h-3v1h2c-3 1-4 1-6 3v-3l1-1-1-1v-1c-1 0-1 1-1 1h-1c0 1-1 1-2 1h0v-1-2h1v-1c1-2 0-4 1-6 0-1 1-2 1-3 0-3-1-6 0-10 0-2 1-4 0-6v-11h1c0-4-1-9 0-12 1 1 1 2 2 3 1 2 4 3 6 4 1 0 3 0 4 1h0z" class="D"></path><path d="M249 560c1 0 1 0 2 1v2h-3c0-2 0-2 1-3z" class="F"></path><path d="M259 541v1l1 2c1 1 1 1 2 1l-1 1c-1 1-1 1-2 1-1-1-2-1-2-2 0-2 1-3 1-4h1z" class="h"></path><path d="M252 560l1-1c-2-2-2-2-4-2v-1c2 0 3 0 4-2h0l1-1c1 1 1 2 2 2l3 3c-1 1 0 2-1 2h-2l-2 2c-1 0-1 0-2-1v-1z" class="O"></path><path d="M249 543c1 0 3-1 3 0h2 1l1 1v1c-1 2 0 4-2 4v1h-1s-1 1-2 1h-1l1-1h-1l-1 1c-1 0-2 0-3-1l1-1c0-1 0-1 1-1v-1c1 0 2-1 2-1l1-1c-1 0-1 0-1-1h-1v-1z" class="G"></path><path d="M259 558l-1-1h1c1 0 1 1 1 2v2l1 1h0c1 1 3 2 4 4-1 1-3 0-3 2h0 0-3c-1-1-2-1-2 0-1 0-1 0-2-1s-2-2-2-4l1-1 2-2h2c1 0 0-1 1-2z" class="H"></path><path d="M254 562l2-2h2l-1 1h-1v3c1 1 2 0 3 1v3c-1-1-2-1-2 0-1 0-1 0-2-1s-2-2-2-4l1-1z" class="E"></path><path d="M267 524c1-1 1-1 3 0 0 0 1 0 1 1 1 1 2 1 2 2l1 1h0v6c-1 1-1 3-1 4-1 1-1 2-1 2l1 1h-2v2c-1 0-1 1-2 1v1l-1-1c-1 1-1 2-1 3 0 0 1 0 1 1h-2c-1 2-1 3-2 5h0c-1 1-2 1-3 1h-1c-2-1-3-2-4-3 0-2 4-1 5-1s1 0 1-1h-1v-3l1-1c-1 0-1 0-2-1l-1-2v-1-1-1h2c1 0 1-1 1-2l1 1v1l2-1c0-2 1-3 1-5l1-2c0-2 0-3-1-4s-1-1-1-2l2-1z" class="H"></path><path d="M267 524c1-1 1-1 3 0 0 0 1 0 1 1 0 0-2-1-3 0h1c0 1 1 2 1 2l-1 1c-1-1-1-2-2-2s-1 0-1 1c-1-1-1-1-1-2l2-1z" class="D"></path><path d="M265 538c0-2 1-3 1-5 0 1 0 2 1 3 1-1 1-1 1-2h1c0 1 0 3-1 4l-2 3c-1 2-2 3-4 4-1 0-1 0-2-1l-1-2v-1-1-1h2c1 0 1-1 1-2l1 1v1l2-1z" class="B"></path><g class="a"><path d="M262 537l1 1v1l2-1c-1 1-1 2-2 3-2 1-2 0-4 1v-1-1-1h2c1 0 1-1 1-2z"></path><path d="M273 527l1 1h0v6c-1 1-1 3-1 4-1 1-1 2-1 2l1 1h-2v2c-1 0-1 1-2 1v1l-1-1c-1 1-1 2-1 3 0 0 1 0 1 1h-2c-1 2-1 3-2 5h0c-1 1-2 1-3 1h-1c-2-1-3-2-4-3 0-2 4-1 5-1s1 0 1-1c2-1 3-3 4-4 2-2 4-4 5-7 2-3 3-7 2-11z"></path></g><path d="M260 554c0-1-1-2-1-3h2c1 0 2 1 3 2-1 1-2 1-3 1h-1zm-15-18h0c8-1 16-7 21-13l1 1-2 1c0 1 0 1 1 2s1 2 1 4l-1 2c0 2-1 3-1 5l-2 1v-1l-1-1c0 1 0 2-1 2h-2v1 1h-1-2-1l1 2v1l-1-1h-1-2c0-1-2 0-3 0h-1 0v-2h0v-2h-3c-2 2-4 3-6 4 0 1-3 3-3 4-1 1-2 3-3 5v-12c0-4-1-9 0-12 1 1 1 2 2 3 1 2 4 3 6 4 1 0 3 0 4 1h0z" class="L"></path><path d="M259 533c1 0 1-1 2-1h1v2l-3-1z" class="I"></path><path d="M235 539c0-1-1-2 0-3 0-1 1-2 1-2 1 0 3 1 4 1h-1c-1 2-3 2-4 4z" class="D"></path><path d="M239 543h0c1-2 4-4 6-4l1-1h0l4-2h1 2s0 1 1 1 2 0 2 1h-5l-3 1h-3c-2 2-4 3-6 4z" class="e"></path><path d="M241 535c1 0 3 0 4 1h0c-3 1-6 4-9 6h0c-1-1-1-2-1-3h0c1-2 3-2 4-4h1 1z" class="i"></path><path d="M259 533l3 1c0 1 1 2 0 3 0 1 0 2-1 2h-2v1h-2v-2h-2-4 5c0-1-1-1-2-1h1c1-2 2-3 4-4z" class="D"></path><path d="M248 539l3-1h4 2v2h2v1h-1-2-1l1 2v1l-1-1h-1-2c0-1-2 0-3 0h-1 0v-2h0v-2z" class="i"></path><path d="M255 538h2v2h2v1h-1-2-1l1 2v1l-1-1h-1-2c0-1-2 0-3 0h-1 0v-2h0l1 1c2-1 5 0 6-2 1 0 1 0 1-1l-1-1z" class="H"></path><path d="M245 539h3v2h0v2h0 1v1h1c0 1 0 1 1 1l-1 1s-1 1-2 1v1c-1 0-1 0-1 1l-1 1c1 1 2 1 3 1h0l-2 1c1 1 1 1 2 1v1h-2-1c0 1 0 2-1 2h2v1h-1c-3 1-5 2-6 5l-2 4c-1 1-2 3-1 4v1c1 1 1 2 1 3l2 1-2 2c1 1 1 1 2 1v1 1h-3v1h2c-3 1-4 1-6 3v-3l1-1-1-1v-1c-1 0-1 1-1 1h-1c0 1-1 1-2 1h0v-1-2h1v-1c1-2 0-4 1-6 0-1 1-2 1-3 0-3-1-6 0-10 0-2 1-4 0-6v-11h1v12c1-2 2-4 3-5 0-1 3-3 3-4 2-1 4-2 6-4z" class="Y"></path><path d="M245 539h3v2h0-1c0-1-1-1-2-2z" class="B"></path><path d="M241 553l1 1v-1c0-1-1-1 0-2h2v4 1h-1c-1-1-2-2-2-3z" class="D"></path><path d="M248 543h0v1c-2 1-3 2-5 3l-1 1-1-1v-1c2-2 4-3 7-3z" class="R"></path><path d="M236 566h2c-1 1-2 3-1 4v1c1 1 1 2 1 3l2 1-2 2c-2-4-2-7-2-11z" class="e"></path><path d="M248 543h1v1h1c0 1 0 1 1 1l-1 1s-1 1-2 1v1c-1 0-1 0-1 1-1-2-3-1-5-1l1-1c2-1 3-2 5-3v-1z" class="V"></path><path d="M231 579v-1h1v-2-1h1l1 1v1c1 0 1 1 1 1l1 1h0 4v1h-3v1h2c-3 1-4 1-6 3v-3l1-1-1-1v-1c-1 0-1 1-1 1h-1z" class="G"></path><path d="M236 566v-1c-1-5 0-9 3-14l1 1 1 1c0 1 1 2 2 3h1 1 2v1h-1c-3 1-5 2-6 5l-2 4h-2z" class="C"></path></svg>