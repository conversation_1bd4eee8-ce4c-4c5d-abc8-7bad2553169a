<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:v="https://vecta.io/nano" viewBox="49 40 462 636"><!--oldViewBox="0 0 556 752"--><style>.B{fill:#7a797a}.C{fill:#585858}.D{fill:#525252}.E{fill:#686768}.F{fill:#9e9e9e}.G{fill:#626162}.H{fill:#828182}.I{fill:#757474}.J{fill:#989798}.K{fill:#908f90}.L{fill:#4c4c4c}.M{fill:#8b8a8b}.N{fill:#878686}.O{fill:#a4a3a4}.P{fill:#424242}.Q{fill:#7f7e7f}.R{fill:#6d6c6c}.S{fill:#706f70}.T{fill:#474647}.U{fill:#363536}.V{fill:#5e5d5e}.W{fill:#949393}.X{fill:#383738}.Y{fill:#b6b5b5}.Z{fill:#aaa9aa}.a{fill:#afaeaf}.b{fill:#2e2d2d}.c{fill:#2e2e2e}.d{fill:#bab9ba}.e{fill:#272627}.f{fill:#c0bfbf}.g{fill:#d4d2d3}.h{fill:#181818}.i{fill:#d5d4d5}.j{fill:#121212}</style><path d="M50 348c0 1 0 1 1 1s2 0 3 1h0c-1 1-2 2-3 2s-1-1-1-1l-1-1s0-1 1-2z" class="K"></path><path d="M328 646l2-1c1-1 3-2 4-2h2 0l-3 2h0 1 0 2l1 1h0 1c-3 1-7 2-10 1v-1z" class="H"></path><path d="M293 70c5 0 10 2 14 5h-1c-2 0-4-1-6-2-1 0-2 0-4-1h-1v-1c-1 0-2 0-2-1z" class="S"></path><path d="M147 601c-2-1-3-2-4-3 2 0 4 1 6 2h1 0c2 1 3 3 3 6v1c-2-2-3-5-6-6z" class="Z"></path><path d="M288 64h3c1 1 1 2 2 3l-1 2s-1 0-1-1l-1 2c-2-1-2 0-4 0 1-1 1-1 1-2l1-3h1l-1-1z" class="C"></path><path d="M287 68h1l1-2c1 0 2 1 2 2l-1 2c-2-1-2 0-4 0 1-1 1-1 1-2z" class="J"></path><path d="M308 76c2 1 3 2 4 3h-1-1c0 1 1 1 2 2l-9-1v-1c0-1 1-1 1-2h1c2 0 3 0 3-1z" class="S"></path><path d="M49 350h0c-1-1-2-4-1-6 0-1 1-3 3-4 1-1 2 0 4 0 1 1 1 2 1 4l-1-1c0-1 0-1-1-1s-2-1-3 0-2 2-2 4c0 1 0 2 1 2-1 1-1 2-1 2z" class="B"></path><path d="M379 628l1 1c-2 1-2 1-3 3-1 0-3 1-4 3 0 0 0 1-1 1s-2 1-3 1c0-1 1-1 2-2h1 0v-1h0-2v-2c2-1 3-2 5-2h0c2 0 3-2 4-2z" class="O"></path><path d="M51 342c2 2 4 3 4 5 0 1 0 2-1 3h0c-1-1-2-1-3-1s-1 0-1-1c-1 0-1-1-1-2 0-2 1-3 2-4zm131 293l8 4 15 4-2 1h0 1l-1 1-17-6c-1-1-3-2-4-4z" class="F"></path><path d="M48 355v1c1 3 0 6 3 8 1 0 2 1 4 1 1-1 1-2 2-3 0 2 1 1 1 3l-2 2h-1c-1 1-3 1-4 0-2 0-3-2-4-4-1-3 0-5 1-8z" class="W"></path><path d="M55 365c1-1 1-2 2-3 0 2 1 1 1 3l-2 2c-1-1-1-1-2-1l1-1z" class="I"></path><path d="M51 342c1-1 2 0 3 0s1 0 1 1l1 1c0 1 0 2 1 4h0 1 0-2v1h1l1 1h2c2 0 3 0 5-1s4-2 7-3h0c-1 1-2 2-3 2l1 2-1 1c-1 0-1 0-2 1-4 0-7-1-11-1-1-1-1-1-2-1 1-1 1-2 1-3 0-2-2-3-4-5z" class="M"></path><path d="M389 618l3-2c1-2 2-3 3-5l3-3s0-1 1-1c1-1 1-1 2-1-4 6-8 12-13 17-2 2-4 5-6 6s-3 2-5 3c1-2 1-2 3-3l-1-1 10-10z" class="J"></path><path d="M479 270c1 0 1 0 2 1v3l1 4c1 2 1 4 2 6v5c1 1 1 3 1 4 0 3 1 6 2 9v7c1 5 2 10 2 15 0 2 1 4 0 5 0 1-1 1-1 2-1-2 0-3 0-5 0-8-2-16-3-25 0-2 0-5-1-7l-3-17c-1-2-1-5-2-7z" class="S"></path><path d="M258 648l1 1v1h1l1-1c0 1 1 1 2 2v2c1 1 1 2 1 3l-1-1-2 1s0-1-1-1c0 0-1 0-2-1-1 1-3 2-3 3v1s-1 0 0 1c0 2 0 2 2 4h2 0c0 1-1 1-2 1h0-1c1 1 3 1 3 1v2c-1-1-3-1-4-2-1 0-3-2-3-3 0-3 2-5 4-7 0-3-1-3-2-5h2 1v-1l1-1z" class="W"></path><defs><linearGradient id="A" x1="320.519" y1="653.576" x2="331.681" y2="638.448" xlink:href="#B"><stop offset="0" stop-color="#6e6e72"></stop><stop offset="1" stop-color="#949391"></stop></linearGradient><linearGradient id="B" gradientUnits="userSpaceOnUse"></linearGradient></defs><path fill="url(#A)" d="M330 636h2l2 2c2 0 4 1 7 2l-1 1c-1 1-2 1-3 1l-1 1h-2c-1 0-3 1-4 2l-2 1c-4 2-8 5-13 6l1-1c1-1 6-4 8-5l6-3-1-1 2-2c0-1 0-1-1-2v-1-1z"></path><path d="M334 638c2 0 4 1 7 2l-1 1c-1 1-2 1-3 1h-1c0-1 0-1 1-1v-1h-1l-1 1h-1l1-2-1-1z" class="F"></path><path d="M330 636h2l2 2 1 1c-2 1-3 3-5 4l-1-1 2-2c0-1 0-1-1-2v-1-1z" class="Z"></path><path d="M148 590c3 3 5 6 6 10l1 4c-2-2-3-3-4-5h-1v1h0-1c0-1-3-2-4-2-2-1-4-3-5-4v-1c1-1 2-1 3-2s2 0 4 0v1-1l1-1z" class="K"></path><path d="M140 593c2-1 2-1 4 0v1c2 1 4 2 5 3h1l-1-1h1c1 1 1 1 1 3h-1v1h0-1c0-1-3-2-4-2-2-1-4-3-5-4v-1z" class="O"></path><path d="M147 601c3 1 4 4 6 6 6 8 11 16 19 22 3 3 7 4 10 6 1 2 3 3 4 4-10-4-18-11-25-19-3-3-5-7-8-10l-6-9z" class="Q"></path><path d="M269 65c0 1 1 2 2 3 1-1 2-3 3-4s2 0 3 0c0 0 1 1 1 2 0 0 1 0 1 1 1-1 2-3 3-4h3c1 1 1 1 1 2v1l2-2 1 1h-1l-1 3c0 1 0 1-1 2-2 0-2 2-4 2l-1-1c1 0 1 0 1-1l2-1-1-1c-1 1-1 2-3 3-1-1-3-1-4-2 0-1 1-1 0-2-2 2-2 2-4 3h0-1-2c0-1-1-2-1-4l1-1z" class="F"></path><path d="M57 361v1 1h1c0-1 1-1 1-1l1 1c1 0 1 1 2 1h1v6h0v-1h-1v8c0 2 1 5 0 8l1 22c-2-4-2-9-2-13l-2-22c0-2-1-4 0-7h0-1c0-2-1-1-1-3v-1z" class="H"></path><path d="M57 361v1 1h1c0-1 1-1 1-1l1 1c1 0 1 1 2 1v2l-2-1c-1 1-1 5-1 7 0-2-1-4 0-7h0-1c0-2-1-1-1-3v-1z" class="R"></path><path d="M62 364h1v6h0v-1h-1v8c0 2 1 5 0 8 0-2 0-5-1-7 0-1 0-2-1-3v-4c0-2 0-3 1-4h1v-1-2z" class="K"></path><path d="M108 177c0 5-6 10-8 15-1 1-1 2-1 4l-4 7c-1 1-1 3-2 4 0 1-1 2-1 3-1 1-1 1-1 2-1 3-3 7-4 9-1 1-1 2-1 3 0 2-1 3-2 5-1 1-2 5-2 6-1 1-3 6-3 8-1 3-1 5-3 8v1c-1 0-1 1-2 1 1-4 3-9 4-13 6-17 13-34 22-50 2-4 5-10 8-13zm385 163v1h2c1-1 2-2 3-2 2 1 3 2 4 3 1 2 1 4 1 6 0 1-1 2-1 2-1 1-2 2-2 3l-2-1c-4-1-8-1-12-1h0l2-1h0c1-1 1-2 1-3h2v-1-1-1h1c1-1 1-1 1-2l-1-1c1 0 1-1 1-1z" class="F"></path><path d="M495 348c0-1 1-3 1-4v-2c0 1 0 3 1 3 1 1 1 1 2 1 1 2-1 3 1 4h0l-1 1-3-1c-1-1-1-1-1-2z" class="K"></path><path d="M495 341c1-1 2-2 3-2 2 1 3 2 4 3v4 1c0-2-1-3-2-4 0-1-1-2-3-2l-1 1v2c0 1-1 3-1 4 0-2 0-3-1-4 0-2 0-2 1-3z" class="B"></path><path d="M493 340v1h2c-1 1-1 1-1 3 1 1 1 2 1 4 0 1 0 1 1 2l3 1 1-1h0c1-1 1-2 2-3v-1-4c1 2 1 4 1 6 0 1-1 2-1 2-1 1-2 2-2 3l-2-1c-4-1-8-1-12-1h0l2-1h0c1-1 1-2 1-3h2v-1-1-1h1c1-1 1-1 1-2l-1-1c1 0 1-1 1-1z" class="T"></path><path d="M493 340v1 2 1 2l1 1v2l-1 1h-5c1-1 1-2 1-3h2v-1-1-1h1c1-1 1-1 1-2l-1-1c1 0 1-1 1-1z" class="B"></path><path d="M493 340v1 2 1 2l1 1v2s0-1-1-2h0l-1 2h-1l1-1c-1-2-1-2-1-3v-1h1c1-1 1-1 1-2l-1-1c1 0 1-1 1-1z" class="E"></path><path d="M488 331c0-1 1-1 1-2 1 1 1 1 1 2 1 0 1 1 2 2v1l1 6s0 1-1 1l1 1c0 1 0 1-1 2h-1v1 1c-1-1-2-1-4-1v1h1v1h-2c-2-1-2-2-3-4l-2 1v-1l-1-1v-1h0c0-1 0-2 1-3h0l1-2h1v-3l1 1c1 0 1-2 2-2v2c-1 0-1 1-1 1h1c1-1 1-1 1-2l1-2z" class="K"></path><path d="M484 341h3c0 1-1 1-2 1 0 1 0 1-1 0v-1z" class="O"></path><path d="M487 343h1c0-1 2-1 3 0h1l-1 1h-4l-1-1h1z" class="F"></path><path d="M481 341c1 0 2-1 2 0h1v1h-2v1h1l-2 1v-1l-1-1 1-1z" class="J"></path><path d="M481 338l1-2c1 1 1 2 1 3v1h-2v1l-1 1v-1h0c0-1 0-2 1-3h0z" class="G"></path><path d="M488 331c0-1 1-1 1-2 1 1 1 1 1 2 1 0 1 1 2 2v1h-2c-1-1 0-1-1-1-1 1-2 2-3 4h0 2v1h-4 0c0-1-1-2-1-2v-3l1 1c1 0 1-2 2-2v2c-1 0-1 1-1 1h1c1-1 1-1 1-2l1-2z" class="V"></path><path d="M291 68c0 1 1 1 1 1s0 1 1 1c0 1 1 1 2 1v1h1c2 1 3 1 4 1 2 1 4 2 6 2h1l1 1c0 1-1 1-3 1h-1c0 1-1 1-1 2v1c-2 0-2-1-3 0h-1c-1 0-2-1-2-2l-1-1v1h-1-1 0c-1 0-1 0-2-1v1c-1 0-2-1-3-1l-1 2v-1c-1-1-2-1-3-1l-3-3v-1h0c-1 0-1-1-2-1v-1h1l1 1c2 0 2-2 4-2s2-1 4 0l1-2z" class="Q"></path><path d="M292 77c1 0 1-1 2-2v1 1l1 1h-1 0c-1 0-1 0-2-1z" class="B"></path><path d="M296 72v1c-2 0-3 2-4 2h-3c-1 0-2 0-4 1 0 0 0-1-1-1h0v-1c1 0 2-1 2-1 2-1 3-1 5-1 1-1 2 0 4 0h0 1z" class="F"></path><defs><linearGradient id="C" x1="410.651" y1="593.959" x2="385.437" y2="613.173" xlink:href="#B"><stop offset="0" stop-color="#848385"></stop><stop offset="1" stop-color="#b9b7b7"></stop></linearGradient></defs><path fill="url(#C)" d="M397 601l1-4 2-2c1-1 3-3 5-3 0-1 2 0 3 0s3 0 3 1v2c-2 2-4 3-6 4h0 2v1c-1 1-3 2-4 3s-1 2-2 3c-1 0-1 0-2 1-1 0-1 1-1 1l-3 3c-1 2-2 3-3 5l-3 2s-1 0-1-1c3-5 7-10 9-16z"></path><path d="M246 644c0-1 2-3 3-3s2 0 2-1c1 0 1 0 1-1 0 0 0-1-1-2h2 0c1 0 2 0 3 1l3 2v3l1 1-1 1c2 0 5 3 6 4 0 1 1 2 1 3h0l-2-2v1 1l-1 1v-2c-1-1-2-1-2-2l-1 1h-1v-1l-1-1-1 1v1h-1-2 0-1l1 1c0 2 0 2-1 3 0 1-1 1-2 1h0c-2 0-4-1-5-2-1-2-1-3-1-5v-2h0l1-2z" class="J"></path><path d="M254 651c0 2 0 2-1 3 0 1-1 1-2 1 1 0 1 0 0-1h0l3-3zm-5-2h1l1-1c1 0 1 1 2 2h-1 0c-1 1-1 2-1 2-2 1-2 0-3 0v-2h0l1-1z" class="K"></path><path d="M249 645h4c-1 1-1 2-2 2s-2 1-2 2l-1 1c-1-1-1-1-1-2s1-1 2-3h0z" class="O"></path><path d="M253 645c1 1 1 1 2 1 1 1 2 1 3 2h0l-1 1v1h-1-2 0-1 0c-1-1-1-2-2-2l-1 1h-1c0-1 1-2 2-2s1-1 2-2z" class="H"></path><path d="M253 637c1 0 2 0 3 1l3 2v3l1 1-1 1-3-2h-3v-3c0-1 0-1 1-2h0l-1-1z" class="M"></path><path d="M253 643h3l3 2c2 0 5 3 6 4 0 1 1 2 1 3h0l-2-2v1 1l-1 1v-2c-1-1-2-1-2-2l-1 1h-1v-1l-1-1h0c-1-1-2-1-3-2-1 0-1 0-2-1h-4 0c1-1 2-2 4-2z" class="G"></path><path d="M263 651v-1c-1-1-2-2-3-2l1-1c1 0 2 1 2 2l1 1v1 1l-1 1v-2z" class="S"></path><path d="M253 643h3l-1 1h-1v1c1 0 4 1 5 2l-1 1c-1-1-2-1-3-2-1 0-1 0-2-1h-4 0c1-1 2-2 4-2z" class="D"></path><path d="M74 461c3 7 5 15 8 23l16 36c2 5 5 9 8 14 7 12 15 24 24 35 4 4 8 10 12 15 2 2 4 3 6 5v1l-1 1c-6-6-12-13-17-20-11-13-20-27-29-42-1-3-3-7-5-10l-13-29c-3-7-5-15-7-22-1-2-2-5-2-7z" class="G"></path><defs><linearGradient id="D" x1="349.192" y1="635.541" x2="355.733" y2="644.6" xlink:href="#B"><stop offset="0" stop-color="#949093"></stop><stop offset="1" stop-color="#a9aaaa"></stop></linearGradient></defs><path fill="url(#D)" d="M365 634l5-2v2h2 0v1h0-1c-1 1-2 1-2 2-3 1-5 2-8 4-7 3-15 4-23 5h-1 0l-1-1h-2 0-1 0l3-2h0l1-1c1 0 2 0 3-1l1-1v-1h3c1 0 2-2 3-2 1-1 2 0 2 0l1-1c1 0 1 0 1 1h1v-2c1 1 1 1 2 1 1 1 5-1 7-2 2 0 2 0 4-1v1z"></path><path d="M365 634l5-2v2c-2 1-4 1-6 2l1-2z" class="H"></path><path d="M352 635c1 1 1 1 2 1 1 1 5-1 7-2 2 0 2 0 4-1v1l-1 2c-9 4-18 6-28 7h0l1-1c1 0 2 0 3-1l1-1v-1h3c1 0 2-2 3-2 1-1 2 0 2 0l1-1c1 0 1 0 1 1h1v-2z" class="I"></path><path d="M461 331h4c2 1 4 2 5 3l3 2 3 2 2-2c0 1 1 2 3 2-1 1-1 2-1 3h0v1l1 1v1l2-1c1 2 1 3 3 4h2v-1h-1v-1c2 0 3 0 4 1v1h-2c0 1 0 2-1 3h0-2l-1-1c-1 0-3-1-5-2v1c-1 0-2 1-3 1s0 1-1 0l-2-1v-1c-1-2-1-3-1-4v-1 2l-1-1h-1c-1-1-2-1-2-1-1-1-1-2-2-3l1-1c-1-1-2-1-3-1h-1c-1-1-2 0-3 0-1 1-2 2-3 4l1 1c-1 0-3-1-4-1-1-1-1-2-2-3h1l1 1h0c0-1 1-1 1-2h-1c1-2 4-4 6-6z" class="Z"></path><path d="M481 343l-1 1-1-1v-1h1l1 1z" class="Y"></path><path d="M478 336c0 1 1 2 3 2-1 1-1 2-1 3h0l-4-3 2-2z" class="U"></path><path d="M488 347v-1h-1v-1c2 0 3 0 4 1v1h-2c0 1 0 2-1 3h0-2l-1-1c-1 0-3-1-5-2v-1h0c1 0 3 1 3 2l1-1h1c0 1 1 2 1 2h1 0c0-1 1-1 1-2z" class="J"></path><path d="M455 339l1 1h1c1-1 1-2 2-3h0c1-1 3-1 4-2 3 1 6 2 9 4l3 3c2 0 3 3 5 4v1 1c-1 0-2 1-3 1s0 1-1 0l-2-1v-1c-1-2-1-3-1-4v-1 2l-1-1h-1c-1-1-2-1-2-1-1-1-1-2-2-3l1-1c-1-1-2-1-3-1h-1c-1-1-2 0-3 0-1 1-2 2-3 4l1 1c-1 0-3-1-4-1-1-1-1-2-2-3h1l1 1z" class="N"></path><path d="M473 343c2 2 4 4 4 6h-1l-2-1v-1c-1-2-1-3-1-4z" class="K"></path><path d="M468 338c1 0 2 1 3 2l1 1 1 1v2l-1-1h-1c-1-1-2-1-2-1-1-1-1-2-2-3l1-1z" class="O"></path><path d="M67 325h1v2c0 2 1 2 1 4l1 3h1 1c1 0 1 0 2 1l1 1h1c-1 2-3 5-6 6-1 1-1 2-3 3h-1c-2 1-3 2-5 4-1 0-1 0-1 1h-2l-1-1h-1v-1h2 0-1c-1-2 0-6 0-9l3-9v-4c1 1 1 2 1 3h1l1 1 1 1c1 0 1 1 2 1v-1-3-1c1 0 1-1 1-2z" class="K"></path><path d="M58 348l1 1c1 0 1 0 1-1s-1-2-1-3h1 1c0 1 0 1 1 2 0 0-2 0-1 2-1 0-1 0-1 1h-2l-1-1h-1v-1h2zm0-9h4c1 0 3 0 4 1h1 0v1c0 1-1 1-2 2h-2c-2 0-3 1-4 1v-1l2-2h0-2l-1-2z" class="F"></path><path d="M66 340h1 0v1c0 1-1 1-2 2 0-1-1-1-1-1l-1-1 3-1z" class="Q"></path><path d="M75 336h1c-1 2-3 5-6 6-1 1-1 2-3 3h-1-2c0 1-1 1-1 1v-1l1-1-1-1h2c1-1 2-1 2-2v-1h0l1-1c1 0 0 0 0-1h1c1 0 1 1 2 1s3-2 3-2l1-1z" class="R"></path><path d="M64 344c2-1 4-2 6-2-1 1-1 2-3 3h-1-2c0 1-1 1-1 1v-1l1-1z" class="F"></path><path d="M60 330h1c1 1 2 1 2 3l1 1v1h0c1 1 1 2 2 3h2c0 1 1 1 0 1l-1 1h-1c-1-1-3-1-4-1h-4-1l3-9z" class="N"></path><path d="M61 330c1 1 2 1 2 3h-2v-3zm3 5c-1 1-4 1-6 1l1-1c2-1 3-1 5-1v1z" class="F"></path><path d="M67 325h1v2c0 2 1 2 1 4l1 3h1 1c1 0 1 0 2 1l1 1-1 1s-2 2-3 2-1-1-2-1h-1-2c-1-1-1-2-2-3h0v-1l-1-1c0-2-1-2-2-3h-1v-4c1 1 1 2 1 3h1l1 1 1 1c1 0 1 1 2 1v-1-3-1c1 0 1-1 1-2z" class="D"></path><path d="M68 325v2c0 2 1 2 1 4-1 1-1 2 0 3v1 1 2h-1 0c-1-1 0-2-1-4v-4c0-1 1-3 1-4v-1z" class="P"></path><path d="M69 336v-1-1c-1-1-1-2 0-3l1 3h1 1c1 0 1 0 2 1l1 1-1 1s-2 2-3 2-1-1-2-1v-2z" class="L"></path><path d="M69 336v-1-1c-1-1-1-2 0-3l1 3h1 1c1 0 1 0 2 1l1 1-1 1v-1h-2l-1-1s-1 0-2 1z" class="D"></path><path d="M465 494c0-1 1-1 1-1l1-2c-1 5-3 9-5 14-7 16-15 31-25 46-9 14-20 27-31 40 1 0 1 0 2 1-1 0-3-1-3 0-2 0-4 2-5 3l-2 2-1 4-1-1 2-6c1-2 1-3 2-5h1c0-1 1-1 1-2h0 2 0l2-3v1c-1 1-1 2-2 3 0 0 0 1-1 1 2 0 2-2 3-1 9-9 17-19 24-30 14-20 25-41 35-64z" class="L"></path><path d="M406 584v1c-1 1-1 2-2 3 0 0 0 1-1 1 2 0 2-2 3-1-1 2-5 5-7 7l5-8h0l2-3z" class="H"></path><path d="M48 355c2-2 5-4 8-4 4 0 7 1 11 1l-3 3v2c2 1 5-5 7-5h0c1 1 0 2 0 3 1 0 1-1 2-1l-3 6v1l-6 3h0l1 1v2 5 11l-1-6h0v-1c-1-2 0-4-1-6v-6h-1c-1 0-1-1-2-1l-1-1s-1 0-1 1h-1v-1-1 1c-1 1-1 2-2 3-2 0-3-1-4-1-3-2-2-5-3-8v-1z" class="Y"></path><path d="M71 355c1 0 1-1 2-1l-3 6v1l-6 3h0l1 1v2 5 11l-1-6h0v-1c-1-2 0-4-1-6v-6h-1c-1 0-1-1-2-1l-1-1s-1 0-1 1h-1v-1-1h-2-2c-1 0-1 0-2-1 0-1 0-2 1-3 1-2 3-2 4-3 1 1 2 1 3 2v1 2c1 1 3 2 4 3h0 1c1-1 2-1 3-1h0v-1c2 0 1-1 2-2 0-1 1-1 1-1s1-1 1-2z" class="T"></path><path d="M55 359c-1 1-2 1-2 1l-1-1s0-1 1-2 2-1 4-2c1 1 1 1 1 2s-1 1-1 1c-1 1-2 1-2 1z" class="O"></path><path d="M71 355c1 0 1-1 2-1l-3 6c-1 1-2 1-3 2-1 0-4 1-5 1s-3-2-4-3l-3-1s1 0 2-1v1c2 0 2-1 2-2v2c1 1 3 2 4 3h0 1c1-1 2-1 3-1h0v-1c2 0 1-1 2-2 0-1 1-1 1-1s1-1 1-2z" class="F"></path><path d="M265 64c1 0 2-1 3 0l1 1-1 1c0 2 1 3 1 4h2 1 0c2-1 2-1 4-3 1 1 0 1 0 2 1 1 3 1 4 2 2-1 2-2 3-3l1 1-2 1c0 1 0 1-1 1h-1v1c1 0 1 1 2 1h0v1c-3-1-5-2-9-2-1 0-1 0-2 1-1 0-3 0-4 1h0c-1 0-1 1-1 1-1 0-1 1-2 1h-2-1v1h-1-3c-1 0-2 1-3 1s-2 1-3 2c-1-1-2-1-3-1l-5 1-8 2c1-1 2-1 3-2 1-2 3-3 5-5 4-2 9-4 13-5l3-1v-1-3c1 0 2-1 3-1l1 1c1 0 2-1 2-1z" class="K"></path><path d="M265 64c1 0 2-1 3 0l1 1-1 1c0 2 1 3 1 4l-1 1c0-1 0-1-1-2l-1 1h0c-1-1-1-3-1-3 0-1 1-2 2-3h-2z" class="Q"></path><path d="M250 75l1 1-2 2h0 1c0 1-1 1-2 1l-5 1-8 2c1-1 2-1 3-2 1 0 2 0 3-1 3 0 7-2 9-4z" class="C"></path><path d="M250 75c1 0 2 0 2-1l1 1-1 1v1h1l3-2 1 1c1 0 2-1 2-1 1 0 1 1 1 2h-3c-1 0-2 1-3 1s-2 1-3 2c-1-1-2-1-3-1 1 0 2 0 2-1h-1 0l2-2-1-1zm9-7c0 1 0 1 1 1 0 0 1 0 1 1h2c0 1 0 1-1 1l-1 1c-3 0-5 0-8 1l-8 3s-1 0-2-1c4-2 9-4 13-5l3-1v-1z" class="B"></path><path d="M259 68c0 1 0 1 1 1 0 0 1 0 1 1-1 1-2 1-4 1l-1-1 3-1v-1z" class="N"></path><path d="M60 326c1-1 1-3 1-4l1-9 6-35c1-6 2-12 4-17v6c1-1 1-3 2-4h0l-1 7c-1 4-2 7-3 11 1 0 1 0 1 1v3h-1v2h1 1l-1 8-2 12v6c0 2 0 5-1 6-1 2 0 4-1 5v1c0 1 0 2-1 2v1 3 1c-1 0-1-1-2-1l-1-1-1-1h-1c0-1 0-2-1-3z" class="H"></path><path d="M72 267c1-1 1-3 2-4h0l-1 7c-1 4-2 7-3 11 1 0 1 0 1 1v3h-1v2c0 2 0 3-1 5-1 1-1 2-1 2l-1 1v-2c0-1 1-1 0-3 0 2 0 3-1 4v-2c1-2 1-6 2-9 0-2 1-3 1-5 1-4 1-7 3-11z" class="a"></path><path d="M69 287c0-2 1-4 1-6 1 0 1 0 1 1v3h-1l-1 2z" class="B"></path><path d="M70 285v2c0 2 0 3-1 5-1 1-1 2-1 2 0-2 0-4 1-7l1-2z" class="R"></path><path d="M66 292v2c1-1 1-2 1-4 1 2 0 2 0 3v2c1 3-1 7-1 11l-1 9c-1 3 0 7-1 11 0 1-1 1-2 2v-1c0-6 1-11 1-17 1-6 1-12 3-18z" class="F"></path><path d="M70 287h1 1l-1 8-2 12v6c0 2 0 5-1 6-1 2 0 4-1 5v1c0 1 0 2-1 2v1 3 1c-1 0-1-1-2-1l-1-1h1l1-1v-4-10l1-9c0-4 2-8 1-11l1-1s0-1 1-2c1-2 1-3 1-5z" class="G"></path><path d="M67 324c0-3-1-8 0-12l2-5v6c0 2 0 5-1 6-1 2 0 4-1 5z" class="L"></path><path d="M66 306c0 1 0 5 1 6 0 2 0 8-1 9v2 1 1-1 2h0 0c-1 1-1 2-1 3v-4-10l1-9z" class="E"></path><path d="M259 640c4 3 8 7 10 12v-1c0-1-1-3-1-4l-1-1-2-2h0l1-1c1-1 1 0 2 0v-1c2 1 4 1 5 3h-1v2h0 0v1c1 1 0 3 0 5v3l1 2c1 1 1 1 1 2h1c0 1 0 1-1 2h0c2 1 3 0 4 1h1c1 1 2 0 2 1s-1 1-1 2h-1v1-3h-1l-1 1v1c0 1-2 1-1 2h0c1 0 1 1 2 2h-2l-1-1h0l-2 2h-4c-2-1-2-1-2-3l-2 1c-1 1-2 1-3 1-1-1-2-1-3-2v-1-2s-2 0-3-1h1 0c1 0 2 0 2-1h0-2c-2-2-2-2-2-4-1-1 0-1 0-1v-1c0-1 2-2 3-3 1 1 2 1 2 1 1 0 1 1 1 1l2-1 1 1c0-1 0-2-1-3l1-1v-1-1l2 2h0c0-1-1-2-1-3-1-1-4-4-6-4l1-1-1-1v-3z" class="H"></path><path d="M274 660h0l-2 2-1-1v-2c1 0 1-1 0-1v-1l1-1 1 2c1 1 1 1 1 2z" class="W"></path><path d="M268 644c1 1 3 2 4 3h0 0v1c1 1 0 3 0 5v-2c0-1-1-1-1-1-1-1-1-2-1-3-1-1-1-1-2-1v-2z" class="I"></path><path d="M268 642c2 1 4 1 5 3h-1v2c-1-1-3-2-4-3h-3 0l1-1c1-1 1 0 2 0v-1z" class="W"></path><path d="M267 653v-1h0 1c1 1 1 2 1 3v1c1 1 1 3 1 5v1 1h0c-2 0-3 0-5-1l1-1v-2-5l1-1z" class="J"></path><path d="M266 654l1-1c0 2 0 3 1 4v1c-1 1-1 2-1 2 0 1 0 1-1 2l1 1h3 0c-2 0-3 0-5-1l1-1v-2-5z" class="I"></path><path d="M255 659c1-1 2-3 4-4 1 1 2 1 2 3 0 0 0 1 1 1h0l-1 1c0 1 0 2-1 2v1l1-1c1 0 2-1 2-2 1-2 2-4 2-6h0 1v5 2l-1 1c2 1 3 1 5 1h3 1c1 0 2 1 3 2v1c0 1-2 1-1 2h0c1 0 1 1 2 2h-2l-1-1h0l-2 2h-4c-2-1-2-1-2-3l-2 1c-1 1-2 1-3 1-1-1-2-1-3-2v-1-2s-2 0-3-1h1 0c1 0 2 0 2-1h0-2c-2-2-2-2-2-4z" class="F"></path><path d="M265 662c2 1 3 1 5 1h3 1c-1 2-1 2-1 4 0-1-1-1-1-2-2-1-3-1-5-1s-4 1-7 1c2 0 4-1 5-2v-1z" class="h"></path><path d="M273 667c0-2 0-2 1-4 1 0 2 1 3 2v1c0 1-2 1-1 2h0c1 0 1 1 2 2h-2l-1-1h0l-2 2h-4c-2-1-2-1-2-3l-2 1c-1 1-2 1-3 1-1-1-2-1-3-2h2v-1h0c1 0 1 0 1 1h1c0-1 1-1 2-2h2c2-1 3 0 4 1h0c-1 1-1 0-3 0v1h1l1 1h2c1-1 1-1 1-2z" class="K"></path><path d="M157 591h1c0 3 0 7 2 10 1 5 3 9 8 12l4 2c-1 1-1 1-2 1h-1-1c-2-1-4-3-5-4 0 1 0 2 1 3s1 2 2 3c2 2 4 5 6 6 4 3 7 6 12 8 1 2 4 3 6 3l6 2c2 0 3 1 4 1l10 1c0 1 0 1 1 0l2 1c-1 0-1 1-2 1v1l1 1 1 2-8-2-15-4-8-4c-3-2-7-3-10-6-8-6-13-14-19-22v-1c0-3-1-5-3-6v-1h1c1 2 2 3 4 5l-1-4h1l-1-4 1 1v-1-2h0 1l1 1v-4z" class="f"></path><path d="M164 615c1 1 1 2 2 3l-1 1-3-3 2-1z" class="G"></path><path d="M159 607c1 1 2 3 4 5 0 1 0 2 1 3l-2 1-3-5v-1c0-1 0-2-1-3h1z" class="C"></path><path d="M165 619l1-1c2 2 4 5 6 6h-1c4 4 10 9 15 11 3 1 5 2 8 3v1l-3-1c-10-4-19-10-26-19z" class="E"></path><path d="M155 594h1l1 1v5c0 2 1 4 2 7h-1c1 1 1 2 1 3v1c-2-2-3-5-4-7l-1-4h1l-1-4 1 1v-1-2h0z" class="B"></path><path d="M155 594h1l1 1v5c-1-2-2-4-2-6z" class="R"></path><path d="M155 600l3 7c1 1 1 2 1 3v1c-2-2-3-5-4-7l-1-4h1z" class="D"></path><defs><linearGradient id="E" x1="168.168" y1="616.765" x2="157.337" y2="596.055" xlink:href="#B"><stop offset="0" stop-color="#282828"></stop><stop offset="1" stop-color="#535353"></stop></linearGradient></defs><path fill="url(#E)" d="M157 591h1c0 3 0 7 2 10 1 5 3 9 8 12l4 2c-1 1-1 1-2 1h-1-1c-2-1-4-3-5-4-2-2-3-4-4-5-1-3-2-5-2-7v-5-4z"></path><path d="M172 624c4 3 7 6 12 8 1 2 4 3 6 3l6 2c2 0 3 1 4 1l10 1c0 1 0 1 1 0l2 1c-1 0-1 1-2 1v1l1 1 1 2-8-2-15-4 1-1 3 1v-1c-3-1-5-2-8-3-5-2-11-7-15-11h1z" class="N"></path><defs><linearGradient id="F" x1="203.963" y1="639.046" x2="204.965" y2="641.874" xlink:href="#B"><stop offset="0" stop-color="#5a5a57"></stop><stop offset="1" stop-color="#706e70"></stop></linearGradient></defs><path fill="url(#F)" d="M194 638c3 1 5 1 8 2 3 0 6 0 9 1h0 0v1c-6 0-12-2-17-3v-1z"></path><path d="M191 638l3 1c5 1 11 3 17 3l1 1 1 2-8-2-15-4 1-1z" class="Y"></path><path d="M254 80c0 1 0 2-1 3 0 1-1 1-2 1l-3 3c-1 0-1 0-2-1 0 1-1 1-1 2-1-1-1-1-2-1h-1c-2 0-5 2-6 2v2h-1c-1-1-1-1-2-1v2l-1-1-1 1c0 1 1 1 0 2v1c-1-1-1-2-2-2s-1 1-2 1h-1c0 1 1 2 1 3h-1v-1l-1 1h-3 1 0v1l-6-2c-1 0-1 0-2 1l-2-1-3 1h0-3l-8 3c-1 1-3 1-4 2l7-4h0l-1-1 6-3-1-1c-2 1-4 0-5 0 5-1 9-4 14-6 6-2 13-4 20-5l8-2 4 1c-1 0-1 1-2 1l1 1c1 0 6-2 8-3z" class="Q"></path><path d="M202 98c3-2 7-3 10-4l-5 3-8 3c-1 1-3 1-4 2l7-4z" class="T"></path><path d="M220 91c1 0 1-1 2-1 7-3 16-5 24-6 2 0 4-1 5 0l-3 3c-1 0-1 0-2-1 0 1-1 1-1 2-1-1-1-1-2-1h-1c-2 0-5 2-6 2s-3 0-4 1h0v-1c-2 0-5 2-8 1h0 0c-1 1-2 1-4 1z" class="X"></path><path d="M235 82l8-2 4 1c-1 0-1 1-2 1-3 0-6 1-8 2-6 1-12 3-18 5l-12 5-1-1c-2 1-4 0-5 0 5-1 9-4 14-6 6-2 13-4 20-5z" class="a"></path><path d="M220 91c2 0 3 0 4-1h0 0c3 1 6-1 8-1v1h0c1-1 3-1 4-1v2h-1c-1-1-1-1-2-1v2l-1-1-1 1c0 1 1 1 0 2v1c-1-1-1-2-2-2s-1 1-2 1h-1c0 1 1 2 1 3h-1v-1l-1 1h-3 1 0v1l-6-2c-1 0-1 0-2 1l-2-1-3 1h0-3l5-3 8-3z" class="b"></path><path d="M213 96c1-1 2-1 3-1h0l1-1c1 0 5-2 6-2h2c-2 2-6 3-8 4-1 0-1 0-2 1l-2-1z" class="T"></path><path d="M232 90h0c1-1 3-1 4-1v2h-1c-1-1-1-1-2-1v2l-1-1-1 1c0 1 1 1 0 2v1c-1-1-1-2-2-2s-1 1-2 1h-1c0 1 1 2 1 3h-1v-1l-1 1h-3 1 0v1l-6-2c2-1 6-2 8-4 2-1 5-1 7-2z" class="C"></path><path d="M222 97c0-1 1-1 1-2 2 0 2 0 3 1l-1 1h-3z" class="I"></path><path d="M473 342v1c0 1 0 2 1 4v1l2 1c1 1 0 0 1 0s2-1 3-1v-1c2 1 4 2 5 2l1 1h2l-2 1h0c4 0 8 0 12 1l2 1 2 1c2 2 3 5 2 8 0 2-2 4-3 5-2 1-3 1-5 0-1 0-2-2-3-3-1 0-1-1-2-1v1c-2 0-4 1-5 0s-2-1-3-1c-2-1-4-3-5-4-2-2-4-5-5-8h-4-1-2v-1-1h0c1 0 2-1 2-2s1-2 2-3l1-1h1l1 1v-2z" class="O"></path><path d="M480 348v-1c2 1 4 2 5 2l1 1h2l-2 1h0l-3 1h0 1l4 4-2 1c-1 0 0-1-1-2 0-1-3-2-4-3v-1l-2-1c0-1 1-1 1-2z" class="B"></path><path d="M481 352c1 1 4 2 4 3 1 1 0 2 1 2l2-1h0l3-3c2 0 4 0 6 1-1 0-1 0-2 1-1 0-1-1-2 0s-2 3-3 4h0c0 1-1 1-1 2-2 0-3-1-4 0-2-2-4-4-5-6 0-1 0-2 1-3z" class="Z"></path><path d="M473 342v1c0 1 0 2 1 4v1l2 1c1 1 0 0 1 0s2-1 3-1c0 1-1 1-1 2l2 1v1c-1 1-1 2-1 3 1 2 3 4 5 6h-1c-1 0-1 0-2-1-1-2-2-3-2-4h-3l2 2-1 1c-2-2-4-5-5-8h-4-1-2v-1-1h0c1 0 2-1 2-2s1-2 2-3l1-1h1l1 1v-2z" class="I"></path><path d="M470 344v3h1c0 1 0 1 1 1-1 1-1 2-2 2h-1l-3-1h0c1 0 2-1 2-2s1-2 2-3z" class="W"></path><path d="M468 347c1 0 1 1 2 1 0 1 0 2-1 2l-3-1h0c1 0 2-1 2-2z" class="H"></path><path d="M473 342v1c0 1 0 2 1 4v1c0 1 0 1-1 2h-1l1-1-1-1c-1 0-1 0-1-1h-1v-3l1-1h1l1 1v-2z" class="F"></path><path d="M471 343h1v3l-1 1h-1v-3l1-1z" class="M"></path><path d="M476 352c2-1 2-1 5 0-1 1-1 2-1 3 1 2 3 4 5 6h-1c-1 0-1 0-2-1-1-2-2-3-2-4h-3c-1-1-1-2-2-4h1z" class="N"></path><path d="M476 352c2 1 3 2 4 4h-3c-1-1-1-2-2-4h1z" class="O"></path><path d="M498 352l2 1 2 1c2 2 3 5 2 8 0 2-2 4-3 5-2 1-3 1-5 0-1 0-2-2-3-3-1 0-1-1-2-1v1c-2 0-4 1-5 0s-2-1-3-1c-2-1-4-3-5-4l1-1-2-2h3c0 1 1 2 2 4 1 1 1 1 2 1h1c1-1 2 0 4 0 0-1 1-1 1-2h0c1-1 2-3 3-4s1 0 2 0c1-1 1-1 2-1h1v-2z" class="B"></path><path d="M503 359c0 2 0 4-2 5-1 1-2 2-3 2-3 0-3-2-5-4h1 0c1 1 1 1 2 1v1h1c1-1 2-1 3-1 2-1 2-2 3-4z" class="F"></path><path d="M498 352l2 1 2 1v3l1 2c-1 2-1 3-3 4-1 0-2 0-3 1h-1v-1c-1 0-1 0-2-1h0c0-1 1-1 2-2 2 0 2 0 3 1h1c1-1 0-1 0-2-1-2-3-3-5-4 1-1 1-1 2-1h1v-2z" class="Y"></path><path d="M498 352l2 1 2 1v3c-1-2-2-2-4-3v-2z" class="H"></path><path d="M490 359c1-1 2-3 3-4s1 0 2 0c2 1 4 2 5 4 0 1 1 1 0 2h-1c-1-1-1-1-3-1-1 1-2 1-2 2h-1v2c-1 0-1-1-2-1v1c-2 0-4 1-5 0s-2-1-3-1c-2-1-4-3-5-4l1-1-2-2h3c0 1 1 2 2 4 1 1 1 1 2 1h1c1-1 2 0 4 0 0-1 1-1 1-2h0z" class="C"></path><path d="M494 356l1-1c1 1 2 1 3 2h0c0 1-1 2-2 2h-1l-1 1c-1 1-1 1-2 1s-2 1-2 1h-1c1-1 2-1 2-2 1-1 3-3 3-4z" class="B"></path><path d="M480 356c0 1 1 2 2 4 1 1 1 1 2 1h1c1-1 2 0 4 0 0-1 1-1 1-2h0 1c0-1 1-1 1-1 0-1 1-2 2-2 0 1-2 3-3 4 0 1-1 1-2 2h1l-2 1c-2 0-4-1-6-1v-1c-1-1-2-1-3-3l-2-2h3z" class="F"></path><path d="M277 643h4v2h-1l2 2c0-1 1-1 1-1h1l-1 2h0c-1 2-1 4-2 5 0 2 0 4-1 5v3h1l1-2h1 0v-1l1 1 1-1h0l1-1c0-1 1-3 1-4 1-2 3-4 5-5 1 0 2-1 3-1l3 2-2 3c0 1 0 2-1 2v1h1c1 0 2 1 3 2s1 3 1 4c0 2-1 3-3 4 0 1-1 1-2 1 0 1-1 0-1 0-1 1-1 2-2 3h-1l-1 1c-1 0-2 0-3-1h-1-1l-1 1c-1 1-2 1-3 1s-2-1-3-1c-1-1-1-2-2-2h0c-1-1 1-1 1-2v-1l1-1h1v3-1h1c0-1 1-1 1-2s-1 0-2-1h-1c-1-1-2 0-4-1h0c1-1 1-1 1-2h-1c0-1 0-1-1-2l-1-2v-3c0-2 1-4 0-5v-1h0 0v-2h1 3s1 0 1-1v-1z" class="K"></path><path d="M287 669s1 0 1-1h2l1-1h0v2l-1 1c-1 0-2 0-3-1z" class="H"></path><path d="M283 659v-1l1 1s1 1 2 1h0c0 1 1 2 1 2v1h-2c-1-1-2-2-2-4h0z" class="J"></path><path d="M286 657l1 1v1 2l1 1v1l-1-1s-1-1-1-2h0c-1 0-2-1-2-1l1-1h0l1-1z" class="H"></path><path d="M277 665l1-1h1v3h-1c0 1 1 2 1 2 1 1 2 1 3 1h0c1 0 1 0 2-1h0 1l-1 1c-1 1-2 1-3 1s-2-1-3-1c-1-1-1-2-2-2h0c-1-1 1-1 1-2v-1z" class="F"></path><path d="M296 655c0 1 1 2 1 3 1 1 1 2 1 2l-3 3c-2 0-2-1-4-2-1 0-1 0-1-1h0 1l1 1c1-1 3-1 4-2 0-2 0-2-1-3h-1c0 1-1 2-1 2-1 0-2-1-2-1h0v-4-1h0l1-1h1c1-1 1-1 2-1h1v1l-2 1h0c-1 0 0 1 0 2h1 0v1h1z" class="H"></path><path d="M277 643h4v2h-1l2 2c0-1 1-1 1-1h1l-1 2h0c-1 2-1 4-2 5 0 2 0 4-1 5h0c-1-1-1-2-1-3h-1v1 2h-1v-2h-1v-1c-1-1-2-1-3-1 1 1 1 3 0 4l-1-2v-3c0-2 1-4 0-5v-1h0 0v-2h1 3s1 0 1-1v-1z" class="P"></path><path d="M278 648v-2c1 0 1 0 2-1l2 2-4 1z" class="I"></path><path d="M277 643h4v2h-1c-1 1-1 1-2 1v2c-2 0-5 0-6-1h0v-2h1 3s1 0 1-1v-1z" class="H"></path><path d="M273 654l1-1c1-1 1-1 2-1l1 1h0c0-1 0-1-1-1v-1c1 0 2-1 3-1v2h0c1-1 1-3 2-3 1-1 1-1 2-1-1 2-1 4-2 5 0 2 0 4-1 5h0c-1-1-1-2-1-3h-1v1 2h-1v-2h-1v-1c-1-1-2-1-3-1z" class="S"></path><path d="M278 656s0-1-1-1h0v-1l2 1c0-1 1-1 2-2 0 2 0 4-1 5h0c-1-1-1-2-1-3h-1v1z" class="B"></path><path d="M260 77h1v-1h1 2c1 0 1-1 2-1 0 0 0-1 1-1h0c1-1 3-1 4-1 1-1 1-1 2-1 4 0 6 1 9 2l3 3c1 2 1 3 0 4l-1 1v1l-1 2-2 1-1-2c-1 1-1 2-1 3 1 1 1 1 1 2h-3-1v2c-1 1-2 0-3 1l-1-1v2c-1 0-2 0-3-1l-2-2c1 0 1-1 2-1h-1c-1-1-2-2-3-2h-1v-1l1-1h0c-1-1-1 0-3 1h-2v2h-1l1 1v1h-1c-1 0-2 1-3 0v-1h-2-1v-1c-1 0-1 1-2 1 0 0-1 1-2 1-1 1-1 1-2 1h-1 0v-3h-1 0c0-1 1-1 1-2 1 1 1 1 2 1l3-3c1 0 2 0 2-1 1-1 1-2 1-3-2 1-7 3-8 3l-1-1c1 0 1-1 2-1l-4-1 5-1c1 0 2 0 3 1 1-1 2-2 3-2s2-1 3-1h3z" class="U"></path><path d="M248 79c1 0 2 0 3 1-2 1-4 2-6 2 1 0 1-1 2-1l-4-1 5-1z" class="J"></path><path d="M283 77c1 1 1 1 0 2 0 1-1 1-2 1l-1 2c-1-2-2-2-3-2v-1c1 0 1-1 2-1h0v-1l1 1c2 0 2-1 3-1z" class="L"></path><path d="M254 80l9-3h1v3h-2c-1 0-1 0-2 1h0l-1-1c-1 0-1 1-2 1-1 1-2 2-4 2 1-1 1-2 1-3z" class="N"></path><path d="M269 74c2-1 3-1 4-1 2-1 6 0 7 1l1 1c1 0 1 1 2 2-1 0-1 1-3 1l-1-1v1h-5 0-3v-2l-2-2z" class="O"></path><path d="M273 73c1 0 2 0 2 1s-1 1-2 2h-1l-1-1h1c1-1 1-1 1-2z" class="f"></path><path d="M281 75c1 0 1 1 2 2-1 0-1 1-3 1l-1-1v1h-5v-1c2-1 4-1 6-1l1-1z" class="K"></path><path d="M267 76c1-1 1-2 2-2l2 2v2h3 0 5 0c-1 0-1 1-2 1v1c-3 0-6 0-8 2 0 1-1 2-1 2 0 1 0 1-1 1 0 0-1-1-2-1l-1-1v-1h3l-1-2h0c-1-1 0-3 1-4z" class="V"></path><path d="M266 80h0c-1-1 0-3 1-4 0 1 1 1 1 2h1 2 3c-1 1-2 1-3 1l-2 2c-1-1-1-1-1-2h-1l-1 1z" class="I"></path><path d="M267 76c1-1 1-2 2-2l2 2v2h-2-1c0-1-1-1-1-2z" class="F"></path><path d="M268 84s1-1 1-2c2-2 5-2 8-2 1 0 2 0 3 2h0v1 1c-1 1-1 2-1 3h-1 0-1l-1-1c-1 1-1 1-2 1l-2-1v-1h0l-2 1h-1c-1 0-1 0-2-1 1 0 1 0 1-1z" class="S"></path><path d="M275 82h0c1 0 1 1 1 2v1h-1-1c0-1 0-2 1-3z" class="F"></path><path d="M275 82c2 0 4 1 5 0v1 1c-1 1-1 2-1 3h-1 0-1v-1c1-1 1-1 0-2h-1c0-1 0-2-1-2z" class="W"></path><path d="M268 84s1-1 1-2c2-2 5-2 8-2 1 0 2 0 3 2h0c-1 1-3 0-5 0h0c-1 0-2 0-3 1h-1c-1 0-1 0-1 1l-1 1h-1v-1z" class="Y"></path><path d="M262 84c1-1 1-1 2-1l1 1c1 0 2 1 2 1 1 1 1 1 2 1h1l2-1h0v1l2 1c1 0 1 0 2-1l1 1h1 0 1c1 1 1 1 1 2h-3-1v2c-1 1-2 0-3 1l-1-1v2c-1 0-2 0-3-1l-2-2c1 0 1-1 2-1h-1c-1-1-2-2-3-2h-1v-1l1-1h0c-1-1-1 0-3 1v-2z" class="O"></path><path d="M269 89c1 1 1 2 2 3l1-1-2-2v-1h1c1 1 1 2 1 3h0v2c-1 0-2 0-3-1l-2-2c1 0 1-1 2-1zm-16-6c2 0 3-1 4-2 1 0 1-1 2-1l1 1h0c1-1 1-1 2-1l2 2v1c-1 0-1 0-2 1v2h-2v2h-1l1 1v1h-1c-1 0-2 1-3 0v-1h-2-1v-1c-1 0-1 1-2 1 0 0-1 1-2 1-1 1-1 1-2 1h-1 0v-3h-1 0c0-1 1-1 1-2 1 1 1 1 2 1l3-3c1 0 2 0 2-1z" class="J"></path><path d="M248 87v2h1c2-1 6-3 8-5h0c-1 1-2 2-2 3s-1 1-1 2h-1v-1c-1 0-1 1-2 1 0 0-1 1-2 1-1 1-1 1-2 1h-1 0v-3h-1 0c0-1 1-1 1-2 1 1 1 1 2 1z" class="C"></path><path d="M257 84h1c1 1 1 2 1 3v1l1 1v1h-1c-1 0-2 1-3 0v-1h-2c0-1 1-1 1-2s1-2 2-3z" class="Q"></path><path d="M260 81c1-1 1-1 2-1l2 2v1c-1 0-1 0-2 1v2h-2v2h-1v-1c0-1 0-2-1-3h-1 0c0-1 2-2 3-3h0z" class="D"></path><path d="M260 84h2v2h-2v-2z" class="H"></path><path d="M260 81c1-1 1-1 2-1l2 2v1c-1 0-1 0-2 1h-2l-1-2 1-1z" class="M"></path><path d="M244 632c1 0 2 0 3 1l4 3 2 1h-2c1 1 1 2 1 2 0 1 0 1-1 1 0 1-1 1-2 1s-3 2-3 3l-1 2h0v2c0 2 0 3 1 5l-1 1c-2 0-4 0-6-1-6-1-11-6-17-7v1c-7 0-13-1-19-2l1-1h-1 0l2-1 8 2-1-2-1-1v-1c1 0 1-1 2-1s2-1 3-1l1-1 2-1 2-1c2-1 4-1 6-2 1 0 2-1 2-1h3 1 2v1s0 1-1 1c2 0 3 0 5-1 1-1 3-2 5-2z" class="H"></path><path d="M216 639l1-1 1 1h0c1 1 1 2 2 3h0l-4-2v-1z" class="Y"></path><path d="M216 639v1 1c1 1 3 2 4 2v1c-2 0-5-2-7-3h0c0 1 0 1 1 1 1 1 3 2 5 3h-6l-1-2-1-1v-1c1 0 1-1 2-1s2-1 3-1zm29 12l-1 1c-3 0-5-1-7-2s-4-1-6-2-3-3-5-4c-1-1-2-1-3-2v-1c3 1 6 4 8 5 1 0 3 1 4 1 2 1 3 2 5 3 2 0 4 0 5 1z" class="O"></path><path d="M229 633h3 1 2v1s0 1-1 1l-1 1 1 1-3 2s-1 0-1-1h-2-1v2h-1l-1-2h-1v2h-3 0c-2 0-2-1-3-1l-1-1 2-1 2-1c2-1 4-1 6-2 1 0 2-1 2-1z" class="a"></path><path d="M229 633h3 1 2v1s0 1-1 1l-1 1h0v-1h-1-1c-1-1-2 0-4-1 1 0 2-1 2-1z" class="E"></path><path d="M244 632c1 0 2 0 3 1l4 3 2 1h-2c1 1 1 2 1 2 0 1 0 1-1 1 0 1-1 1-2 1s-3 2-3 3l-1 2h0v2c0 2 0 3 1 5l-1 1v-3c-1-1-3-1-5-1-2-1-3-2-5-3 0 0-1-1-1-2-1 0-1-2-1-2h0v-2-1h0c0-1 0-2 1-3h0 0l-1-1 1-1c2 0 3 0 5-1 1-1 3-2 5-2z" class="O"></path><path d="M237 637l2-1 1 1c0 1-1 2-2 3-1-1-1-2-1-3z" class="D"></path><path d="M246 643v-1c-1 0-2 1-3 1h0-1c0-1 1-1 2-2v-1l-2-2h1 1v-1h0c-1-1-2-1-2-2v-1c2 0 3 0 4 1h1c1 1 2 1 2 1h2l2 1h-2c1 1 1 2 1 2 0 1 0 1-1 1 0 1-1 1-2 1s-3 2-3 3v-1z" class="W"></path><path d="M233 640c1-1 2-2 4-3 0 1 0 2 1 3v1l2 1c-1 1-1 2-1 2 0 2-1 3 1 4 1 1 2 1 3 1 1-1 1-2 0-3v-1h0c1 0 2-1 3-2v1l-1 2h0v2c0 2 0 3 1 5l-1 1v-3c-1-1-3-1-5-1-2-1-3-2-5-3 0 0-1-1-1-2-1 0-1-2-1-2h0v-2-1z" class="C"></path><path d="M233 640c1-1 2-2 4-3 0 1 0 2 1 3v1l-1 1 1 1v1h-1-2c-1-1-1-1-1-2v-1h0c-1 0-1 1-1 2h0v-2-1z" class="G"></path><path d="M100 321h0v2 4l1 1v1h0c1 2 2 6 1 8 0 1-1 1-1 2h0 3c0 1 0 2-1 2h0c-1 1-2 1-2 2h0l-1 3c-1 1-1 1-1 2h0c1 1 1 2 2 2h0-2 0-3v1c3 0 7-1 9 0H91v2c0 1 0 1-1 1h-3c-1 0-1-1-1-1h-1-1l-1 1c-2 0-3 2-5 3h-1l-2 1c0 1-2 3-4 4h-1v-1h0v-1l3-6c-1 0-1 1-2 1 0-1 1-2 0-3h0c-2 0-5 6-7 5v-2l3-3c1-1 1-1 2-1l1-1-1-2c1 0 2-1 3-2h0c-3 1-5 2-7 3s-3 1-5 1c0-1 0-1 1-1 2-2 3-3 5-4h1c2-1 2-2 3-3 3-1 5-4 6-6h0l2-1h1c3-2 6-3 9-3 2 0 3 1 5 2h0l-2-2c-1 0-1 0-1-2h1v1l1-1s1-1 2-1v-1h3 0 1c0-2 0-2 1-3 0-1-1-2 0-3v-2l1 1z" class="I"></path><path d="M71 352c2 0 2-1 3 0 0 1 0 1-1 2-1 0-1 1-2 1 0-1 1-2 0-3z" class="O"></path><path d="M95 344c0 1 1 1 1 2l-1 1v2h1l-1 1h-3l-1-1c-1 1-1 1-2 1v-2-1h-1c1-1 3-1 4-1l3-2z" class="J"></path><path d="M95 344c0 1 1 1 1 2l-1 1h0c-1-1-1-1-2 0l-1-1 3-2z" class="F"></path><path d="M80 353h1c0-1 0-1 1-2h9v2c0 1 0 1-1 1h-3c-1 0-1-1-1-1h-1-1l-1 1c-2 0-3 2-5 3h-1l-2 1c1-2 4-4 5-5z" class="Z"></path><path d="M70 361c2-3 4-6 5-9 2-1 2 0 3 1v-1c1-1 1-1 2-1v2c-1 1-4 3-5 5 0 1-2 3-4 4h-1v-1z" class="Y"></path><path d="M101 339h3c0 1 0 2-1 2h0c-1 1-2 1-2 2h0l-1 3c-1 1-1 1-1 2h0v1h-2v-1l-1 1h-1v-2l1-1c0-1-1-1-1-2 2-1 3-3 5-4 1 0 1 0 1-1z" class="Z"></path><path d="M84 337v1c-1 0-1 1-2 1-1 1-1 2-1 3s0 1-1 2c1 0 1 1 1 2 1 1 2 1 2 2h1c1 0 1-1 1-1h3 0 1v1l-1 2h-1c-1-1-1-1-1-2v1l-1 1h-3 0c-1-1-1-2-2-2 0-1-1-2-1-3l-1 1c0 1 1 1 1 1 0 1 1 2 1 3l-1 1-1-2-1-1h-1c0 1 1 1 1 2h-2v-2c0-1 1-1 2-1v-1h0c0-2 0-3 1-5 2-2 3-3 6-4z" class="F"></path><path d="M84 337v1c-1 0-1 1-2 1-1 1-1 2-1 3s0 1-1 2h-1-1v1-4c2-2 3-3 6-4z" class="Z"></path><path d="M100 321h0v2 4l1 1v1h0c1 2 2 6 1 8 0 1-1 1-1 2h0c0 1 0 1-1 1-2 1-3 3-5 4l-3 2c-1 0-3 0-4 1h0-3s0 1-1 1h-1c0-1-1-1-2-2 0-1 0-2-1-2 1-1 1-1 1-2s0-2 1-3c1 0 1-1 2-1s1 0 1 1h0l-1 1c0 1 0 1 1 2l2 2c1 0 1 1 2 1 2 0 4-1 6-2h0c2-1 3-2 4-4 2-1 1-6 1-8l-3-3h0 1c0-2 0-2 1-3 0-1-1-2 0-3v-2l1 1z" class="B"></path><g class="K"><path d="M85 342l2 2c1 0 1 1 2 1h-4c-1-1-1-1-1-2 0 0 0-1 1-1z"></path><path d="M82 339v3c1 1 1 1 1 2s2 2 2 3c0 0 0 1-1 1h-1c0-1-1-1-2-2 0-1 0-2-1-2 1-1 1-1 1-2s0-2 1-3z"></path></g><path d="M94 328h3l3 3c0 2 1 7-1 8-1 2-2 3-4 4h0c-2 1-4 2-6 2-1 0-1-1-2-1 1 0 2-1 3-1l2-1c3-1 3-3 5-5l-2-2h0c-1 0-1 0-2-1h0l-2-2c-1 0-1 0-1-2h1v1l1-1s1-1 2-1v-1z" class="Z"></path><path d="M94 328c0 1 0 1 1 2 1 0 1 0 2 1s1 2 1 3v2l-1 1-2-2h0c-1 0-1 0-2-1h0l-2-2c-1 0-1 0-1-2h1v1l1-1s1-1 2-1v-1z" class="F"></path><path d="M95 335h0l2 2c-2 2-2 4-5 5l-2 1c-1 0-2 1-3 1l-2-2c-1-1-1-1-1-2l1-1h0c0-1 0-1-1-1v-1c-3 1-4 2-6 4-1 2-1 3-1 5h0v1c-1 0-2 0-2 1v2h2c-1 0-2 1-3 1l-1-2-1 1c-1 0-1 1-2 1h-1 0l1-1-1-2c1 0 2-1 3-2h0l2-2v-1h0c4-4 8-6 13-8 3 1 5 1 7 4h1v-4z" class="K"></path><path d="M74 343h1c0 1 0 1 1 1v2h-1l-1-1v-1-1h0z" class="F"></path><path d="M84 337h3 2c2 0 2 1 3 2v2 1l-2 1h0c-1-2-2-3-2-5h-2l-1 1c0-1 0-1-1-1v-1z" class="O"></path><path d="M86 338h2c0 2 1 3 2 5h0c-1 0-2 1-3 1l-2-2c-1-1-1-1-1-2l1-1h0l1-1z" class="Y"></path><path d="M86 338h2l-2 2h-1v-1h0l1-1z" class="i"></path><path d="M79 335c3-2 6-3 9-3 2 0 3 1 5 2 1 1 1 1 2 1v4h-1c-2-3-4-3-7-4-5 2-9 4-13 8h0v1l-2 2c-3 1-5 2-7 3s-3 1-5 1c0-1 0-1 1-1 2-2 3-3 5-4h1c2-1 2-2 3-3 3-1 5-4 6-6h0l2-1h1z" class="Y"></path><path d="M65 349c0-1 1-2 2-2 2-2 4-3 7-4v1l-2 2c-3 1-5 2-7 3z" class="Z"></path><path d="M201 93c1 0 3 1 5 0l1 1-6 3 1 1h0l-7 4c1-1 3-1 4-2v1l1 1-3 1-3 2-6 3-4 3c-3 1-6 4-8 6l-3 3v2c-1 0-1 0-1 1s-1 1-1 2c-1 1 0 1-1 1l-1 1v2c-1 1-1 2-1 3v1h2 1v-3l1-1c0 1 1 2 1 3h0l-1 1h1c-1 1-2 1-2 2 0 0-1-1-2 0v1h0l-3-2h0c-1 0-1 0-2 1-2-1-2-1-3-1h-3c-1 0-2 0-3 1l-2 1c0-1 0-2 1-2-1 0-1 0-2-1-2 2-4 5-6 6h-1c1-1 1-2 2-3l-1-1-14 15h2c-1 2-5 6-7 8-6 8-12 17-17 25-8 12-14 26-19 39l-1-1s-1 0-1 1-1 3-2 4v1c0 2-1 5-3 7h0v-1l4-10-1-2c1-2 3-6 4-9 0-1 0-1 1-2 0-1 1-2 1-3 1-1 1-3 2-4l4-7c0-2 0-3 1-4 2-5 8-10 8-15 1-1 2-2 2-4 1-1 2-2 3-4l9-12c3-4 6-8 10-12 15-17 33-32 54-44 4-3 9-5 15-8z" class="d"></path><path d="M116 171l-1-1h-1v-1c1 0 1-1 2-2s3-4 4-5h1 0 1c-2 3-5 6-6 9z" class="g"></path><path d="M158 124v1l-1 2h1l-6 6c-2 2-4 5-6 6h-1c1-1 1-2 2-3l-1-1c1-1 10-9 12-11z" class="C"></path><path d="M181 107l1 1c-1 0-1 1-2 1s-1 1-2 1c-3 2-5 4-8 6v1c-4 4-8 7-12 10h-1l1-2v-1c7-6 15-11 23-17z" class="R"></path><path d="M181 107c3-2 6-3 9-5l11-5 1 1h0l-7 4-25 15v-1c3-2 5-4 8-6 1 0 1-1 2-1s1-1 2-1l-1-1zm-49 43h2c-1 2-5 6-7 8-6 8-12 17-17 25-8 12-14 26-19 39l-1-1s-1 0-1 1-1 3-2 4v1c0 2-1 5-3 7h0v-1l4-10c3-9 7-19 12-27 4-9 10-17 16-25 1-3 4-6 6-9l10-12z" class="B"></path><path d="M195 102c1-1 3-1 4-2v1l1 1-3 1-3 2-6 3-4 3c-3 1-6 4-8 6l-3 3v2c-1 0-1 0-1 1s-1 1-1 2c-1 1 0 1-1 1l-1 1v2c-1 1-1 2-1 3v1h2 1v-3l1-1c0 1 1 2 1 3h0l-1 1h1c-1 1-2 1-2 2 0 0-1-1-2 0v1h0l-3-2h0c-1 0-1 0-2 1-2-1-2-1-3-1h-3c-1 0-2 0-3 1l-2 1c0-1 0-2 1-2-1 0-1 0-2-1l6-6c4-3 8-6 12-10l25-15z" class="c"></path><path d="M154 134l13-11 9-6-3 3v2c-1 0-1 0-1 1s-1 1-1 2c-1 1 0 1-1 1l-1 1v2c-1 1-1 2-1 3v1h2 1v-3l1-1c0 1 1 2 1 3h0l-1 1h1c-1 1-2 1-2 2 0 0-1-1-2 0v1h0l-3-2h0c-1 0-1 0-2 1-2-1-2-1-3-1h-3c-1 0-2 0-3 1l-2 1c0-1 0-2 1-2z" class="L"></path><path d="M161 130h-1l1-1c1 0 1 0 2-1h2c1-1 2-2 2-3l2-2h1c1 0 1-1 1-2h2v-1 2c-1 0-1 0-1 1s-1 1-1 2c-1 1 0 1-1 1l-1 1v2l-3-1-1 1v1 1c-1 0-1-1-2-1h0v3h0-1c-1-1-1-1-1-2v-1z" class="C"></path><path d="M161 130v1c0 1 0 1 1 2h1 0v-3h0c1 0 1 1 2 1v-1-1l1-1 3 1c-1 1-1 2-1 3v1h2 1v-3l1-1c0 1 1 2 1 3h0l-1 1h1c-1 1-2 1-2 2 0 0-1-1-2 0v1h0l-3-2h0c-1 0-1 0-2 1-2-1-2-1-3-1h-3c0-1 0-1 1-2h1l1-2z" class="E"></path><path d="M62 385c1-3 0-6 0-8v-8h1v1h0c1 2 0 4 1 6v1h0l1 6v1h0c0 1 1 2 1 3v1 3h1v-6h0c0 5 0 9 1 13 0 3 0 7 2 10h-1l-1 1v1c0 1 1 1 1 2l2 1h0c1 1 1 2 1 3 1 0 1 0 2 1 0 0 1 1 1 2 0 0 0 1 1 1l1 2h1c1 1 1 1 3 1h2-1 0c1 1 1 1 1 2h-3v1h0l-1 1v1h0l-2 1v2l1 1s-1 1-1 2c0 0 1 1 0 1 0 1 0 1-1 2l-1 2v1h-1l8 29h-1c1 2 3 5 2 8 0 4 3 9 4 13 10 24 22 46 38 67 2 4 5 8 8 11 1 0 1 0 2 1h0c0 1 1 2 2 3 1 0 1 2 2 2 1 2 2 3 3 4v-1l2 1c1 1 2 2 4 3 1 1 2 2 3 4h0v-4c1 2 1 3 1 4-1 0-1 1-1 1 1 2 1 2 1 3l2 7 1 4h-1c-1-4-3-7-6-10v-1c-2-2-4-3-6-5-4-5-8-11-12-15-9-11-17-23-24-35-3-5-6-9-8-14l-16-36c-3-8-5-16-8-23-3-8-4-16-6-24l-5-30-1-22z" class="Y"></path><path d="M63 370c1 2 0 4 1 6v1h0l1 6v1 6c1 3 1 7 2 10-1 2-1 3-1 5-1-3-1-6-2-9v-8l-1-18h0z" class="S"></path><path d="M67 413v-3h1c0 1 1 1 1 2l2 1h0c1 1 1 2 1 3s-1 2-1 3l-1-1c1 2 1 3 1 5l-2 2-2-11v-1z" class="C"></path><path d="M67 413v-3h1c0 1 1 1 1 2s0 1 1 3l-2 1c0-1 0-2-1-2v-1z" class="L"></path><path d="M69 412l2 1h0c1 1 1 2 1 3s-1 2-1 3l-1-1v-3c-1-2-1-2-1-3zm-4-28h0c0 1 1 2 1 3v1 3h1v-6h0c0 5 0 9 1 13 0 3 0 7 2 10h-1l-1 1v1h-1v3c-1-3-1-5-1-8 0-2 0-3 1-5-1-3-1-7-2-10v-6z" class="b"></path><path d="M67 400c0 3 0 5 1 8h1l-1 1v1h-1v3c-1-3-1-5-1-8 0-2 0-3 1-5z" class="E"></path><path d="M133 568c1 0 1 0 2 1h0c0 1 1 2 2 3 1 0 1 2 2 2 1 2 2 3 3 4v-1l2 1c1 1 2 2 4 3 1 1 2 2 3 4h0v-4c1 2 1 3 1 4-1 0-1 1-1 1 1 2 1 2 1 3l2 7 1 4h-1c-1-4-3-7-6-10v-1c-2-2-4-3-6-5 1 0 2 0 3 1v-3l-12-14z" class="V"></path><path d="M145 582l5 8h-1l-1-1c-2-2-4-3-6-5 1 0 2 0 3 1v-3z" class="J"></path><path d="M142 578v-1l2 1c1 1 2 2 4 3 1 1 2 2 3 4h0v-4c1 2 1 3 1 4-1 0-1 1-1 1 1 2 1 2 1 3-1 0-1-2-2-2-2-4-5-6-8-9z" class="B"></path><path d="M69 425l2-2v2c0 5 2 10 3 15l8 29h-1c1 2 3 5 2 8-2-4-3-8-4-12-3-9-5-18-7-28l-3-12z" class="G"></path><path d="M72 416c1 0 1 0 2 1 0 0 1 1 1 2 0 0 0 1 1 1l1 2h1c1 1 1 1 3 1h2-1 0c1 1 1 1 1 2h-3v1h0l-1 1v1h0l-2 1v2l1 1s-1 1-1 2c0 0 1 1 0 1 0 1 0 1-1 2l-1 2v1h-1c-1-5-3-10-3-15v-2c0-2 0-3-1-5l1 1c0-1 1-2 1-3z" class="c"></path><path d="M75 426h0l1 1c-1 1-1 1-1 2l1 1v3c-1-1-2-2-2-3-1-1 0-3 1-4z" class="C"></path><path d="M76 427h1v2 2l1 1s-1 1-1 2c0 0 1 1 0 1 0 1 0 1-1 2l-1-1c-1 0-1-1-1-1l1-1s1 0 1 1l1-1-1-1v-3l-1-1c0-1 0-1 1-2z" class="P"></path><path d="M70 418l1 1h0 2c0 1 1 2 1 2v3h-1v2 1h-1v-3c-1 0-1 0-1 1v-2c0-2 0-3-1-5z" class="U"></path><path d="M72 416c1 0 1 0 2 1 0 0 1 1 1 2 0 0 0 1 1 1l1 2h1c1 1 1 1 3 1h2-1 0c1 1 1 1 1 2h-3v1h0l-1 1v1h0l-2 1v-2h-1l-1-1h0c-1 0-1-1-1-2v-3s-1-1-1-2h-2 0c0-1 1-2 1-3z" class="G"></path><path d="M81 423h2-1 0c1 1 1 1 1 2h-3v1h0l-1 1v1c0-1 0-1-1-2v-2h1c1 0 1-1 2-1z" class="S"></path><path d="M72 416c1 0 1 0 2 1 0 0 1 1 1 2 0 0 0 1 1 1l1 2c0 1-1 1-1 2l-1 1v-3c0-1 0-1-1-1h0s-1-1-1-2h-2 0c0-1 1-2 1-3z" class="L"></path><path d="M320 610l-1 1c1 0 1 0 2 1 0 1 0 1-1 2h2 2c1 0 2-1 3-2h0l-2 2-2 1v1h2v1h0-3v1c1 1 2 2 3 2 0 1 0 1 1 1 1 2 1 3 2 5l-1 1v2c0 1 0 1 1 1h1l-2 2 1 1 1-1v1h1v1c1 1 1 1 2 1v1h-2v1 1c1 1 1 1 1 2l-2 2 1 1-6 3c-2 1-7 4-8 5l-1 1c-2 1-5 2-8 2l-1-1v-1c-1 2-3 3-4 3-2 1-3 0-5-1v-2h-1l2-3-3-2c-1 0-2 1-3 1-2 1-4 3-5 5 0 1-1 3-1 4l-1 1h0l-1 1-1-1v1h0-1l-1 2h-1v-3c1-1 1-3 1-5 1-1 1-3 2-5h0l1-2c1 0 3-2 3-3 1 1 1 1 2 1l2-3h0c2-2 5-4 7-6 0 0 1-2 2-2l9-11c1 0 1-1 1-2-2 0-5 2-7 2-1-2-1-2-2-3 1-1 2-1 2-2l4-1s1-1 2-1l1-1c0-1 0-1-1-1l8-2c1-1 1-1 3-1z" class="M"></path><path d="M319 611c1 0 1 0 2 1 0 1 0 1-1 2h0c-1-1-2-1-2-2l1-1z" class="O"></path><path d="M285 657c-1-1-1-2-1-3 1-1 1-3 2-5h0c1-1 1-2 2-2h2c-2 1-3 3-4 4v1h0c0 2-1 4-1 5z" class="H"></path><path d="M311 616c1-1 2-1 3-2 1 0 2-1 3-2h0c-1 2-3 4-5 5-1 0-1 1-2 1h-1 0c0-1 1-2 2-2z" class="U"></path><path d="M324 614c1 0 2-1 3-2h0l-2 2-2 1v1h2v1c-3 0-5 0-7 1h0 0c-2 1-3 2-4 2l6-6h0 2 2z" class="Q"></path><path d="M307 616h4c-1 0-2 1-2 2h0 1c1 0 1-1 2-1l-2 3c-2 0-5 2-7 2-1-2-1-2-2-3 1-1 2-1 2-2l4-1z" class="L"></path><path d="M291 641h0c0 2-1 3-2 4h-1c-3 4-6 8-5 14h0 0-1l-1 2h-1v-3c1-1 1-3 1-5 1-1 1-3 2-5h0l1-2c1 0 3-2 3-3 1 1 1 1 2 1l2-3z" class="S"></path><path d="M283 648l1 1-2 5v5l-1 2h-1v-3c1-1 1-3 1-5 1-1 1-3 2-5h0z" class="J"></path><path d="M290 647c2-1 3-2 5-3s4-1 6-1c1 1 2 2 2 3h0l-2-2h-2v1h2l-1 1-5 1c-1 0-2 1-3 1-2 1-4 3-5 5 0 1-1 3-1 4l-1 1h0v-1c0-1 1-3 1-5h0v-1c1-1 2-3 4-4z" class="E"></path><path d="M314 620c1 0 2-1 4-2h0c-1 2-3 4-3 6h0c0 1 0 1-1 1 1 1 1 1 1 2 0 2-1 2-2 3h-1l-1 2c1 0 1 0 1 1h-1c-2 0-4-1-5 0h-2l10-13z" class="V"></path><path d="M314 625c1 1 1 1 1 2 0 2-1 2-2 3h-1l-1 2h-1v-1c0-1 1-3 1-3 1-1 3-2 3-3z" class="L"></path><path d="M300 646l1-1h-2v-1h2l2 2h0c1 0 1 1 1 2h0v1l2-1v1 2 1c-1 2-3 3-4 3-2 1-3 0-5-1v-2h-1l2-3-3-2 5-1z" class="W"></path><path d="M304 650v1c0 1-1 2-2 2-1 1-1 0-2 0l-1-1h1c2 0 2 0 4-2z" class="O"></path><path d="M300 646v2c0 2-2 3-3 4h-1l2-3-3-2 5-1zm3 0c1 0 1 1 1 2h0v2c-2 2-2 2-4 2v-2c0-1 2-2 3-3v-1h0z" class="N"></path><path d="M325 617h0-3v1c1 1 2 2 3 2 0 1 0 1 1 1 1 2 1 3 2 5l-1 1v2c0 1 0 1 1 1h1l-2 2 1 1 1-1v1h1v1c1 1 1 1 2 1v1h-2c-2-1-4-1-6-1h-3l-1-1c0 2-1 1-2 1 1 1 1 2 1 2h-1c-2-2-4-3-6-4h0c0-1 0-1-1-1l1-2h1c1-1 2-1 2-3 0-1 0-1-1-2 1 0 1 0 1-1h0c0-2 2-4 3-6h0c2-1 4-1 7-1z" class="G"></path><path d="M317 628c1 1 1 1 3 1h2 0 0l-1 2c-2 0-3-1-5-3h1z" class="L"></path><path d="M311 632l1-2h1v1h2c1 1 1 1 1 3h-1v-1h-3 0c0-1 0-1-1-1z" class="D"></path><path d="M325 628c1 1 1 1 2 1 0 1 0 1 1 1h1l-2 2h-3v1c-1 0-1 0-2-1v-1h1c1 0 2-2 2-3z" class="I"></path><path d="M325 617h0-3v1c1 1 2 2 3 2 0 1 0 1 1 1 1 2 1 3 2 5l-1 1v2c-1 0-1 0-2-1-1-2-2-4-4-5v1c0 1 1 2 1 3v2h-2c-2 0-2 0-3-1h-1v-1h-1c0-1 0-1-1-2 1 0 1 0 1-1h0c0-2 2-4 3-6h0c2-1 4-1 7-1z" class="F"></path><path d="M318 622c1-1 2-1 4-1l1 1v1c-1-1-2-1-4-1v1h2v1c0 1 1 2 1 3v2h-2c-2 0-2 0-3-1h-1v-1h-1c0-1 0-1-1-2 1 0 1 0 1-1 1-1 2-1 3-2z" class="Q"></path><path d="M318 622v1c-1 1-2 2-1 3v2h-1v-1h-1c0-1 0-1-1-2 1 0 1 0 1-1 1-1 2-1 3-2z" class="D"></path><path d="M317 626l1-1v2h1c1 0 1 0 1-1l-1-1 1-1h1c0 1 1 2 1 3v2h-2c-2 0-2 0-3-1v-2z" class="M"></path><path d="M306 633c1-1 3 0 5 0h1 0c2 1 4 2 6 4h1s0-1-1-2c1 0 2 1 2-1l1 1h3c2 0 4 0 6 1v1 1c1 1 1 1 1 2l-2 2 1 1-6 3c-2 1-7 4-8 5l-1 1c-2 1-5 2-8 2l-1-1v-1-1-2-1l-2 1v-1h0c0-1 0-2-1-2 0-1-1-2-2-3l1-1c2 0 2 1 3 2l1-1h0c-2-1-3-2-5-4h1l1 1h1 2 0c-1-1-1 0-2-1l-1-1h-3c0 1-1 1-1 1v-1c1-1 1-2 3-2 1-1 1-2 2-3h2z" class="W"></path><path d="M308 634h2v1l-1 1 1 1c-1 1-1 1-2 1h0v-4z" class="S"></path><path d="M313 640c-1-1-1-2-1-3l1-1c1 0 1 0 2 1-1 2-1 2-2 3z" class="D"></path><path d="M304 648v-2c1-1 1-1 2-1 0 1 0 1 1 2v2h-1v-1l-2 1v-1h0z" class="O"></path><path d="M304 633h2 2v1 4c-1-1-1-1-2-1 0-1 1-1 1-1 0-1 0-1-1-1h-1v1h-3c1-1 1-2 2-3z" class="F"></path><path d="M306 645h0v-1l2-2h4v1h-1c0 1 1 1 0 2h-1v3c-1 1-1 1-1 2h-1v1h1l-1 1h-1l-1 1v-1-1-2h1v-2c-1-1-1-1-1-2z" class="M"></path><path d="M315 637c1 2 2 3 4 3l1-1v-1h3v2c-1 0-1-1-2-1-1 1 0 2 0 3s-1 1-1 2 0 2-1 2v-1h-1v-1h-2v-1c0-1 0-1-1-1 0 0-1-1-2-1v-1c1-1 1-1 2-3z" class="H"></path><path d="M324 635c2 0 4 0 6 1v1 1c1 1 1 1 1 2l-2 2v-1h0c-1 0-2 0-2-1h-4v-2h3c-2 0-2 0-3-1v-1l1-1z" class="F"></path><path d="M326 638v-1h1v3h1 1v-2h1c1 1 1 1 1 2l-2 2v-1h0c-1 0-2 0-2-1h-4v-2h3z" class="M"></path><path d="M313 641c1 0 2 1 2 1 1 0 1 0 1 1v1h2v1l-1 1c-1 2-3 3-4 4h-1-3c0-1 0-1 1-2v-3h1c1-1 0-1 0-2h1v-1l1-1z" class="P"></path><path d="M312 643c1 1 2 2 2 4-1 1-2 1-2 2-1 0-1 0-2-1v-3h1c1-1 0-1 0-2h1z" class="J"></path><path d="M319 646c1 0 1-1 1-2s1-1 1-2-1-2 0-3c1 0 1 1 2 1h4c0 1 1 1 2 1h0v1l1 1-6 3c-2 1-7 4-8 5l-1 1c-2 1-5 2-8 2l-1-1 1-1h1l1-1h-1v-1h1 3 1c1-1 3-2 4-4l1-1h1v1z" class="K"></path><path d="M319 646c1 0 1-1 1-2s1-1 1-2-1-2 0-3c1 0 1 1 2 1h4c0 1 1 1 2 1h0c-2 1-3 3-5 4l-3 1c-1 0-2 2-3 1l1-1z" class="B"></path><path d="M319 646c1 0 1-1 1-2s1-1 1-2-1-2 0-3c1 0 1 1 2 1h4l-1 1h-2c-1 0-1 0-2 1h0c0 1 0 3-1 4-1 0-2 2-3 1l1-1z" class="G"></path><path d="M173 132c1 0 2 0 3 1v3l1 1v1c0 1-1 1-1 1v1l-3 3c-5 5-10 9-15 14-2 2-3 5-5 6l-2 2c-1 2-3 4-4 6l-3 4c-2 0-2 0-3 1-1 2-2 3-3 4 0 1-1 1-2 2s-1 1-2 1l-2-2h0-2v-1-2-2l-1 1c-1 2-1 3-2 4 2 0 3 2 5 3-1 1-2 1-3 2 0 0 0 1-1 2l-1 1-2-1-2-2c-1 0-2 1-4 2v1 1l-1-1-1 1c-1 0-1 1-1 2l-1-2c1-2 1-3 1-5-1-1-1-1-1-3h-1l-2 5h-2l1-2s0-1-1-2c5-8 11-17 17-25 2-2 6-6 7-8h-2l14-15 1 1c-1 1-1 2-2 3h1c2-1 4-4 6-6 1 1 1 1 2 1-1 0-1 1-1 2l2-1c1-1 2-1 3-1h3c1 0 1 0 3 1 1-1 1-1 2-1h0l3 2h0v-1c1-1 2 0 2 0 0-1 1-1 2-2h-1l1-1z" class="K"></path><path d="M126 162l3-3h1 0c0 2-2 4-4 5v-2z" class="C"></path><path d="M126 162v2c-1 2-3 4-4 7h-1v-1c1-1 2-2 1-4l1-1 3-3z" class="D"></path><path d="M122 166c1 2 0 3-1 4 0 1-1 1-1 1 0 1-1 3-2 4v1c-1 1-1 1-1 2s1 1 1 2-1 4-2 5c-1-1-1-1-1-3h-1c1-6 5-11 8-16z" class="J"></path><path d="M145 142s0-1 1-1c1-1 1-2 2-2v2l1-1c1-1 2-1 2-3v4l-3 6h0c0 1 0 1-1 2v1c-1 0-1 0-1-1v-1-1c-3 1-6 6-8 8v-1c1-2 6-7 8-8h0c1-2 0-1-1-2v-2z" class="L"></path><path d="M128 169c0 2-1 1-2 3l1 1 1-1 1 1h1v3l-1 1h-1 0c-1 2-1 2-3 3v-1c-1 0-2 1-3 1v3h-2c-1 2-2 4-2 6l-1 1c0-2 0-4 1-6 2-6 5-11 10-15z" class="T"></path><path d="M126 172l1 1 1-1 1 1h1v3l-1 1h-1 0c-1 2-1 2-3 3v-1-1h-1l-1-1v-1l1-1c1-1 1-2 2-3z" class="G"></path><path d="M129 173h1v3l-1 1h-1c0-2 0-3 1-4z" class="P"></path><path d="M126 176h1s0 1 1 1c-1 2-1 2-3 3v-1-1h-1l-1-1v-1h3z" class="R"></path><path d="M123 176h3v1c-1 0-1 1-2 1l-1-1v-1z" class="Q"></path><path d="M128 177h1c-1 2-1 3-2 4 2 0 3 2 5 3-1 1-2 1-3 2 0 0 0 1-1 2l-1 1-2-1-2-2c-1 0-2 1-4 2v1 1l-1-1c0-2 1-4 2-6h2v-3c1 0 2-1 3-1v1c2-1 2-1 3-3h0z" class="M"></path><path d="M127 182h1c0 1 0 1 1 2l-1 1h-1 0c0-2-1-2 0-3z" class="J"></path><path d="M128 177h1c-1 2-1 3-2 4h0-3c-1 0-1 0-2-1h0c1 0 2-1 3-1v1c2-1 2-1 3-3h0z" class="L"></path><path d="M123 186v-4h1 0c1 1 1 1 1 2s1 1 1 2l-1 2-2-2z" class="J"></path><path d="M138 155c2-2 5-7 8-8v1 1c0 1 0 1 1 1v1c1 0 1 1 2 1v1l-3 2c1 1 1 1 2 3h-1l-1 1-2 1-3 1c0 1-1 1-2 2 0-1 0-2-1-3l2-2c-1 0-4 3-5 3 0 0 0-1 1-1 1-2 2-3 2-5z" class="O"></path><path d="M146 155c-1 1-2 1-3 1h0c1-1 1-2 2-2l1-1h0l-2-1c1-1 1-2 2-3 0 1 0 1 1 1v1c1 0 1 1 2 1v1l-3 2z" class="G"></path><path d="M146 139c2-1 4-4 6-6 1 1 1 1 2 1-1 0-1 1-1 2l-2 1c0 2-1 2-2 3l-1 1v-2c-1 0-1 1-2 2-1 0-1 1-1 1-4 4-7 7-10 11 0 2-2 4-4 5h0l-1 1h0-1l-3 3-3 3-1 1c-3 5-7 10-8 16l-2 5h-2l1-2s0-1-1-2c5-8 11-17 17-25 2-2 6-6 7-8h-2l14-15 1 1c-1 1-1 2-2 3h1z" class="P"></path><path d="M145 139h1l-7 8c-2 1-4 4-4 6h0c0 2-2 4-4 5h0c0-2 1-2 2-4l3-6c1-2 4-5 6-7l3-2z" class="B"></path><path d="M146 139c2-1 4-4 6-6 1 1 1 1 2 1-1 0-1 1-1 2l-2 1c0 2-1 2-2 3l-1 1v-2c-1 0-1 1-2 2-1 0-1 1-1 1-4 4-7 7-10 11h0c0-2 2-5 4-6l7-8z" class="X"></path><path d="M149 148l3 2c1 1 2 2 2 3v3c0 1-1 3 0 5l-2 2h1l-2 2c-1 2-3 4-4 6l-3 4c-2 0-2 0-3 1-1 2-2 3-3 4 0 1-1 1-2 2s-1 1-2 1l-2-2h0-2v-1-2-2-3h-1l-1-1-1 1-1-1c1-2 2-1 2-3 1-3 4-6 7-8 1 0 4-3 5-3l-2 2c1 1 1 2 1 3 1-1 2-1 2-2l3-1 2-1 1-1h1c-1-2-1-2-2-3l3-2v-1c-1 0-1-1-2-1v-1-1c1 0 1 0 2-1z" class="S"></path><path d="M141 161l3-1v1l-1 1c1 1 1 2 2 2l-1 1 1 2h-2 0c-2-1-4-1-5-1v-1-1c1 1 1 1 3 1 1-1 0-2 1-3l-1-1z" class="B"></path><path d="M132 166c1 0 1 1 2 2l-1 1 1 1c0-1 0-1 1-1v1h2c1 1 1 1 1 3h0c-2 0-3 0-5-1l-1-1c0-1 0-2-1-3l1-2z" class="K"></path><path d="M143 167c1 0 2 0 2 1-2 2-4 4-7 5 0-2 0-2-1-3h-2v-1h0 5 0c1-1 2-1 3-2h0z" class="J"></path><path d="M135 161c1 0 4-3 5-3l-2 2c1 1 1 2 1 3h-1c-1 1-3 1-4 0-1 1-2 1-2 2v1l-1 2h0c-1 0-1 0-3 1v1h1 1l1 2-1 1h-1l-1-1-1 1-1-1c1-2 2-1 2-3 1-3 4-6 7-8z" class="D"></path><path d="M134 163l4-3c1 1 1 2 1 3h-1c-1 1-3 1-4 0z" class="Y"></path><path d="M152 163h1l-2 2c-1 2-3 4-4 6l-3 4c-2 0-2 0-3 1v-2h-1-1-1 0c-1 1-3 0-4 0 0-1-1-1-1-2 2 1 3 1 5 1h0c3-1 5-3 7-5 3-2 5-4 7-5z" class="P"></path><path d="M140 174c1-1 3-2 4-3 3-2 4-5 7-6-1 2-3 4-4 6l-3 4c-2 0-2 0-3 1v-2h-1z" class="E"></path><path d="M131 168c1 1 1 2 1 3l1 1c0 1 1 1 1 2 1 0 3 1 4 0h0 1 1 1v2c-1 2-2 3-3 4 0 1-1 1-2 2s-1 1-2 1l-2-2h0-2v-1-2-2-3l1-1-1-2h-1-1v-1c2-1 2-1 3-1h0z" class="S"></path><path d="M134 178c1-1 1-1 0-2 1 0 2 1 3 1h1 0c-1 1-1 2-3 2l-1-1z" class="N"></path><path d="M132 181c1 0 2-1 3-1h2 1c0 1-1 1-2 2s-1 1-2 1l-2-2z" class="Q"></path><path d="M131 172v1c1 0 1 0 1 1h0l1 1v3h1l1 1c-2 0-3 2-5 1v-2-2-3l1-1z" class="B"></path><path d="M131 172v1c1 0 1 0 1 1h0l-1 1c0 1 0 1 1 2-1 0-1 1-2 1v-2-3l1-1z" class="V"></path><path d="M149 148l3 2c1 1 2 2 2 3v3c0 1-1 3 0 5l-2 2c-2 1-4 3-7 5 0-1-1-1-2-1h2l-1-2 1-1c-1 0-1-1-2-2l1-1v-1l2-1 1-1h1c-1-2-1-2-2-3l3-2v-1c-1 0-1-1-2-1v-1-1c1 0 1 0 2-1z" class="H"></path><path d="M146 159c0 1-1 2-1 3v1h0c1 0 1-1 2-1l2 2c-1 1-2 1-3 1l-1-1c-1 0-1-1-2-2l1-1v-1l2-1zm3-7c1 1 3 2 3 3 1 3-1 6-2 8-1-1-3-2-3-3v-2h1c-1-2-1-2-2-3l3-2v-1z" class="L"></path><path d="M149 156l1 2v1h-2l-1 1v-2h1l1-2z" class="G"></path><path d="M149 153v3l-1 2c-1-2-1-2-2-3l3-2z" class="W"></path><path d="M173 132c1 0 2 0 3 1v3l1 1v1c0 1-1 1-1 1v1l-3 3c-5 5-10 9-15 14-2 2-3 5-5 6h-1l2-2c-1-2 0-4 0-5v-3c0-1-1-2-2-3l-3-2c-1 1-1 1-2 1 1-1 1-1 1-2h0l3-6v-4l2-1 2-1c1-1 2-1 3-1h3c1 0 1 0 3 1 1-1 1-1 2-1h0l3 2h0v-1c1-1 2 0 2 0 0-1 1-1 2-2h-1l1-1z" class="B"></path><path d="M159 137h2 3 0c1 1 1 1 1 2v2c-1 1-2 2-3 2-2 0-3-1-5-2v-1c1-1 1-2 2-3z" class="M"></path><path d="M159 137h2 3 0c-2 1-2 2-4 1l-1-1z" class="W"></path><path d="M155 142c1-1 1-1 2-1 2 1 3 2 5 2 1 0 2-1 3-2v1h1v1l1 1v1c-1 0-1 0-1-1 0 1-1 1-1 1-1 0-3-1-3 1v2h-2l-1-2h-1-1c-1 0-1 0-2-1h-1c-1 0-2-1-2-3h0c1-1 0-1 2 0h1z" class="B"></path><path d="M155 142c1-1 1-1 2-1 2 1 3 2 5 2 1 0 2-1 3-2v1h1v1l1 1v1c-1 0-1 0-1-1h-1c-2 0-2 0-3 1-2-1-3-1-4-2l-3-1z" class="G"></path><path d="M152 142h0c1-1 0-1 2 0h1l3 1c0 1-1 2-2 2h-1-1c-1 0-2-1-2-3z" class="M"></path><path d="M151 141l1 1c0 2 1 3 2 3h1c1 1 1 1 2 1h1 1l1 2h2 1c-1 0-1 1-2 1h-1c-1 0-1 1-1 2-1 1-1 2-2 3-1 0-1 0-2 1h0l-1 1v-3c0-1-1-2-2-3l-3-2c-1 1-1 1-2 1 1-1 1-1 1-2h0l3-6z" class="E"></path><path d="M149 148s0-1 1-1l2 2v1l-3-2z" class="C"></path><path d="M158 146h1l1 2h2 1c-1 0-1 1-2 1h-1c-1 0-1 1-1 2l-1-2h-4c-1-1-2-1-2-2 1 1 2 1 2 1 2 0 3-1 4-2z" class="D"></path><path d="M173 132c1 0 2 0 3 1v3l1 1v1c0 1-1 1-1 1v1l-3 3c-5 5-10 9-15 14-2 2-3 5-5 6h-1l2-2c-1-2 0-4 0-5l1-1h0c1-1 1-1 2-1 1-1 1-2 2-3 0-1 0-2 1-2h1c1 0 1-1 2-1h-1v-2c0-2 2-1 3-1 0 0 1 0 1-1 0 1 0 1 1 1v-1l-1-1v-1h-1v-1-2c0-1 0-1-1-2h1 1l-1-1v-1l1-1 3 2h0v-1c1-1 2 0 2 0 0-1 1-1 2-2h-1l1-1z" class="C"></path><path d="M166 142h2l1 1c0 1 0 2-1 2h-1c-1 1-2 2-2 3-1-1-1-2-2-2-1 1 0 1 0 2h-1v-2c0-2 2-1 3-1 0 0 1 0 1-1 0 1 0 1 1 1v-1l-1-1v-1z" class="E"></path><path d="M163 148c0-1-1-1 0-2 1 0 1 1 2 2-2 1-4 3-5 4s-2 3-3 4h-1l-1-1h0c1-1 1-1 2-1 1-1 1-2 2-3 0-1 0-2 1-2h1c1 0 1-1 2-1z" class="I"></path><path d="M173 132c1 0 2 0 3 1v3l-1 1c0 1-1 2-1 3h-2v2c-3-2-5-4-7-5h1l-1-1v-1l1-1 3 2h0v-1c1-1 2 0 2 0 0-1 1-1 2-2h-1l1-1z" class="K"></path><path d="M176 133v3l-1 1-1-1c0-2 1-2 2-3z" class="H"></path><path d="M166 134l3 2h0v-1c1-1 2 0 2 0 0-1 1-1 2-2v2h-2-1v2c1 0 1 1 1 2 1 0 2 1 3 1h-2v2c-3-2-5-4-7-5h1l-1-1v-1l1-1z" class="S"></path><path d="M112 187l2-5h1c0 2 0 2 1 3 0 2 0 3-1 5l1 2c0 1-1 3-1 4 1 1 1 2 2 4 0-2 0-5 1-6h0l1 3-2 13-1 6-1 4-2 6c-1 2-3 4-4 6l-2 2-1 3v4l-1 1-6 4v1h-2s-1 0-1 1v2l1-1h0v1l-1 1h-1c-1 1-2 2-4 3v2c-1 1-2 2-4 3-1 2-3 3-5 5h-1l-2 2-2 7c-1 2-2 4-2 7 0 1 0 2 1 2 0 2 0 3-1 5h0c0 2-1 5-2 7v3l-2 1 1-3h-1l1-8h-1-1v-2h1v-3c0-1 0-1-1-1 1-4 2-7 3-11l1-7h0c-1 1-1 3-2 4v-6l2-8c1 0 1-1 2-1v-1c2-3 2-5 3-8 0-2 2-7 3-8 0-1 1-5 2-6 1-2 2-3 2-5 0-1 0-2 1-3l1 2-4 10v1h0c2-2 3-5 3-7v-1c1-1 2-3 2-4s1-1 1-1l1 1c5-13 11-27 19-39 1 1 1 2 1 2l-1 2h2z" class="c"></path><path d="M73 290v4 3l-2 1 1-3c0-2 1-4 1-5z" class="L"></path><path d="M75 280c0 1 0 2 1 2 0 2 0 3-1 5h0l-1-1 1-6z" class="R"></path><path d="M74 286l1 1c0 2-1 5-2 7v-4l1-4z" class="G"></path><path d="M71 282h1v1l1-2c0 1 0 5-1 6h-1-1v-2h1v-3z" class="C"></path><path d="M86 243h1c0 2 0 2 1 4l-2 3h1c-1 1-1 1-2 1l-1-1 2-7z" class="Z"></path><path d="M98 212v-1h2 0l-1 2c-1 1-1 1-1 2v1c-1 0-1 0-1 1v1h0c-1 0-1 0-2-1h0c1-1 1-2 2-3 0-1 0-2 1-2z" class="L"></path><path d="M104 199l1 2-1 1v1c-1 1-1 1-1 2h0c-1 1-1 2-1 3-1 1-1 2-2 4v-1h0-2v1c0-3 2-5 3-7v-2c1-1 2-3 3-4z" class="V"></path><path d="M90 232c1 2 2 5 1 7l-2 7-1 1c-1-2-1-2-1-4h-1c1-2 2-6 3-8 1-1 1-2 1-3z" class="J"></path><path d="M87 243l2-1h0v1c0 1-1 2 0 3l-1 1c-1-2-1-2-1-4z" class="a"></path><path d="M73 270l2-2c0-3 1-5 3-7l-5 20-1 2v-1h-1c0-1 0-1-1-1 1-4 2-7 3-11z" class="E"></path><path d="M84 234h0c2-2 3-5 3-7v-1c1-1 2-3 2-4s1-1 1-1l1 1-12 35-1-1c0-1 0-3 1-4 0-1 0-2 1-3h-1c0-4 2-7 3-11 1-1 1-3 2-4z" class="N"></path><path d="M87 250c1 0 2-1 3-1 0 0-1 1 0 2v1c1-1 1-1 1-2h1v1l1 1c0-1 1-2 2-2v1c-1 1-2 2-4 3v2c-1 1-2 2-4 3-1 2-3 3-5 5h-1l-2 2c0-4 3-7 5-10 0-1 0-1-1-1 0-2 0-4 1-5l1 1c1 0 1 0 2-1z" class="C"></path><path d="M87 250c1 0 2-1 3-1 0 0-1 1 0 2l-6 6c0-2 2-4 2-5l-1-1c1 0 1 0 2-1z" class="B"></path><path d="M92 250v1l1 1c0-1 1-2 2-2v1c-1 1-2 2-4 3v2c-1 1-2 2-4 3-1 2-3 3-5 5h-1l9-12c1-1 1-1 1-2h1z" class="F"></path><path d="M92 227h1c1 1 2 2 4 3l1 2-2 2c0 1 1 1 1 2v1l-1 1c-1 3-1 6-3 8 0 2-1 3-1 4h-1c0 1 0 1-1 2v-1c-1-1 0-2 0-2-1 0-2 1-3 1h-1l2-3 1-1 2-7c1-2 0-5-1-7l1-2c0-1 0-2 1-3z" class="f"></path><path d="M92 227h1c1 1 2 2 4 3l1 2-2 2c0 1 1 1 1 2v1l-1 1c-1 3-1 6-3 8 0 2-1 3-1 4h-1c0 1 0 1-1 2v-1c-1-1 0-2 0-2v-1c2-3 3-6 4-10-1-2 0-4-1-6 0-1-1-2-2-2 0-1 0-2 1-3z" class="S"></path><path d="M90 248c1 1 1 1 1 2s0 1-1 2v-1c-1-1 0-2 0-2v-1z" class="G"></path><path d="M96 238c-1-4-2-7-4-10l1-1c1 1 2 2 4 3l1 2-2 2c0 1 1 1 1 2v1l-1 1z" class="Y"></path><path d="M95 217c1 1 1 1 2 1h0v2c0 2 1 2 1 3 1 1 0 1 1 2v1c1 0 1 1 2 2h2l1-1v1h2v1l-1 1 1 1-1 3-1 1c-1 1-2 1-4 1 0 0 0 1-1 1h0 0v-1c-1-1-1-2-1-4l-1-2c-2-1-3-2-4-3h-1c1-2 1-5 2-6h0c1-1 0-1 0-2l1-2z" class="B"></path><path d="M105 230l1 1-1 3c-1 0-2 0-2-1 0-2 0-1 1-2 1 0 1 0 1-1zm-10-13c1 1 1 1 2 1h0v2c0 2 1 2 1 3 1 1 0 1 1 2v1c1 0 1 1 2 2h2l1-1v1c0 1 0 2-1 3-1 0-1-1-1-2-1 0-2-1-3-2-1 0-2 0-3 1v1s1 0 1 1c-2-1-3-2-4-3h-1c1-2 1-5 2-6h0c1-1 0-1 0-2l1-2z" class="D"></path><path d="M97 220c0 2 1 2 1 3 1 1 0 1 1 2-1 1-1 1-2 1h-1c0-1-1-1-1-3h1c1-1 1-2 1-3z" class="I"></path><path d="M95 217c1 1 1 1 2 1h0v2c0 1 0 2-1 3h-1c-1-1-1-1-1-2h0c1-1 0-1 0-2l1-2z" class="C"></path><path d="M74 253c1 0 1-1 2-1v-1c2-3 2-5 3-8 0-2 2-7 3-8 0-1 1-5 2-6 1-2 2-3 2-5 0-1 0-2 1-3l1 2-4 10v1c-1 1-1 3-2 4-1 4-3 7-3 11h1c-1 1-1 2-1 3-1 1-1 3-1 4l1 1-1 4c-2 2-3 4-3 7l-2 2 1-7h0c-1 1-1 3-2 4v-6l2-8z" class="O"></path><path d="M79 249h1c-1 1-1 2-1 3-1 1-1 3-1 4l1 1-1 4c-2 2-3 4-3 7l-2 2 1-7c2-4 3-10 5-14z" class="B"></path><path d="M107 204v-3c2 2 1 5 3 7v-1h0v2 1c0 1 0 5-1 6v5c1 1 1 1 1 3h1 0l2 2c-1 2-3 4-4 6l-2 2-1 3v4l-1 1-6 4v1h-2s-1 0-1 1v2l1-1h0v1l-1 1h-1v-1c-1 0-2 1-2 2l-1-1v-1c0-1 1-2 1-4 2-2 2-5 3-8l1-1v-1c0-1-1-1-1-2l2-2c0 2 0 3 1 4v1h0 0c1 0 1-1 1-1 2 0 3 0 4-1l1-1 1-3c1-3 2-5 2-8 0-2 1-4 1-6-1-4-2-8-2-13z" class="Z"></path><path d="M111 224l2 2c-1 2-3 4-4 6 0-3 2-6 2-8z" class="S"></path><path d="M99 237h0 0c1 0 1-1 1-1 2 0 3 0 4-1-1 3-3 4-5 6 0 1-1 2-1 3 0-2 0-2 1-3v-2h0v-2z" class="C"></path><path d="M99 246c0-2 4-3 4-4v-1c1-1 2-3 3-4v4l-1 1-6 4zm8-53v3c-1 2 0 5 0 7v1c0 5 1 9 2 13 0 2-1 4-1 6 0 3-1 5-2 8l-1-1 1-1v-1h-2v-1l-1 1h-2c-1-1-1-2-2-2v-1c-1-1 0-1-1-2 0-1-1-1-1-3v-2-1c0-1 0-1 1-1v-1c0-1 0-1 1-2l1-2v1c1-2 1-3 2-4 0-1 0-2 1-3h0c0-1 0-1 1-2v-1l1-1-1-2c0-1 1-2 2-4 0-1 1-1 1-2z" class="E"></path><path d="M101 223c0-1 0-1 1-2 1 0 1 1 2 2h-3z" class="G"></path><path d="M107 193v3c-1 2 0 5 0 7l-1-1-1 1h0c0 1 0 2-1 3 0 1 1 1 1 1 0 1 0 2 1 2l-1 1c1 2 2 2 2 3h-1l-3-3c-1 0-1 0-2 1 1 0 1 0 2 1h-3c1-2 1-3 2-4 0-1 0-2 1-3h0c0-1 0-1 1-2v-1l1-1-1-2c0-1 1-2 2-4 0-1 1-1 1-2z" class="I"></path><path d="M104 199c0-1 1-2 2-4 0 2 0 4-1 5v1l-1-2z" class="G"></path><path d="M100 212h3c1 0 2 1 2 2h1c1 2 1 3 0 4v1h-1-1l-6-3v-1c0-1 0-1 1-2l1-2v1z" class="H"></path><path d="M98 216v-1c0-1 0-1 1-2l1 3h1 3l1 3h-1l-6-3z" class="V"></path><path d="M97 220c2 1 2 2 4 3h3v-1l1 1v1c1-1 2-1 3-2v1c0 3-1 5-2 8l-1-1 1-1v-1h-2v-1l-1 1h-2c-1-1-1-2-2-2v-1c-1-1 0-1-1-2 0-1-1-1-1-3z" class="B"></path><path d="M105 224c1-1 2-1 3-2v1c0 3-1 5-2 8l-1-1 1-1v-1h-2v-1l-1 1v-1l-2-1h0 2c0-1 1-1 2-1v1h1c0-1-1-1-1-2z" class="V"></path><path d="M112 187l2-5h1c0 2 0 2 1 3 0 2 0 3-1 5l1 2c0 1-1 3-1 4 1 1 1 2 2 4 0-2 0-5 1-6h0l1 3-2 13-1 6-1 4-2 6-2-2h0-1c0-2 0-2-1-3v-5c1-1 1-5 1-6v-1-2h0v1c-2-2-1-5-3-7v3-1c0-2-1-5 0-7v-3l3-5v-1h2z" class="O"></path><path d="M111 200c1-2 0-6 1-7 0 2-1 6 0 7h1 1c0 3 0 5-1 8 0 0-1 0-1 1v2c0-1 0-5-1-7-1-1 0-3 0-4z" class="W"></path><path d="M112 211v-2c0-1 1-1 1-1 0 3 1 6 1 9 0 1-1 2 0 3h1l-2 6-2-2h0c1-4 1-9 1-12v-1z" class="N"></path><path d="M117 200c0-2 0-5 1-6h0l1 3-2 13-1 6-1 4h-1c-1-1 0-2 0-3v-8c1-1 1-2 1-3h0c0-1 0-1 1-1v-1c0-1 1-3 1-4z" class="a"></path><path d="M110 187h2c0 2-1 4 0 6-1 1 0 5-1 7 0 1-1 3 0 4 1 2 1 6 1 7v1c0 3 0 8-1 12h-1c0-2 0-2-1-3v-5c1-1 1-5 1-6v-1-2h0v1c-2-2-1-5-3-7v3-1c0-2-1-5 0-7v-3l3-5v-1z" class="Y"></path><path d="M110 187h2c0 2-1 4 0 6-1 1 0 5-1 7l-1-3h0v-4c-1 0-1 1-2 1v5c1 3 2 5 2 8v1c-2-2-1-5-3-7v3-1c0-2-1-5 0-7v-3l3-5v-1z" class="F"></path><path d="M110 187h2c0 2-1 4 0 6-1 1 0 5-1 7l-1-3c0-2 1-7 0-9v-1z" class="E"></path><path d="M83 354l1-1h1 1s0 1 1 1h3c-1 1-1 1-1 3h-1v1c0 1-1 2-1 3h-2v1h2l1 1h4-3v1h3c3 1 6 2 8 5 0 1-1 1-2 1h-1c1 1 1 2 1 3 0 0-1 1-2 1h0c-1-1-1-1-1-2l-1-2c-1 1-1 1-1 2l-2 2c-1 0-1 0-1 2l1 1h0c2 1 3 2 3 3v9c0 3 1 5 1 8v3l1 5 1 6c1 7 2 14 4 22 0 3 1 6 2 9l2 7c0 1 1 3 1 4 1 7 4 13 6 20l-1 1v-2h-1v3h-1l-8-25-2-9v4 3l-2-3h0c0-1 0-2-1-3 0-2 0-3-1-4v-3c-1-1-1-1-2 0l-1-1v2h0c0-1 0-1-1-1l1-2-2-2v-4h0v-1c-1-2-1-3-2-4l-1 1 1 1v1l-5-2h-2c-2 0-2 0-3-1h-1l-1-2c-1 0-1-1-1-1 0-1-1-2-1-2-1-1-1-1-2-1 0-1 0-2-1-3h0l-2-1c0-1-1-1-1-2v-1l1-1h1c-2-3-2-7-2-10-1-4-1-8-1-13h0v6h-1v-3-1c0-1-1-2-1-3h0v-1-11-5-2l-1-1h0l6-3h0v1h1c2-1 4-3 4-4l2-1h1c2-1 3-3 5-3z" class="O"></path><path d="M91 377c2 1 3 2 3 3v9c0 3 1 5 1 8v3l1 5c-1-3-2-7-3-11v-6c0-1-1-2-1-3v-6c-1 0-1-1-1-2z" class="N"></path><path d="M87 383h2c1-1 1-1 1-2 0 3 0 7 1 11v7c1 1 1 3 1 4l-1 1h0-1c-1 0-1 0-1-2v-1-2c-1-1-1-2-2-2-1-1-3-2-4-3 0-1 1-2 1-3v-1h-2v-1l1-1h-1l1-1c2-1 3-1 4-3v-1z" class="L"></path><path d="M85 389c1-1 1-2 2-3h1v3h-3 0z" class="Q"></path><path d="M83 388l2 1h0 3l1 1v1 2h-2 0c1 1 1 1 2 1 1 2 1 8 2 10h-1c-1 0-1 0-1-2v-1-2c-1-1-1-2-2-2-1-1-3-2-4-3 0-1 1-2 1-3v-1h-2v-1l1-1z" class="H"></path><path d="M85 389h3l1 1v1h-3 0-2c0-1 0-1 1-2z" class="M"></path><path d="M91 404l1-1 7 38v4 3l-2-3h0c0-1 0-2-1-3 0-2 0-3-1-4v-3c-1-1-1-1-2 0l-1-1v2h0c0-1 0-1-1-1l1-2-2-2v-4-1h1v-2l1-1c0 1-1 3 0 4v-7-1-1c0-4-1-8-1-11v-3z" class="P"></path><path d="M92 420c1 2 2 5 2 8h-2v-1-7z" class="M"></path><path d="M90 426h1v-2l1-1c0 1-1 3 0 4v1h2c1 2 1 5 1 7-1-1-1-1-2 0l-1-1v2h0c0-1 0-1-1-1l1-2-2-2v-4-1z" class="O"></path><path d="M92 433v-4h1c0 2 0 4-1 5v2h0c0-1 0-1-1-1l1-2z" class="M"></path><path d="M80 389l2 2h2c0 1-1 2-1 3 1 1 3 2 4 3 1 0 1 1 2 2v2 1c0 2 0 2 1 2h1 0v3c0 3 1 7 1 11v1 1 7c-1-1 0-3 0-4l-1 1v2h-1v1h0v-1c-1-2-1-3-2-4l-1 1 1 1v1l-5-2h-2c-2 0-2 0-3-1h-1l-1-2c-1 0-1-1-1-1 0-1-1-2-1-2-1-1-1-1-2-1 0-1 0-2-1-3h0l-2-1c0-1-1-1-1-2v-1l1-1h1v-1l2-2c-1-1-1-2-1-3l1-6 2 3 2 3 1-1h1v-2h1 0c1 1 0 1 1 0v-3-3-1-3z" class="H"></path><path d="M78 422v-1h0c2 0 3-1 5-1v1c-1 0-1 1-2 1 0 1-2 0-3 0z" class="K"></path><path d="M87 423c0-1-1-1-1-1l-1-1h-2l1-1h1c1 0 2-3 4-3 0 1-1 1-1 1 0 1 1 1 1 2-1 0-1 1-2 1l1 1-1 1z" class="I"></path><path d="M90 426c1-1 1-3 0-4 0-1-1-2-1-2l1-2-1-1h2l1 2v1 7c-1-1 0-3 0-4l-1 1v2h-1z" class="J"></path><path d="M86 409v-2l1-1h1v2s1 0 1 1c1-1 0-1 1-3h1v1c0 3 1 7 1 11v1l-1-2v-2h-4v-1h1v-1h-1c-1 0-2-1-3-1 0-1 1-2 1-2l1-1z" class="E"></path><path d="M84 412l2-1h1c0 1 0 1 1 2h-1c-1 0-2-1-3-1z" class="B"></path><path d="M71 413v-2l-1-1 1-1h1c1 1 1 2 3 3 1 0 1 0 2 1h1c1 1 1 1 3 1l1 1v1l-1-1c-1 2 0 2-1 3 1 0 1 0 2 1v1c-2-1-3-2-5-2 0 1-1 1-1 2-1 0-1-1-1-1 0-1-1-2-1-2-1-1-1-1-2-1 0-1 0-2-1-3h0z" class="J"></path><path d="M71 413c4 1 6 3 9 5 1 0 1 0 2 1v1c-2-1-3-2-5-2 0 1-1 1-1 2-1 0-1-1-1-1 0-1-1-2-1-2-1-1-1-1-2-1 0-1 0-2-1-3z" class="C"></path><path d="M74 417h1c1 0 1 0 1 1h1c0 1-1 1-1 2-1 0-1-1-1-1 0-1-1-2-1-2z" class="V"></path><path d="M72 396l2 3 2 3c2 1 3 3 3 5l2 2c0 1 0 2-1 2-1-1 0-1-1-2-1 0-1 1-2 0 0 0-1-1-2-1h0c0 1 0 1 1 2 2 0 2 2 4 3h-3c-1-1-1-1-2-1-2-1-2-2-3-3h-1l-1 1 1 1v2l-2-1c0-1-1-1-1-2v-1l1-1h1v-1l2-2c-1-1-1-2-1-3l1-6z" class="M"></path><path d="M72 396l2 3 2 3c2 1 3 3 3 5l-1-1h-2v-2h-1c0 1 0 1-1 2h0v-2c-1 0-1 0-2 1-1-1-1-2-1-3l1-6z" class="C"></path><path d="M80 389l2 2h2c0 1-1 2-1 3 1 1 3 2 4 3 1 0 1 1 2 2v2 1c0 2 0 2 1 2h1 0v3-1h-1c-1 2 0 2-1 3 0-1-1-1-1-1v-2h-1l-1 1v2l-1 1-1-1s-1 1-2 1l-1-1-2-2c0-2-1-4-3-5l1-1h1v-2h1 0c1 1 0 1 1 0v-3-3-1-3z" class="G"></path><path d="M84 404l1 3-1 1h1 1v1l-1 1-1-1-2-2 1-1h0l1-2z" class="T"></path><path d="M80 396l1 1v1h1 1l-2 2 1 1h1c0 1 1 2 1 3l-1 2h0l-1 1c0-1-1-1-1-2l-1-6v-3z" class="D"></path><path d="M78 399h1 0c1 1 0 1 1 0l1 6c0 1 1 1 1 2l2 2s-1 1-2 1l-1-1-2-2c0-2-1-4-3-5l1-1h1v-2z" class="B"></path><path d="M80 389l2 2h2c0 1-1 2-1 3 1 1 3 2 4 3h-1l1 1c-1 0-1 1-1 1-1 0-1 0-2 1 0-1 0-1-1-2h-1-1v-1l-1-1v-3-1-3z" class="L"></path><path d="M82 391h2c0 1-1 2-1 3-1 0-1-1-2-2l1-1zm-1 6l1-1c-1 0-1 0-1-1h1 1v1h2l1 1 1 1c-1 0-1 1-1 1-1 0-1 0-2 1 0-1 0-1-1-2h-1-1v-1z" class="B"></path><path d="M83 354l1-1h1 1s0 1 1 1h3c-1 1-1 1-1 3h-1v1c0 1-1 2-1 3h-2v1h2l1 1h4-3v1h3c3 1 6 2 8 5 0 1-1 1-2 1h-1c1 1 1 2 1 3 0 0-1 1-2 1h0c-1-1-1-1-1-2l-1-2c-1 1-1 1-1 2l-2 2c-1 0-1 0-1 2l1 1v1l-1 1h0l1 1-1 1c0 1 0 1-1 2h-2v1c-1 2-2 2-4 3l-1 1h1l-1 1v1h2v1h-2l-2-2v3 1 3 3c-1 1 0 1-1 0h0-1v2h-1l-1 1-2-3-2-3-1 6c0 1 0 2 1 3l-2 2v1c-2-3-2-7-2-10-1-4-1-8-1-13h0v6h-1v-3-1c0-1-1-2-1-3h0v-1-11-5-2l-1-1h0l6-3h0v1h1c2-1 4-3 4-4l2-1h1c2-1 3-3 5-3z" class="O"></path><path d="M87 371v-1l-3 1h-1 1c1-1 2-1 3-1 2-1 5-1 7-1 0 1 0 1 1 1h0-1c-1 1-1 1-1 2 0-1 0-1-1-2l-5 1z" class="d"></path><path d="M87 371l5-1c1 1 1 1 1 2l-2 2-1-1h-4 0-1-1v-1l3-1z" class="i"></path><path d="M70 384c1 0 1-1 2-1 2-4 5-7 10-9h0c-3 2-6 4-8 7v1s0 2-1 2l-1 1c0 1 0 2-1 3v-1-2l-1-1z" class="N"></path><path d="M85 373h1 0 4l1 1c-1 0-1 0-1 2l1 1v1l-1 1-1-1h-2 0v-3-1h0s-1 0-2-1h0z" class="d"></path><path d="M69 381h0c1-1 2-3 3-5-1 2-2 5-2 7-1 2-1 3-1 6l-1 1 1 4h-1v4c-1-4-1-8-1-13 0-2 1-4 0-6l1 1c0 1-1 2 0 2l1-1z" class="D"></path><defs><linearGradient id="G" x1="75.001" y1="397.367" x2="68.869" y2="386.422" xlink:href="#B"><stop offset="0" stop-color="#656563"></stop><stop offset="1" stop-color="#7d7d7e"></stop></linearGradient></defs><path fill="url(#G)" d="M70 383v1h0l1 1v2 1c1-1 1-2 1-3l1-1v11l1 2v2l-2-3c-2-2-2-4-3-7 0-3 0-4 1-6z"></path><path d="M69 394l-1-4 1-1c1 3 1 5 3 7l-1 6c0 1 0 2 1 3l-2 2v1c-2-3-2-7-2-10v-4h1z" class="G"></path><path d="M70 400v2h1c0 1 0 2 1 3l-2 2v-7z" class="U"></path><path d="M69 394l-1-4 1-1c1 3 1 5 3 7l-1 6h-1v-2l-1-6z" class="b"></path><path d="M89 364h3c3 1 6 2 8 5 0 1-1 1-2 1h-1c-1 0-2-1-3-1-2-2-7-1-10 0-1 1-2 1-3 1h0c0-2 1-2 2-3 0-1 0-1 1-2h5v-1z" class="f"></path><path d="M84 365h5c-1 1-2 1-3 2-3 1-3 1-5 3h0c0-2 1-2 2-3 0-1 0-1 1-2z" class="Y"></path><path d="M82 377c1 0 3-1 5-2v3h0 2l1 1h0l1 1-1 1c0 1 0 1-1 2h-2v1c-1 2-2 2-4 3l-1 1h1l-1 1v1h2v1h-2l-2-2v3 1 3 3c-1 1 0 1-1 0h0-1v2h-1l-1 1-2-3v-2l-1-2v-11c1 0 1-2 1-2h0c1 0 1-1 1-1 1 0 1-1 1-1l3-2c1-1 2-1 3-1z" class="J"></path><path d="M82 382h2l-1 2c-1 1-1 1-1 2l1 1-1 1h1l-1 1v1h2v1h-2l-2-2c0-2 1-3 2-4h0v-3z" class="G"></path><path d="M74 395c0-4 0-9 1-13 1 2 0 3 0 4v1l1 9-1 1c0-1-1-1-1-2z" class="I"></path><path d="M90 379l1 1-1 1c0 1 0 1-1 2h-2v1c-1 0-1 0-2-1v-1-1l5-2z" class="U"></path><path d="M85 382l2-1v1 1 1c-1 0-1 0-2-1v-1z" class="O"></path><path d="M82 377c1 0 3-1 5-2v3h0 2-3c0 1-1 1-2 1-2 0-4 1-6 2h-1c1-1 2-1 2-2 1-1 2-1 3-2z" class="Y"></path><path d="M75 387c1 1 1 2 1 3h0c0 2 0 4 1 6v1 1-2c-1-1 0-2-1-4v-1-1c0-1 1-2 1-3v8c1 1 0 2 1 3v1h0v2h-1l-1 1-2-3v-2l-1-2h1c0 1 1 1 1 2l1-1-1-9z" class="K"></path><path d="M74 397c1 1 2 2 3 4l-1 1-2-3v-2z" class="R"></path><path d="M77 387v-1s0-1 1-1c1-2 2-2 4-3v3h0c-1 1-2 2-2 4v3 1 3 3c-1 1 0 1-1 0h0-1 0v-1c-1-1 0-2-1-3v-8z" class="N"></path><path d="M83 354l1-1h1 1s0 1 1 1h3c-1 1-1 1-1 3h-1v1c0 1-1 2-1 3h-2v1h2l1 1h4-3v1 1h-5l-3 1c-1 0-2 1-3 1l-2 2-5 4-3 8h1l-1 1c-1 0 0-1 0-2l-1-1c1 2 0 4 0 6h0v6h-1v-3-1c0-1-1-2-1-3h0v-1-11-5-2l-1-1h0l6-3h0v1h1c2-1 4-3 4-4l2-1h1c2-1 3-3 5-3z" class="G"></path><path d="M75 358l2-1h1c2-1 3-3 5-3-4 5-9 7-14 11h-4 0l-1-1h0l6-3h0v1h1c2-1 4-3 4-4z" class="F"></path><defs><linearGradient id="H" x1="78.479" y1="364.331" x2="86.162" y2="352.997" xlink:href="#B"><stop offset="0" stop-color="#8d8c8e"></stop><stop offset="1" stop-color="#a8a7a7"></stop></linearGradient></defs><path fill="url(#H)" d="M85 353h1s0 1 1 1h3c-1 1-1 1-1 3h-1l-12 7-1-1h-1c4-3 9-5 11-10z"></path><path d="M65 365h0c2 0 3 0 4 1h2 0l-1 2c-1 2-2 8-3 11 1 2 0 4 0 6h0v6h-1v-3-1c0-1-1-2-1-3h0v-1-11-5-2z" class="h"></path><path d="M76 364l12-7v1c0 1-1 2-1 3h-2v1h2l1 1h4-3v1 1h-5l-3 1c-1 0-2 1-3 1l-2 2-5 4h0c-1 0-1 0-1-1s0-1 1-2h0c0-2 2-4 4-5l1-1h0z" class="D"></path><path d="M74 367l2 2-5 4h0l1-2c0-2 1-2 2-4z" class="E"></path><path d="M74 367c2-1 3-3 4-4l1 1h1l1 2c-1 0-2 1-3 1l-2 2-2-2z" class="C"></path><path d="M80 363c2-1 3-2 5-2v1h2l1 1h4-3v1 1h-5l-3 1-1-2h-1l-1-1h2z" class="E"></path><path d="M83 363h0c1-1 3-1 4-1l1 1s-1 1-2 1-2-1-3-1z" class="B"></path><path d="M80 363c2-1 3-2 5-2v1h2c-1 0-3 0-4 1h0-3z" class="N"></path><path d="M369 580c0 1-1 1-1 3 0 1 0 2 1 3s2 1 3 1 2 0 2-1h1l1 2v2 2l-1 1c-1 3-2 5-4 7 1 0 2 0 3 1-2 2-4 2-6 3l-1 2-2 1h1l1 1 3-2h1c1 0 0 1 1 2h0c0 2 2 4 4 5h1s1 0 2 1l2-1c5-1 7-5 9-9l1 1v4l2-2c2-1 2-5 3-7l1 1c-2 6-6 11-9 16 0 1 1 1 1 1l-10 10c-1 0-2 2-4 2h0c-2 0-3 1-5 2l-5 2v-1c-2 1-2 1-4 1-2 1-6 3-7 2-1 0-1 0-2-1v2h-1c0-1 0-1-1-1l-1 1s-1-1-2 0c-1 0-2 2-3 2h-3v1c-3-1-5-2-7-2l-2-2v-1c-1 0-1 0-2-1v-1h-1v-1l-1 1-1-1 2-2h-1c-1 0-1 0-1-1v-2l1-1c-1-2-1-3-2-5-1 0-1 0-1-1-1 0-2-1-3-2v-1h3 0v-1h-2v-1l2-1 2-2h0c0-1 1-2 1-2h2 3c2 0 5-2 7-3l5-5 4-2v-1h-3l1-1-1-2c2-2 4-3 5-4v-1h0 2l1-1c1 1 1 1 1 2h0l2 2c1-1 2-2 2-3v-2c1 0 1 0 2 1h0c0-1 0-2 1-2h1l1-1c0-1 0-2-1-3 1 0 2-1 2-1 2-1 3-2 4-3z" class="S"></path><path d="M365 625l1-1 1 1h0v1c-2 0-2 0-2-1h0z" class="B"></path><path d="M365 621h2v2l-1 1h-1c0-1-1-1-1-2l1-1z" class="D"></path><path d="M344 634h-1v-1h1l3-3h2c0 1-1 2-1 3h0v1c-1 0-1-1-2-1 0 0-1 1-2 1z" class="H"></path><path d="M360 621h1c1 2 2 3 4 4h0l-1 1c-2 0-2-2-3-3-2 0-2 0-3 1h-2c-1 1-2 1-3 1h-1 0l2-2-1-1c1 0 2-1 3-1l1 2h0c1 0 1-1 1-1 1-1 1-1 2-1z" class="D"></path><path d="M356 631h-1v-1h0v-1h1 0 2 0 1 1c0 1-1 1 0 2h1l1 2h-2l-1-1c-2 2-2 2-5 3-1-1-1-1-1-2 0 1 0 1-1 2-1-1-2-1-3-2h0c2-2 5-2 7-2z" class="G"></path><path d="M349 633h0c2-2 5-2 7-2l2 1-2 2c-1-1-1-1-2-1h-1c0 1 0 1-1 2-1-1-2-1-3-2z" class="Q"></path><path d="M372 623c1 1 1 1 3 1l-1 2c-2 1-3 2-4 3-3 1-4 2-6 3l-2-2v-1c3-2 2 0 5 1 0-1 1-1 1-2h0-1c-1 0-2-1-2-1v-1h2c0 1 1 1 2 1v1h1c-1-1-1-2-2-2h0v-1c2-1 1 0 3 1h0c0-1-1-2-2-2v-1h2 1z" class="G"></path><path d="M345 618c2 0 2 0 3 1 1-1 2-1 2-1 1 0 3 1 3 1v1l1-1h-1v-1h1 3 2 2c0 1 0 2-1 2v1c-1 0-1 0-2 1 0 0 0 1-1 1h0l-1-2c-1 0-2 1-3 1l1 1-2 2h0l-1 1v1 2h0v1h-1c-1-1 0-3-1-4h-1v-2c-1-1-2-3-2-4s-1-1-1-2z" class="E"></path><path d="M353 622v-2h3v1c-1 0-2 1-3 1z" class="I"></path><path d="M346 620v-1c1 0 2 1 2 1 1 0 2 0 2-1h1l1 1-1 1v1 2 1h1 0l-1 1v1 2h0v1h-1c-1-1 0-3-1-4h-1v-2c-1-1-2-3-2-4z" class="C"></path><path d="M329 632l1-1v-1c0-1 1-1 2-1v1l1 1s1 1 2 0h1c0 1 1 1 2 1 0 1 1 1 1 1 1 0 1-1 2-1l1 1v1h-3 0c-1-1-1-1-2-1h-1l2 2s-1 1 1 1h2c1 0 2-1 2-2h1 0c1 0 2-1 2-1 1 0 1 1 2 1v-1l1-1v1c1 1 2 1 3 2h0v2h-1c0-1 0-1-1-1l-1 1s-1-1-2 0c-1 0-2 2-3 2h-3v1c-3-1-5-2-7-2l-2-2v-1c-1 0-1 0-2-1v-1h-1v-1z" class="V"></path><path d="M329 632l1-1v-1c0-1 1-1 2-1v1l1 1v3c1 0 1-2 2 0h1-1-1 0 1v2h0-1c-1 0-1-1-2-1s-1 0-2-1v-1h-1v-1z" class="E"></path><path d="M390 604l1 1v4l-6 9c-1 0-1-1-2-1h-4v1l-1 1s0 1-1 1c0 1-1 2-2 3v1c-2 0-2 0-3-1l-1-2h0-3-1c0-1-1-2-2-2l-1 1-3 1h-1v-1c1 0 1-1 1-2h-2-2-3-1v1h1l-1 1v-1s-2-1-3-1c0 0-1 0-2 1-1-1-1-1-3-1h-2c0-1 1-1 1-1h6 23c1 0 2-1 3-1s3 0 4-1c0 0-1 0-1-1h-1 1l2-1c5-1 7-5 9-9z" class="P"></path><path d="M361 618h1 2v1 1l-3 1h-1v-1c1 0 1-1 1-2z" class="G"></path><path d="M378 617h1v1l-1 1s0 1-1 1c0 1-1 2-2 3v1c-2 0-2 0-3-1l-1-2h0-3-1c0-1-1-2-2-2 3-1 5-1 8-1h0c1 0 3 0 5-1z" class="E"></path><path d="M371 621c2 0 3 1 4 1v1 1c-2 0-2 0-3-1l-1-2z" class="P"></path><path d="M378 617h1v1l-1 1s0 1-1 1c0 1-1 2-2 3v-1-1-2c1 0 2-1 2-2h1z" class="L"></path><path d="M393 607c2-1 2-5 3-7l1 1c-2 6-6 11-9 16 0 1 1 1 1 1l-10 10c-1 0-2 2-4 2h0c-2 0-3 1-5 2l-5 2v-1c-2 1-2 1-4 1-2 1-6 3-7 2-1 0-1 0-2-1h0c1-1 1-1 1-2 0 1 0 1 1 2 3-1 3-1 5-3l1 1h2v-1h2c2-1 3-2 6-3 1-1 2-2 4-3l1-2v-1c1-1 2-2 2-3 1 0 1-1 1-1l1-1v-1h4c1 0 1 1 2 1l6-9 2-2z" class="G"></path><path d="M378 619c1 1 1 2 1 4l-4 4h0c0-1-1-1-1-1l1-2v-1c1-1 2-2 2-3 1 0 1-1 1-1z" class="I"></path><path d="M388 617c0 1 1 1 1 1l-10 10c-1 0-2 2-4 2h0a79.93 79.93 0 0 0 13-13z" class="d"></path><path d="M374 626s1 0 1 1c-3 2-7 5-10 6-2 1-2 1-4 1-2 1-6 3-7 2-1 0-1 0-2-1h0c1-1 1-1 1-2 0 1 0 1 1 2 3-1 3-1 5-3l1 1h2v-1h2c2-1 3-2 6-3 1-1 2-2 4-3z" class="B"></path><path d="M370 606h1c1 0 0 1 1 2h0c0 2 2 4 4 5h1s1 0 2 1h-1 1c0 1 1 1 1 1-1 1-3 1-4 1s-2 1-3 1h-23-6s-1 0-1 1h0l-1-1c-2 0-4-2-6-3h4l-2-1c3 0 6-1 8-2l6-3 1 1c2 0 4-1 5-2h2v1l1-1v1h1c0-1 0 0 1-1h0 1 1 1l1 1 3-2z" class="N"></path><path d="M340 614h1c2 0 5 0 8 1v1c-1 0-6 0-7 1-2 0-4-2-6-3h4z" class="K"></path><path d="M370 606h1c1 0 0 1 1 2h0c0 2 2 4 4 5h1-2c-7-1-15 0-22 0 5-1 10 0 15 0l-1-1h4 1c-1-2-1-3-1-4v-1l-1-1z" class="L"></path><path d="M370 606l1 1v1c0 1 0 2 1 4h-1-4c-1-2 0-2 0-4l3-2z" class="N"></path><path d="M372 612h-1l-3-3c1-1 2-1 3-1 0 1 0 2 1 4z" class="J"></path><path d="M358 607h2v1l1-1v1h1c0-1 0 0 1-1h0 1 1 1l1 1c0 2-1 2 0 4l1 1c-5 0-10-1-15 0-4 0-8 0-12 1h-1l-2-1c3 0 6-1 8-2l6-3 1 1c2 0 4-1 5-2z" class="b"></path><path d="M358 607h2v1l1-1v1h1c0-1 0 0 1-1h0 1c-2 2-5 2-7 3-1 0-2 1-3 1l-1-1c-3 1-4 2-6 1h-1l6-3 1 1c2 0 4-1 5-2z" class="G"></path><path d="M330 614h3c1-1 2 0 3 0 2 1 4 3 6 3l1 1h0 2c0 1 1 1 1 2s1 3 2 4v2c-1 1-1 0-1 1-1 2-2 3-3 5l-2 1-1-1c-1 0-1 1-2 1 0 0-1 0-1-1-1 0-2 0-2-1h-1c-1 1-2 0-2 0l-1-1v-1c-1 0-2 0-2 1v1l-1 1-1 1-1-1 2-2h-1c-1 0-1 0-1-1v-2l1-1c-1-2-1-3-2-5-1 0-1 0-1-1-1 0-2-1-3-2v-1h3 0v-1h-2v-1l2-1h5z" class="C"></path><path d="M330 620l-1-1c2 1 4 3 6 4l1 1c-1 1-1 2-1 3v1h-1-1l1-2c0-1 0-2-1-2h-1-1c0-2 0-3-1-4z" class="Q"></path><path d="M335 623l-1-3h1c1 0 2 1 2 1l1-1 1 2h2v1c0 1 0 1-1 1-2 0-2 0-3 1h0-1l-1 2c0-1 0-2 1-3l-1-1z" class="R"></path><path d="M335 623l-1-3h1c1 0 2 1 2 1l1 1c-1 1-1 1-1 2h-1l-1-1z" class="i"></path><path d="M337 625h0c1-1 1-1 3-1-1 1-2 4-1 5 0 0 0 1 1 2l1 1c-1 0-1 1-2 1 0 0-1 0-1-1-1 0-2 0-2-1h-1c-1 1-2 0-2 0l-1-1 1-1 1 1c2-2 3-3 3-5z" class="I"></path><path d="M330 620c1 1 1 2 1 4h1c0 1 0 1-1 1v1 1 1c-1 0-1 1-1 2h-1-1c-1 0-1 0-1-1v-2l1-1c-1-2-1-3-2-5-1 0-1 0-1-1l2 2c2-1 2-1 3-2z" class="E"></path><path d="M327 627c1 0 1-1 2 0h1c-1 1-2 2-2 3-1 0-1 0-1-1v-2z" class="D"></path><path d="M330 620c1 1 1 2 1 4h1c0 1 0 1-1 1 0 1 0 1-1 1-1-2-2-3-3-4 2-1 2-1 3-2z" class="Y"></path><path d="M325 617c3 0 7-1 9 1v1h1v1h-1l1 3c-2-1-4-3-6-4l1 1c-1 1-1 1-3 2l-2-2c-1 0-2-1-3-2v-1h3z" class="f"></path><path d="M330 614h3c1-1 2 0 3 0 2 1 4 3 6 3l1 1h-1c-1 0-2 0-3-1l-2 1h2v2l1 1-1 1-1-2-1 1s-1-1-2-1v-1h-1v-1c-2-2-6-1-9-1h0v-1h-2v-1l2-1h5z" class="Y"></path><path d="M325 614h5l-2 1 2 1h-5-2v-1l2-1z" class="O"></path><path d="M330 616c3 0 6 0 9 1l-2 1h2v2l1 1-1 1-1-2-1 1s-1-1-2-1v-1h-1v-1c-2-2-6-1-9-1h0v-1h5z" class="K"></path><path d="M338 620l-6-3c2 0 3 0 5 1h2v2l1 1-1 1-1-2z" class="Y"></path><path d="M339 617c1 1 2 1 3 1h1 0 2c0 1 1 1 1 2s1 3 2 4v2c-1 1-1 0-1 1-1 2-2 3-3 5l-2 1-1-1-1-1c-1-1-1-2-1-2-1-1 0-4 1-5 1 0 1 0 1-1v-1h-2l1-1-1-1v-2h-2l2-1z" class="F"></path><path d="M348 624v2c-1 1-1 0-1 1l-1-1c-1 1-1 1-1 2l-2-1v-1c1 0 1 1 2 0h1l-1-1 1-1h2z" class="I"></path><path d="M341 630v-2-2h2 0v1l2 1-1 1h-2l-1 1z" class="J"></path><path d="M345 628c0-1 0-1 1-2l1 1c-1 2-2 3-3 5l-1-1c-1 0-1-1-2-1l1-1h2l1-1z" class="K"></path><path d="M369 580c0 1-1 1-1 3 0 1 0 2 1 3s2 1 3 1 2 0 2-1h1l1 2v2 2l-1 1c-1 3-2 5-4 7 1 0 2 0 3 1-2 2-4 2-6 3l-1 2-2 1h-1-1 0c-1 1-1 0-1 1h-1v-1l-1 1v-1h-2c-1 1-3 2-5 2l-1-1-6 3c-2 1-5 2-8 2l2 1h-4c-1 0-2-1-3 0h-3-5l2-2h0c0-1 1-2 1-2h2 3c2 0 5-2 7-3l5-5 4-2v-1h-3l1-1-1-2c2-2 4-3 5-4v-1h0 2l1-1c1 1 1 1 1 2h0l2 2c1-1 2-2 2-3v-2c1 0 1 0 2 1h0c0-1 0-2 1-2h1l1-1c0-1 0-2-1-3 1 0 2-1 2-1 2-1 3-2 4-3z" class="F"></path><path d="M352 598c1 0 2 1 2 1 2-1 2-2 3-3 1 1 1 1 1 2-1 1-3 2-4 3-1-1-2-1-2-3z" class="Y"></path><path d="M361 590h3c0 1 0 1-1 1v1c0 1 1 1 2 2v2c-2 0-2 0-3-1v-1h1c-1-1-1-2-2-3v-1z" class="d"></path><path d="M351 591h0c1 1 2 1 3 2h-2 0l-1 1v1c-1 1-2 2-4 3h0l-1-2c2-2 4-3 5-4v-1z" class="K"></path><path d="M351 595l1 1c0-1 1-1 2-1h1s-1-1 0-1l2 1-1 1-1-1-3 3c0 2 1 2 2 3 1-1 3-2 4-3l3-1h1l-1 1c-1 0-2 1-2 1-2 2-3 4-4 5h-1l1-1v-1c-3 2-5 3-7 5-2 1-5 2-7 3v-1c2-1 3-1 5-2s3-3 5-4c1 0 1 0 2-1l-1-1-1-1h-2v-1h-3l1-1h0c2-1 3-2 4-3z" class="C"></path><path d="M341 610c2-1 5-2 7-3 2-2 4-3 7-5v1l-1 1h1c1-1 2-3 4-5 0 0 1-1 2-1 0 1 0 2-1 3 0 1-1 2-2 3l-6 4-6 3c-2 1-5 2-8 2h-3 0l6-3h0z" class="J"></path><path d="M363 591c1 0 2 1 3 2 0 1 0 2 1 3h2v1c-1 2-2 2-3 3-1 2-2 4-4 5-2 0-3 1-4 2s-3 2-5 2l-1-1 6-4c1-1 2-2 2-3 1-1 1-2 1-3l1-1h-1v-1h1v-1c1 1 1 1 3 1v-2c-1-1-2-1-2-2v-1z" class="M"></path><path d="M367 596h2v1c-1 2-2 2-3 3h0c0-1 0-2 1-3h0v-1zm-5-1c1 1 1 1 3 1v3h0c-1 1-2 2-2 3h-1v-2-2-1h-1v-1h1v-1z" class="O"></path><path d="M349 600h2l1 1 1 1c-1 1-1 1-2 1-2 1-3 3-5 4s-3 1-5 2v1h0l-6 3h0 3l2 1h-4c-1 0-2-1-3 0h-3-5l2-2h0c0-1 1-2 1-2h2 3c2 0 5-2 7-3l5-5 4-2z" class="N"></path><path d="M328 610h2 3c-2 1-4 2-6 2h0c0-1 1-2 1-2z" class="U"></path><path d="M349 600h2l1 1h-1c-2 2-4 2-6 4-1 0-2 1-3 2h-2l5-5 4-2z" class="R"></path><path d="M369 580c0 1-1 1-1 3 0 1 0 2 1 3s2 1 3 1 2 0 2-1h1l1 2v2 2l-1 1c-1 3-2 5-4 7 1 0 2 0 3 1-2 2-4 2-6 3l-1 2-2 1h-1-1 0c-1 1-1 0-1 1h-1v-1l-1 1v-1h-2c1-1 2-2 4-2 2-1 3-3 4-5 1-1 2-1 3-3v-1h-2c-1-1-1-2-1-3-1-1-2-2-3-2 1 0 1 0 1-1h-3c0-1 0-2 1-2h1l1-1c0-1 0-2-1-3 1 0 2-1 2-1 2-1 3-2 4-3z" class="S"></path><path d="M373 593h2c-1 2-2 3-4 5l1-2c0-1 0-2 1-3z" class="K"></path><path d="M368 604l-2 1h0c2-1 3-4 5-5 1 0 2 0 3 1-2 2-4 2-6 3z" class="M"></path><path d="M369 591h2c-1 2-1 4-1 5h2l-1 2h0c0 1-1 2-2 2v-3-1c1-1 0-3 0-5z" class="J"></path><path d="M371 591v-1c0-1-1-1-1-2 1 1 2 1 3 0l1 1v1h-2v1l1 2c-1 1-1 2-1 3h-2c0-1 0-3 1-5z" class="Z"></path><path d="M374 586h1l1 2v2 2l-1 1h0-2l-1-2v-1h2v-1-3z" class="L"></path><path d="M372 591h2c1 0 1 1 2 1l-1 1h0-2l-1-2z" class="M"></path><path d="M369 580c0 1-1 1-1 3 0 1 0 2 1 3s2 1 3 1 2 0 2-1v3l-1-1c-1 1-2 1-3 0 0 1 1 1 1 2v1h-2c0-1-2-3-3-5h0c-1 1-1 1 0 1v1h-1v-1h-1c0-1 0-2-1-3 1 0 2-1 2-1 2-1 3-2 4-3z" class="V"></path><path d="M364 587h1v1h1v-1c-1 0-1 0 0-1h0c1 2 3 4 3 5 0 2 1 4 0 5h-2c-1-1-1-2-1-3-1-1-2-2-3-2 1 0 1 0 1-1h-3c0-1 0-2 1-2h1l1-1z" class="a"></path><path d="M262 86c2-1 2-2 3-1h0l-1 1v1h1c1 0 2 1 3 2h1c-1 0-1 1-2 1l2 2c0 3 0 5-1 7l-1 1v-1l-1 1h0l1 1c0-1 1-1 2-2h0 1 0c1 1 1 2 1 3h0l1 1-2 2c-1 0-2 2-3 3l-22 3h-2v-1c-4 1-9 2-13 3-1 1-3 2-4 3-3 1-5 2-8 3-7 3-13 7-20 11-4 2-8 6-12 8h-1c-2 1-4 3-7 4v-1c-2 1-3 3-4 4-1 0-1 0-1-1v-1l3-3v-1s1 0 1-1v-1l-1-1v-3c-1-1-2-1-3-1h0c0-1-1-2-1-3l-1 1v3h-1-2v-1c0-1 0-2 1-3v-2l1-1c1 0 0 0 1-1 0-1 1-1 1-2s0-1 1-1v-2l3-3c2-2 5-5 8-6l4-3 6-3 3-2 3-1-1-1v-1l8-3h3 0l3-1 2 1c1-1 1-1 2-1l6 2v-1h0-1 3l1-1v1h1c0-1-1-2-1-3h1c1 0 1-1 2-1s1 1 2 2v-1c1-1 0-1 0-2l1-1 1 1v-2c1 0 1 0 2 1h1v-2c1 0 4-2 6-2h1c1 0 1 0 2 1h0 1v3h0 1c1 0 1 0 2-1 1 0 2-1 2-1 1 0 1-1 2-1v1h1 2v1c1 1 2 0 3 0h1v-1l-1-1h1v-2h2z" class="F"></path><path d="M266 100h0l1 1c0-1 1-1 2-2h0c0 1-1 2-2 3v-1c-1 0-2 1-3 1-1 1-3 1-4 1 1 0 2-1 3-2l2-2 1 1z" class="Q"></path><path d="M247 105c3 0 8-1 11-2v-1c0-1 1-1 2-2l1 1 1-2 1 1v1c-1 1-2 2-3 2-8 3-17 4-25 5h0c1-1 2-1 3-1s3-1 4-1c2 0 3 0 5-1z" class="D"></path><path d="M271 102l1 1-2 2c-1 0-2 2-3 3l-22 3h-2v-1l13-2c2 0 3 0 4-1 4 0 8-2 11-5z" class="X"></path><path d="M186 138c0-1 1-2 1-2 2-2 4-3 5-5l6-3c5-4 11-7 16-10 2 0 3-1 4-2 4-1 8-3 12-3-1 1-3 2-4 3-3 1-5 2-8 3-7 3-13 7-20 11-4 2-8 6-12 8z" class="N"></path><path d="M231 95v-1c1-1 0-1 0-2l1-1 1 1h0 2c0 1-1 2-1 3s1 2 1 3h0c1 1 1 1 2 1s1 1 2 1c0 0 1 0 1 1v1c1 1 2 1 2 2h1 2c1 0 1-1 2-1v2c-2 1-3 1-5 1-1 0-3 1-4 1s-2 0-3 1h0l-1 1-17 6-4 2-1-1 1-1 1-2h1 1c0-2-3-4-4-5 0-1 1-2 2-3h-1c-1-1-1-1-1-2v-1c1-1 0-3 0-4h1l2-1c1-1 1-1 2-1l6 2v-1h0-1 3l1-1v1h1c0-1-1-2-1-3h1c1 0 1-1 2-1s1 1 2 2z" class="R"></path><path d="M214 105c0 1 1 2 1 3 0 0-1 0-1 1 1 1 1 2 2 2h1l2 1 1-1h1 0c-2 1-3 1-4 3v1l-4 2-1-1 1-1 1-2h1 1c0-2-3-4-4-5 0-1 1-2 2-3z" class="G"></path><path d="M224 106h2c0-1 0-2 1-3h0l-2-2v-1h2v1c1 0 2 0 3 1 0 1 0 1 1 2-1 1-1 2-2 2h-1l-3 3h0v-2h-1v-1z" class="M"></path><path d="M231 100c1 0 2 0 3 1 1 0 2 1 2 2l1 1 2-1c1 0 1 0 1-1 1 1 2 1 2 2h1 2c1 0 1-1 2-1v2c-2 1-3 1-5 1-1 0-3 1-4 1s-2 0-3 1h0l-1 1h0c-1-1-1-1-2-1h0l1-1s0-1 1-1h0v-4h-1v2h-1v-2s-1-1-1-2z" class="E"></path><path d="M240 102c1 1 2 1 2 2-1 0-2 1-4 1h-2 0v-2l1 1 2-1c1 0 1 0 1-1z" class="N"></path><path d="M226 96v1h1l-6 4c-1 2-1 2-1 4l1 3v1h-1-1c1-1 1-1 1-2l-1-1h-1v1c-1-1-1-1-2-1h0l-1-1v1c1 0 1 0 1 1 0 2 2 2 2 4h-1-1c-1 0-1-1-2-2 0-1 1-1 1-1 0-1-1-2-1-3h-1c-1-1-1-1-1-2v-1c1-1 0-3 0-4h1l2-1c1-1 1-1 2-1l6 2v-1h0-1 3l1-1z" class="E"></path><path d="M212 98h1 1c0 1 1 1 1 2h1l-1 1h-1 0l-2 2v-1c1-1 0-3 0-4z" class="C"></path><path d="M214 101c1 0 2 0 4 1v-1l1-1 2 1c-1 1-2 1-2 3l-3 2h0l-1-1v1c1 0 1 0 1 1 0 2 2 2 2 4h-1-1c-1 0-1-1-2-2 0-1 1-1 1-1 0-1-1-2-1-3h-1c-1-1-1-1-1-2l2-2z" class="I"></path><path d="M231 95v-1c1-1 0-1 0-2l1-1 1 1h0 2c0 1-1 2-1 3s1 2 1 3h0c1 1 1 1 2 1s1 1 2 1c0 0 1 0 1 1v1c0 1 0 1-1 1l-2 1-1-1c0-1-1-2-2-2-1-1-2-1-3-1l-1 1h0v1c-1-1-2-1-3-1v-1h-2v1l2 2h0c-1 1-1 2-1 3h-2v1l-1 2c-1 0-1-1-2-1l-1-3c0-2 0-2 1-4l6-4c0-1-1-2-1-3h1c1 0 1-1 2-1s1 1 2 2z" class="H"></path><path d="M227 94c1 0 1-1 2-1s1 1 2 2c-1 0-1 1-2 1 0-1-1-1-2-2z" class="S"></path><path d="M221 101h3v1c-1 0-2 1-2 2v1l-1-1-1 1c0-2 0-2 1-4z" class="K"></path><path d="M220 105l1-1 1 1 2 1v1l-1 2c-1 0-1-1-2-1l-1-3zm10-4l-1-1c0-1 0-1 1-2h1c1 0 1 1 2 1 0-1 0-1-1-1v-1c2 0 2 0 3 1h0c1 1 1 1 2 1s1 1 2 1c0 0 1 0 1 1v1c0 1 0 1-1 1l-2 1-1-1c0-1-1-2-2-2-1-1-2-1-3-1l-1 1z" class="F"></path><path d="M239 100s1 0 1 1v1c0 1 0 1-1 1v-3z" class="I"></path><path d="M262 86c2-1 2-2 3-1h0l-1 1v1h1c1 0 2 1 3 2h1c-1 0-1 1-2 1l2 2c0 3 0 5-1 7l-1 1v-1l-1 1-1-1-2 2v-1l-1-1-1 2-1-1c-1 1-2 1-2 2v1c-3 1-8 2-11 2v-2c-1 0-1 1-2 1h-2-1c0-1-1-1-2-2v-1c0-1-1-1-1-1-1 0-1-1-2-1s-1 0-2-1h0c0-1-1-2-1-3s1-2 1-3h-2 0v-2c1 0 1 0 2 1h1v-2c1 0 4-2 6-2h1c1 0 1 0 2 1h0 1v3h0 1c1 0 1 0 2-1 1 0 2-1 2-1 1 0 1-1 2-1v1h1 2v1c1 1 2 0 3 0h1v-1l-1-1h1v-2h2z" class="G"></path><path d="M244 97v-1c2 0 2 0 3 1 0 1 0 1-1 2-1 0-1-1-2-2z" class="Q"></path><path d="M245 88h1v3h-1v1 1l-2 2h-1-1-1v-1c0-3 2-4 3-5 0 0 1-1 2-1z" class="B"></path><path d="M245 88h1v3h-1v1c-1-1-1-2-2-3 0 0 1-1 2-1z" class="R"></path><path d="M241 95l-1-1h1c1-1 1-1 2-1 0-1 1 0 2 0l-2 2h-1-1z" class="W"></path><path d="M241 96c1 1 1 1 2 1s2 3 3 3c0 1 1 1 1 2h1l-1-2v-1l1-1h1c0 1 1 2 2 2l1 1v1h-1l-1-1c-1 0-1 0-2 1v1h-1c-1 0-1 1-2 1h-2-1c0-1-1-1-2-2v-1h1v-5z" class="B"></path><path d="M236 89l1 1 1 1 1-1 1 1c0 1-1 2-1 3v3h1c0-1 0-1 1-1v5h-1c0-1-1-1-1-1-1 0-1-1-2-1s-1 0-2-1h0c0-1-1-2-1-3s1-2 1-3h-2 0v-2c1 0 1 0 2 1h1v-2z" class="W"></path><path d="M236 89l1 1 1 1-1 1 1 1v1c-1 0-1 0-2-1v2 1l-1 2h0c0-1-1-2-1-3s1-2 1-3h-2 0v-2c1 0 1 0 2 1h1v-2zm13 1c1 0 2-1 2-1 1 0 1-1 2-1v1h1 2v1l-2 1c0 2 1 2 2 3h-1 0c0 1 1 1 1 1v3h-2v-1l1-1c-2 0-3-1-4-1-1 1-2 1-4 2-1-1-1-1-3-1v1l-2-2h1l2-2v-1-1h1 0 1c1 0 1 0 2-1z" class="I"></path><path d="M246 91h1c1 0 1 0 2-1 0 1 0 1 1 1v1c-1 0-1 1-1 1v1h0-1c-1-1-2 0-3 0v-1-1-1h1 0z" class="L"></path><path d="M246 91h1c1 0 1 0 2-1 0 1 0 1 1 1v1c-1 0-1 1-1 1-1 0-2 0-3-2z" class="E"></path><path d="M249 90c1 0 2-1 2-1 1 0 1-1 2-1v1h1 2v1l-2 1h-2c0 1 0 1-1 2h-1v-1-1c-1 0-1 0-1-1z" class="N"></path><path d="M262 86c2-1 2-2 3-1h0l-1 1v1h1c1 0 2 1 3 2h1c-1 0-1 1-2 1l2 2c0 3 0 5-1 7l-1 1v-1l-1 1-1-1-2 2v-1l-1-1-1 2-1-1c-1 1-2 1-2 2v1c-3 1-8 2-11 2v-2h1v-1c1-1 1-1 2-1l1 1h1v-1l-1-1c-1 0-1-1-1-2h0l2 1v-1h0c1-1 1-1 2-1v1h2v-3s-1 0-1-1h0 1c-1-1-2-1-2-3l2-1c1 1 2 0 3 0h1v-1l-1-1h1v-2h2z" class="S"></path><path d="M261 89h0v-1h3 0v1h-3z" class="H"></path><path d="M265 99c-1 0-1 0-1-1 1-1 1-1 3-1v2l-1 1-1-1z" class="N"></path><path d="M256 99l2 1c-1 1-1 2-1 2l-2-1v-1l1-1z" class="D"></path><path d="M260 96l1-1v3c0 1-1 1-2 1v-1l1-2z" class="T"></path><path d="M261 89h3v2 2h0l-1-2h-1 0l-1-2z" class="G"></path><path d="M267 97c-1 0-2-1-3-2h0l2 1v-1c0-1 1-1 2-2 0 2 0 3-1 4z" class="N"></path><path d="M268 89h1c-1 0-1 1-2 1h-1v1 3h-1l-1-1v-2c0-1 0-1 1-2h3 0z" class="H"></path><path d="M256 98v-3l1 2s1 0 1-1h2l-1 2v1l-1 1-2-1v-1z" class="V"></path><path d="M267 90l2 2c0 3 0 5-1 7l-1 1v-1-2h0c1-1 1-2 1-4-2-1-2-1-2-3h1z" class="G"></path><path d="M259 90l1 1 1-1v1c0 2-2 3-3 5 0 1-1 1-1 1l-1-2s-1 0-1-1h0 1c-1-1-2-1-2-3l2-1c1 1 2 0 3 0z" class="H"></path><path d="M259 90l1 1h-4v1h1c0 1-1 1-1 2-1-1-2-1-2-3l2-1c1 1 2 0 3 0z" class="E"></path><path d="M210 97l3-1 2 1-2 1h-1c0 1 1 3 0 4v1c0 1 0 1 1 2h1c-1 1-2 2-2 3 1 1 4 3 4 5h-1-1l-1 2-1 1 1 1c-12 6-23 13-34 21-1 0-3 2-3 2v-1s1 0 1-1v-1l-1-1v-3c-1-1-2-1-3-1h0c0-1-1-2-1-3l-1 1v3h-1-2v-1c0-1 0-2 1-3v-2l1-1c1 0 0 0 1-1 0-1 1-1 1-2s0-1 1-1v-2l3-3c2-2 5-5 8-6l4-3 6-3 3-2 3-1-1-1v-1l8-3h3 0z" class="D"></path><path d="M201 113h6v1h1 1l-1 1 1 1v1h-2c-1 0-2 1-3 1v1c-1 0-2 0-4 1h1l-1 1-1-1-3-3h-1l-1-1v-1h0c2-1 5-1 7-2z" class="B"></path><path d="M201 113l3 1v1h-6c-1 1-2 0-4 1v-1h0c2-1 5-1 7-2z" class="U"></path><path d="M201 113h6v1h1 1l-1 1 1 1v1h-2c-1-1-1-1-2-1s-1 0-1-1v-1l-3-1z" class="R"></path><path d="M184 111l4-3c1 0 2 0 3 1h3 1 2c2 0 2 1 3 2h3l3 1h0 2v1h0v1h-1v-1h-6c-2 1-5 1-7 2h0v1l1 1 4 4-2 2h-2c0-1 0-1-1-1h0c1 1 1 1 1 3h0c-2 0-3-1-5-3 0-1-1-2-2-2 1-1 1-2 1-2 0-1-1-2-1-2h0l1-2c0 1 0 1 1 1v-3l-1-1h-5z" class="J"></path><path d="M184 111l4-3c1 0 2 0 3 1h3 1 2c-1 1-5 1-6 2h6 0v1c-3 0-3 0-5 1 0 1 1 1 1 2l-1 1c0 2 1 3 2 3v1l-1-1h-1-2v1 2c0-1-1-2-2-2 1-1 1-2 1-2 0-1-1-2-1-2h0l1-2c0 1 0 1 1 1v-3l-1-1h-5z" class="R"></path><path d="M189 114c0 1 0 1 1 1h1c0 1-1 2-2 3 0-1-1-2-1-2h0l1-2z" class="C"></path><path d="M210 97l3-1 2 1-2 1h-1c0 1 1 3 0 4v1c0 1 0 1 1 2h1c-1 1-2 2-2 3 1 1 4 3 4 5h-1-1l-1 2-1 1-3-2h-1v-1h0v-1h-2 0l-3-1h-3c-1-1-1-2-3-2h-2-1-3c-1-1-2-1-3-1l6-3 3-2 3-1-1-1v-1l8-3h3 0z" class="M"></path><path d="M202 109c2 0 3 1 5 1 0 1-1 1-1 2l-3-1-1-1v-1z" class="Z"></path><path d="M188 108l6-3 2 1h0c-1 1-2 1-4 2v1h2-3c-1-1-2-1-3-1z" class="H"></path><path d="M195 109l-1-1v-1c3 1 6 1 8 2h0v1l1 1h-3c-1-1-1-2-3-2h-2zm12 1c2 0 3 1 4 2l2 1v2l-1 1-3-2h-1v-1h0v-1h-2 0c0-1 1-1 1-2z" class="F"></path><path d="M206 105h1c1 1 1 2 2 2 0 0 1-1 2-1l1 2c1 1 4 3 4 5h-1-1l-1 2v-2l-2-1v-1s0-1-1-1-2-2-4-2c-1-1-2-1-2-2 1 0 1 0 2-1z" class="K"></path><path d="M199 104l1-1h3c1-1 0-1 2-1 3 0 4 2 6 4h0c-1 0-2 1-2 1-1 0-1-1-2-2h-1c-1 1-1 1-2 1 0 1 1 1 2 2h-1c-1 0-1 0-2-1h-2c-1-1-2-1-3-1h-2l-2-1 3-2c1 1 1 1 2 1z" class="B"></path><path d="M197 103c1 1 1 1 2 1l1 2h-2-2l-2-1 3-2z" class="R"></path><path d="M203 107v-1h-2v-1c1 0 2 0 3-1h0c1 0 1 0 2 1-1 1-1 1-2 1 0 1 1 1 2 2h-1c-1 0-1 0-2-1z" class="H"></path><path d="M210 97l3-1 2 1-2 1h-1c0 1 1 3 0 4v1c0 1 0 1 1 2h1c-1 1-2 2-2 3l-1-2h0c-2-2-3-4-6-4-2 0-1 0-2 1h-3l-1 1c-1 0-1 0-2-1l3-1-1-1v-1l8-3h3 0z" class="D"></path><path d="M207 97h3 0l-10 5-1-1v-1l8-3z" class="U"></path><path d="M211 106c0-1 0-1 1-2-1-1-2-1-3-1l-2-2c1-2 3-1 4-3h1 0c0 1 1 3 0 4v1c0 1 0 1 1 2h1c-1 1-2 2-2 3l-1-2h0z" class="E"></path><path d="M184 111h5l1 1v3c-1 0-1 0-1-1l-1 2h0s1 1 1 2c0 0 0 1-1 2-1 0-1 0-2-1v1c1 1 3 1 5 3 1 1 1 2 2 3l-1 1h-2c-1 1-1 1-1 2-2 2-4 2-6 4-1 1-1 0-2 0-1 1-3 3-3 4h-1l-1-1v-3c-1-1-2-1-3-1h0c0-1-1-2-1-3l-1 1v3h-1-2v-1c0-1 0-2 1-3v-2l1-1c1 0 0 0 1-1 0-1 1-1 1-2s0-1 1-1v-2l3-3c2-2 5-5 8-6z" class="H"></path><path d="M168 133v-2c1 0 2 1 3 1v1h-1-2z" class="M"></path><path d="M178 127h-3c0-2-1-3-1-4l1-1h1l1 2c1 2 1 2 1 3z" class="J"></path><path d="M177 124c0-2-1-3-2-4 1-2 2-3 4-4v1c0 2 0 2 1 4 0 1 1 2 2 3 1 0 1 0 2 1v1h-4c-1-1-2-1-3-2z" class="W"></path><path d="M181 115c1 0 1 0 1-1 2-1 5 0 7 0l-1 2h0s1 1 1 2c0 0 0 1-1 2-1 0-1 0-2-1h-1c0 1-1 3-1 4h-1l-1 1c-1-1-2-2-2-3-1-2-1-2-1-4h0l2-2z" class="D"></path><path d="M179 117h0l2-2 1 1c1 0 1 0 2-1 1 0 1 0 2 1v1l-1-1c-2 1-3 2-4 3s-1 2-1 2c-1-2-1-2-1-4z" class="C"></path><path d="M188 116s1 1 1 2c0 0 0 1-1 2-1 0-1 0-2-1h-1c0 1-1 3-1 4h-1l-1 1c-1-1-2-2-2-3 0 0 0-1 1-2h2c1-1 3-1 4-2l1-1z" class="Q"></path><path d="M181 119h2c0 2-1 3 0 4l-1 1c-1-1-2-2-2-3 0 0 0-1 1-2z" class="E"></path><path d="M183 123h1c0-1 1-3 1-4h1v1c1 1 3 1 5 3 1 1 1 2 2 3l-1 1h-2c-1 1-1 1-1 2-2 2-4 2-6 4-1 1-1 0-2 0-1 1-3 3-3 4h-1l-1-1v-3c-1-1-2-1-3-1h0c0-1-1-2-1-3l-1 1v-1h-1 2v-2h1v1h1v1c0 1 1 1 2 2 1-1 1-3 2-4 0-1 0-1-1-3h0c1 1 2 1 3 2h4v-1c-1-1-1-1-2-1l1-1z" class="S"></path><path d="M108 239l1-1v1l1-1-3 10h0c0 1 0 1 1 2 0 1-1 3-1 5h1v1 2 1l-5 22v6c-1 2-2 4-2 6 0 1 0 2-1 3h2v1h0c-1 1-2 6-2 8l1 1c0 4-2 10-2 14v2c-1 1 0 2 0 3-1 1-1 1-1 3h-1 0-3v1c-1 0-2 1-2 1l-1 1v-1h-1c0 2 0 2 1 2l2 2h0c-2-1-3-2-5-2-3 0-6 1-9 3h-1l-2 1h0-1l-1-1c-1-1-1-1-2-1h-1-1l-1-3c0-2-1-2-1-4v-2h-1v-1c1-1 0-3 1-5 1-1 1-4 1-6v-6l2-12h1l-1 3 2-1v-3c1-2 2-5 2-7h0c1-2 1-3 1-5-1 0-1-1-1-2 0-3 1-5 2-7l2-7 2-2h1c2-2 4-3 5-5 2-1 3-2 4-3v-2c2-1 3-2 4-3h1l1-1v-1h0l-1 1v-2c0-1 1-1 1-1h2v-1l6-4 1-1 2-2h0z" class="R"></path><path d="M82 292h1l-3 1v1h2v1l-4 3h-1c0-1 1-2 1-3s1-2 1-2c1-1 2-1 3-1z" class="H"></path><path d="M83 290h2l1 2c1-1 1-1 2-1h1v2l-2 4c-1-1-1-1-2-1-1-1-1-1-2 0l-1-1v-1h-2v-1l3-1h-1l1-2z" class="I"></path><path d="M83 290h2l1 2h-3-1l1-2z" class="M"></path><path d="M88 269c2 1 4 2 6 2-1 1-2 1-3 1 0 0 0 1 1 1-1 1-4 2-4 3v1l1 1-1 1-1-1h-1c0 2-1 2-2 4l-1 1-2 2h0c0-1 1-3 1-4 2-1 3-4 4-5l2-2c-3-1-3-1-5 0 0-1 2-2 3-3v-1s1 0 2-1z" class="X"></path><path d="M88 269c2 1 4 2 6 2-1 1-2 1-3 1h-2v-1h-1 0c1 1 0 1 0 2v1h0c-3-1-3-1-5 0 0-1 2-2 3-3v-1s1 0 2-1z" class="G"></path><path d="M83 283h1l1 1 1 1h0c2 0 3 2 5 2l1 1h-1c-1 0-2 1-2 1-1 1-2-1-3 0l-1 1h0-2c-1 0-2 0-3 1h-1l2-3s-1 0-1-1l1-1v-1l2-2z" class="K"></path><path d="M83 283h1l-1 1c1 1 0 1 1 1v2 1h-2v1h0 3v1h0 0-2c-1 0-2 0-3 1h-1l2-3s-1 0-1-1l1-1v-1l2-2z" class="H"></path><path d="M83 283h1l-1 1c1 1 0 1 1 1l-3 2v-1-1l2-2z" class="M"></path><path d="M94 287h1l1-1c-1-1-1-2-1-2 1-1 1-1 3-2l-1 3c0 2-1 5-2 8l-1-1v2h-1v-2h-1v2c-1 0-2 1-2 1h-1v-1-1-2h-1c-1 0-1 0-2 1l-1-2h0l1-1c1-1 2 1 3 0 0 0 1-1 2-1h1l-1-1h1 2z" class="B"></path><g class="G"><path d="M94 287h1l1-1c-1-1-1-2-1-2 1-1 1-1 3-2l-1 3c0 2-1 5-2 8l-1-1c1-1 1-1 1-2-2-1-2 1-4 1v-1c0-1 1-1 2-2h1v-1z"></path><path d="M94 292l1 1-1 4v4l-2 8h-1-2 2v-2c1-2 0-3 0-4l-2-2v-2l-2-2 2-4v1 1h1s1-1 2-1v-2h1v2h1v-2z"></path></g><path d="M92 299l-2-2h0l1-2h1v2h1v1c0 1 0 1-1 1z" class="E"></path><path d="M93 297h1v4c-1-1-2-1-3-1 0 0-1 0-1-1h1 1c1 0 1 0 1-1v-1z" class="C"></path><path d="M94 292l1 1-1 4h-1-1v-2c1 0 1 0 2-1v-2z" class="T"></path><path d="M86 270v1c-1 1-3 2-3 3h0c0 1-1 2-1 2-1 1-1 3-2 4-2 5-3 10-4 15l1 1v1c0 1-1 2-1 3h0v1c0 1-1 3-1 4 1 0 1 0 1 1h-1-1v-2l1-8c0-2 1-5 1-7-1-1-1-1-1-2 1-2 1-3 1-5v-1h2v-2c2-4 4-7 8-9z" class="P"></path><path d="M76 281h2l-2 8c-1-1-1-1-1-2 1-2 1-3 1-5v-1z" class="N"></path><defs><linearGradient id="I" x1="77.355" y1="274.685" x2="83.802" y2="275.031" xlink:href="#B"><stop offset="0" stop-color="#888787"></stop><stop offset="1" stop-color="#a8a8ab"></stop></linearGradient></defs><path fill="url(#I)" d="M89 265l2 1v1c1 1 2 1 3 2 1 0 2 1 2 2l1 2-2-1-1-1c-2 0-4-1-6-2-1 1-2 1-2 1-4 2-6 5-8 9v2h-2v1c-1 0-1-1-1-2 0-3 1-5 2-7v1h1l4-5c1-2 2-2 4-3 1 0 2 0 3-1z"></path><path d="M89 265l2 1v1l-1-1c-1 1-2 1-3 1-4 1-7 4-9 7-1 3-1 5-2 7v1c-1 0-1-1-1-2 0-3 1-5 2-7v1h1l4-5c1-2 2-2 4-3 1 0 2 0 3-1z" class="B"></path><path d="M87 267c1 0 2 0 3-1l1 1c1 1 2 1 3 2 1 0 2 1 2 2l1 2-2-1-1-1c-2 0-4-1-6-2h-2-1c0-1 1-2 2-2h0z" class="d"></path><path d="M94 271l1 1 2 1h0 0v4s-1 0-1 1v1c-1 2-2 4-2 6v1h-2v1h-1c-2 0-3-2-5-2h0l-1-1-1-1h-1l1-1c1-2 2-2 2-4h1l1 1 1-1-1-1v-1c0-1 3-2 4-3-1 0-1-1-1-1 1 0 2 0 3-1z" class="B"></path><path d="M84 282h3v-1h-1l1-1 1 1v1h1v1c2 0 3 0 4 1v1h1v1h-2v1h-1c-2 0-3-2-5-2h0l-1-1-1-1h-1l1-1z" class="R"></path><path d="M89 283c2 0 3 0 4 1l-1 1h-2c0-1-1-1-1-2z" class="C"></path><path d="M94 271l1 1 2 1h0 0v4s-1 0-1 1v1c-2 1-3 0-5 0v1c1 0 2 0 2 1v1c-1-1-2-1-3-2l-1-2-1-1v-1c0-1 3-2 4-3-1 0-1-1-1-1 1 0 2 0 3-1z" class="Q"></path><path d="M91 276c1 0 2 0 2 1v1c-1 0-2 0-3-1l1-1z" class="I"></path><path d="M94 271l1 1 1 1v1h-1c-1-1-1-1-2-1h-1c-1 0-1-1-1-1 1 0 2 0 3-1z" class="T"></path><path d="M92 273h1l-2 3-1 1h-1v-1h-1v1-1c0-1 3-2 4-3z" class="C"></path><path d="M83 296c1-1 1-1 2 0 1 0 1 0 2 1l2 2v2l2 2c0 1 1 2 0 4v2h-2 0c0 1-1 1-1 2l-2-1h-2c0-2 0-2-1-2h-5 0v-1h3 0-1v-1s0-1-1-1h0v-1c0-2 0-2-2-3 1-1 1-2 1-3l4-3 1 1z" class="G"></path><path d="M80 302l-1-2c1-1 1-2 1-2 0-1 1-1 2-1h0c0 1-1 2-1 2v1 1l-1 1z" class="S"></path><path d="M81 299l1 1 1-1 1 1c0 1 0 1-1 1v2c-1 0-1 0-2-1v1c0 1 0 1-1 2v-3l1-1v-1-1z" class="E"></path><path d="M83 296c1-1 1-1 2 0 1 0 1 0 2 1l2 2v2l2 2c0 1 1 2 0 4v2h-2 0c-2 0-3 0-4-1l-1-1h0c1-1 0-2 1-3h0 1v1l-1 1v1h2v-2c1-1 1-1 1-2-1-3-3-5-5-7z" class="H"></path><path d="M91 303c0 1 1 2 0 4h-3l2-4h1z" class="W"></path><path d="M108 239l1-1v1l1-1-3 10-2 5-4 15c-1 1-1 1-1 2 0 4-1 8-2 12-2 1-2 1-3 2 0 0 0 1 1 2l-1 1h-1-2v-1h2v-1c0-2 1-4 2-6v-1c0-1 1-1 1-1v-4h0 0l-1-2c0-1-1-2-2-2-1-1-2-1-3-2v-1l-2-1c-1 1-2 1-3 1-2 1-3 1-4 3l-4 5h-1v-1l2-7 2-2h1c2-2 4-3 5-5 2-1 3-2 4-3v-2c2-1 3-2 4-3h1l1-1v-1h0l-1 1v-2c0-1 1-1 1-1h2v-1l6-4 1-1 2-2h0z" class="D"></path><path d="M100 263l1 1-3 3c1-1 1-1 1-2h-1 0v-1l2-1z" class="U"></path><path d="M98 261c2-1 3-2 5-3 0 2-2 4-3 4s-1-1-2-1z" class="M"></path><path d="M94 262c1-1 3-1 4-1s1 1 2 1v1l-2 1v1h-3v-2h1l-1-1h-1z" class="Y"></path><path d="M96 263l1-1 1 2v1h-3v-2h1z" class="F"></path><path d="M96 254c1-3 5-3 7-5 1-1 2-2 3-2 0 1-1 2-2 3-1 2-5 3-6 3s-2 1-2 1z" class="Z"></path><path d="M95 265h3 0 1c0 1 0 1-1 2v1h1c-1 1-1 2-2 3v2h0 0l-1-2c0-1-1-2-2-2-1-1-2-1-3-2v-1c1-1 3-1 4-1z" class="V"></path><path d="M108 239l1-1v1c-1 2-2 4-2 6v1l-1 1c-1 0-2 1-3 2-2 2-6 2-7 5l-3 1-2 1v-2c2-1 3-2 4-3h1l1-1v-1h0l-1 1v-2c0-1 1-1 1-1h2v-1l6-4 1-1 2-2h0z" class="f"></path><path d="M108 239l1-1v1c-1 2-2 4-2 6v1l-1-1-1 1c-1 1-1 1-2 1h-1l1-1c1 0 2-1 3-2s0-1 0-2h-1l1-1 2-2h0zm-14 16l9-3h1c-1 1-1 3-2 4h-1c-1 3-2 3-5 4l-2 1v1h0 1l1 1h-1v2c-1 0-3 0-4 1l-2-1c-1 1-2 1-3 1-2 1-3 1-4 3l-4 5h-1v-1l2-7 2-2h1c2-2 4-3 5-5 2-1 3-2 4-3l2-1h1z" class="a"></path><path d="M101 256l-1 1h-1c0 1-1 1-1 0s1-1 2-2h2v1h-1z" class="Y"></path><path d="M94 262h0 1l1 1h-1v2c-1 0-3 0-4 1l-2-1c-1 1-2 1-3 1l-1-1v-1c2 0 5-1 6-2h3z" class="f"></path><path d="M89 265h2l1-1h2 0l1-1v2c-1 0-3 0-4 1l-2-1z" class="d"></path><path d="M91 256l2-1h1c-4 3-11 7-13 12l1 2-4 5h-1v-1l2-7 2-2h1c2-2 4-3 5-5 2-1 3-2 4-3z" class="M"></path><path d="M107 248h0c0 1 0 1 1 2 0 1-1 3-1 5h1v1 2 1l-5 22v6c-1 2-2 4-2 6 0 1 0 2-1 3h2v1h0c-1 1-2 6-2 8l1 1c0 4-2 10-2 14v2c-1 1 0 2 0 3-1 1-1 1-1 3h-1 0-3v1c-1 0-2 1-2 1l-1 1v-1h-1c0-3 0-6 1-10l1-11 2-8v-4l1-4c1-3 2-6 2-8l1-3c1-4 2-8 2-12 0-1 0-1 1-2l4-15 2-5z" class="W"></path><path d="M96 313v2 1c1-1 1-2 1-2l-1 13 1 1h0-3v1c-1 0-2 1-2 1h-1l1-2h1c1-1 1-4 1-5l2-10z" class="D"></path><path d="M108 256v2 1l-5 22c-1 3-1 6-2 8-1 6-3 13-3 19-1 2-1 4-1 6 0 0 0 1-1 2v-1-2c0-12 4-23 6-34l3-15 3-8z" class="X"></path><path d="M97 314c0-2 0-4 1-6 0-6 2-13 3-19 1-2 1-5 2-8v6c-1 2-2 4-2 6 0 1 0 2-1 3h2v1h0c-1 1-2 6-2 8l1 1c0 4-2 10-2 14v2c-1 1 0 2 0 3-1 1-1 1-1 3h-1l-1-1 1-13z" class="M"></path><path d="M107 248h0c0 1 0 1 1 2 0 1-1 3-1 5h1v1l-3 8-3 15h-1s0 1-1 2v-2c-1 2-1 6-2 7l-1-1 1-3c1-4 2-8 2-12 0-1 0-1 1-2l4-15 2-5z" class="J"></path><path d="M107 248h0c0 1 0 1 1 2 0 1-1 3-1 5h1v1l-3 8s-1-1-1-2 1-3 1-4v-2c1-1 1-2 0-3l2-5z" class="K"></path><path d="M75 287h0c0 1 0 1 1 2 0 2-1 5-1 7l-1 8v2h1v5c1-1 1-2 2-2l1-1h0 5c1 0 1 0 1 2h2l2 1c0-1 1-1 1-2h0 2 1l-1 11c-1 4-1 7-1 10 0 2 0 2 1 2l2 2h0c-2-1-3-2-5-2-3 0-6 1-9 3h-1l-2 1h0-1l-1-1c-1-1-1-1-2-1h-1-1l-1-3c0-2-1-2-1-4v-2h-1v-1c1-1 0-3 1-5 1-1 1-4 1-6v-6l2-12h1l-1 3 2-1v-3c1-2 2-5 2-7z" class="B"></path><path d="M71 331s1-1 2 0c0 0 0 1 1 1-1 1-1 2-2 2h-1v-3z" class="N"></path><path d="M73 297l-1 7v-1c-1-2-1-3-1-5l2-1z" class="C"></path><path d="M73 314c0-3 1-7 1-10v2h1v5 1h0v2h-2z" class="D"></path><path d="M73 314h2c0 2-1 4 0 6v2c-1 1 0 1 0 2h1 0l-1 2h1l-1 1c-1 0-1 1 0 2l-1 1h-1v-3c0-1-1-1 0-2v-7-2-2z" class="C"></path><path d="M69 313c1 1 1 2 1 3 0 2-1 4-1 6 1 3 1 6 2 9v3h-1l-1-3c0-2-1-2-1-4v-2h-1v-1c1-1 0-3 1-5 1-1 1-4 1-6z" class="X"></path><path d="M69 307l2-12h1l-1 3c0 2 0 3 1 5v1c-1 3-1 8-2 12 0-1 0-2-1-3v-6z" class="P"></path><path d="M76 329h0c1 0 3 1 4 2v1c-1 1-1 1-1 3h-1l-2 1h0-1l-1-1c-1-1-1-1-2-1 1 0 1-1 2-2s2-2 2-3z" class="C"></path><path d="M76 333l1-1s1-1 2-1l1 1h0c-1 1-1 1-1 3h-1c0-1-1-1-2-2z" class="E"></path><path d="M74 335c0-1 0-1 1-2h1c1 1 2 1 2 2l-2 1h0-1l-1-1z" class="Q"></path><path d="M78 323l1-1h1 2v2h0v2s1 0 1 1 0 2-1 3h-1l-1 1c-1-1-3-2-4-2h0-1c-1-1-1-2 0-2l1-1h-1l1-2c1 0 1-1 2-1z" class="C"></path><path d="M80 322h2v2h-2v-2z" class="Q"></path><path d="M76 324c1 0 1-1 2-1 0 1-1 2 0 3 0 0 1 1 1 2l-1 1 1 1h2l-1 1c-1-1-3-2-4-2h0-1c-1-1-1-2 0-2l1-1h-1l1-2z" class="R"></path><path d="M78 308h0 5c1 0 1 0 1 2h2l2 1c0-1 1-1 1-2h0 2 1l-1 11c-1 4-1 7-1 10 0 2 0 2 1 2l2 2h0c-2-1-3-2-5-2-3 0-6 1-9 3 0-2 0-2 1-3v-1l1-1h1c1-1 1-2 1-3s-1-1-1-1v-2h0v-2h-2-1l-1 1c-1 0-1 1-2 1h0-1c0-1-1-1 0-2v-2c-1-2 0-4 0-6v-2h0v-1c1-1 1-2 2-2l1-1z" class="I"></path><path d="M84 316l2 1v1h-2v-2z" class="K"></path><path d="M81 320v-2h1l1 1h0c1 1 1 1 3 1v1c-1 0-2 1-3 1h-1l-1-2z" class="N"></path><path d="M89 321l2-2v1c-1 4-1 7-1 10 0 2 0 2 1 2l2 2h0c-2-1-3-2-5-2v-1h-2 0l1-1 1 1 1-1-1-1h-1-1v-1h2 1v-7z" class="T"></path><path d="M89 309h2 1l-1 11v-1l-2 2v-1-2c0-1-1-1-2-2l-1 1-2-1h0v-1h-2 0v-1h3c0-1 1-2 0-3v-1h1l2 1c0-1 1-1 1-2h0z" class="C"></path><path d="M89 309h2l-1 3-2-1c0-1 1-1 1-2h0z" class="D"></path><path d="M86 310l2 1 2 1v1l-1 1s-1-1-1 0h0c0 1-1 1-1 1-1 0-2 0-2-1s1-2 0-3v-1h1z" class="K"></path><path d="M78 308h0 5c1 0 1 0 1 2h2-1v1c1 1 0 2 0 3h-3v1h0 2v1h0v2c-1 0-1 1-1 1l-1-1h-1v2l1 2h0-2-1l-1 1c-1 0-1 1-2 1h0-1c0-1-1-1 0-2v-2c-1-2 0-4 0-6v-2h0v-1c1-1 1-2 2-2l1-1z" class="I"></path><path d="M76 324c0-1 0-3 1-4v-1c0-1-1-1-1-2l1-1 2 3 2 1 1 2h0-2-1l-1 1c-1 0-1 1-2 1h0z" class="E"></path><path d="M84 310h2-1v1c1 1 0 2 0 3h-3v1h0 2v1h0v2c-1 0-1 1-1 1l-1-1h-1v2l-2-1-2-3c0-1 1-3 2-4s3-2 5-2z" class="N"></path><path d="M85 311c1 1 0 2 0 3h-3v1c-1-2 0-2 0-3 1 0 2-1 3-1z" class="B"></path><path d="M79 319l1-1c0-1 0-1-1-2h0c0-1 0-1 1-2v2h1l1-1h2v1h0v2c-1 0-1 1-1 1l-1-1h-1v2l-2-1z" class="Q"></path><path d="M87 423l1-1c1 1 1 2 2 4v1h0v4l2 2-1 2c1 0 1 0 1 1h0v-2l1 1c1-1 1-1 2 0v3c1 1 1 2 1 4 1 1 1 2 1 3h0l2 3v-3-4l2 9 8 25h1v-3h1v2l1-1c-2-7-5-13-6-20l1 1 1-1c2 7 4 13 6 19 9 24 20 47 35 68 3 3 5 6 8 9l7 8c0 1 1 2 3 3h0 1l-1 2h0c-1 4-1 9-2 13l-2 2v-1h-1l-1 2-1-1c0-1-1-2-1-3v3-1l-1 15h-1v4l-1-1h-1 0v2 1l-1-1-2-7c0-1 0-1-1-3 0 0 0-1 1-1 0-1 0-2-1-4v4h0c-1-2-2-3-3-4-2-1-3-2-4-3l-2-1v1c-1-1-2-2-3-4-1 0-1-2-2-2-1-1-2-2-2-3h0c-1-1-1-1-2-1-3-3-6-7-8-11-16-21-28-43-38-67-1-4-4-9-4-13 1-3-1-6-2-8h1l-8-29h1v-1l1-2c1-1 1-1 1-2 1 0 0-1 0-1 0-1 1-2 1-2l-1-1v-2l2-1h0v-1l1-1h0v-1h3c0-1 0-1-1-2h0 1l5 2v-1l-1-1z" class="R"></path><path d="M106 521h1 1s0 1 1 1v1h-1v1h1c1 1 1 2 2 2 0 0 0 1 1 1h0l-1 1h0v2l-5-9z" class="b"></path><path d="M111 528c3 2 2 3 3 6 1 1 2 2 2 3l2 3c0 2 2 5 3 6s1 2 1 3h0l-11-19v-2zm-12-21h0l1 2h1l-1-2h1l1 2v1c1 1 2 3 2 4v1c0 1 1 1 1 2l2 2h2l1 1h0l1 1c0-1 1-1 1-1s-1 1-1 2h0-1c-1 1-1 1-1 2h0-1v-1h1v-1c-1 0-1-1-1-1h-1-1c-1-1-2-3-2-4l-5-10z" class="X"></path><path d="M112 527h1v1h0c2 0 3 1 3 1l1 1v1l-1 1h0 1c0-1 1-1 1-1v-1h0c1 1 1 2 1 3h2v2h1-2v2l-1 2c-1 0-1 0-1 1l-2-3c0-1-1-2-2-3-1-3 0-4-3-6h0l1-1h0z" class="E"></path><path d="M116 537h3v-1s-1-1-1-2h1c1 0 1 0 1 1v2l-1 2c-1 0-1 0-1 1l-2-3z" class="C"></path><path d="M118 540c0-1 0-1 1-1 0 0 1 0 2 1h2l-1 1-1 1h-1c1 3 4 4 6 7 1 2 3 4 5 6l1-1 2 2h-1l-3-1v1c1 2 2 3 3 4v1h0c-1 0-2-1-2-1 0-2-3-4-4-5-2-2-4-4-5-6h0c0-1 0-2-1-3s-3-4-3-6z" class="G"></path><path d="M121 540h2l-1 1-1 1h-1c-1-1-1-1-1-2h2z" class="b"></path><path d="M123 540c1 0 2 1 3 1 1 1 2 2 2 3l-1 1c0 1 1 2 1 2 0 1 0 2 1 2 0 1 1 2 2 3 0-1 0-1-1-1v-3c1 0 2 1 2 1v2h1 0l1 1-1 1-1 1-1 1c-2-2-4-4-5-6-2-3-5-4-6-7h1l1-1 1-1z" class="L"></path><path d="M122 541h1c2 1 3 1 4 2l-1 1v1h-1l-1-2h-2l-1-1 1-1z" class="Q"></path><path d="M84 476h1c0 1 1 2 1 3s1 2 1 3v-1-3c1 3 2 6 4 9l4 7 4 8c0 2 1 3 2 4l1 1v2l-1-2h-1l1 2h-1l-1-2h0c-1-1-2-3-3-5-4-9-9-17-12-26z" class="P"></path><defs><linearGradient id="J" x1="119.111" y1="531.183" x2="122.903" y2="529.245" xlink:href="#B"><stop offset="0" stop-color="#5b5b5c"></stop><stop offset="1" stop-color="#727071"></stop></linearGradient></defs><path fill="url(#J)" d="M114 525c2 1 3 2 5 3h1l-1-1 1-1h1v2l1 1h0c2 0 4 1 5 2v1h-1v2h-1-1v2h1c1 1 2 2 2 3l-2-1v1h1v2c-1 0-2-1-3-1h-2c-1-1-2-1-2-1l1-2v-2h2-1v-2h-2c0-1 0-2-1-3h0l1-1c-1-1-3-2-5-3v-1z"></path><path d="M122 535v-1-1h1c1 0 1 1 2 1h0-1v2h1c1 1 2 2 2 3l-2-1-1-1h-4v-2h2z" class="B"></path><path d="M120 537h4l1 1v1h1v2c-1 0-2-1-3-1h-2c-1-1-2-1-2-1l1-2z" class="M"></path><path d="M112 520h1v-2c1 0 2 2 3 2 2 1 4 2 5 2l1 1h0-1c-1 1-1 2-1 3l-1 1 1 1h-1c-2-1-3-2-5-3v1c2 1 4 2 5 3l-1 1v1s-1 0-1 1h-1 0l1-1v-1l-1-1s-1-1-3-1h0v-1h-1c-1 0-1-1-1-1-1 0-1-1-2-2h0c0-1 0-1 1-2h1 0c0-1 1-2 1-2z" class="N"></path><path d="M111 522c0 1 1 1 1 2h1v-1h3l1 1h0c0 1 0 1-1 1-1-1-1-1-2-1v1 1c2 1 4 2 5 3l-1 1v1s-1 0-1 1h-1 0l1-1v-1l-1-1s-1-1-3-1h0v-1h-1c-1 0-1-1-1-1-1 0-1-1-2-2h0c0-1 0-1 1-2h1 0z" class="B"></path><path d="M132 529c2 2 3 5 4 8 0 1 1 1 1 2 1 2 1 2 1 4l-1 1v2c-2 0-2 0-3 1h0-3 0l-1 1v3c1 0 1 0 1 1-1-1-2-2-2-3-1 0-1-1-1-2 0 0-1-1-1-2l1-1c0-1-1-2-2-3v-2h-1v-1l2 1c0-1-1-2-2-3h0s1 0 1-1h0 1c1 0 1 1 3 1v-4l1 2h0c1-1 1-1 2-1-1-2-1-3-1-4z" class="J"></path><path d="M130 532l1 2c0 1-1 2 0 3v3h-1v-4h-1l-2 1c1 1 1 2 2 2v1h-1l-1-1h0c0-1-1-2-2-3h0s1 0 1-1h0 1c1 0 1 1 3 1v-4z" class="N"></path><path d="M132 529c2 2 3 5 4 8 0 1 1 1 1 2 1 2 1 2 1 4l-1 1v2c-2 0-2 0-3 1h0-3 0c0-1 0-1 1-2v-2h2 1l-2-2v-1c0-1 0-1 1-2v-1h-3c-1-1 0-2 0-3h0c1-1 1-1 2-1-1-2-1-3-1-4z" class="H"></path><path d="M134 538v-1h1v1l1 1c-1 1-1 1-2 1 0 1 0 1-1 1v-1c0-1 0-1 1-2z" class="C"></path><path d="M131 534h0 1 2v3h-3c-1-1 0-2 0-3z" class="Z"></path><path d="M137 539c1 2 1 2 1 4l-1 1v2c-2 0-2 0-3 1h0-3 0c0-1 0-1 1-2 2-1 3-1 5-1v-5h0z" class="D"></path><path d="M88 455l1 1h0c1 1 1 2 1 3l1 1-2 7c-1 1-2 2-1 4h0v4c1 0 1 1 1 1v2h1v-1 2l1-2h1v4c1 1 1 2 1 3l4 10c0 1 0 1 1 2h0 0c1 2 2 5 2 7l1 1-1 1h1v1c-1-1-2-2-2-4l-4-8-4-7c-2-3-3-6-4-9v-1l-1-9-1-2v-7-2-1c1 1 2 2 3 2v-3z" class="K"></path><path d="M91 477h1v4c1 1 1 2 1 3l-1-1c-1-1-1-2-2-4l1-2z" class="I"></path><path d="M89 456h0c1 1 1 2 1 3l1 1-2 7v-1c-1 0-1-3-1-4 1-1 1-1 1-2v-4z" class="O"></path><path d="M87 477c1 0 1-1 2-1 0 4 2 8 2 11-2-3-3-6-4-9v-1z" class="H"></path><path d="M85 456c1 1 2 2 3 2-1 1-1 3-1 4 0 2 0 5-1 5 0 0 0-1-1-1v-7-2-1z" class="O"></path><path d="M136 537l2 3c1 1 1 2 1 3 3 0 3 3 5 4v2l1 1 1 1 2 3 1 1c-1 2-2 3-4 4v1h-1l-2-2v1l-1 1c0 1-1 1-2 1v-1l-3-3c-1 0-1 0-1-1h-1l-2-2 1-1 1-1-1-1h0-1v-2s-1-1-2-1l1-1h0 3 0c1-1 1-1 3-1v-2l1-1c0-2 0-2-1-4 0-1-1-1-1-2z" class="I"></path><path d="M139 550h0 1c0 1 1 1 1 2 1 0 2 0 2 1 0 0-1 1-1 2h0l-1 1c0-1-1-1-1-2v-2c-1-1-1-1-1-2z" class="M"></path><path d="M146 551l2 3 1 1c-1 2-2 3-4 4h0c-2-1-2-1-2-3-1 0 0 0 0-1 1-2 2-3 3-4z" class="F"></path><path d="M146 551l2 3h-1v2l-1 1s-1 0-1-1c0 0 0-1-1-1h-1c1-2 2-3 3-4z" class="J"></path><path d="M134 552c1 0 1 0 3 1 1 1 1 2 2 3l1 1h0-1-1l1 1h1l2 1-1 1c0 1-1 1-2 1v-1l-3-3c-1 0-1 0-1-1h-1l-2-2 1-1 1-1z" class="N"></path><path d="M134 552c1 0 1 0 3 1 1 1 1 2 2 3h-1c-1 0-3-1-4-1-1-1-1-2-1-2l1-1z" class="K"></path><path d="M136 537l2 3c1 1 1 2 1 3 3 0 3 3 5 4v2l1 1h0v1l-2 2c0-1-1-1-2-1 0-1-1-1-1-2h-1 0-2-1c-1-1-1-1 0-2v-1l1 1 1-1v-1h-1v-2l1-1c0-2 0-2-1-4 0-1-1-1-1-2z" class="E"></path><path d="M139 550c1 0 2 0 2-1 1 0 1-1 2-1v1c0 1 1 1 2 1v1l-2 2c0-1-1-1-2-1 0-1-1-1-1-2h-1z" class="F"></path><path d="M76 437c1-1 1-1 1-2l1 1 1 1h0l2 2v2l1 2v1c1 2 1 4 2 5v1c0 1 0 3 1 4h1l-1 2v1 2 7l1 2 1 9v1 3 1c0-1-1-2-1-3s-1-2-1-3h-1l-2-7-8-29h1v-1l1-2z" class="U"></path><path d="M83 461v-3h1l1 1h0v7l1 2c-1-2-2-4-3-7z" class="E"></path><path d="M83 451c0-1 0-1 1-2v1c0 1 0 3 1 4h1l-1 2v1 2h0l-1-1h-1v3l-1-2h0l-1-1c0-1-1-2-1-4 1 0 1 1 2 1v-3-1h1z" class="C"></path><path d="M83 451c0-1 0-1 1-2v1c0 1 0 3 1 4h1l-1 2v1c-1-1-1-1-3-1v-1h1v-4z" class="I"></path><path d="M76 437c1-1 1-1 1-2l1 1 1 1h0l2 2v2l1 2v1c1 2 1 4 2 5-1 1-1 1-1 2h-1v1 3c-1 0-1-1-2-1 0-1-1-3-1-5v-1c-1 0-1 0-1-1v-1c-1-1-1-3-2-4 1-1 1-2 1-2-1-1-1-1-2-1l1-2z" class="G"></path><path d="M79 437l2 2v2l1 2v1c1 2 1 4 2 5-1 1-1 1-1 2h-1l-1-1c0-1-1-1-1-1v-2c0-1-1-1-1-1v-1-2-1h1c-1-2-2-3-1-5z" class="B"></path><path d="M82 443v1c1 2 1 4 2 5-1 1-1 1-1 2h-1l-1-1 1-1-1-1c0-2 0-2-1-3v-1c1-1 1 0 2-1z" class="R"></path><path d="M115 506c0 1 1 1 1 2 1 2-1 4 2 4h0c2 1 3 1 4 1l3 3c1 1 3 4 4 5v2l3 6c0 1 0 2 1 4-1 0-1 0-2 1h0l-1-2v4c-2 0-2-1-3-1h-1 0c0 1-1 1-1 1h0-1v-2h1 1v-2h1v-1c-1-1-3-2-5-2h0l-1-1v-2h-1c0-1 0-2 1-3h1 0l-1-1c-1 0-3-1-5-2-1 0-2-2-3-2v2h-1s-1 0-1 1l-1-1h0l-1-1h-2l-2-2c0-1-1-1-1-2v-1c0-1-1-3-2-4v-1-2l4 4c1-1 2-1 3-2l2 2h0 0l2-2h0c0-1 1-2 2-3z" class="V"></path><path d="M125 534h1v-2h1l2 1v2s-1-1-2 0h-1 0c0 1-1 1-1 1h0-1v-2h1z" class="Q"></path><path d="M121 526h1c1 0 1 0 2-1h0v-1h1v1l1 1c0 1-1 1-2 2h-3v-2z" class="B"></path><defs><linearGradient id="K" x1="129.909" y1="523.884" x2="128.945" y2="532.591" xlink:href="#B"><stop offset="0" stop-color="#8f8e8e"></stop><stop offset="1" stop-color="#ababab"></stop></linearGradient></defs><path fill="url(#K)" d="M128 523h1l3 6c0 1 0 2 1 4-1 0-1 0-2 1h0l-1-2c0-1-1-2-1-3 0-2-1-3-2-4l1-2z"></path><path d="M116 515l1-1 4 1v1h3 0 1c1 1 3 4 4 5v2h-1l-1 2c-1-1-2-2-3-2-1-1-3-2-4-3-2 0-4-1-6-3h0c0-1 0-2 1-2h1z" class="V"></path><path d="M122 517l1 1h0v2c0 1 1 1 1 1h3c0 1 0 1 1 2l-1 2c-1-1-2-2-3-2-1-1-3-2-4-3h-1v-1-1h3v-1z" class="W"></path><path d="M114 517h0c0-1 0-2 1-2h1c2 0 4 1 6 2v1h-3v1 1h1c-2 0-4-1-6-3z" class="Z"></path><path d="M115 506c0 1 1 1 1 2 1 2-1 4 2 4h0c2 1 3 1 4 1l3 3h-1 0-3v-1l-4-1-1 1h-1c-1 0-1 1-1 2h0-1c0 1-1 1-1 2-1 1-1 1-2 1l-1-1h-2l-2-2c0-1-1-1-1-2v-1c0-1-1-3-2-4v-1-2l4 4c1-1 2-1 3-2l2 2h0 0l2-2h0c0-1 1-2 2-3z" class="L"></path><path d="M109 509l2 2h0c2 0 2 0 4-1v1c0 1-1 2-1 3-1 0-2 2-2 2-1 0-2 1-2 1-1-1-2-1-2-1v-1h2v-1c-1-1-2-2-4-3 1-1 2-1 3-2z" class="K"></path><path d="M115 506c0 1 1 1 1 2 1 2-1 4 2 4h0c2 1 3 1 4 1l3 3h-1 0-3v-1l-4-1-1 1h-1c-1 0-1 1-1 2h0-1c0 1-1 1-1 2-1 1-1 1-2 1l-1-1 1-1 2-2s1-2 2-2c0-1 1-2 1-3v-1c-2 1-2 1-4 1h0l2-2h0c0-1 1-2 2-3z" class="F"></path><path d="M87 423l1-1c1 1 1 2 2 4v1h0v4l2 2-1 2c1 0 1 0 1 1h0v-2l1 1c1-1 1-1 2 0v3c1 1 1 2 1 4 1 1 1 2 1 3h0l-1 1v4l-2-1-3 11-1-1c0-1 0-2-1-3h0l-1-1v3c-1 0-2-1-3-2l1-2h-1c-1-1-1-3-1-4v-1c-1-1-1-3-2-5v-1l-1-2v-2l-2-2h0l-1-1-1-1c1 0 0-1 0-1 0-1 1-2 1-2l-1-1v-2l2-1h0v-1l1-1h0v-1h3c0-1 0-1-1-2h0 1l5 2v-1l-1-1z" class="I"></path><path d="M81 441v-2h1c1 1 1 2 1 3s1 3 1 4l2-1v4 3 2h-1c-1-1-1-3-1-4v-1c-1-1-1-3-2-5v-1l-1-2z" class="C"></path><path d="M84 446l2-1v4c-1 0-1 0-2-1v-2h0z" class="G"></path><path d="M81 428c1 0 2 0 3 1v2 4h0c1 0 1-2 2-2 0 1 1 1 1 2l1-1h1c-1 2-2 4-2 6v3h-1c-2-1-2-1-2-2s-1-2 0-3h0c-1-1-1-2-1-2v-1-3-1h0v-1l-1-1h-2 0l1-1z" class="D"></path><path d="M88 434h1c-1 2-2 4-2 6v3h-1c-2-1-2-1-2-2l1-1v-1h1c0-1 0-1-1-2l2-2 1-1z" class="G"></path><path d="M87 423l1-1c1 1 1 2 2 4v1h0v4l-1 2v1h-1l-1 1c0-1-1-1-1-2-1 0-1 2-2 2h0v-4-2c-1-1-2-1-3-1l-1 1-1-1h0v-1l1-1h0v-1h3c0-1 0-1-1-2h0 1l5 2v-1l-1-1z" class="E"></path><path d="M79 428v-1l1-1h1v2l-1 1-1-1h0z" class="B"></path><path d="M88 434c-1-1-2-2-2-3l1-1c1 1 1 2 2 3v1h-1zm-1-11l1-1c1 1 1 2 2 4v1h-1v2h0-2c0-2 0-2-1-4h-2v1l-1-1c0-1 0-1-1-2h0 1l5 2v-1l-1-1z" class="D"></path><path d="M90 431l2 2-1 2c1 0 1 0 1 1h0v-2l1 1c1-1 1-1 2 0v3c1 1 1 2 1 4 1 1 1 2 1 3h0l-1 1v4l-2-1-3 11-1-1c0-1 0-2-1-3h0l-1-1v3c-1 0-2-1-3-2l1-2v-2-3-4l1-2v-3c0-2 1-4 2-6v-1l1-2z" class="d"></path><path d="M95 438c1 1 1 2 1 4 1 1 1 2 1 3h0l-1 1v4l-2-1c1-4 1-8 1-11z" class="E"></path><path d="M92 434l1 1c0 2 0 4-1 6v3h0c-1 1-1 2-1 3-1 3-1 6-2 9h0l-1-1v3c-1 0-2-1-3-2l1-2v-2h2c1-1 1-2 1-2 1-3 0-6 1-8 0-3 1-5 1-7 1 0 1 0 1 1h0v-2z" class="J"></path><path d="M86 452h2c1-1 1-2 1-2 0 1-1 3-1 5v3c-1 0-2-1-3-2l1-2v-2z" class="Y"></path><path d="M90 431l2 2-1 2c0 2-1 4-1 7-1 2 0 5-1 8 0 0 0 1-1 2h-2v-3-4l1-2v-3c0-2 1-4 2-6v-1l1-2z" class="f"></path><path d="M90 431l2 2-1 2c0 2-1 4-1 7 0-2 0-3-1-4h0l-2 2c0-2 1-4 2-6v-1l1-2z" class="Y"></path><path d="M110 477l2 5c1 2 3 3 4 5s1 4 2 6c3 7 6 14 10 21 1 1 1 2 2 3l3 6c1 2 3 4 3 6 1 2 2 3 3 4h-2l3 5-2 2-2-3c-1-3-2-6-4-8l-3-6v-2c-1-1-3-4-4-5l-3-3c-1 0-2 0-4-1h0c-3 0-1-2-2-4 0-1-1-1-1-2-1 1-2 2-2 3h0l-2 2h0 0l-2-2c-1 1-2 1-3 2l-4-4-1-1v-1h-1l1-1-1-1c0-2-1-5-2-7v-5c2-3 3-4 5-5h1c4 0 6 0 8 3h0c0-2 0-3-1-4l-3-7h1s0 1 1 1v-1h-1l1-1z" class="U"></path><path d="M119 509h2l1 2v1h-1c-1 0-1-1-2-1l-1 1h0c0-2 0-2 1-3z" class="I"></path><path d="M113 490c0 1 0 1 1 2s1 4 2 5l5 12h-2c-1 1-1 1-1 3-3 0-1-2-2-4 0-1-1-1-1-2-1 0-1 0-2-1v-3c-2 0-3 0-4-1v-1-1h-1s0-1-1-1h2l1-1v-1c-1 0-2 0-2-1h-1 3c2-2 2-2 3-5z" class="E"></path><path d="M107 495h3 0c1 1 1 1 2 1v2h1l2 2-1 2h0c-1-2-3-2-5-3h-1s0-1-1-1h2l1-1v-1c-1 0-2 0-2-1h-1z" class="R"></path><path d="M109 499c2 1 4 1 5 3h0c1 1 2 2 2 3 0 0 0 2 1 2 0 1 1 1 1 1l1 1c-1 1-1 1-1 3-3 0-1-2-2-4 0-1-1-1-1-2-1 0-1 0-2-1v-3c-2 0-3 0-4-1v-1-1z" class="H"></path><path d="M112 482c1 2 3 3 4 5s1 4 2 6c3 7 6 14 10 21 1 1 1 2 2 3l3 6c1 2 3 4 3 6 1 2 2 3 3 4h-2l3 5-2 2-2-3c-1-3-2-6-4-8l-3-6v-2h1c-6-13-14-25-18-39z" class="J"></path><path d="M130 521c3 3 5 8 7 12l3 5-2 2-2-3c-1-3-2-6-4-8l-3-6v-2h1z" class="T"></path><path d="M104 486c4 0 6 0 8 3h0l1 1c-1 3-1 3-3 5h-3 1c0 1 1 1 2 1v1l-1 1h-2c1 0 1 1 1 1h1v1 1c1 1 2 1 4 1v3c1 1 1 1 2 1-1 1-2 2-2 3h0l-2 2h0 0l-2-2c-1 1-2 1-3 2l-4-4-1-1v-1h-1l1-1-1-1c0-2-1-5-2-7v-5c2-3 3-4 5-5h1z" class="Y"></path><path d="M109 490c1 1 2 2 2 3-2 1-3 1-4 0 0 0 1 0 1-1 1 0 1-1 1-2z" class="Z"></path><path d="M103 488c1-1 2-1 3-1 2 1 2 2 3 3h0c0 1 0 2-1 2h0l-2-3c-1 1-1 1-2 1h-1v-2h0z" class="S"></path><path d="M103 488h0v2h1c1 0 1 0 2-1l2 3h0c0 1-1 1-1 1h-1v1c0 1 0 1 1 1h1c0 1 1 1 2 1v1l-1 1h-2l1-1-1-1h-1c-1 0-1-1-3 0 1 1 1 1 1 3h-1v-2h-3 0v-1c0-4 1-6 3-8z" class="V"></path><path d="M100 497h0 3v2h1c0-2 0-2-1-3 2-1 2 0 3 0h1l1 1-1 1c1 0 1 1 1 1h1v1 1c1 1 2 1 4 1v3c1 1 1 1 2 1-1 1-2 2-2 3h0l-2 2h0 0l-2-2c-2-1-4-3-6-5s-3-5-3-7z" class="S"></path><path d="M111 507v-3h1l1 1c1 1 1 1 2 1-1 1-2 2-2 3h0l-2-2z" class="C"></path><path d="M111 511l-3-3c1-1 1-1 2-3h0l1 2 2 2-2 2z" class="Z"></path><path d="M100 497h0 3v2h1c0-2 0-2-1-3 2-1 2 0 3 0h1l1 1-1 1c1 0 1 1 1 1h1v1l-2 1s-1 1-1 2c2 1 2 2 2 4h0c-1-1-2-1-2-2h0v-1-1c-1 0-1 0-1 1h-2c-2-2-3-5-3-7z" class="B"></path><path d="M99 441l2 9 8 25 1 2-1 1h1v1c-1 0-1-1-1-1h-1l3 7c1 1 1 2 1 4h0c-2-3-4-3-8-3h-1c-2 1-3 2-5 5v5h0 0c-1-1-1-1-1-2l-4-10c0-1 0-2-1-3v-4h-1l-1 2v-2 1h-1v-2s0-1-1-1v-4h0c-1-2 0-3 1-4l2-7 3-11 2 1v-4l1-1 2 3v-3-4z" class="G"></path><path d="M100 463v-2l1 1c0 2 2 3 2 6h-1s0-1-1-1v-1-2l-1-1z" class="D"></path><path d="M94 473c1-2 3-3 5-5 1 1 3 1 3 2 1 0 1 2 2 2l-1 1-2-1c-2 0-4 0-5 1l-1 1h0l-1-1z" class="K"></path><path d="M94 465l3-3c1 1 1 1 2 1h0 1l1 1c0 1-1 1-1 2h-1c-2 0-4 1-5 3-1 1-2 2-3 4 0 1 0 2-1 4v1h-1v-2s0-1-1-1c1-1 2-3 2-5l3-3c1 0 1-1 1-1v-1z" class="J"></path><path d="M94 449l2 1-1 1c-1 1-1 2-1 3 0 0 1 0 2 1l-1 1h1l2-1v1c0 1 0 1-1 2l1 1v1l-5 4 1 1v1s0 1-1 1l-3 3c0 2-1 4-2 5v-4h0c-1-2 0-3 1-4l2-7 3-11z" class="Z"></path><path d="M98 455v1c0 1 0 1-1 2l1 1h-2c-1 1-2 1-3 1 1-1 2-2 3-4l2-1z" class="J"></path><path d="M94 449l2 1-1 1c-1 1-1 2-1 3 0 0 1 0 2 1l-1 1h1c-1 2-2 3-3 4l-1 1 1 1c-1 0-1 0-2 1-1 2-2 5-3 8h0c-1-2 0-3 1-4l2-7 3-11z" class="C"></path><path d="M94 454s1 0 2 1l-1 1-1 1h-1l1-3z" class="I"></path><path d="M99 441l2 9 8 25 1 2-1 1h1v1c-1 0-1-1-1-1h-1 0l-2-5v-2c-1-1-1-1-1-2h0c0-1 0-1-1-2 0-1 0-1-1-2 0-1-1-2 0-3 0-1-1-2-1-3-1 0-2 0-2-1h-1l-1 2v-1l-1-1c1-1 1-1 1-2v-1l-2 1h-1l1-1c-1-1-2-1-2-1 0-1 0-2 1-3l1-1v-4l1-1 2 3v-3-4z" class="L"></path><path d="M94 454c0-1 0-2 1-3 0 1 1 2 1 4-1-1-2-1-2-1z" class="B"></path><path d="M98 455s1-1 2-1h1v2l-2 2-1 2v-1l-1-1c1-1 1-1 1-2v-1z" class="M"></path><path d="M99 441l2 9v2h0-1v-1c-1 1-1 1-1 2-1 1-1 1-2 1l-1-1v-2h2s0-1-1-2c0-1 0-2-1-3h0l1-1 2 3v-3-4z" class="C"></path><path d="M95 474h0l1-1c1-1 3-1 5-1l2 1 1-1c1 1 1 1 2 1h0l2 5h0l3 7c1 1 1 2 1 4h0c-2-3-4-3-8-3h-1c-2 1-3 2-5 5v5h0 0c-1-1-1-1-1-2l-4-10c0-1 0-2-1-3v-4h-1v-1l3-3 1 1z" class="I"></path><path d="M101 472l2 1v1s-1 0-1 1h-1c0-1-1-1-1-2l1-1z" class="U"></path><path d="M95 485l1-2v-2-1l1 1v3l-1 1h-1z" class="B"></path><path d="M95 476l1-1c1-1 2 0 3 1l-2 1h-3l1-1z" class="R"></path><path d="M96 490c1-1 1-2 2-2 0-1 0-1-1-1 1-1 1-1 2-1h1c0 1-1 2-1 2-1 0-1 0-1 1v2 5h0 0c-1-1-1-1-1-2v-1c1-2 0-2-1-3z" class="Q"></path><path d="M95 474h0l1-1c1-1 3-1 5-1l-1 1h-1c0 1 1 1 0 3-1-1-2-2-3-1l-1 1v-1-1z" class="L"></path><path d="M103 486v-1-1h-2 0v-1h2v-1h1 1 1c0 1 0 1 1 2h-1 1l1 1h-1c-1 0-2 0-3 1h-1z" class="S"></path><path d="M94 473l1 1v1 1l-1 1c0 1 0 2-1 3 1 1 2 0 2 1l-1 1c0 1 0 2 1 3h1c-1 1-1 1-1 2 1 0 2 1 2 2l-1 1c1 1 2 1 1 3v1l-4-10c0-1 0-2-1-3v-4h-1v-1l3-3z" class="P"></path><path d="M94 473l1 1v1c-3 2-3 3-3 6v-4h-1v-1l3-3z" class="R"></path><path d="M148 554h1l-1-2c2 1 3 2 4 4 0 1 2 2 2 3 1-1 1-1 1-2l3 3v1h2l-1-1v-1c2 2 3 4 3 6 1 0 2-1 2-2 0 1 1 1 1 2h0c0-1 1-2 1-3h1c-1 4-1 9-2 13l-2 2v-1h-1l-1 2-1-1c0-1-1-2-1-3v3-1l-1 15h-1v4l-1-1h-1 0v2 1l-1-1-2-7c0-1 0-1-1-3 0 0 0-1 1-1 0-1 0-2-1-4v4h0c-1-2-2-3-3-4-2-1-3-2-4-3v-2l-6-6 1-1-3-4c-1-1-1-2-1-2-1-1-1-2-2-2v-1c-1-1-2-2-3-4v-1l3 1h1 1c0 1 0 1 1 1l3 3v1c1 0 2 0 2-1l1-1v-1l2 2h1v-1c2-1 3-2 4-4l-1-1z" class="B"></path><path d="M155 568l2 2c1 0 1 0 1 1v1c-2 0-2-1-3-1-1-1 0-2 0-3z" class="K"></path><path d="M149 565l1 2c0 1 0 2 1 3 0 0 1 0 1 1h-4v-1h2c-1-1-2-2-2-4l1-1z" class="D"></path><path d="M133 560h0 2v-2c1 0 2 1 3 2h0v1h-1c-1 0-1 1-2 2-1-1-1-2-2-2v-1z" class="N"></path><path d="M157 570l1-2c1 2 1 2 1 4v1 1 3-1l-1-1v1-1l-1-1c0-1-1-1-1-2h2v-1c0-1 0-1-1-1z" class="E"></path><path d="M150 564l1 1v1c1 1 2 1 2 2v3h-1c0-1-1-1-1-1-1-1-1-2-1-3l-1-2s1 0 1-1z" class="R"></path><path d="M151 565h1v1h1 1c1 1 1 1 1 2s-1 2 0 3l-1 1-1-1v-3c0-1-1-1-2-2v-1z" class="D"></path><path d="M153 563h1v1l1 1v-1l4 1-2 1 1 1v1l-1 2-2-2c0-1 0-1-1-2h-1v-3z" class="C"></path><path d="M148 554h1l-1-2c2 1 3 2 4 4 0 1 2 2 2 3s-1 1-1 2h-2v2h2v3h-1v-1h-1l-1-1h-2v-1l-1 1c-1 0-2-1-2-2-1-1-1-1-2-1l1-1h1v-1c2-1 3-2 4-4l-1-1z" class="G"></path><path d="M151 561c0-1 0-1-1-2h0v-3h1c1 2 1 3 2 5h-2z" class="L"></path><path d="M145 560c1-1 3-1 4-1h0l-1 2h2c-1 1-2 1-2 2l-1 1c-1 0-2-1-2-2-1-1-1-1-2-1l1-1h1z" class="N"></path><path d="M139 569c0-2 1-3 3-4h0c0 1 0 1-1 1 0 1-1 1-1 2h1 1c1 0 1 1 1 1 1 1 2 1 2 2 1 0 4 3 5 3h1 0c-1 1-1 3 0 4v2 1 4h0c-1-2-2-3-3-4-2-1-3-2-4-3v-2l-6-6 1-1z" class="P"></path><path d="M144 576l4 3v2c-2-1-3-2-4-3v-2z" class="C"></path><path d="M139 569h3c0 1 1 2 2 2v1h0l-2 1v-1c-1 0-2-2-3-3z" class="B"></path><path d="M151 580h-1l-3-3c-1-1-3-2-4-4h3c1 1 2 1 3 2 0 1 0 2 1 3h1v2zm3-21c1-1 1-1 1-2l3 3v1h2l-1-1v-1c2 2 3 4 3 6 1 0 2-1 2-2 0 1 1 1 1 2h0c0-1 1-2 1-3h1c-1 4-1 9-2 13l-2 2v-1h-1l-1 2-1-1c0-1-1-2-1-3v-1-1c0-2 0-2-1-4v-1l-1-1 2-1-4-1v1l-1-1v-1h-1-2v-2h2c0-1 1-1 1-2z" class="I"></path><path d="M164 563c0 1 1 1 1 2h0c0 2 0 4-1 6h0c0-1-1-2-1-4 1 0 1-1 1-2l-1 1-1-1c1 0 2-1 2-2z" class="K"></path><path d="M159 565l1 1h1c1 1 1 2 1 3v7h-2v1c0-1-1-2-1-3v-1-1c0-2 0-2-1-4v-1l-1-1 2-1z" class="W"></path><path d="M154 559c1-1 1-1 1-2l3 3v1h2c1 2 1 3 1 5h-1l-1-1-4-1v1l-1-1v-1h-1-2v-2h2c0-1 1-1 1-2z" class="F"></path><path d="M154 559c1-1 1-1 1-2l3 3v1c-1 0-2 0-3-1v1 1h0 2l-1 1-1 1v1l-1-1v-1h-1-2v-2h2c0-1 1-1 1-2z" class="T"></path><path d="M151 574l1-1v1c1 0 1 1 2 2v-1-1h1l1 2h2v-1l1 1-1 15h-1v4l-1-1h-1 0v2 1l-1-1-2-7c0-1 0-1-1-3 0 0 0-1 1-1 0-1 0-2-1-4v-1-2c-1-1-1-3 0-4z" class="G"></path><path d="M154 576v-1-1h1l1 2c0 2 1 4 0 6h1c0 1 0 2-1 3-1-1-1-2-2-3v-2c-1-2 0-3 0-4z" class="K"></path><defs><linearGradient id="L" x1="153.738" y1="589.787" x2="157.223" y2="588.229" xlink:href="#B"><stop offset="0" stop-color="#6a6a69"></stop><stop offset="1" stop-color="#7f7d7f"></stop></linearGradient></defs><path fill="url(#L)" d="M154 582c1 1 1 2 2 3 1-1 1-2 1-3v9 4l-1-1h-1 0l-1-12z"></path><path d="M151 574l1-1v1c1 0 1 1 2 2 0 1-1 2 0 4v2l1 12v2 1l-1-1-2-7c0-1 0-1-1-3 0 0 0-1 1-1 0-1 0-2-1-4v-1-2c-1-1-1-3 0-4z" class="F"></path><path d="M108 453c2 7 4 13 6 19 9 24 20 47 35 68 3 3 5 6 8 9l7 8c0 1 1 2 3 3h0 1l-1 2h0-1c0 1-1 2-1 3h0c0-1-1-1-1-2 0 1-1 2-2 2 0-2-1-4-3-6v1l1 1h-2v-1l-3-3c0 1 0 1-1 2 0-1-2-2-2-3-1-2-2-3-4-4l1 2h-1l-2-3-1-1-1-1v-2c-2-1-2-4-5-4 0-1 0-2-1-3l2-2-3-5h2c-1-1-2-2-3-4 0-2-2-4-3-6l-3-6c-1-1-1-2-2-3-4-7-7-14-10-21-1-2-1-4-2-6s-3-3-4-5l-2-5-1-2h1v-3h1v2l1-1c-2-7-5-13-6-20l1 1 1-1z" class="C"></path><path d="M158 555v-1-1h0l4 4v1h-1c-2-1-2-2-3-3z" class="N"></path><path d="M162 557c2 2 3 3 4 5 0 1-1 2-1 3h0c0-1-1-1-1-2l-2-5v-1z" class="Q"></path><path d="M155 552l3 3c1 1 1 2 3 3h1l2 5c0 1-1 2-2 2 0-2-1-4-3-6l-3-5h-1c0-1 0-1-1-2h0 1 0z" class="F"></path><defs><linearGradient id="M" x1="106.676" y1="482.489" x2="123.603" y2="489.038" xlink:href="#B"><stop offset="0" stop-color="#9d9d9c"></stop><stop offset="1" stop-color="#c1c0c2"></stop></linearGradient></defs><path fill="url(#M)" d="M109 475h1v-3h1v2l1-1 9 24c1 1 1 3 2 4 1 3 3 5 4 7 0 2 1 4 1 6-4-7-7-14-10-21-1-2-1-4-2-6s-3-3-4-5l-2-5-1-2z"></path><path d="M127 508c2 3 3 4 4 8 1 1 2 3 3 5 1 1 2 2 3 4l8 13 10 14h0-1 0c1 1 1 1 1 2h1l3 5v1l1 1h-2v-1l-3-3c0 1 0 1-1 2 0-1-2-2-2-3-1-2-2-3-4-4l1 2h-1l-2-3-1-1-1-1v-2c-2-1-2-4-5-4 0-1 0-2-1-3l2-2-3-5h2c-1-1-2-2-3-4 0-2-2-4-3-6l-3-6c-1-1-1-2-2-3 0-2-1-4-1-6z" class="Z"></path><path d="M139 533c1 2 3 4 4 6h-2l-1-1-3-5h2z" class="K"></path><path d="M134 521c1 1 2 2 3 4l8 13h0c-2 0-4-4-5-5s-2-2-2-3c0-2-2-4-3-6v-1c-1-1-1-1-1-2z" class="J"></path><path d="M140 538l1 1h2l6 8c1 2 3 4 4 6 1 0 1 1 2 1h0 1l3 5v1l1 1h-2v-1l-3-3c0 1 0 1-1 2 0-1-2-2-2-3-1-2-2-3-4-4l1 2h-1l-2-3-1-1-1-1v-2c-2-1-2-4-5-4 0-1 0-2-1-3l2-2z" class="D"></path><path d="M141 539h2l6 8c1 2 3 4 4 6 1 0 1 1 2 1h0 1l3 5v1l1 1h-2v-1l-3-3-14-18z" class="N"></path><path d="M477 365l1-1c-1 0-2-1-2-2v-1c2 2 3 3 5 4 0-1 1-1 2-1v-1c1 0 2 0 3 1s3 0 5 0c-1 6-1 13-2 19l-1 15c0 5-1 9-1 14-1 2-1 4-1 6l-1 7c-2 9-3 19-6 28l-12 38-1 2s-1 0-1 1c-10 23-21 44-35 64-7 11-15 21-24 30-1-1-1 1-3 1 1 0 1-1 1-1 1-1 1-2 2-3v-1l-2 3h0-2 0c0 1-1 1-1 2h-1c-1 2-1 3-2 5l-2 6c-1 2-1 6-3 7l-2 2v-4l-1-1c-2 4-4 8-9 9l-2 1c-1-1-2-1-2-1h-1c-2-1-4-3-4-5h0c-1-1 0-2-1-2h-1l-3 2-1-1h-1l2-1 1-2c2-1 4-1 6-3-1-1-2-1-3-1 2-2 3-4 4-7l1-1v-2-2l-1-2h-1c0 1-1 1-2 1s-2 0-3-1-1-2-1-3c0-2 1-2 1-3 2-6 8-9 10-15h-1v-3c1-2 3-3 5-4l1-1 1-1v-1c1 0 2-2 3-2l6-8c12-17 21-34 29-53 5-12 9-24 13-37v-2l2-5c0-2 0-2 1-3v-1c0-3 1-5 1-8l3-13c2-12 3-24 4-36 0-2 1-6 0-8 1-2 0-2 2-3l-1-1v-2h-1l-1-1c-1-1-2-2-2-3 1-1 1-2 3-3h-2 0l2-1c4-2 8-2 12-2h1v1c4 0 7 0 11 2h2l3 3 2-1c-1-1-3-2-3-3v-1l2 1z" class="J"></path><path d="M390 604c0-1 0-4 1-5 0-1 0 0 1-1h0v3l-2 3z" class="M"></path><path d="M376 606c1 1 1 1 2 1 0 2 0 2-1 3l-1-1v-3z" class="T"></path><path d="M390 590c0 2 1 4-1 5v1c-1 1-1 1-1 2v-3-1l1-1c1-1 1-3 1-3z" class="B"></path><path d="M387 558c1 1 1 1 1 3l-1 1c-1 0-1 1-2 1v-1c0-2 1-2 2-4z" class="O"></path><path d="M379 604h1 2v1 1h0v3h-1 0 0c0-2-1-3-2-5z" class="L"></path><path d="M388 596v-1 3c0 2-1 3-1 5l-2-2v-2c-1-1 0-1 0-2h3v-1z" class="D"></path><path d="M388 586c1 1 1 2 2 4 0 0 0 2-1 3l-1 1v1 1c0-3 0-7-2-9h2v-1z" class="E"></path><path d="M385 599v2l2 2c-1 2-2 5-4 6h-1v-3h0v-1h1c1-2 2-4 2-6z" class="C"></path><path d="M452 406v1l1-1c0 2-1 5-1 7l-2 9v-5c-1-1 0-3 0-4l2-7z" class="T"></path><path d="M384 589l1-1 1-1c2 2 2 6 2 9v1h-3-1v-3c0 1-1 1-2 2 2-3 3-4 2-7z" class="P"></path><path d="M389 563h0v4c0 1-1 2 0 4s0 4 1 6c0 4 2 8 2 13 1 1 1 1 1 2v-2l1-2v4 4 1h-1c-1-1 0-2-1-3 0 1 1 3 0 4h0c0-5-1-11-2-16-1-2-2-5-2-8-1-2 0-3-1-5 0-2 0-4 2-6z" class="S"></path><path d="M450 417v5l-6 30h-1c1-1 1-2 0-3v-1s1-1 1-2l2-7 2-11 2-11z" class="P"></path><path d="M382 596c1-1 2-1 2-2v3h1c0 1-1 1 0 2 0 2-1 4-2 6h-1v-1h-2-1-1c-1 1-2 1-4 1-1 1-2 2-2 3h0c-1-1 0-2-1-2h-1l-3 2-1-1h-1l2-1c2-1 6-1 8-3 3-2 5-5 7-7z" class="U"></path><path d="M378 604c1-2 3-3 4-4 0 1 1 2 0 4h0-2-1-1z" class="C"></path><path d="M384 597h1c0 1-1 1 0 2 0 2-1 4-2 6h-1v-1h0c1-2 0-3 0-4 1-1 1-2 2-3z" class="G"></path><defs><linearGradient id="N" x1="436.406" y1="466.069" x2="440.461" y2="467.803" xlink:href="#B"><stop offset="0" stop-color="#454346"></stop><stop offset="1" stop-color="#5b5c59"></stop></linearGradient></defs><path fill="url(#N)" d="M443 448v1c1 1 1 2 0 3h1l-9 30c0 1-1 2-1 3l-8 17h0l-1-1c1-1 2-3 2-4 2-4 4-7 5-11l7-22c1-5 3-10 4-16z"></path><path d="M425 501l1 1-4 10v2c-1 3-3 6-5 9 0 2-1 2 0 4h-1l-1 4 1 1-3 4c0-1 0-2-1-2-1 3-3 6-5 8 0 0-1 0-1-1l-11 15c-2 1-3 1-4 2 10-13 20-27 27-42l7-15z" class="P"></path><path d="M412 532h1l-1 2c-1 3-3 6-5 8 0 0-1 0-1-1l6-9z" class="Z"></path><path d="M422 512v2c-1 3-3 6-5 9 0 2-1 2 0 4h-1l-1 4 1 1-3 4c0-1 0-2-1-2l1-2h-1c0-2 2-5 2-6l8-14z" class="F"></path><path d="M384 557v4c0 1 0 2-1 2l1 1h-2v1c0 1 2 1 2 2s0 1-1 1v1l1 2c0 1 0 2 1 3 0 1 1 2 0 3l1 1c0 1 2 4 2 6v2 1h-2l-1 1-1 1c1 3 0 4-2 7-2 2-4 5-7 7-2 2-6 2-8 3l1-2c2-1 4-1 6-3-1-1-2-1-3-1 2-2 3-4 4-7l1-1v-2-2l-1-2h-1c0 1-1 1-2 1s-2 0-3-1-1-2-1-3c0-2 1-2 1-3 2-6 8-9 10-15h-1v-3c1-2 3-3 5-4l1-1z" class="W"></path><path d="M380 580c1 0 2 1 2 2l-1 1-1-1v-2h0 0z" class="F"></path><path d="M379 593h1 1v1 1h-2c0 1 0 2-1 3h-1c1-2 1-3 2-5z" class="a"></path><path d="M378 590l2-2c0 1 0 1-1 2v3c-1 2-1 3-2 5l-3 4 1 1c-2 2-6 2-8 3l1-2c2-1 4-1 6-3 2-4 4-7 4-11z" class="E"></path><path d="M376 590h2c0 4-2 7-4 11-1-1-2-1-3-1 2-2 3-4 4-7l1-1v-2z" class="F"></path><path d="M382 582h0c1 1 1 1 2 1h2l2 1v2 1h-2l-1 1-1 1c0-1 0-1-1-1v1h-1v-1l2-1h0v-1c-1 0-2 0-2-1h-1l-1-1h0l1-1 1-1z" class="D"></path><path d="M382 582h0v2h0c2-1 2 0 3 0h1l-1 1h-4l-1-1h0l1-1 1-1z" class="B"></path><path d="M381 578c1 0 1-1 2-1v-1c1-1 1-2 2-2 0 1 1 2 0 3l1 1c0 1 2 4 2 6l-2-1h-2c-1 0-1 0-2-1h0c0-1-1-2-2-2l-1-1v-1h2 0z" class="I"></path><path d="M379 579v-1h2c2 2 4 3 5 5h-2c-1 0-1 0-2-1h0c0-1-1-2-2-2l-1-1z" class="P"></path><path d="M384 557v4c0 1 0 2-1 2l1 1h-2v1c0 1 2 1 2 2s0 1-1 1v1l1 2c0 1 0 2 1 3-1 0-1 1-2 2v1c-1 0-1 1-2 1h0-2v1h-2v3h0v1h0v1h-1c0 1 0 1 1 2v2h-1l-1-2h-1c0 1-1 1-2 1s-2 0-3-1-1-2-1-3c0-2 1-2 1-3 2-6 8-9 10-15h-1v-3c1-2 3-3 5-4l1-1z" class="B"></path><path d="M372 580h0l-1-1c1-1 2-1 3-3h0l2 2s-1 0-2 1l-2 1z" class="C"></path><path d="M384 557v4c0 1 0 2-1 2l-1-1v-1c0-1 1-2 1-3l1-1z" class="O"></path><path d="M377 572c2-2 3-4 4-5h0l1 1-1 1c-1 1-1 2-1 3h-1-2z" class="P"></path><path d="M374 576l3-3v-1h2v3c-1 0-1 1-1 2l-2 1-2-2z" class="D"></path><path d="M374 579l2 1v1h-1v-1l-1 1c1 1 1 2 2 2h1v1h-1c0 1 0 1 1 2v2h-1l-1-2h-1c0 1-1 1-2 1s-2 0-3-1-1-2-1-3l1-1c0 2 1 3 2 3h0 0l-1-2v-1h2c-1 1-1 1-1 2 1 0 2-1 3-2-1 0-1-1-2-2h0l2-1z" class="F"></path><path d="M382 568l1 1 1 2c0 1 0 2 1 3-1 0-1 1-2 2v1c-1 0-1 1-2 1h0-2v1h-2v3h0v1h0-1c-1 0-1-1-2-2l1-1v1h1v-1l-2-1c1-1 2-1 2-1l2-1c0-1 0-2 1-2v-3h1c0-1 0-2 1-3l1-1z" class="E"></path><path d="M384 571c0 1 0 2 1 3-1 0-1 1-2 2v1c-1 0-1 1-2 1h0c0-1 0-2 1-3h0 1c0-1-1-1-1-2h0c0-1 0-1-1-1h0v-1h2s0 1 1 1v-1z" class="H"></path><path d="M420 524l1 1c0 3-2 4-3 6l-1 3h0c-1 1-1 2-1 2l-3 4-2 3c0 1-1 1-1 2s1 1 1 2h-1v-1c-2 1-3 4-5 5 3 1 4 2 5 3v1l1 1h1c-1 1-1 2-1 3l-1 1h2c0-1 0-1 1-2l1 1v1c1 1 1 2 1 3h0c1 1 1 1 1 2s-2 1-2 2l-1 1-4 5h0l-1 1c-1-1-1 0-1-1-2 0-4 1-6 0-1 0-1 1-2 1v1c1 1 1 1 2 1-1 1-1 3-2 4 1 2 0 2 0 4 0 1 1 2 1 3v2c-1 2-1 3-2 5l-2 6c-1 2-1 6-3 7l-2 2v-4l-1-1h0l2-3v-3c1-1 0-3 0-4 1 1 0 2 1 3h1v-1-4-4l-1 2v2c0-1 0-1-1-2 0-5-2-9-2-13-1-2 0-4-1-6s0-3 0-4v-4h0c2-3 6-3 9-6l9-12v-3c2-2 4-5 5-8 1 0 1 1 1 2l3-4 4-8z" class="V"></path><path d="M394 574h0-1v-1c0-1 0-3 1-4l1 2v-1l1 1c0 1-1 2-2 3z" class="H"></path><path d="M392 601v2 2h0c1 0 1-1 1-1v3l-2 2v-4l-1-1h0l2-3z" class="C"></path><path d="M395 584l1 1c0-1 0-1 1-1v4l-1 5v2l-1-11z" class="B"></path><defs><linearGradient id="O" x1="393.476" y1="600.324" x2="397.871" y2="595.242" xlink:href="#B"><stop offset="0" stop-color="#767474"></stop><stop offset="1" stop-color="#8b8c8c"></stop></linearGradient></defs><path fill="url(#O)" d="M396 595v-2s1 0 1 1c0 0 1-1 1 0l-2 6c-1 2-1 6-3 7v-3c1 0 1-1 1-2l2-7z"></path><path d="M397 584h-1v-1-1c1-1 2-2 3-2 1 2 0 2 0 4 0 1 1 2 1 3v2c-1 2-1 3-2 5 0-1-1 0-1 0 0-1-1-1-1-1l1-5v-4z" class="F"></path><path d="M396 574c1 0 1 0 2-1l1 1v1c1 1 1 1 2 1-1 1-1 3-2 4-1 0-2 1-3 2v1 1h1c-1 0-1 0-1 1l-1-1c0-1-1-2-1-4v-3l2-3z" class="W"></path><path d="M389 563c2-3 6-3 9-6v2s0 1 1 1h0v-2h1v3h-1 0-1l-2 2h0-1v-2c-2 1-4 3-4 5h0l3-3h1c0 2-3 3-3 6 0 6 2 13 2 19l-1 2v2c0-1 0-1-1-2 0-5-2-9-2-13-1-2 0-4-1-6s0-3 0-4v-4h0z" class="K"></path><path d="M389 563c2-3 6-3 9-6v2s0 1 1 1h0v-2h1v3h-1 0-1l-2 2h0-1v-2l2-2h-1c-1 0-2 1-3 1s-2 1-3 3h-1 0z" class="D"></path><path d="M420 524l1 1c0 3-2 4-3 6l-1 3h0c-1 1-1 2-1 2l-3 4-2 3c0 1-1 1-1 2s1 1 1 2h-1v-1c-2 1-3 4-5 5v1c-1 1-3 3-3 4v1l1 1h-1 0-1v1 4h1c1 1 1 1 3 2-2 2-3 4-3 6l1 1c-1 0-1 0-2 1-1 0-1 1-2 1l-1-1c-1 1-1 1-2 1l-2 3v-3c1-1 2-2 2-3l-1-1c-1 0-1-1-1-2 1-1 1-1 2-1 3-1 2-2 2-4 0-1 1-1 1-2h0 1v-3h-1v2h0c-1 0-1-1-1-1v-2l9-12v-3c2-2 4-5 5-8 1 0 1 1 1 2l3-4 4-8z" class="T"></path><path d="M412 534c1 0 1 1 1 2l-6 9v-3c2-2 4-5 5-8z" class="M"></path><path d="M401 567h1v2l-3 1c1 1 1 1 2 1h1 0l1 1c-1 0-1 0-2 1-1 0-1 1-2 1l-1-1c-1 1-1 1-2 1v-2c1-1 1-2 2-2l3-3z" class="S"></path><path d="M401 567c-1 0-2-1-2-2s0-1 1-2h1 0 1c1 1 1 1 3 2-2 2-3 4-3 6h0-1c-1 0-1 0-2-1l3-1v-2h-1z" class="C"></path><path d="M405 551c3 1 4 2 5 3v1l1 1h1c-1 1-1 2-1 3l-1 1h2c0-1 0-1 1-2l1 1v1c1 1 1 2 1 3h0c1 1 1 1 1 2s-2 1-2 2l-1 1-4 5h0l-1 1c-1-1-1 0-1-1-2 0-4 1-6 0 1-1 1-1 2-1l-1-1c0-2 1-4 3-6-2-1-2-1-3-2h-1v-4-1h1 0 1l-1-1v-1c0-1 2-3 3-4v-1z" class="M"></path><path d="M402 563v-1h2l1 1v2c-2-1-2-1-3-2z" class="W"></path><path d="M403 572c2-1 3-4 5-6h1l-2 3h-1v2c0 1 2 1 3 2l-1 1c-1-1-1 0-1-1-2 0-4 1-6 0 1-1 1-1 2-1z" class="X"></path><path d="M410 555l1 1c-1 2-3 4-6 5h0l-1-1h-1c-1 0-1 0-2-1v-1h1 0 1 0 2c1 1 1 1 2 1 0-1 1-1 1-1l2-3z" class="E"></path><path d="M407 569c1 0 1 0 2-1v-1c1 2 3 1 4 1l-4 5h0c-1-1-3-1-3-2v-2h1z" class="Q"></path><path d="M412 560c0-1 0-1 1-2l1 1v1c1 1 1 2 1 3h0c1 1 1 1 1 2s-2 1-2 2c-1 0-2 0-3-1v-2l-1-1h0v-2c1-1 1-1 2-1z" class="H"></path><path d="M405 551c3 1 4 2 5 3v1l-2 3s-1 0-1 1c-1 0-1 0-2-1h-2 0l-1-1v-1c0-1 2-3 3-4v-1z" class="J"></path><path d="M405 551c3 1 4 2 5 3v1l-2 3c-1-1-1-1-1-2l1-2h0-1c-1 0-1-1-2-2v-1z" class="Y"></path><path d="M439 481c0 1 0 2 1 3v1h0 1 1v1c2-1 3 0 5 0h2c2 2 3 3 4 6 0 0 0 1-1 2 1 1 1 1 0 2 0 1 0 4-1 5h0c1 1 1 2 1 3 0 2-1 3-3 4l-1 1h-3-1c-1 1-3 2-3 3v2c-1 1-1 1-2 1s-2 1-3 1l2 1-1 1c-1 0-2 1-4 1l-2 1c-1 0-2 0-2-1l-2 1c0 1 1 2 1 2l-1 1c-2 1-3 1-5 4 0 2-1 3-1 5v-1h-3c1-2 3-3 3-6l-1-1-4 8-1-1 1-4h1c-1-2 0-2 0-4 2-3 4-6 5-9v-2l4-10h0l8-17c0-1 1-2 1-3l2 1-1 2c1 0 1 0 1 1l2-5z" class="O"></path><path d="M434 485v4l-3 8c-1 1-2 2-2 4 0 0 0 1-1 1 0 0 0 1-1 1 0 0 0-1-1-1l8-17z" class="K"></path><path d="M426 502h0c1 0 1 1 1 1l-10 24c-1-2 0-2 0-4 2-3 4-6 5-9v-2l4-10z" class="H"></path><defs><linearGradient id="P" x1="427.394" y1="504.748" x2="433.983" y2="503.578" xlink:href="#B"><stop offset="0" stop-color="#3b3639"></stop><stop offset="1" stop-color="#414744"></stop></linearGradient></defs><path fill="url(#P)" d="M433 496l2 2c-1 1-1 2-2 4 0 1-1 3-2 4l-3 7v-1-1c1-1 0-1 0-2l5-13z"></path><path d="M439 481c0 1 0 2 1 3v1h0 1 1v1c-1 0-3 1-3 3h0l-1 1v3h-1c0 1-1 4-2 5h0l-2-2 4-10 2-5z" class="L"></path><path d="M428 509c0 1 1 1 0 2v1 1 1l-1 1v1h0c2 0 3 0 4 1h0l-2 2-2 1c0 1 1 2 1 2l-1 1c-2 1-3 1-5 4 0 2-1 3-1 5v-1h-3c1-2 3-3 3-6l-1-1 8-15z" class="X"></path><path d="M421 525c0-1 0-1 1-2v2c1-1 1-1 1-2h-1c0-1 0-1 1-1h1v-2l3-4c0 1-1 2-2 4 0 0 1 0 1 1l1-1c0 1 1 2 1 2l-1 1c-2 1-3 1-5 4 0 2-1 3-1 5v-1h-3c1-2 3-3 3-6z" class="W"></path><path d="M431 506l1 1h0 1 1l1 1c1 1 1 1 0 2h1 1l1 1h1-1 0l2 1v-1c1-1 2-1 2-1 1-1 1-1 2-1-1 1-3 2-3 3v2c-1 1-1 1-2 1s-2 1-3 1l2 1-1 1c-1 0-2 1-4 1l-2 1c-1 0-2 0-2-1l2-2h0c-1-1-2-1-4-1h0v-1l1-1v-1l3-7z" class="O"></path><path d="M435 510h1 1l1 1s-1 1-2 1h-3v-1c1 0 2-1 2-1z" class="K"></path><path d="M431 516l1-1c1-1 2-1 4-1h1v-1c1 0 1 0 1-1h3v2c-1 1-1 1-2 1s-2 1-3 1h0-1-4z" class="J"></path><path d="M431 516h4 1 0l2 1-1 1c-1 0-2 1-4 1l-2 1c-1 0-2 0-2-1l2-2h0v-1z" class="Y"></path><path d="M431 506l1 1h0 1 1l1 1c1 1 1 1 0 2 0 0-1 1-2 1v1l-5 2v-1l3-7z" class="G"></path><path d="M434 507l1 1c1 1 1 1 0 2 0 0-1 1-2 1-1-2 0-1 1-2h-1c0-1 0-1 1-2z" class="J"></path><path d="M442 486c2-1 3 0 5 0h2c2 2 3 3 4 6 0 0 0 1-1 2 1 1 1 1 0 2 0 1 0 4-1 5h0c1 1 1 2 1 3 0 2-1 3-3 4l-1 1h-3-1c-1 0-1 0-2 1 0 0-1 0-2 1v1l-2-1h0 1-1l-1-1h-1-1c1-1 1-1 0-2l-1-1h-1-1 0l-1-1c1-1 2-3 2-4 1-2 1-3 2-4h0c1-1 2-4 2-5h1v-3l1-1h0c0-2 2-3 3-3z" class="B"></path><path d="M441 505h-1v-1h-1v-1h1c1-1 1-1 1-2h0 1c0 2 0 2-1 4z" class="N"></path><path d="M438 509c0-1 0-1 1-1h1c0-1 1-1 2-1v1l-3 3h-1l-1-1 1-1z" class="F"></path><path d="M450 492c1 2 0 5 0 6v1c-1 3-3 5-6 7v-2c-1 0-2 1-2 1h-1c1-2 1-2 1-4h1l1 1h1c0-1-1-1-2-2v-1h1v1c1-1 2-5 3-6 1 0 2-1 2-1h1v-1z" class="S"></path><path d="M435 498h0c1-1 2-4 2-5h1v-3 3l3 3c1 0 2 0 3-1v-1c1 0 1-1 1-2 2-1 3 0 5 0v1h-1s-1 1-2 1c-1 1-2 5-3 6v-1h-1c0-1 0-1-1-2h-1c0 1 1 1 1 1-1 1-2 1-2 1-1 0-1 0-2-1v1 2h-1c-1 1-1 2-1 3s0 1 1 1l-1 1c0 1 0 1 1 2l1-2h0v3l-1 1h-1-1c1-1 1-1 0-2l-1-1h-1-1 0l-1-1c1-1 2-3 2-4 1-2 1-3 2-4z" class="C"></path><path d="M435 498h0c1-1 2-4 2-5h1v-3 3l-1 2v2h-1v1 1h0c0 1 0 2-1 3h0v2l-3 3h0l-1-1c1-1 2-3 2-4 1-2 1-3 2-4z" class="B"></path><path d="M442 486c2-1 3 0 5 0h2c2 2 3 3 4 6 0 0 0 1-1 2 1 1 1 1 0 2 0 1 0 4-1 5h0c1 1 1 2 1 3 0 2-1 3-3 4l-1 1h-3-1c-1 0-1 0-2 1 0 0-1 0-2 1v1l-2-1h0 1l3-3 2-2c3-2 5-4 6-7v-1c0-1 1-4 0-6-2 0-3-1-5 0 0 1 0 2-1 2v1c-1 1-2 1-3 1l-3-3v-3l1-1h0c0-2 2-3 3-3z" class="Y"></path><path d="M451 501c1 1 1 2 1 3 0 2-1 3-3 4l-1 1h-3c4-2 5-5 6-8z" class="T"></path><path d="M442 494c-1-1-1-2-2-3 0 0 0-1 1-1 0-1 2-2 3-2 1-1 2-1 4 0 1 1 2 3 2 4-2 0-3-1-5 0 0 1 0 2-1 2h-2z" class="I"></path><path d="M442 494v-3c2 0 2 0 3 1 0 1 0 2-1 2h-2z" class="J"></path><defs><linearGradient id="Q" x1="450.638" y1="441.326" x2="444.992" y2="440.576" xlink:href="#B"><stop offset="0" stop-color="#8a898a"></stop><stop offset="1" stop-color="#b5b3b4"></stop></linearGradient></defs><path fill="url(#Q)" d="M467 409h2s0-1 1-1 1-1 2-1v2h0v1c1 1 2 0 3 0l1-1h0c0-2-1-1-2-2h1 2c1 0 1 1 2 1h1 1v1c1 3-2 7-2 10-1 7-2 15-5 22l-10 38c-1 1-2 3-2 5l-8 18h-1l-1 2c0-1 0-2-1-3h0c1-1 1-4 1-5 1-1 1-1 0-2 1-1 1-2 1-2-1-3-2-4-4-6h-2c-2 0-3-1-5 0v-1h-1-1 0v-1c-1-1-1-2-1-3l-2 5c0-1 0-1-1-1l1-2-2-1 9-30 6-30 2-9h1v4s-1 0-1 1l1-1v5 1h1v2l1 2v3-1c0-1 1-1 1-2 0-2 1-5 1-8l1-1v-1c0-1 1-2 2-3l2 1c0-1 0-2 1-3 1 0 1 0 2-1l2-2z"></path><path d="M451 439l1 1v-1c0 2-1 3-1 5h1c0 1 0 2-1 3v1h0l-2-1 2-8z" class="L"></path><path d="M447 454h-1c-1-2 0-4 1-6l1-5c1 0 1 1 1 2s-1 1-1 1c0 1 0 2 1 3-1 2-1 4-2 5z" class="W"></path><path d="M454 425l1 2v3 1c0 1-1 5-1 5l-2 3v1l-1-1 3-14z" class="P"></path><path d="M437 483c0-3 1-5 2-7 2-7 4-14 7-20h1c-1 5-3 10-5 15-1 3-2 7-3 10l-2 5c0-1 0-1-1-1l1-2z" class="N"></path><path d="M449 447l2 1v1c0 1-2 3-1 4 1 0 1-1 2-1h0v2h1l-1 1-2-1v1h0s1 1 1 2l1 1h0-2 0l2 3c-1 0-2-1-3 0v2h-1l-1 1c0 1-1 3-2 4l-1 1h1l1 1c-1 1-2 1-2 2-1 2-2 3-2 5h0v1h0c0 1-1 2-1 3-1 1-1 2-1 2v1c-1-1-1-2-1-3 1-3 2-7 3-10 2-5 4-10 5-15v-2c1-1 1-3 2-5v-2z" class="U"></path><g class="G"><path d="M448 463h-1c0-1 1-3 2-4v-1c0-1-1-1-1-2 1-1 1-2 2-2h0v1h0s1 1 1 2l1 1h0-2 0l2 3c-1 0-2-1-3 0v2h-1z"></path><path d="M455 431h0c1 1 1 2 1 2v1 2 2l3 11c0 1 1 5 1 6l1 6c1 1 1 2 1 3l-1 1v3h0 0c0 1 0 2-1 3-1-2-1-6-3-9h-2-1v1l-2-2-2-3h0 2 0l-1-1c0-1-1-2-1-2h0v-1l2 1 1-1h-1v-2h0c-1 0-1 1-2 1-1-1 1-3 1-4v-1h0v-1c1-1 1-2 1-3h-1c0-2 1-3 1-5l2-3 1-5z"></path></g><path d="M452 454c0-2 0-3 1-3h1v1c0 1 1 1 1 2s0 1 1 1l-1 1c-1 0-1-1-2-2h-1z" class="B"></path><path d="M452 439l2-3v8l1 1v3c-1 0-2 0-2-1s0-3-1-4v1h-1c0-2 1-3 1-5z" class="D"></path><path d="M452 461l-2-3h0 2 0l-1-1c0-1-1-2-1-2h0v-1l2 1c5 3 7 7 9 13 0 1 0 2-1 3-1-2-1-6-3-9h-2-1v1l-2-2z" class="O"></path><path d="M455 431h0c1 1 1 2 1 2v1 2 2l3 11c0 1 1 5 1 6l1 6c1 1 1 2 1 3l-1 1-6-20-1-1v-8l1-5z" class="f"></path><path d="M455 431h0c1 1 1 2 1 2v1 2 2c0-1 0-1-1-2v4c0 1 0 1 1 2 0 1-1 1-2 2v-8l1-5z" class="Y"></path><path d="M448 463h1v-2c1-1 2 0 3 0l2 2v-1h1 2c2 3 2 7 3 9l1 5h-2v1h0l-1 1v2 1 1h-1-3l-1 2h-1c-1 1-1 1-1 2h-2-2c-2 0-3-1-5 0v-1h-1-1 0v-1-1s0-1 1-2c0-1 1-2 1-3h0v-1h0c0-2 1-3 2-5 0-1 1-1 2-2l-1-1h-1l1-1c1-1 2-3 2-4l1-1z" class="D"></path><path d="M454 463v-1h1 2c2 3 2 7 3 9l1 5h-2c-1-5-4-9-7-12v-1h1 1 0 0z" class="Y"></path><path d="M448 463h1v-2c1-1 2 0 3 0l2 2h0 0-1-1v1 3h1c1 1 1 2 2 3l-1 1 2 2v1h-1c-2-1-4-1-6-2-1 0-2 0-3-1v-1l-1-1h-1l1-1c1-1 2-3 2-4l1-1z" class="H"></path><path d="M448 463h1v-2c1-1 2 0 3 0l2 2h0 0-1c-1-1-1-1-2-1v2c-1 1-2 2-1 3v1l-1 2-1-1c0-1 1-2 1-4h-1c-1 1 0 3-2 5l-1-1h-1l1-1c1-1 2-3 2-4l1-1z" class="C"></path><path d="M446 470v1c1 1 2 1 3 1h-2c1 1 1 1 2 1s1 1 3 1c1 0 2 1 3 2s1 1 1 2c-1 1-1 1-1 2 1 0 1 1 3 1v1h-1-3l-1 2h-1c-1 1-1 1-1 2h-2-2c-2 0-3-1-5 0v-1h-1-1 0v-1-1s0-1 1-2c0-1 1-2 1-3h0v-1h0c0-2 1-3 2-5 0-1 1-1 2-2z" class="B"></path><path d="M452 474l-1 1c0 1-1 2-2 2-1-1-1-2-2-2l-1-1v-2h1c1 1 1 1 2 1s1 1 3 1z" class="E"></path><path d="M455 476c1 1 1 1 1 2-1 1-1 1-1 2 1 0 1 1 3 1v1h-1-3 0l-2-2c1-1 2-3 3-4z" class="R"></path><path d="M442 478h0v-1h0c0-2 1-3 2-5 0 1 1 2 1 3s1 1 2 2v1c-2 1-2 1-2 3v1l1 1v2h-1l2 1c-2 0-3-1-5 0v-1h-1-1 0v-1-1s0-1 1-2c0-1 1-2 1-3z" class="E"></path><path d="M442 478l1 1v1c1 2 2 3 2 5l2 1c-2 0-3-1-5 0v-1h-1-1 0v-1-1s0-1 1-2c0-1 1-2 1-3z" class="H"></path><path d="M442 478l1 1v1l-1 1c0 1 1 2 0 2h-2s0-1 1-2c0-1 1-2 1-3z" class="S"></path><path d="M471 439v1h2 0l1 1-10 38c-1 1-2 3-2 5l-8 18h-1l-1 2c0-1 0-2-1-3h0c1-1 1-4 1-5 1-1 1-1 0-2 1-1 1-2 1-2-1-3-2-4-4-6h2c0-1 0-1 1-2h1l1-2h3 1v-1-1-2l1-1h0v-1h2l-1-5c1-1 1-2 1-3h0 0v-3l1-1c0-1 0-2-1-3l-1-6h1c1 1 1 2 1 4 1 0 2-1 2-1v-1-7-1c1 0 1 0 1-1 1-1 0-2 0-3s1-2 1-3c1 1 1 1 1 2v1c0-2 0-3 1-5h1v1 1l2-3z" class="b"></path><path d="M460 455h1c1 1 1 2 1 4 1 0 2-1 2-1v-1l1 6-1 4c0 3-1 7-2 10v3c-1-1 0-3-1-4l-1-5c1-1 1-2 1-3h0 0v-3l1-1c0-1 0-2-1-3l-1-6z" class="M"></path><path d="M471 439v1h2l-5 15c0 2 0 5-1 7 0 2-1 2-2 3l-1 2 1-4-1-6v-7-1c1 0 1 0 1-1 1-1 0-2 0-3s1-2 1-3c1 1 1 1 1 2v1c0-2 0-3 1-5h1v1 1l2-3z" class="S"></path><path d="M466 442c1 1 1 1 1 2v1 1c-1 3-1 7-1 11h1c1-1 1-1 1-2 0 2 0 5-1 7 0 2-1 2-2 3l-1 2 1-4-1-6v-7-1c1 0 1 0 1-1 1-1 0-2 0-3s1-2 1-3z" class="L"></path><path d="M465 463v-3h0c1 1 1 2 2 2h0c0 2-1 2-2 3l-1 2 1-4z" class="P"></path><defs><linearGradient id="R" x1="452.033" y1="498.512" x2="461.39" y2="476.988" xlink:href="#B"><stop offset="0" stop-color="#767576"></stop><stop offset="1" stop-color="#9b999a"></stop></linearGradient></defs><path fill="url(#R)" d="M461 476c1 1 0 3 1 4-2 7-6 14-9 22l-1 2c0-1 0-2-1-3h0c1-1 1-4 1-5 1-1 1-1 0-2 1-1 1-2 1-2-1-3-2-4-4-6h2c0-1 0-1 1-2h1l1-2h3 1v-1-1-2l1-1h0v-1h2z"></path><path d="M459 477c1 1 0 3 0 5h-1v-1-1-2l1-1h0z" class="B"></path><path d="M454 482h3c-1 3-2 5-2 8-1 2-1 4-2 6h-1c1-1 1-1 0-2 1-1 1-2 1-2 0-2 1-3 1-4s1-3 1-4c-1-1-1-1-1-2z" class="L"></path><path d="M449 486h2c0-1 0-1 1-2h1l1-2c0 1 0 1 1 2 0 1-1 3-1 4s-1 2-1 4c-1-3-2-4-4-6z" class="R"></path><path d="M467 409h2s0-1 1-1 1-1 2-1v2h0v1c1 1 2 0 3 0l1-1h0c0-2-1-1-2-2h1 2c1 0 1 1 2 1h1 1v1c1 3-2 7-2 10-1 7-2 15-5 22l-1-1h0-2v-1l-2 3v-1-1h-1c-1 2-1 3-1 5v-1c0-1 0-1-1-2 0 1-1 2-1 3s1 2 0 3c0 1 0 1-1 1v1 7 1s-1 1-2 1c0-2 0-3-1-4h-1c0-1-1-5-1-6l-3-11v-2-2-1s0-1-1-2h0v-1-1c0-1 1-1 1-2 0-2 1-5 1-8l1-1v-1c0-1 1-2 2-3l2 1c0-1 0-2 1-3 1 0 1 0 2-1l2-2z" class="M"></path><path d="M472 416l5-3v3h0c-1 0-2 0-3 1h-1l-1-1z" class="U"></path><path d="M458 417c0-1 1-2 2-3l2 1c-1 0-2 1-2 2-1 0-1 1-2 1v-1z" class="I"></path><path d="M463 416c2-3 6-5 8-6 0 1-1 2-2 3-2 0-2 0-3 1v2h-1-2z" class="S"></path><path d="M472 416l1 1h1c1-1 2-1 3-1-1 1-1 2-2 3h0-2v1l2 1h0v2s-1 1-1 2h-2c0-1-1-1-2-2h0v-1h0c1 0 2 0 2-1-1-2-3 0-4-1 1-2 3-3 4-4z" class="I"></path><path d="M475 421h0v2s-1 1-1 2h-2c0-1-1-1-2-2h0v-1h0c1 0 2 0 3 1h1l1-2z" class="G"></path><path d="M466 423c1-1 3-1 4-1v1h0c1 1 2 1 2 2h2 0l-1 1c-1 1-1 2-2 2l-1 1c-1 0-1 1-2 1l-1-1h0 0c-1-1-2-1-3-2h0 0c0-1 0-1-1-2l-1-1h-1 1c1 0 1 0 2-1h2z" class="I"></path><path d="M467 429l1-1c1-1 1-1 2-1l1 1-1 1c-1 0-1 1-2 1l-1-1z" class="B"></path><path d="M470 423h0c1 1 2 1 2 2h2 0l-1 1h0-1l-1 1c-1 0-2 0-2-1-1 0-1 0 0-1h1v-2z" class="E"></path><path d="M466 423h2c-1 1-2 1-3 1v1c1 1 2 2 2 4h0c-1-1-2-1-3-2h0 0c0-1 0-1-1-2l-1-1h-1 1c1 0 1 0 2-1h2z" class="D"></path><path d="M463 416h2 1v-2c1-1 1-1 3-1 0 1 1 1 1 2 1-1 1-1 2-1v1c-1 1-1 0-2 0 0 1-1 1-1 2-1 0-2-1-2-1s-1 0-1 1v1c0 1-1 2-2 2l1 1s1 0 0 1c0 0-1 0-1 1-1 1-1 1-2 1s-2-1-3 0l1-3c0-2 1-4 2-5h1z" class="H"></path><path d="M462 416c1 1 1 0 2 1-1 1-1 1-2 1l1 1v1c0 1-1 1-1 1l-1 1h2s1-1 2-1v1s-1 0-1 1c-1 1-1 1-2 1s-2-1-3 0l1-3c0-2 1-4 2-5z" class="R"></path><path d="M481 409c1 3-2 7-2 10-1 7-2 15-5 22l-1-1h0-2v-1l-2 3v-1l1-1c0-2 1-3 1-5-1-2-1-3-1-5v-1l1-1c1 0 1-1 2-2l1-1h0c0-1 1-2 1-2v-2h0l-2-1v-1h2 0c1-1 1-2 2-3h0v-3c2-1 3-2 4-4z" class="c"></path><path d="M475 421h1l1 1c-1 1-1 2-2 4h-1v-1h0c0-1 1-2 1-2v-2z" class="L"></path><path d="M472 437c1-2 1-3 2-5h0c0 3 0 5-1 7h0v1h0-2v-1l1-2z" class="C"></path><path d="M472 437c0 1 1 1 1 2v1h0-2v-1l1-2z" class="B"></path><path d="M474 425v1h1c0 1 0 1-1 2 1 0 1 1 1 1v1 2h-1 0c-1 2-1 3-2 5l-1 2-2 3v-1l1-1c0-2 1-3 1-5-1-2-1-3-1-5v-1l1-1c1 0 1-1 2-2l1-1z" class="P"></path><path d="M470 430h1c1 0 1-1 1-1h1v2c-1 1-2 2-2 4-1-2-1-3-1-5z" class="G"></path><path d="M474 425v1h1c0 1 0 1-1 2 0 0-1 0-1 1h-1s0 1-1 1h-1v-1l1-1c1 0 1-1 2-2l1-1z" class="C"></path><defs><linearGradient id="S" x1="467.561" y1="431.695" x2="457.879" y2="439.161" xlink:href="#B"><stop offset="0" stop-color="#595858"></stop><stop offset="1" stop-color="#707071"></stop></linearGradient></defs><path fill="url(#S)" d="M457 419l3-1h0v3l-1 3c1-1 2 0 3 0h-1 1l1 1c1 1 1 1 1 2h0 0c1 1 2 1 3 2h0 0l1 1c1 0 1-1 2-1v1c0 2 0 3 1 5 0 2-1 3-1 5l-1 1v-1h-1c-1 2-1 3-1 5v-1c0-1 0-1-1-2 0 1-1 2-1 3s1 2 0 3c0 1 0 1-1 1v1 7 1s-1 1-2 1c0-2 0-3-1-4h-1c0-1-1-5-1-6l-3-11v-2-2-1s0-1-1-2h0v-1-1c0-1 1-1 1-2 0-2 1-5 1-8z"></path><path d="M463 425c1 1 1 1 1 2h-1c0 1 1 3 1 4 0 0 1 1 1 2 0 0-1 1-1 2h-1l-1-1 1-1c0-2 0-5-2-6v-1s1-1 2-1z" class="S"></path><path d="M464 427h0 0c1 1 2 1 3 2h0 0l1 1c1 0 1-1 2-1v1c0 2 0 3 1 5 0 2-1 3-1 5l-1 1v-1h-1c-1 2-1 3-1 5v-1c0-1 0-1-1-2v-3c-1-1-1-1-1-2h-1 0v-2c0-1 1-2 1-2 0-1-1-2-1-2 0-1-1-3-1-4h1z" class="G"></path><path d="M466 439v-6h0l1 1h0c0 2 0 4 1 6-1 2-1 3-1 5v-1c0-1 0-1-1-2v-3z" class="D"></path><path d="M468 430c1 0 1-1 2-1v1c0 2 0 3 1 5 0 2-1 3-1 5h-1c0-2-1-4 0-6h0c0-1-1-1-2-2l-1 1v-1l2-2h0z" class="S"></path><path d="M457 419l3-1h0v3l-1 3v4 2c2 7 4 13 4 20h1v7 1s-1 1-2 1c0-2 0-3-1-4h-1c0-1-1-5-1-6l-3-11v-2-2-1s0-1-1-2h0v-1-1c0-1 1-1 1-2 0-2 1-5 1-8z" class="F"></path><path d="M459 430c2 7 4 13 4 20v4h0c-1-3-1-5-2-8l-1-9c-1-2-1-5-1-7z" class="d"></path><path d="M447 365c4-2 8-2 12-2h1v1c4 0 7 0 11 2h2l3 3 2-1 2 5 1 3c1 1 1 2 1 4v3l2 1v1c0 4-1 7-1 10 0 1-1 3 0 4-1 1-1 7-2 8v1h0-1-1c-1 0-1-1-2-1h-2-1c1 1 2 0 2 2h0l-1 1c-1 0-2 1-3 0v-1h0v-2c-1 0-1 1-2 1s-1 1-1 1h-2l-2 2c-1 1-1 1-2 1-1 1-1 2-1 3l-2-1c-1 1-2 2-2 3v1l-1 1c0 3-1 6-1 8 0 1-1 1-1 2v1-3l-1-2v-2h-1v-1-5l-1 1c0-1 1-1 1-1v-4h-1c0-2 1-5 1-7l-1 1v-1l2-26h-2c-1 2-1 5-1 7l-2 14-2 14c-7 44-21 90-47 127-4 5-8 11-13 16-1 2-2 2-2 4v1c1 1 1 4 1 5h-1l-1-7v-4l1-1v-1c1 0 2-2 3-2l6-8c12-17 21-34 29-53 5-12 9-24 13-37v-2l2-5c0-2 0-2 1-3v-1c0-3 1-5 1-8l3-13c2-12 3-24 4-36 0-2 1-6 0-8 1-2 0-2 2-3l-1-1v-2h-1l-1-1c-1-1-2-2-2-3 1-1 1-2 3-3h-2 0l2-1z" class="U"></path><path d="M465 385h-1c-1 0-1-1-2-1v-1c2-1 2-2 4-2 1 2 2 3 3 4h-4z" class="F"></path><path d="M439 445l1-1v2l-2 10h-1v-2-1l-1 2v-2l2-5c0-2 0-2 1-3z" class="Z"></path><path d="M447 379l1-2 2 2-10 67v-2l-1 1v-1c0-3 1-5 1-8l3-13c2-12 3-24 4-36 0-2 1-6 0-8z" class="J"></path><path d="M453 406l2-26c1-1 2-1 3-2 1 1 1 2 1 3v2c1 1 1 1 2 1-1 1-1 2-1 3h0v1c-1 1-1 12-1 14l-1 1c0 2 0 3 1 5-1 1-1 2-1 2v1h1v1l-1 2h0 1c0-1 0-1 1-1h0v1c-1 1-2 2-2 3v1l-1 1c0 3-1 6-1 8 0 1-1 1-1 2v1-3l-1-2v-2h-1v-1-5l-1 1c0-1 1-1 1-1v-4h-1c0-2 1-5 1-7z" class="W"></path><path d="M459 381v2c1 1 1 1 2 1-1 1-1 2-1 3h0v1c-1 1-1 12-1 14l-1 1c0 2 0 3 1 5-1 1-1 2-1 2v1h1v1l-1 2h0 1c0-1 0-1 1-1h0v1c-1 1-2 2-2 3v1l-1 1c0 3-1 6-1 8 0 1-1 1-1 2v1-3l-1-2v-2l5-42z" class="L"></path><path d="M458 411h1v1l-1 2h0 1c0-1 0-1 1-1h0v1c-1 1-2 2-2 3l-1-1c0-2 0-3 1-5z" class="T"></path><path d="M436 453v2l1-2v1 2h1c-5 19-12 36-20 54-5 9-10 19-17 28-4 7-10 14-15 21h-1v-3-1c1 0 2-2 3-2l6-8c12-17 21-34 29-53 5-12 9-24 13-37v-2z" class="F"></path><path d="M466 381c2 1 2 1 3 2 2 5 2 13 1 18-1 1-1 3-1 4-1 1-1 3-2 4h0l-2 2c-1 1-1 1-2 1-1 1-1 2-1 3l-2-1v-1h0c-1 0-1 0-1 1h-1 0l1-2v-1h-1v-1s0-1 1-2c-1-2-1-3-1-5l1-1c0-2 0-13 1-14v-1h0c0-1 0-2 1-3 0 1 1 1 1 2 1 1 2 2 3 2h2v-1c-1 0-1-1-2-2h4c-1-1-2-2-3-4z" class="N"></path><path d="M461 384c0 1 1 1 1 2s-1 1-1 2 1 1 2 1h1 2 1c-1 1-1 1-2 1 0 1 0 1-1 2 0 0-1 0-2-1 0 1-1 2-1 3v4l-1 3v2l-1-1c0-2 0-13 1-14v-1h0c0-1 0-2 1-3z" class="I"></path><path d="M466 381c2 1 2 1 3 2 2 5 2 13 1 18-1 1-1 3-1 4l-1-1h0c-1 0-1-1-2-1 1-1 1-1 1-2s-1-2-2-3v-1h-1 0c-1 2-2 4-4 4h0l1-3c1-1 1-2 2-3s2-1 4-1l1 1h-1-1-1c0 1 1 2 2 3s1 3 1 4h1v-6-2c0-1-1-1-2-1v-1h1 1 0v-1c0-1-1-1-2-1v-2-1c-1 0-1-1-2-2h4c-1-1-2-2-3-4z" class="D"></path><path d="M469 385v6c0-1-1-1-2-1v-2-1c-1 0-1-1-2-2h4z" class="K"></path><path d="M460 401h0c2 0 3-2 4-4h0 1v1c1 1 2 2 2 3s0 1-1 2c1 0 1 1 2 1h0l1 1c-1 1-1 3-2 4h0l-2 2c-1 1-1 1-2 1-1 1-1 2-1 3l-2-1v-1h0c-1 0-1 0-1 1h-1 0l1-2v-1h-1v-1s0-1 1-2c-1-2-1-3-1-5l1-1 1 1v-2z" class="E"></path><path d="M458 403h1 2v1h0c-1 1 1 4-1 5v-1l-1 1c0 1 1 1 2 2h-2v1-1h-1v-1s0-1 1-2c-1-2-1-3-1-5zm7-5c1 1 2 2 2 3s0 1-1 2c1 0 1 1 2 1h0l1 1c-1 1-1 3-2 4h0l-2 2c-1 1-1 1-2 1l-2-2 1-2s0-1 1-2h0c-1-2 0-1 1-3l-2-1c1-2 1-1 2-1 0-1 1-2 1-3z" class="V"></path><path d="M465 411c0-1-1-2-2-3l1-1 1 1 1 1h1 0l-2 2z" class="E"></path><path d="M468 404l1 1c-1 1-1 3-2 4h-1l-1-1c1-2 1-3 3-4z" class="T"></path><path d="M447 365c4-2 8-2 12-2h1v1c4 0 7 0 11 2h2l3 3 2-1 2 5 1 3c1 1 1 2 1 4v3l2 1v1c0 4-1 7-1 10 0 1-1 3 0 4-1 1-1 7-2 8v1h0-1-1c-1 0-1-1-2-1h-2-1c1 1 2 0 2 2h0l-1 1c-1 0-2 1-3 0v-1h0v-2c-1 0-1 1-2 1s-1 1-1 1h-2 0c1-1 1-3 2-4 0-1 0-3 1-4 1-5 1-13-1-18-1-1-1-1-3-2s-4-1-5-1l-3-3 1-1 1-2c-1-2-2-3-3-3 1 1 1 2 1 4 0 1-1 2-3 3-1 1-2 1-3 0h0-1c-1 0-2-1-2-2l-1-1v-2h-1l-1-1c-1-1-2-2-2-3 1-1 1-2 3-3h-2 0l2-1z" class="I"></path><path d="M478 368l2 5 1 3-3-4-2-3 2-1z" class="E"></path><path d="M479 376c1 1 1 2 1 4 0 1-1 2-1 2h-1l-1-1v-2h1c-1-1-1-2-1-3 1 1 1 1 2 0z" class="Q"></path><path d="M466 377l1-1c2 2 4 3 6 6 0 1 1 1 1 2-2-2-4-3-6-4-1-1-2-1-3-2l1-1z" class="Y"></path><path d="M470 382l-3-2h1c2 1 4 2 6 4v2 7c0 1 0 3-1 3v1c0 1 0 2-1 3v-1c1-3 1-6 1-10v-4l-3-3z" class="V"></path><path d="M460 364c4 0 7 0 11 2 2 2 4 3 5 5-2 0-3-2-4-2-2-1-3-2-5-2l-1-1-1 1h-1c-2 0-3-1-5-3h1z" class="O"></path><path d="M470 382l3 3v4c0 4 0 7-1 10-1 1-1 2-1 3-1 0-1 0-1-1 1-5 1-13-1-18l1-1z" class="M"></path><path d="M479 405h-3 0c-1 0-1 0-1-1l-1 1c-1 1-2 2-3 2h-1l3-6 5-9c1-2-1-6 1-8v1 6c-1 1-1 2-2 4 0 2-2 3-2 5 0 1-1 2-1 4h2c0-1 2-1 2-2h-1v-1c1 0 1-1 2-2h0v6z" class="D"></path><path d="M475 400c5-6 5-13 7-20v3c0 6-1 11-3 16h0 0c-1 1-1 2-2 2v1h1c0 1-2 1-2 2h-2c0-2 1-3 1-4z" class="P"></path><path d="M482 383l2 1v1c0 4-1 7-1 10 0 1-1 3 0 4-1 1-1 7-2 8l-2-2v-6h0c2-5 3-10 3-16z" class="b"></path><path d="M467 367c3 2 6 3 8 6 2 1 3 2 4 3-1 1-1 1-2 0 0 1 0 2 1 3h-1v2c-1 0-1-1-2-2s-3-4-5-4l1 1c2 1 3 3 4 5 1 1 1 1 1 2l-1 1c-1-3-4-6-7-8-1 0-1-1-2-1 0 0 1 0 2 1h2 0l-2-2h0l-1-1c-2-1-4-3-7-3-1 0-1 0-1-1h3 3c1 1 2 1 3 1v1l1-1c-1 0-1 0-2-1 0 0-1 0-1-1l1-1z" class="O"></path><path d="M475 379c1 0 1 0 1-1-1-3-5-7-7-9 2 1 4 2 5 4 1 1 1 0 1 1v-1c2 1 3 2 4 3-1 1-1 1-2 0 0 1 0 2 1 3h-1v2c-1 0-1-1-2-2z" class="M"></path><path d="M447 365c4-2 8-2 12-2h1v1h-1c2 2 3 3 5 3h1 2l-1 1c0 1 1 1 1 1 1 1 1 1 2 1l-1 1v-1c-1 0-2 0-3-1h-3-3c0 1 0 1 1 1 3 0 5 2 7 3l1 1h0l2 2h0-2c-1-1-2-1-2-1-1 0-2-1-3-1v1l2 1s0 1 1 1l-1 1c1 1 2 1 3 2h-1l3 2-1 1c-1-1-1-1-3-2s-4-1-5-1l-3-3 1-1 1-2c-1-2-2-3-3-3 1 1 1 2 1 4 0 1-1 2-3 3-1 1-2 1-3 0h0-1c-1 0-2-1-2-2l-1-1v-2h-1l-1-1c-1-1-2-2-2-3 1-1 1-2 3-3h-2 0l2-1z" class="Y"></path><path d="M459 376c1-1 1-1 2-1v1s-1 0-1 1v1h3l1 1 1-1v-2s0 1 1 1l-1 1c1 1 2 1 3 2h-1l3 2-1 1c-1-1-1-1-3-2s-4-1-5-1l-3-3 1-1z" class="F"></path><path d="M448 375c0-3 1-5 2-7 0 2 0 3-1 4 1 2 3 4 4 4h1 1c2-1 2-1 3-1 0 1-1 2-3 3-1 1-2 1-3 0h0-1c-1 0-2-1-2-2l-1-1z" class="O"></path><path d="M447 365c4-2 8-2 12-2h1v1h-1c-3 1-6 1-8 4h-1c-1 2-2 4-2 7v-2h-1l-1-1c-1-1-2-2-2-3 1-1 1-2 3-3h-2 0l2-1z" class="P"></path><path d="M447 366c1 0 3-1 5-1-1 2-5 5-5 8l-1-1c-1-1-2-2-2-3 1-1 1-2 3-3z" class="F"></path><path d="M477 365l1-1c-1 0-2-1-2-2v-1c2 2 3 3 5 4 0-1 1-1 2-1v-1c1 0 2 0 3 1s3 0 5 0c-1 6-1 13-2 19l-1 15c0 5-1 9-1 14-1 2-1 4-1 6l-1 7c-2 9-3 19-6 28l-12 38-1 2s-1 0-1 1c-10 23-21 44-35 64-7 11-15 21-24 30-1-1-1 1-3 1 1 0 1-1 1-1 1-1 1-2 2-3v-1l-2 3h0-2 0c0 1-1 1-1 2h-1v-2c0-1-1-2-1-3 0-2 1-2 0-4 1-1 1-3 2-4-1 0-1 0-2-1v-1c1 0 1-1 2-1 2 1 4 0 6 0 0 1 0 0 1 1l1-1h0l4-5 1-1c0-1 2-1 2-2s0-1-1-2h0c0-1 0-2-1-3v-1l-1-1c-1 1-1 1-1 2h-2l1-1c0-1 0-2 1-3h-1l-1-1v-1c-1-1-2-2-5-3 2-1 3-4 5-5v1h1c0-1-1-1-1-2s1-1 1-2l2-3 3-4s0-1 1-2h0l1-3h3v1c0-2 1-3 1-5 2-3 3-3 5-4l1-1s-1-1-1-2l2-1c0 1 1 1 2 1l2-1c2 0 3-1 4-1l1-1-2-1c1 0 2-1 3-1s1 0 2-1v-2c0-1 2-2 3-3h1 3l1-1c2-1 3-2 3-4l1-2h1l8-18c0-2 1-4 2-5l10-38c3-7 4-15 5-22 0-3 3-7 2-10v-1h0v-1c1-1 1-7 2-8-1-1 0-3 0-4 0-3 1-6 1-10v-1l-2-1v-3c0-2 0-3-1-4l-1-3-2-5c-1-1-3-2-3-3v-1l2 1z" class="Z"></path><path d="M483 363c1 0 2 0 3 1s3 0 5 0c-1 6-1 13-2 19l-1-17v-1h-1l-1 16v5c-1-5 0-11 1-16 0-2 0-4-1-5h-1 0-4c0-1 1-1 2-1v-1z" class="Q"></path><path d="M485 365h1c1 1 1 3 1 5-1 5-2 11-1 16l-2 16c-1 0-1-2-1-3-1-1 0-3 0-4 0-3 1-6 1-10l1-2c1-3 1-9 0-13v-4c1 0 1 0 0-1z" class="D"></path><path d="M483 399c0 1 0 3 1 3 0 8-2 16-4 23-1 5-1 10-2 14-1-1-1-1-1-2 1-1 0-1 1-2 0-1-1-2 0-3v-1l1-6v-1c0-1 0-2 1-3 0-1 0-1-1-2 0-3 3-7 2-10v-1h0v-1c1-1 1-7 2-8z" class="G"></path><path d="M478 451c0 1 1 2 1 2l-12 38-1 2s-1 0-1 1l13-43z" class="D"></path><path d="M478 451c2-5 2-11 4-17l3-20c1-5 1-11 3-16 0 5-1 9-1 14-1 2-1 4-1 6l-1 7c-2 9-3 19-6 28 0 0-1-1-1-2z" class="L"></path><path d="M477 365l1-1c-1 0-2-1-2-2v-1c2 2 3 3 5 4h4 0c1 1 1 1 0 1v4c1 4 1 10 0 13l-1 2v-1l-2-1v-3c0-2 0-3-1-4l-1-3-2-5c-1-1-3-2-3-3v-1l2 1z" class="c"></path><path d="M483 366c0 2 0 2-1 4h0 0l-2-2s-1 0-1-1v-1h4z" class="h"></path><path d="M477 365l1-1c-1 0-2-1-2-2v-1c2 2 3 3 5 4h4l-2 1h-4c-1 0-1 0-2-1z" class="T"></path><path d="M475 364l2 1c1 1 1 1 2 1v1c0 1 1 1 1 1v2h0c1 0 1 1 1 1 0 1 0 1-1 2l-2-5c-1-1-3-2-3-3v-1z" class="e"></path><path d="M463 486l1 1c-11 29-26 57-46 83-4 5-8 10-12 14l-2 3h0-2 0c0 1-1 1-1 2h-1v-2h1 0c0-1 0-1 1-2h0v-1h-2 0c1-2 3-3 4-5l-1 2c0 1 1 2 2 3 2-1 5-5 7-7 3-5 7-9 10-14 2-3 4-5 5-8 2-4 6-7 8-11l8-13c1-2 3-4 3-6 2-4 4-7 6-11 0-3 2-5 3-8l2-4c0-1 1-1 1-2s1-2 1-3c1-4 3-7 4-11h0z" class="V"></path><path d="M479 419c1 1 1 1 1 2-1 1-1 2-1 3v1l-1 6v1c-1 1 0 2 0 3-1 1 0 1-1 2 0 1 0 1 1 2-1 4-2 9-3 13l-6 21-5 14-1-1h0v-1h0v-1h-1c0-2 1-4 2-5l10-38c3-7 4-15 5-22z" class="R"></path><path d="M464 479h1 0c0 2 0 2-1 4 0 1 0 2-1 3h0v-1h0v-1h-1c0-2 1-4 2-5z" class="B"></path><path d="M462 484h1v1h0v1c-1 4-3 7-4 11 0 1-1 2-1 3s-1 1-1 2l-2 4c-1 3-3 5-3 8-2 4-4 7-6 11 0 2-2 4-3 6l-8 13c-2 4-6 7-8 11-1 3-3 5-5 8-3 5-7 9-10 14l-1-1 1-1h-1 0-1c2-2 6-6 6-9l3-3c1-3 3-5 5-8s4-6 7-9c3-5 6-10 9-16v-1-1h-1l-1-1h-1-2c0-1-1-2-2-3v-1c0-1 2-2 2-3v-1h-2c2 0 3-1 4-1l1-1-2-1c1 0 2-1 3-1s1 0 2-1v-2c0-1 2-2 3-3h1 3l1-1c2-1 3-2 3-4l1-2h1l8-18z" class="Q"></path><path d="M452 504l1-2h1l-7 14c-2 5-4 10-7 14v-1-1h-1l-1-1h-1-2c0-1-1-2-2-3v-1c0-1 2-2 2-3v-1h-2c2 0 3-1 4-1l1-1-2-1c1 0 2-1 3-1s1 0 2-1v-2c0-1 2-2 3-3h1 3l1-1c2-1 3-2 3-4z" class="E"></path><path d="M447 511c-1 1-1 3-2 4l-2-1c0-1 1-2 2-2s1-1 2-1z" class="G"></path><path d="M441 518h0c-1-1-2-1-3-1l1-1c1 0 2 0 4 1h2c-1 1-1 1-1 2-1 0-1 0-1 1h-1l-1-2z" class="B"></path><path d="M433 519c2 0 3-1 4-1v1l1-1 1 1c0 2-2 3-3 4h0c0-1 0-2 1-2l-1-1c-1 1-1 2-2 3l2 2h0v1l1 1h-2c0-1-1-2-2-3v-1c0-1 2-2 2-3v-1h-2z" class="W"></path><path d="M452 504l1-2h1l-7 14c-2 5-4 10-7 14v-1-1h-1l-1-1 1-1 2 1c0-1-1-2-1-3 1-1 2-1 3-3h0c-1 0-2 0-3-1v-1l1-1 1 2h1c0-1 0-1 1-1 0-1 0-1 1-2h-2l2-2c1-1 1-3 2-4l1-2 1-1c2-1 3-2 3-4z" class="P"></path><path d="M433 519h2v1c0 1-2 2-2 3v1c1 1 2 2 2 3h2 1l1 1h1v1 1c-3 6-6 11-9 16-3 3-5 6-7 9s-4 5-5 8l-3 3c0 3-4 7-6 9h1 0 1l-1 1 1 1c-2 2-5 6-7 7-1-1-2-2-2-3l1-2c-1 2-3 3-4 5h0 2v1h0c-1 1-1 1-1 2h0-1c0-1-1-2-1-3 0-2 1-2 0-4 1-1 1-3 2-4-1 0-1 0-2-1v-1c1 0 1-1 2-1 2 1 4 0 6 0 0 1 0 0 1 1l1-1h0l4-5 1-1c0-1 2-1 2-2s0-1-1-2h0c0-1 0-2-1-3v-1l-1-1c-1 1-1 1-1 2h-2l1-1c0-1 0-2 1-3h-1l-1-1v-1c-1-1-2-2-5-3 2-1 3-4 5-5v1h1c0-1-1-1-1-2s1-1 1-2l2-3 3-4s0-1 1-2h0l1-3h3v1c0-2 1-3 1-5 2-3 3-3 5-4l1-1s-1-1-1-2l2-1c0 1 1 1 2 1l2-1z" class="T"></path><path d="M426 542c1-1 2 0 4 0l-1 1h-4l1-1z" class="E"></path><path d="M429 537c1 0 2 0 2 1v1s-1 1-2 1l-1-2c1 0 1 0 1-1z" class="N"></path><path d="M414 560c2 0 2 0 4 2-1 0-1 1-2 1h-1 0c0-1 0-2-1-3z" class="I"></path><path d="M399 574c1 0 1-1 2-1 2 1 4 0 6 0v1h-1l-2 2h-1c0-1-1-1-1-1-1 0-1 0-1 1-1 0-1 0-2-1v-1z" class="V"></path><path d="M433 533l1-1h2 1c-1 1-2 1-2 1l-1 1c1 0 1 0 2 1h-1-1v2l-2 1h0v-1l-1 1c0-1-1-1-2-1 1 0 1-1 1-2h1l1 1v-2l1-1z" class="Q"></path><path d="M416 566c0 3-4 7-6 9h1 0 1l-1 1 1 1c-2 2-5 6-7 7-1-1-2-2-2-3l1-2c1-1 3-2 5-4 2-3 5-6 7-9z" class="E"></path><path d="M421 540h0c1 0 3-2 4-2 1-1 2-1 3 0l1 2c-1 0-4 1-5 2v2h1c2 1 4-1 5 0-1 1-1 0-2 1-1 0-1 1-2 1h-1v2h0l-1-1h-1c0 1 0 2-1 2h0c-2-2-3-4-4-7h0 1c0-1 0-2 1-3h0v1h1z" class="J"></path><path d="M421 532c0-2 1-3 1-5 2 1 2 1 3 2v1c0 1 0 1 1 1h0 2c2 0 2 1 4 2-1 1-1 1-1 2h-1c0 1 0 2-1 2 0 1 0 1-1 1-1-1-2-1-3 0-1 0-3 2-4 2h0-1v-1h0v-3c-1-1-2-2-3-2l1-3h3v1z" class="B"></path><path d="M418 531h3v1c0 1-1 4-1 5 1 0 1-1 2-2h0 1v1c-1 0 0 1 0 2l-2 2h-1v-1h0v-3c-1-1-2-2-3-2l1-3z" class="O"></path><path d="M433 519h2v1c0 1-2 2-2 3v1c1 1 2 2 2 3h2l-1 1 1 1 1-1h0c0 1-2 2-2 3h-1c-1 0-2 1-3 2h1l-1 1v2l-1-1c0-1 0-1 1-2-2-1-2-2-4-2h-2 0c-1 0-1 0-1-1v-1c-1-1-1-1-3-2 2-3 3-3 5-4l1-1s-1-1-1-2l2-1c0 1 1 1 2 1l2-1z" class="H"></path><path d="M427 520l2-1c0 1 1 1 2 1-1 1-2 2-3 2 0 0-1-1-1-2z" class="F"></path><path d="M426 531h0c0-1 1-2 1-3h0l5 5h1l-1 1v2l-1-1c0-1 0-1 1-2-2-1-2-2-4-2h-2 0z" class="X"></path><path d="M433 524c1 1 2 2 2 3h2l-1 1-1-1c-1 0-1 0-2 1h0c1 1 2 1 3 1l-1 1h-2-1v-1-1c0-2 0-3 1-4z" class="E"></path><path d="M427 523h2v1l-1 1-1-1h0v2s-1 1-2 0h0l-1 1h1v2c-1-1-1-1-3-2 2-3 3-3 5-4z" class="b"></path><path d="M417 534c1 0 2 1 3 2v3c-1 1-1 2-1 3h-1 0c1 3 2 5 4 7h0c1 0 1-1 1-2h1l1 1h0v-2h1c0 1 1 1 1 2-1 2-4 4-4 6 0 1-2 3-3 4-1 0-2 0-2 1h-4l-1-1c-1 1-1 1-1 2h-2l1-1c0-1 0-2 1-3h-1l-1-1v-1c-1-1-2-2-5-3 2-1 3-4 5-5v1h1c0-1-1-1-1-2s1-1 1-2l2-3 3-4s0-1 1-2h0z" class="U"></path><path d="M417 552c1-1 3-2 4-1h1c-1 1-3 4-4 4l-1-2v-1z" class="Q"></path><path d="M417 553l1 2-1 1v1h1c2 0 4-2 5-3 0 1-2 3-3 4-1 0-2 0-2 1h-4l-1-1c-1 1-1 1-1 2h-2l1-1v-1c2-2 4-3 6-5z" class="W"></path><path d="M417 534c1 0 2 1 3 2v3c-1 1-1 2-1 3h-1 0-2v3h1v-1l1-1c0 1 0 2 1 3l-1 1c1 1 1 1 2 1v1c-1 0-1 1-1 0h-2-1-1c-4-1-2-4-3-6h-1l2-3 3-4s0-1 1-2h0z" class="I"></path><path d="M413 540c1 1 1 2 1 3h0c-1 2 1 3 0 5 1 0 2 0 3 1h-1-1c-4-1-2-4-3-6h-1l2-3z" class="S"></path><path d="M417 534c1 0 2 1 3 2v3c-1 1-1 2-1 3h-1 0-2 0c0-1-1-1-1-1l1-1v-4s0-1 1-2h0z" class="Z"></path><path d="M417 534c0 3 0 5 1 8h0-2 0c0-1-1-1-1-1l1-1v-4s0-1 1-2z" class="G"></path><path d="M411 543h1c1 2-1 5 3 6h1v1h2 0c0 1-1 1-1 2h0v1c-2 2-4 3-6 5v1c0-1 0-2 1-3h-1l-1-1v-1c-1-1-2-2-5-3 2-1 3-4 5-5v1h1c0-1-1-1-1-2s1-1 1-2z" class="B"></path><path d="M410 548h1l1 1-1 1h1v1c0 1 0 1-1 2h-1c0-2-1-3 0-5z" class="K"></path><path d="M288 79l1-2c1 0 2 1 3 1v-1c1 1 1 1 2 1h0 1 1v-1l1 1c0 1 1 2 2 2h1c1-1 1 0 3 0l9 1c3 0 7 1 10 2 9 3 19 6 28 11 5 2 9 5 13 6 1 1 2 2 3 2 8 5 16 10 24 16l4 4c1 2 4 4 6 5l5 6c1 1 2 1 3 2 1 3 3 4 5 6 2 3 4 5 7 8 0 0 1 1 2 1 0 1 1 2 1 2 2 2 6 6 6 8 1 1 2 3 2 4h1s0 1 1 1l2 2v1 1c1 0 1 0 1 1l2 4 1 1c1 2 2 4 4 5 1 1 4 6 4 8 1 0 0 1 1 1 0 2 1 3 2 5l4 8c1 1 2 3 2 4l8 17c1 3 2 5 3 8 0 1 1 2 1 3l11 36c1 2 1 5 2 7l3 17c1 2 1 5 1 7 1 9 3 17 3 25 0 2-1 3 0 5l-1 2c0 1 0 1-1 2h-1s0-1 1-1v-2c-1 0-1 2-2 2l-1-1v3h-1l-1 2h0c-2 0-3-1-3-2l-2 2-3-2-3-2c-1-1-3-2-5-3h-4c-2 2-5 4-6 6h1c0 1-1 1-1 2h0l-1-1h-1c1 1 1 2 2 3 1 0 3 1 4 1l-1-1c1-2 2-3 3-4 1 0 2-1 3 0h1c1 0 2 0 3 1l-1 1c1 1 1 2 2 3 0 0 1 0 2 1l-1 1c-1 1-2 2-2 3s-1 2-2 2h0v1 1h2 1 4c1 3 3 6 5 8 1 1 3 3 5 4v1c-1 0-2 0-2 1-2-1-3-2-5-4v1c0 1 1 2 2 2l-1 1-2-1v1c0 1 2 2 3 3l-2 1-3-3h-2c-4-2-7-2-11-2v-1h-1c-4 0-8 0-12 2l-2 1h0 2c-2 1-2 2-3 3 0 1 1 2 2 3l1 1h1v2l1 1c-2 1-1 1-2 3 1 2 0 6 0 8-1 12-2 24-4 36l-3 13c0 3-1 5-1 8v1c-1 1-1 1-1 3l-2 5v2c-4 13-8 25-13 37-8 19-17 36-29 53l-6 8c-1 0-2 2-3 2v1l-1 1-1 1c-2 1-4 2-5 4v3h1c-2 6-8 9-10 15-1 1-2 2-4 3 0 0-1 1-2 1 1 1 1 2 1 3l-1 1h-1c-1 0-1 1-1 2h0c-1-1-1-1-2-1v2c0 1-1 2-2 3l-2-2h0c0-1 0-1-1-2l-1 1h-2 0v1c-1 1-3 2-5 4l1 2-1 1h3v1l-4 2-5 5c-2 1-5 3-7 3h-3-2s-1 1-1 2c-1 1-2 2-3 2h-2-2c1-1 1-1 1-2-1-1-1-1-2-1l1-1c-2 0-2 0-3 1l-8 2c1 0 1 0 1 1l-1 1c-1 0-2 1-2 1l-4 1c0 1-1 1-2 2 1 1 1 1 2 3 2 0 5-2 7-2 0 1 0 2-1 2l-9 11c-1 0-2 2-2 2-2 2-5 4-7 6h0l-2 3c-1 0-1 0-2-1 0 1-2 3-3 3h-1s-1 0-1 1l-2-2h1v-2h-4v1c0 1-1 1-1 1h-3c-1-2-3-2-5-3v1c-1 0-1-1-2 0l-1 1h0l2 2 1 1c0 1 1 3 1 4v1c-2-5-6-9-10-12l-3-2c-1-1-2-1-3-1h0l-2-1-4-3c-1-1-2-1-3-1-2 0-4 1-5 2-2 1-3 1-5 1 1 0 1-1 1-1v-1h-2-1-3s-1 1-2 1c-2 1-4 1-6 2l-2 1-2 1-1 1c-1 0-2 1-3 1l-2-1c-1 1-1 1-1 0l-10-1c-1 0-2-1-4-1l-6-2c-2 0-5-1-6-3-5-2-8-5-12-8-2-1-4-4-6-6-1-1-1-2-2-3s-1-2-1-3c1 1 3 3 5 4h1 1c1 0 1 0 2-1l-4-2c-5-3-7-7-8-12-2-3-2-7-2-10l1-15v1-3c0 1 1 2 1 3l1 1 1-2h1v1l2-2c1-4 1-9 2-13h0l1-2h-1 0c-2-1-3-2-3-3l-7-8c-3-3-5-6-8-9-15-21-26-44-35-68-2-6-4-12-6-19l-1 1-1-1c0-1-1-3-1-4l-2-7c-1-3-2-6-2-9-2-8-3-15-4-22l-1-6-1-5v-3c0-3-1-5-1-8v-9c0-1-1-2-3-3h0l-1-1c0-2 0-2 1-2l2-2c0-1 0-1 1-2l1 2c0 1 0 1 1 2h0c1 0 2-1 2-1 0-1 0-2-1-3h1c1 0 2 0 2-1-2-3-5-4-8-5h-3v-1h3-4l-1-1h-2v-1h2c0-1 1-2 1-3v-1h1c0-2 0-2 1-3 1 0 1 0 1-1v-2h14c-2-1-6 0-9 0v-1h3 0 2 0c-1 0-1-1-2-2h0c0-1 0-1 1-2l1-3h0c0-1 1-1 2-2h0c1 0 1-1 1-2h-3 0c0-1 1-1 1-2 1-2 0-6-1-8h0v-1l-1-1v-4-2h0l-1-1c0-4 2-10 2-14l-1-1c0-2 1-7 2-8h0v-1h-2c1-1 1-2 1-3 0-2 1-4 2-6v-6l5-22v-1-2-1h-1c0-2 1-4 1-5-1-1-1-1-1-2h0l3-10-1 1v-1l-1 1h0l-2 2v-4l1-3 2-2c1-2 3-4 4-6l2-6 1-4 1-6 2-13-1-3h0c-1 1-1 4-1 6-1-2-1-3-2-4 0-1 1-3 1-4s0-2 1-2l1-1 1 1v-1-1c2-1 3-2 4-2l2 2 2 1 1-1c1-1 1-2 1-2 1-1 2-1 3-2-2-1-3-3-5-3 1-1 1-2 2-4l1-1v2 2 1h2 0l2 2c1 0 1 0 2-1s2-1 2-2c1-1 2-2 3-4 1-1 1-1 3-1l3-4c1-2 3-4 4-6l2-2c2-1 3-4 5-6 5-5 10-9 15-14v1c0 1 0 1 1 1 1-1 2-3 4-4v1c3-1 5-3 7-4h1c4-2 8-6 12-8 7-4 13-8 20-11 3-1 5-2 8-3 1-1 3-2 4-3 4-1 9-2 13-3v1h2l22-3c1-1 2-3 3-3l2-2-1-1h0c0-1 0-2-1-3h0-1 0c-1 1-2 1-2 2l-1-1h0l1-1v1l1-1c1-2 1-4 1-7 1 1 2 1 3 1v-2l1 1c1-1 2 0 3-1v-2h1 3c0-1 0-1-1-2 0-1 0-2 1-3l1 2 2-1 1-2v-1l1-1c1-1 1-2 0-4 1 0 2 0 3 1v1z" class="j"></path><path d="M367 324c0 1 0 3-1 4-1-1-1-2-2-3 1 0 2-1 3-1z" class="U"></path><path d="M105 368c1 1 1 1 1 3l-2 1c-1 0-1 0-2-1h1c1-2 2-2 2-3z" class="E"></path><path d="M152 392h1 0c0 1 0 1 1 2 0 2-1 5-1 7l-1-1v-2c0-1 0-2 1-3-1-1-1-1-1-2v-1h0z" class="S"></path><path d="M178 371v1l1-1 1 1-2 2v1 2h-1c-1 0-3 2-4 3l5-9z" class="O"></path><path d="M406 380c3 2 6 4 8 8l-4-3v1 1l-2-2c0-1-1-2-2-3v-2z" class="V"></path><path d="M191 502c-1 0-2-1-3-2l8 5 4 2h0v1c-1 0-5-2-6-3l-3-3h0z" class="M"></path><path d="M395 369h2l8 7h-1c-1-1-2-1-3-1h-1c-1-2-3-4-5-6z" class="H"></path><path d="M371 343l1-1c1 1 2 1 3 1h1 1c0 1 1 1 1 1l1 1s1 0 1 1l-1 1-1-1 1 1c1 0 1 1 1 1l-9-5z" class="h"></path><path d="M172 400l-1-1h0c-1-2 2-8 3-10l1 1c0 1-1 4-1 5l-2 5h0z" class="E"></path><path d="M185 236h3 0l-1 2c0 1 0 1 1 2h-1 0c0 1 0 1-1 2h-1l1-2h0-1-1c-1 1-3 2-4 2l5-6z" class="K"></path><path d="M177 367c1 0 1-1 2-1h0c0 1 0 1 1 1h0v1c-1 1-1 2-1 2v1l1-1c1 1 2 1 2 3 0 1 0 2-1 3h0v-1h-1-2v-1l2-2-1-1-1 1v-1-1c0-1 0-2-1-3z" class="a"></path><path d="M437 357c0 3 0 4 2 6h1c2 2 4 2 7 2l-2 1h0c-2 0-4 1-6-1h0c-1 0-2-1-2-2-1-2-1-4 0-6z" class="N"></path><path d="M178 375h2 1v1l-3 3-1 2-4 6 4-10h1v-2z" class="f"></path><path d="M113 356c1 2 1 4 1 6-1 2-2 3-3 4-1 0-2 1-2 1h-3c0-1 1-2 2-2l2-1c1-1 2-2 2-3s1-2 0-3c0-1 0-2 1-2z" class="E"></path><path d="M108 365l2-1-1 3h-3c0-1 1-2 2-2z" class="B"></path><path d="M376 299c0 4-1 8-1 11l-1 1-1-1s0 1-1 2c0-1 0-3 1-4l-1-1-1-1c1 0 1-1 2-1h1v-5c1 0 1-1 2-1z" class="X"></path><path d="M373 305h1l-1 3-1-1-1-1c1 0 1-1 2-1z" class="R"></path><path d="M356 239c0-2-1-2-2-4h2c-1-1-2-1-3-2h0l1-1c3 2 6 4 8 6v1c0-1 0-1-1-1-1-1-3-2-5-3 1 2 5 5 5 6h-1l-4-2z" class="Q"></path><path d="M178 493l12 10 1 1h-2l-6-4c-1-2-5-5-5-7z" class="M"></path><path d="M436 350h-1l-1-1c2-2 3-3 5-4 1 1 2 3 3 4 0 0 1 0 2 1-3 1-6 0-8 0z" class="K"></path><path d="M171 487h1v-2h0l5 6s1 1 1 2c0 2 4 5 5 7h-2l-3-3 1-1-1-1c-2-3-5-5-7-8z" class="L"></path><path d="M211 176l7 9-1 1c1 2 3 3 3 5-2-2-5-6-7-9l-4-5 2-1z" class="O"></path><path d="M185 219h1v1l3 9v2 1l-1-1c-2-2-3-5-3-7-1-1-1-3-1-4h-2v-1h3z" class="J"></path><path d="M366 319l1-6h1c1 1 2 1 4 1 0 2-1 5-3 7l-3-2z" class="T"></path><path d="M182 465c1 1 2 1 2 3 1 2 1 4 3 6 0 1 1 2 1 3-1 1-1 5-1 8l-5-20z" class="Q"></path><path d="M354 228c1-1 1-3 2-4v1h1c0 2-3 5-3 7l-1 1h0c1 1 2 1 3 2h-2c1 2 2 2 2 4h-1c-1-1-1-1-1-2-1-2-3-3-3-6 2 0 2-1 3-3z" class="R"></path><path d="M423 412h1c1 1 1 2 2 2 1 2 2 4 3 5s2 2 3 4l-2-2h0l3 5-1 1h0l-2-2-5-10c0-1-1-2-2-3z" class="O"></path><path d="M196 494s0 1 1 0c-1-1-1-2 0-3l3 9v2c0 1 0 3 1 4l-1 1-4-2c1 0 2-1 2-1 0-1-1-3-1-4s-1-2-1-4h0v-2z" class="b"></path><path d="M183 197l3 15v1 6h-1v-4c0-6-2-11-3-17l1-1z" class="K"></path><path d="M186 212v1 6h-1v-4 1l1-1v-1-2z" class="M"></path><path d="M390 359c1 1 2 3 4 4 1 1 3 2 5 3l-8-3 6 6h-2 0l-4-3c-1-2-2-2-3-3l-4-3v-1c2 1 4 3 6 3h0v-3z" class="K"></path><path d="M110 353l3 3c-1 0-1 1-1 2 1 1 0 2 0 3s-1 2-2 3l-2 1-4-1-2-1v-1h1 5c1-1 2-2 2-3h0 1c0-2 0-2-1-3v-3z" class="d"></path><path d="M220 191c0-2-2-3-3-5l1-1 8 9h1v-1s2 2 2 3h0-2v-1l-1 1c1 1 2 2 2 3h-1c-2-3-5-5-7-8z" class="Y"></path><path d="M363 340c0-2-4-3-4-5l1-1v-1h0l2 2c4 4 8 6 13 8-1 0-2 0-3-1l-1 1c-2 0-5-3-7-4v1s1 0 1 1c-1 0-2 0-2-1z" class="F"></path><path d="M444 310c1 2 1 3 2 5l1 10v3-2h1c0 1 0 3 1 3v-2 4c-1 2 0 5 0 7 0 1 0 1-1 1-1-2-1-6-1-8l-3-21z" class="M"></path><path d="M417 402c3 2 5 4 7 7 1 2 3 4 4 6-1 0-1-1-1-1h-1c-1 0-1-1-2-2h-1-1c-1-2-2-3-3-5v-2l-3-3h1z" class="F"></path><path d="M242 197l2 2v-3l2 2c0 1 0 1 1 2v2c0 1 0 2-1 3h0v-1c-2-1-1-1-2-2l-2 2h0v-1c-1-1-1-1-2-3h1v-1c1-1 1-1 1-2z" class="i"></path><path d="M196 163h0 1v-1c5 4 10 9 14 14l-2 1c-4-5-8-10-13-14z" class="a"></path><path d="M414 400v-1l2 2h1v1h-1l3 3v2c1 2 2 3 3 5h1c1 1 2 2 2 3h-2l-2-2c-1-2-2-3-4-5 0-2-2-4-3-6v-2h0z" class="Q"></path><path d="M360 156c2 6 1 13 1 19 0 3 0 7-1 11 0-1 0-2-1-3-1-2 1-8 1-10v-8c0-3 0-5-1-8l1-1z" class="R"></path><path d="M174 449c1 1 1 2 1 3h2l1 3c1 3 1 5 3 8v1 1 7c-1-3-1-6-2-9-1-2-2-3-2-5h-1c-2-2-1-6-2-9z" class="T"></path><path d="M174 425c1 2-1 7 0 9s1 7 1 10l2 8h-2c0-1 0-2-1-3v-9h0l-1 2h-1 0c1-2 2-9 1-11-1 1-1 1-1 2-1-1-1-2 0-3s1-3 2-5z" class="C"></path><path d="M107 346v-1c-1 0-2 0-2-1h0 2c2 0 3 1 5 2l3 3v1h-2-4c-2 0-4 0-7-1h3v-2c1 0 1-1 2-1z" class="K"></path><path d="M109 350c1 0 1-1 2-2h1c0 1 1 2 1 2h-4zm-2-4v-1c-1 0-2 0-2-1h0 2c2 0 3 1 5 2h-1l-1 1v1l-1 1v-2h-1c0 1-1 2-2 2v-1s1-1 1-2z" class="J"></path><path d="M104 339l2 2v1c0 1 0 1-1 2h0c0 1 1 1 2 1v1c-1 0-1 1-2 1v2h-3 0l-1 1c-1 0-1-1-2-2h0c0-1 0-1 1-2l1-3h0c0-1 1-1 2-2h0c1 0 1-1 1-2z" class="O"></path><path d="M92 363c2 0 3 0 5 1h3l4 2h-1c1 1 2 1 2 2s-1 1-2 3h-1c1 1 1 1 2 1-1 0-1 0-2 1v2c0-1 0-3-1-4l-1-2c-2-3-5-4-8-5h-3v-1h3z" class="L"></path><path d="M92 363c2 0 3 0 5 1h-4-1-3v-1h3z" class="D"></path><path d="M102 371c0-2-2-4-3-5 1-1 3 0 4 0 1 1 2 1 2 2s-1 1-2 3h-1z" class="F"></path><path d="M184 468l1-4c0 2 0 2 1 3l1 3c1 3 3 5 4 8h0c1 3 2 6 3 10l-4-3-2-8c0-1-1-2-1-3-2-2-2-4-3-6z" class="a"></path><path d="M256 199l1-1 1-1h1v-1h1v2c1 0 1-1 2-1l2-1v1 2h0v1c-1 0-1-1-1-1-1 0-1 0-1 1v3h2c1 0 1-1 1-1h1 0l1 2c-2 1-2 1-3 3v1 2h0-1v-3c0-1 0-1-1-2h-2v-1h-1v1l-2-1c1-1 1-1 1-2 0 0 1-1 1-2v-1h-3z" class="Y"></path><path d="M259 199h0 2v1c0 1 0 2-1 3v1h-1v1l-2-1c1-1 1-1 1-2 0 0 1-1 1-2v-1z" class="f"></path><path d="M259 200l1 3v1h-1v1l-2-1c1-1 1-1 1-2 0 0 1-1 1-2z" class="Z"></path><path d="M189 229l1 2h0c0 1 0 2 1 3v1c-1 0-1 1-2 2l1 1v1 1h1c1 0 1 0 2 1h2l1 1c-1 1-1 0-2 0l-2-1h-2-1-2v-1h1c-1-1-1-1-1-2l1-2h0-3c-1 0-2 1-3 1v-1h0c1-1 3-2 4-2s0 0 1-1c-1 0-2 1-4 1 2-1 3-2 4-2h0l1-1 1 1v-1-2z" class="V"></path><path d="M307 192c1-1 1-2 2-2h1l3-2h0v1c3-2 6-5 8-7 0 1-2 3-2 4h2v1s-1 0-1 1c-1 1-3 2-4 4l-2 1c-1 0-1 0-2 1h-1v-1c-1-1-1-1-2-1v-1l-2 1z" class="Z"></path><path d="M297 195c1 0 2-2 3-3 2-1 3-2 5-3-1 1-2 1-2 3h0l2-1 1-1c1 1 0 1 1 2h0l2-1v1 1 1l-1 1h-1s0-1-1-1c-1 1-1 2-1 3l-2-1c-1 0-1 0-1-1-1 0-2 1-2 2-1 0-2 1-3 2l-1-2v-2h1z" class="Y"></path><path d="M305 191l1-1c1 1 0 1 1 2h0l2-1v1 1 1l-1 1h-1s0-1-1-1c-1 1-1 2-1 3l-2-1c0-2 2-1 3-2 0-1 0-2-1-3z" class="F"></path><path d="M307 192l2-1v1 1 1l-1 1-1-1v-2h0z" class="Y"></path><path d="M189 309h18 0-7v1h2 0-3-7-7-3c-9 1-17 3-25 5 6-3 14-4 21-6h6 1 4z" class="O"></path><path d="M182 194h1 0v1h0v-2h1v1l1 3 2 7c1 3 1 5 2 8 0 2 0 4 1 6-1 0-2 1-3 0l-1-5v-1l-3-15-1-3z" class="T"></path><path d="M389 375l1-1c3 1 6 4 9 5 2 0 4 2 5 3v1h1c1 0 1 0 1-1 1 1 2 2 2 3l2 2c2 2 5 4 6 7-1-1-4-4-5-3-1-1-1-2-2-2-1-1-3-2-3-3v-1h-1l-1 1h-1c0-2-2-3-3-4-3-3-7-5-11-7z" class="O"></path><path d="M87 361c6-2 10-1 16 1h-1v1l2 1 4 1c-1 0-2 1-2 2l-2-1-4-2h-3c-2-1-3-1-5-1h-4l-1-1h-2v-1h2z" class="F"></path><path d="M104 364l4 1c-1 0-2 1-2 2l-2-1-4-2h2 2 0z" class="N"></path><path d="M370 282v-5c-1-1-1-1-1-2h1l2 3s1 1 2 1c0 2 2 3 4 4l-6-3h0c0 1 1 1 1 3h-1c1 1 3 3 3 4h-1s0 1 1 2h-2v1l5 6c-2-1-3-2-4-3l-3-3c0-1-1-1-1-2h1v-1c-1-2-1-3-1-5z" class="K"></path><path d="M430 425l2 2h0l1-1 4 8h-1c-1-1-2-3-3-3 2 2 3 4 3 6 1 2 2 6 3 7v1c-1 1-1 1-1 3l-3-7c-1-2-3-5-3-7 0-3-1-6-2-9z" class="M"></path><path d="M191 478h1 0l1 1c0 1 1 2 2 2v1l1 1 1-1c0 1 1 2 1 3-1 2-1 4-1 6-1 1-1 2 0 3-1 1-1 0-1 0v-1h0c-1 0-1 0-2 1l-1-6h-1v2h0l-2-5 4 3c-1-4-2-7-3-10h0z" class="N"></path><path d="M195 482l1 1 1-1c0 1 1 2 1 3-1 2-1 4-1 6-1 1-1 2 0 3-1 1-1 0-1 0v-1c0-1 0-1-1-2 0-2 0-3-1-5 0-1 0-3 1-4z" class="F"></path><path d="M182 310h3v3c1 0 1-1 2 0v3c0 2 1 1 0 3h1c0 1 0 1 1 1 0 1 0 1 1 2l-6 2v-1-2-3h-1v1h-1v-1c1-2 1-3 1-4v-1c-2 2-2 4-3 5 0-2 1-5 1-7l1-1z" class="P"></path><path d="M184 321l1-4c0-1 0-1 1-1h0v2h1v1h1c0 1 0 1 1 1 0 1 0 1 1 2l-6 2v-1-2z" class="T"></path><defs><linearGradient id="T" x1="173.375" y1="386.778" x2="179.032" y2="391.861" xlink:href="#B"><stop offset="0" stop-color="#a5a3a4"></stop><stop offset="1" stop-color="#c2c0c2"></stop></linearGradient></defs><path fill="url(#T)" d="M178 379v1c1 0 1 1 1 2 1 0 1 1 1 2 0 0-1 1-1 2s-1 2-1 3c0 2-1 5-2 7 0 1-1 2-2 3-1 0-1 1-1 2l-2 2c0-1 0-2 1-3h0l2-5c0-1 1-4 1-5l-1-1c1-2 4-6 3-8l1-2z"></path><path d="M395 369h0c2 2 4 4 5 6h1c1 2 3 4 5 5v2c0 1 0 1-1 1h-1v-1c-1-1-3-3-5-3-1-2-3-3-5-4 0 0 0-1-1-1v-1h0v-1h1l-1-2 2-1z" class="a"></path><path d="M395 369h0c2 2 4 4 5 6v1c-2-1-5-3-6-4l-1-2 2-1z" class="d"></path><path d="M367 265c0-1 0-2 1-2-1-1-2-3-2-4h1l1 2c1 1 2 2 3 4h0-1c0 3 2 5 3 7l-4-4c1 2 2 4 2 5s1 2 2 3c0 1 0 1-1 1v1l-2-3h-1c0 1 0 1 1 2v5h-1c0-1 0-2-1-3 0 0-1-3-1-4v-4l-1-1 1-1v-2l1-1-1-1z" class="Q"></path><path d="M229 196h1c0-1-1-2-1-3 2 2 5 5 8 6v-1-1c1 0 2 2 3 1 0-1 0-1-1-2h1l1 1v-1c1 0 1 1 1 1 0 1 0 1-1 2v1h-1c1 2 1 2 2 3-2 0-5 0-6-1-1 0-1 0-2-1v1l2 3h-1c-1-1-3-3-5-4l-2-1v-1c0-1-1-2-2-3l1-1v1h2 0z" class="g"></path><path d="M404 386l1-1h1v1c0 1 2 2 3 3 1 0 1 1 2 2l15 17h1v1c-2 0-1-1-3 0-2-3-4-5-7-7v-1h1c-4-4-8-7-11-11-1-2-2-3-3-4z" class="a"></path><path d="M418 401l8 7h0 1v1c-2 0-1-1-3 0-2-3-4-5-7-7v-1h1z" class="h"></path><path d="M179 386h1v3 1s1 0 1 1l-1 3c-1 1 0 3-1 4s-1 2-1 3-1 2-1 4h0l-2 4c0 1-1 1-1 2l-1-1v-2-2h-1v1l-2 3 3-8v-1c0-1 0-2 1-2 1-1 2-2 2-3 1-2 2-5 2-7 0-1 1-2 1-3z" class="O"></path><path d="M173 402h0c1-1 1-1 1-2l3-3h0v3s-1 1-1 2 0 2 1 3l-2 4c0 1-1 1-1 2l-1-1v-2-2h-1v1l-2 3 3-8z" class="d"></path><path d="M363 320h0c1 0 3 1 4 1v2 1c-1 0-2 1-3 1-1 1-1 2-1 3v2s-1 0-2 1c1 2 1 2 1 4l-2-2h0v1l-1 1c0 2 4 3 4 5l-9-4c1 0 1-1 2-1h0 1v-4l1-3h1c1 0 1-1 1-1 0-1 0-1 1-2 0-1 0-1 1-2 0-1 1-2 1-3z" class="T"></path><path d="M361 331v-3h1 1v2s-1 0-2 1z" class="b"></path><path d="M363 320c1 0 3 1 4 1v2c-1 0-1 0-2-1h0l-1 1h-1v-3z" class="D"></path><path d="M182 198l-2-9c-1-8-1-16 1-23 1-2 1-5 3-7 1-1 1-1 2-1 4-1 8 2 11 4v1h-1 0c-3-1-7-4-10-3-1 1-2 2-3 4-3 7-2 16-2 23 1 2 1 5 1 7l1 3-1 1z" class="F"></path><path d="M236 205l-2-3v-1c1 1 1 1 2 1 1 1 4 1 6 1v1h0l2-2c1 1 0 1 2 2v3c0 1 1 3 1 5l-1 1v1l-1-1h-1v1 1h0l-1-1h0l-1 1c-2-4-4-7-6-10h0z" class="I"></path><path d="M246 213l-1-1c-1-1 0-3 0-5h1c0 1 1 3 1 5l-1 1z" class="G"></path><path d="M236 205l1-1c1 1 2 3 2 4 1 1 1 1 1 2h1v-1l-1-1s0-1-1-1v-2l1-1c1 2 1 3 2 5 0 2 1 2 1 4v1l-1 1c-2-4-4-7-6-10z" class="E"></path><path d="M282 198s0-1 1-1v1h1c0-1 0-1 1-1l1-1 1-1 2-2v2c0 1 1 1 1 1 1 1 3-3 4-4v3l3-3v1 2h-1v2c0 1 0 2-1 2s-1 0-1-1h0c-1 1-1 2-2 3h-1c0-1-1 0-1 0h-1v2h-1c-1 0-2 1-2 1h0l-2-2-1 1h0-1l-1-2c0-1 0-2 1-3z" class="g"></path><path d="M283 203c0-1 0-2 1-2l1 1c2-1 3-1 5-1h-1v2h-1c-1 0-2 1-2 1h0l-2-2-1 1zm76-20c1 1 1 2 1 3 0 2-1 4-1 7-1 4-6 13-3 17v1c1 1 1 3 2 4 0 3 0 6-1 10h-1v-1-1h0c1-3 0-8-1-11l-1-1h-1-1c0-2 0-4-1-7 0 0-1-1-1-2h1c-1-1-1-1-1-2 1 0 2-1 2-2h0l1-1v1s0 1-1 2v2l1 1v-1l1-1c3-6 4-12 5-18z" class="Z"></path><path d="M352 198l1-1v1s0 1-1 2v2l1 1v-1l1-1c-1 4 0 6 0 10h-1-1c0-2 0-4-1-7 0 0-1-1-1-2h1c-1-1-1-1-1-2 1 0 2-1 2-2h0z" class="E"></path><path d="M355 243c0-1 0-2-1-3h0l1-1 1 1c1 1 2 1 3 2l5 4 2 2h-1l-1-1v1c0 1 1 1 2 2s2 2 3 4c-1-1-3-3-5-3h0c0 2 1 3 3 4 1 3 3 5 5 8-1-1-2-1-3-2h-1l-1-2h-1c0 1 1 3 2 4-1 0-1 1-1 2l-2-4h-1l1-1c0-1 0-1-1-1l1-1 2 1v-1h-1l-1-1c-1 0-4-3-5-5v-1c-1 0-1-1-1-2 0 0-1 0-1-1v-1h2 0c-1 0-1-1-2-1 0 0-1 0-1-1l-2-2z" class="V"></path><path d="M356 240c1 1 2 1 3 2l5 4c-3 0-7-3-8-5v-1z" class="J"></path><path d="M179 292l1 1h3l-1 1h0l1 2h2c-1-1-1-1-1-2 0 1 1 1 1 2v1c0 1 0 1 1 1l-1 1c-1 1-1 1-1 3l-1 1h2v1h0 0l1 1c0 1-2 1-2 2 1 0 1 1 1 2h-1-6c0-1 0-2-1-3h-1l1-4c-1 0-2 1-2 2l-1-1 2-2v-1l3-3s-1 0-1-1l1-1h1v-1h-2 0l1-2z" class="R"></path><path d="M177 302h1s1 0 1-1h1v3l-2 1-1 1h-1l1-4z" class="K"></path><path d="M179 295v1h1c1 1 2 1 2 3h-1s-1 0-2 1-2 1-3 2v-1-1l3-3s-1 0-1-1l1-1z" class="I"></path><path d="M184 294c0 1 1 1 1 2v1c0 1 0 1 1 1l-1 1c-1 1-1 1-1 3l-1 1h2v1h0 0l1 1c0 1-2 1-2 2 1 0 1 1 1 2h-1-6c0-1 0-2-1-3l1-1c1 1 0 1 0 2v1l1 1c0-2 1-3 2-4v-3h0l2-2c-1 0-1-1-1-1v-2h0l1-1h2c-1-1-1-1-1-2z" class="C"></path><path d="M184 309l-1-2s0-1-1-1h0v-1l3-1h0 0l1 1c0 1-2 1-2 2 1 0 1 1 1 2h-1z" class="T"></path><path d="M246 198h0c0-2-1-4-2-6l4 6c1 0 1-1 1-1h1v1c1 0 1-1 2-2 1 1 2 1 2 2 1-1 1-1 1-2h1v3h3v1c0 1-1 2-1 2h0c-1 0-1 0-1-1-1 1-1 2-2 2v5h0c-1 1-1 3-2 4-1 0-1 0-2-1 0-1-1-1-2-1-1-1-1-2-1-4h0l-1 1-1-1v-1h0c1-1 1-2 1-3v-2c-1-1-1-1-1-2z" class="g"></path><path d="M250 207h1c1-1 1-3 1-5h1v2h1c0-1 0-3 1-3v2 5h0l-1-2h0l-1 4h-1c0-1-1-1-1-2-1 0-1 0-1-1z" class="F"></path><path d="M247 202c1 1 1 1 1 3 1-1 1-2 2-2v4c0 1 0 1 1 1 0 1 1 1 1 2h1l1-4h0l1 2c-1 1-1 3-2 4-1 0-1 0-2-1 0-1-1-1-2-1-1-1-1-2-1-4h0l-1 1-1-1v-1h0c1-1 1-2 1-3z" class="N"></path><path d="M126 395l3-3c2-3 5-5 7-8l-3 5c4-3 8-6 13-7h1 3 1 1c0 1 0 2 1 3v1 6h-1c0-1-1-2-1-3-1 0-1-1-2-1v-1l-1-1h0c-1 2-4 1-6 1 0 1 0 1-1 1s-2 0-2 1c-2 1-4 2-6 2l-3 4h0l1-3h-1l-3 4-1-1z" class="Y"></path><path d="M150 382h1 1c0 1 0 2 1 3v1 6h-1c0-1-1-2-1-3-1 0-1-1-2-1v-1l-1-1h0c-1 2-4 1-6 1 0 1 0 1-1 1s-2 0-2 1c-2 1-4 2-6 2l2-2c1-1 2-2 4-2h2c1-1 3-2 5-2 1 0 2 0 4-1h0l1 1c0-2 0-2-1-3z" class="K"></path><path d="M149 387c1 0 1-1 2-1v2h1c1 0 1-1 1-2v6h-1c0-1-1-2-1-3-1 0-1-1-2-1v-1z" class="B"></path><path d="M319 313c11-1 23 1 33 3 4 0 7 1 10 2l4 1 3 2c15 5 30 13 39 26h0c-12-12-25-21-41-26-1 0-3-1-4-1h0l-1-1c-4 0-8-2-12-3-10-1-20-2-31-1h-4c-2 0-4 1-6 0v-1h3c2 0 5 0 7-1z" class="a"></path><path d="M264 199l2-4c0-1 0 0 1-1v2c0 1-1 1-1 2h1v-1c1 1 1 2 1 3 1-1 2-2 2-3h1c0 1 0 2 1 2l1 1 2-3c1 0 1 1 1 2 1 0 2-3 3-4v3h3c-1 1-1 2-1 3v5h0c-1 1-1 2-1 3h-1v-2h-1-1v-2c-1 1-1 2-2 3v-1h-1v2h-1c0-1 0-1-1-2v1h-1v-3c-2-1-1-1-1-3l-2 1c0-1-1-1-2-1h0-1s0 1-1 1h-2v-3c0-1 0-1 1-1 0 0 0 1 1 1v-1z" class="f"></path><path d="M279 198h3c-1 1-1 2-1 3v5h0c-1 1-1 2-1 3h-1v-2h-1-1v-2c-1 1-1 2-2 3v-1h-1v2h-1c0-1 0-1-1-2v1h-1v-3c1 1 2 1 3 1v-3l1-1 2 1v1c1 1 2 3 3 3v-2h-1v-1c0-1 0-1 1-2l1-1c-1-1-1-2-2-3z" class="a"></path><path d="M197 480c2 3 6 8 6 11l1 3h2l1 3 1 3c-2-2-1 0-3-1 0 3 2 7 2 9-1 0-1 0-2-1h0l-2 2c-1-1-1-1-3-2h0l1-1c-1-1-1-3-1-4v-2l-3-9c0-2 0-4 1-6 0-1-1-2-1-3v-2z" class="F"></path><path d="M204 494h2l1 3 1 3c-2-2-1 0-3-1l-1-5z" class="I"></path><path d="M201 496c1 1 1 3 1 4 1 2 3 5 3 7l-2 2c-1-1-1-1-3-2h0l1-1c-1-1-1-3-1-4h1c-1-2-1-4 0-6z" class="T"></path><path d="M201 496c1 1 1 3 1 4v4c0 2 1 3 1 4-1-2-2-4-2-6-1-2-1-4 0-6z" class="b"></path><path d="M197 480c2 3 6 8 6 11-1-1-1-2-3-2v1c0 2 1 4 1 6-1 2-1 4 0 6h-1v-2l-3-9c0-2 0-4 1-6 0-1-1-2-1-3v-2z" class="C"></path><path d="M198 485c2 5 2 9 2 14v1l-3-9c0-2 0-4 1-6z" class="Z"></path><defs><linearGradient id="U" x1="179.565" y1="398.996" x2="170.354" y2="424.767" xlink:href="#B"><stop offset="0" stop-color="#989797"></stop><stop offset="1" stop-color="#b7b5b6"></stop></linearGradient></defs><path fill="url(#U)" d="M178 401c1-1 2-2 3-2v1c1 1 1 1 1 2l1 1v1c0 2-1 3-1 5h-1v-1c-1 2-2 4-1 7l-1 1h-1c0 1 0 3-1 4h0c-1 1-1 1-1 2h-1c-1 1-1 2-1 3-1 2-1 4-2 5v-12c0 1-1 2-1 4h0v-4c0-1 1-2 1-3-1-1-2-2-2-3 1-1 2-3 2-5v-1h1v2 2l1 1c0-1 1-1 1-2l2-4h0c0-2 1-3 1-4z"></path><path d="M175 422c2-6 2-13 6-18h1c0 1 0 2-1 3v1c-1 2-2 4-1 7l-1 1h-1c0 1 0 3-1 4h0c-1 1-1 1-1 2h-1z" class="K"></path><path d="M439 345c1 0 3-2 5-2 0 1 0 1 1 1-1-1-1-1-1-2v-1c1-1 2-2 3-2 3 1 5 3 7 5 1 1 5 3 6 4v1s0 1 1 1c1 1 3 0 5 0v1h-1c-3-1-7 0-10 0h-2c-4 0-6 0-10 1l1-1c-3-1-6 0-8-1 2 0 5 1 8 0-1-1-2-1-2-1-1-1-2-3-3-4z" class="O"></path><path d="M454 344c1 1 5 3 6 4v1s0 1 1 1c1 1 3 0 5 0v1h-1c-3-1-7 0-10 0h-2c-4 0-6 0-10 1l1-1c-3-1-6 0-8-1 2 0 5 1 8 0v-1h1v1l1-1v-1l1 1h6v-1c1-1 1-1 1-2v-1-1z" class="X"></path><path d="M454 344c1 1 5 3 6 4v1l-1 1-6-1v-1c1-1 1-1 1-2v-1-1z" class="F"></path><defs><linearGradient id="V" x1="107.326" y1="428.82" x2="123.3" y2="421.586" xlink:href="#B"><stop offset="0" stop-color="#828586"></stop><stop offset="1" stop-color="#b0abac"></stop></linearGradient></defs><path fill="url(#V)" d="M126 395l1 1c-2 2-3 5-4 8v5c-1 2-1 4-1 6-1 3-3 8-5 10l-1 1h0v4l-1 1v1 2 3c0 1 0 1-1 2v-1l-2 2h0l-1-2c1-3 1-6 1-9 1-3 1-6 2-8 0-1 0-4 1-4v-1c1-1 1-4 2-5 0-1 1-2 1-3s0-1 1-1c0 1 0 1 1 1 1-2 1-5 2-8h2c1-2 1-3 2-5z"></path><path d="M114 438v-1-7c1-5 3-10 5-15 1-4 2-8 4-11v5c-1 2-1 4-1 6-1 3-3 8-5 10l-1 1h0v4l-1 1v1 2 3c0 1 0 1-1 2v-1z" class="B"></path><path d="M337 331s0-1 1-1l13 5 3 1 9 4c0 1 1 1 2 1 0-1-1-1-1-1v-1c2 1 5 4 7 4l9 5c4 3 8 5 11 9l-8-5v1h0c-1 0-3-1-3-1-3-2-7-5-10-6v2h-2c1 0 1 1 1 2-1 0-2-1-3-1h0-1-3 0c-1-2-3-3-3-5 0 0 0-1-1-2h0 0c-1-1-1-2-2-3l-3-2-16-6z" class="Y"></path><path d="M365 343c2 1 4 3 5 3v2h-2c-2-2-2-2-3-5z" class="M"></path><path d="M363 342l2 1c1 3 1 3 3 5 1 0 1 1 1 2-1 0-2-1-3-1v-1c0-1 0-2-1-3s-1-1-2-3z" class="E"></path><path d="M360 341l3 1h0c1 2 1 2 2 3s1 2 1 3l-4-3c0-2-1-3-2-4z" class="G"></path><path d="M356 339l4 2c1 1 2 2 2 4l4 3v1h0-1-3 0c-1-2-3-3-3-5 0 0 0-1-1-2h0 0c-1-1-1-2-2-3z" class="T"></path><path d="M356 339l4 2c1 1 2 2 2 4l-4-3h0c-1-1-1-2-2-3z" class="D"></path><path d="M177 423h0c1-1 1-2 1-2h1s1 1 1 2c-1 2 0 3 0 5l-1 2c1 2 1 4 0 6v3h0l1-1v2c0 2 0 6 1 8 0 2 0 3 1 4l-2 2c1 1 1 2 2 3v5l-1 1c-2-3-2-5-3-8l-1-3-2-8c0-3 0-8-1-10s1-7 0-9c0-1 0-2 1-3h1c0-1 0-1 1-2h0v3z" class="O"></path><path d="M180 454c0-3 0-5-1-7v-1c1-1-1-4 1-6 0 2 0 6 1 8 0 2 0 3 1 4l-2 2z" class="J"></path><path d="M177 423h0c1-1 1-2 1-2h1s1 1 1 2c-1 2 0 3 0 5l-1 2v-1c-1 1-1 3-2 4v-1-2c1-1 1-2 1-3-1-2-1-2-1-4z" class="K"></path><path d="M176 272h1 0v1c1 0 1 0 2 1h-1v1h2v1c-1 0-3 1-4 2 2 0 3-1 4-1l1 1c1-1 2-1 2-1h0l2 2c0 1 0 1-1 2l-1 1v1l1-1v2h-1c0 1 1 1 1 1-1 1-2 1-3 2l1 1-1 1h-2l-1 2v1h1 0l-1 2h0 2v1h-1l-1 1c0 1 1 1 1 1l-3 3c-1 0-1 0-2 1 0-2 2-4 2-5l-3 3c0-2 1-3 2-5l-3 3c0-1 2-3 2-4v-3h0c-1 1-1 1-2 1 1-1 1-3 2-5h0 0l-1 1h-1c1-1 2-4 2-5 0 0-1 0-2 1 1-1 2-2 1-3l-1 1v-1c1-1 1-2 1-3h-1v-1c1-1 1-2 2-4h2z" class="O"></path><path d="M180 277l1 1v2c1 0 1 0 2 1-1 1-1 1-1 2h1 0c0 1 0 1-1 1h-2c-2 0-3 1-4 0 0 0 1-1 1-2v-1c1 0 1 0 2-1l-1-1c0-1 1-1 2-2z" class="Q"></path><path d="M183 277h0l2 2c0 1 0 1-1 2l-1 1v1l1-1v2h-1c0 1 1 1 1 1-1 1-2 1-3 2l1 1-1 1h-2l-1 2v-2c0-2 1-2 2-3-1 0-2 1-3 0 1 0 2-1 3-2h2c1 0 1 0 1-1h0-1c0-1 0-1 1-2-1-1-1-1-2-1v-2c1-1 2-1 2-1z" class="B"></path><path d="M105 351c2 0 4 1 5 2v3c1 1 1 1 1 3h-1 0c0 1-1 2-2 3h-5c-6-2-10-3-16-1 0-1 1-2 1-3v-1h1c0-2 0-2 1-3 1 0 1 0 1-1v-2h14z" class="G"></path><path d="M107 356c1 1 2 1 3 2-2 1-2 2-4 2l-1-2-1-2s1 0 2 1h0l1-1z" class="D"></path><path d="M95 356h3c0 1 0 2 1 3 0 1 0 1-1 1l-1-1h-2-3v-1c1 0 2-1 3-2z" class="Q"></path><path d="M98 356c2 0 4 1 6 0l1 2-1 1h0l1 1-1 1c-1 0-3-1-4-2h-1c-1-1-1-2-1-3z" class="H"></path><path d="M98 356c2 0 4 1 6 0l1 2c-2 0-3-1-4 0-1 0-1 1-1 1h-1c-1-1-1-2-1-3z" class="S"></path><path d="M91 353h2s1 1 2 1c4 0 8 0 12 2l-1 1h0c-1-1-2-1-2-1-2 1-4 0-6 0h-3c-2 0-3 0-4 1-1 0-2 1-3 1v-1h1c0-2 0-2 1-3 1 0 1 0 1-1z" class="P"></path><path d="M91 353h2s1 1 2 1l-6 3c0-2 0-2 1-3 1 0 1 0 1-1z" class="W"></path><defs><linearGradient id="W" x1="93.97" y1="356.543" x2="109.337" y2="353.139" xlink:href="#B"><stop offset="0" stop-color="#9b9a9b"></stop><stop offset="1" stop-color="#bbbaba"></stop></linearGradient></defs><path fill="url(#W)" d="M105 351c2 0 4 1 5 2v3c1 1 1 1 1 3h-1 0v-1c-1-1-2-1-3-2-4-2-8-2-12-2-1 0-2-1-2-1h-2v-2h14z"></path><defs><linearGradient id="X" x1="350.585" y1="181.584" x2="318.131" y2="172.48" xlink:href="#B"><stop offset="0" stop-color="#9a9c9a"></stop><stop offset="1" stop-color="#b4b0b4"></stop></linearGradient></defs><path fill="url(#X)" d="M321 186v-1c2-2 3-5 5-6-1 0-1 1 0 1 2-2 3-4 5-6 6-9 16-19 27-20 1 0 1 1 2 2l-1 1c-1-1-2-1-3-1l-6 3c-10 5-17 15-23 24-4 6-8 12-14 17 0 1-1 2-2 2s-2 1-3 1c0-1-1-1-1-1 0-3 1-4 3-6h-1v-2-1-1c1 0 1 0 2 1v1h1c1-1 1-1 2-1l2-1c1-2 3-3 4-4 0-1 1-1 1-1v-1z"></path><path d="M316 192c1-2 3-3 4-4-1 3-9 10-10 11 0 1 0 1 1 1h0 2c0 1-1 2-2 2s-2 1-3 1c0-1-1-1-1-1 0-3 1-4 3-6h-1v-2-1-1c1 0 1 0 2 1v1h1c1-1 1-1 2-1l2-1z" class="M"></path><path d="M309 192c1 0 1 0 2 1v1h1c1-1 1-1 2-1l2-1c-1 1-2 3-4 4l-1 1h0l1-2-1-1c0 1-1 1-2 2v-2-1-1z" class="F"></path><path d="M245 308c1 0 2 1 3 2h0c0 2 1 3 1 4h0v2c0 2-1 2-2 3l-2-1c-2 0-4-1-6-1 0 0 0-1-1-1l-3-1-4 1h-2c-2 0-3 0-5 1-4 0-9 1-14 2-6 1-12 2-18 4h-3c0 1-1 2-2 3v-1l1-1-2 1c-10 3-21 9-30 15-4 2-7 5-10 8h-1c11-11 25-18 39-24l6-2 2-1 29-6h0c1 0 1 0 2-1h3v-2h-3l-1-1c4 1 8 1 11 2h4 2v-1c1-1 1-1 2-1h1s1-1 2-1 1 0 1 1 1 1 2 2h0l1 1v-2c-1-1-1-1-1-2l-2-2z" class="i"></path><path d="M241 311h1l1 1h-1c1 1 1 1 2 1-1 0-3 0-4 1h-2-4c1 0 2-1 3-1h2v-1c1-1 1-1 2-1z" class="a"></path><path d="M242 311s1-1 2-1 1 0 1 1 1 1 2 2h0l1 1h-1l-1 1h-3-2c2 0 4 0 5-1l-1-1h-1c-1 0-1 0-2-1h1l-1-1z" class="O"></path><defs><linearGradient id="Y" x1="226.157" y1="313.023" x2="232.446" y2="313.451" xlink:href="#B"><stop offset="0" stop-color="#444344"></stop><stop offset="1" stop-color="#5e5c5d"></stop></linearGradient></defs><path fill="url(#Y)" d="M222 311c4 1 8 1 11 2h4c-1 0-2 1-3 1l-13 1h0c1 0 1 0 2-1h3v-2h-3l-1-1z"></path><path d="M245 308c1 0 2 1 3 2h0c0 2 1 3 1 4h0v2c0 2-1 2-2 3l-2-1c-2 0-4-1-6-1 0 0 0-1-1-1l-3-1h6 2 3l1-1h1v-2c-1-1-1-1-1-2l-2-2z" class="W"></path><path d="M243 315h3l1-1c0 2 0 1 1 3l-1 1h0c-1 0-2-1-2-1h-1-2c0-1 0-1 1-2z" class="F"></path><defs><linearGradient id="Z" x1="208.628" y1="171.496" x2="178.753" y2="179.441" xlink:href="#B"><stop offset="0" stop-color="#282728"></stop><stop offset="1" stop-color="#4d4d4e"></stop></linearGradient></defs><path fill="url(#Z)" d="M182 194c0-2 0-5-1-7 0-7-1-16 2-23 1-2 2-3 3-4 3-1 7 2 10 3 5 4 9 9 13 14l4 5c-2 0-4-1-6-1l-1 1h-1c0-1 1-1 1-1-1-1-1-1-2-1s-1 0-2-1h2c-1-1-1-1-2-1v-2l-3-3c-1-1-1-2-2-2-1-1-3-1-3-2s-1-1-1-1c-2-1-3 0-5 0 0-1 0-1 0 0h-1-1c0 1-1 2-2 2h0c-1 2-1 3-1 4 0 4 0 8 1 12v8-1h-1v2h0v-1h0-1z"></path><defs><linearGradient id="a" x1="181.785" y1="354.196" x2="187.549" y2="363.122" xlink:href="#B"><stop offset="0" stop-color="#a3a0a1"></stop><stop offset="1" stop-color="#bebebe"></stop></linearGradient></defs><path fill="url(#a)" d="M191 339l3-1v1c1 2 1 4 2 5h0c1 0 1 1 2 1l2 1-1 1h0l-1 1c-1 1-2 2-2 3-7 4-11 13-16 19l-1 1v-1s0-1 1-2v-1h0c-1 0-1 0-1-1h0c-1 0-1 1-2 1 0 1-1 2-1 3 0-3 3-8 5-10l-1-1-1 2-1-1 2-2v-1l2-3c1-1 2-2 2-4l2-3-1-1-4 4 4-6 1 1c1-2 2-3 4-5l1-1z"></path><path d="M194 348l1-1h1l1 1-3 3v-3z" class="S"></path><path d="M190 340l1-1c2 3 1 8 0 11h0 1l2-2v3c-2 1-4 3-6 5 1-1 2-5 2-6h0c1-2 1-5 1-6s-1-1-1-2v-2z" class="B"></path><path d="M186 347v-1l3-3c1 0 1 0 1 1 0 2-1 5-2 6v1c-1 0-2 2-3 2 0 1 0 1-1 1v1c-1 1-1 2-1 2-1-1 0-1 0-1 0-1-1-2-1-2 1-1 2-2 2-4l2-3z" class="a"></path><path d="M191 339l3-1v1c1 2 1 4 2 5h0c1 0 1 1 2 1l2 1-1 1h0l-2 1-1-1h-1l-1 1-2 2h-1 0c1-3 2-8 0-11z" class="E"></path><path d="M196 345s-1 0-1 1l-1-1v-2c-1-1-1-1-1-3l1-1c1 2 1 4 2 5h0c1 0 1 1 2 1l2 1-1 1h0l-2 1-1-1v-2z" class="D"></path><path d="M196 345c1 1 2 1 3 2l-2 1-1-1v-2z" class="C"></path><path d="M186 220c1 0 2-1 2-1 0 2 0 3 2 5l2 1c1 0 1-1 2-1h1 1c0 1 1 1 0 2l4 2c1 1 3 4 4 5h0c-1 1-1 1 0 2v1c0 1 0 1-1 1 0 1 3 2 3 3v1 4l1 2-2 1h-2 0l-3-3c-1 0-3-1-3-2l-1-1-1-1h-2c-1-1-1-1-2-1h-1v-1-1l-1-1c1-1 1-2 2-2v-1c-1-1-1-2-1-3h0l-1-2-3-9z" class="P"></path><path d="M194 226h0l2 1c0 1 0 1 1 2h1c1 1 1 1 1 2 2 1 3 2 5 2-1 1-1 1 0 2-1-1-2-1-3-2-2-1-5-3-7-5-1 0-2-1-2-2h2z" class="X"></path><path d="M194 224h1 1c0 1 1 1 0 2l4 2c1 1 3 4 4 5h0c-2 0-3-1-5-2 0-1 0-1-1-2h-1c-1-1-1-1-1-2l-2-1h0c-1-1-1 0-2-1 1 0 1-1 2-1z" class="P"></path><path d="M194 224h1c0 1 0 1 1 2h-2 0c-1-1-1 0-2-1 1 0 1-1 2-1z" class="T"></path><path d="M190 239c2 0 5-1 6 1v1c1 1 2 0 4 0 1 1 3 3 4 3 1 1 2 1 2 1l1 2-2 1h-2 0l-3-3c-1 0-3-1-3-2l-1-1-1-1h-2c-1-1-1-1-2-1h-1v-1z" class="C"></path><path d="M197 243h2s1 1 2 1c0 1 1 1 2 1 1 1 1 2 2 3h-2 0l-3-3c-1 0-3-1-3-2z" class="G"></path><path d="M186 220c1 0 2-1 2-1 0 2 0 3 2 5 1 3 2 4 5 6 2 1 4 3 5 4v1l-1 1c-1-1-2-3-4-3 0 1 1 1 0 3 1 0 1 0 2 1h0c1 0 2 0 2 1h1c1 1 2 2 3 2l-1 1h1l-1 1-3-3h-1c1 1 1 1 1 2-1 0-1 0-3-1-1-2-4-1-6-1v-1l-1-1c1-1 1-2 2-2v-1c-1-1-1-2-1-3h0l-1-2-3-9z" class="U"></path><path d="M190 231c1 0 2 1 2 2 1 1 0 2 1 3 0 1 0 1-1 1h-2v1l-1-1c1-1 1-2 2-2v-1c-1-1-1-2-1-3h0z" class="T"></path><path d="M322 310c9-1 19-1 28-1l9 1c3 1 5 1 8 1l1 1v-1h0c1-1 2-1 4-1h0v3l14 4h0-2c-4-2-8-2-12-3-2 0-3 0-4-1h-1l-1 6-4-1c-3-1-6-2-10-2-10-2-22-4-33-3h-2-2-3 2c1 0 2-1 3-1-3 0-8 1-11 1 1-1 1-1 2-1 4-2 10-2 14-2z" class="j"></path><path d="M365 312c1 0 2 1 3 1h0-1l-1 6-4-1h1v-3l2-2v-1z" class="S"></path><path d="M322 310c9-1 19-1 28-1l9 1c3 1 5 1 8 1l1 1v-1h0c1-1 2-1 4-1h0v3l14 4h0-2c-4-2-8-2-12-3-2 0-3 0-4-1h0c-1 0-2-1-3-1l-19-2c-10 0-19 1-29 2-3 0-8 1-11 1 1-1 1-1 2-1 4-2 10-2 14-2z" class="H"></path><defs><linearGradient id="b" x1="198.72" y1="324.549" x2="212.58" y2="303.41" xlink:href="#B"><stop offset="0" stop-color="#121213"></stop><stop offset="1" stop-color="#373636"></stop></linearGradient></defs><path fill="url(#b)" d="M207 309l10 1h6 2l11 1 3 1v1h-2-4c-3-1-7-1-11-2l1 1h3v2h-3c-1 1-1 1-2 1h0l-29 6-2 1c-1-1-1-1-1-2-1 0-1 0-1-1h-1c1-2 0-1 0-3v-3c-1-1-1 0-2 0v-3h7 7 3 0-2v-1h7z"></path><path d="M185 310h7c-1 1-4 0-5 1 0 1 1 2 1 3s1 2 1 3l1-1h1c0 1 0 3 1 5h0l-2 1c-1-1-1-1-1-2-1 0-1 0-1-1h-1c1-2 0-1 0-3v-3c-1-1-1 0-2 0v-3z" class="c"></path><path d="M207 309l10 1h6 2l11 1 3 1v1h-2-4c-3-1-7-1-11-2h-5l-15-1h0-2v-1h7z" class="i"></path><path d="M309 194v2h1c-2 2-3 3-3 6 0 0 1 0 1 1 1 0 2-1 3-1v1h-1c-1 1-2 3-3 4v1l-1 1h-1l-1 2c-1-1-1-2-1-3h-1l-1 1c0 1 0 2-1 2l-2 1h-2c0 1 0 1-1 1 0 0-1 0-2-1v4c-1 0-1 0-1-1v-3c0-1 0-1-1-1 0 0-1 0-2 1v3l-1 1-1-1h-1v2c-1 0 0 1-1 2v-2h-1v-1-2c-1 0-1 1-1 1h-1v-2l-1 1v-3l-1-1-1 1-1 1v-5h1v2h1c0-1 0-2 1-3h0v-5l1 2h1 0l1-1 2 2h0s1-1 2-1h1v-2h1s1-1 1 0h1c1-1 1-2 2-3h0c0 1 0 1 1 1s1-1 1-2l1 2c1-1 2-2 3-2 0-1 1-2 2-2 0 1 0 1 1 1l2 1c0-1 0-2 1-3 1 0 1 1 1 1h1l1-1z" class="Q"></path><path d="M296 207v-3l1-1v3c0 1 0 1-1 1h0 0z" class="N"></path><path d="M281 206v-2l1 1h1c0 1-1 2-1 3s0 2-1 3h0v-2h-1v1l-1-1h0 1c0-1 0-2 1-3h0z" class="J"></path><path d="M281 201l1 2h1 0l1-1 2 2h0c-1 1-2 0-3 1h-1l-1-1v2-5z" class="F"></path><path d="M296 207h1 0v4l1 1h-2c0 1 0 1-1 1 0 0-1 0-2-1l1-2c1-1 1-2 2-3h0 0z" class="R"></path><path d="M296 207h1 0v4l1 1h-2 0v-1-4h0z" class="B"></path><path d="M300 201h0c1 1 1 2 1 3v5c0 1 0 2-1 2l-2 1-1-1v-4c2-2 2-4 3-6z" class="E"></path><path d="M296 197l1 2c1-1 2-2 3-2 0-1 1-2 2-2 0 1 0 1 1 1l-2 1v1l-1 1h-1c-1 0-1 0-1 1-2 1-3 2-4 4v1c1 0 1 0 1 1h-1v-1-1c-1 1-2 1-2 2l-1 1-1 1v-1c-1 1-1 1-1 2-1 1-2 1-2 2l-1-1c0-1 1-2 1-3l-1-1h-1c-1 3 1 5 0 8l-2-6h-1c0-1 1-2 1-3 1-1 2 0 3-1 0 0 1-1 2-1h1v-2h1s1-1 1 0h1c1-1 1-2 2-3h0c0 1 0 1 1 1s1-1 1-2z" class="K"></path><path d="M309 194v2h1c-2 2-3 3-3 6 0 0 1 0 1 1 1 0 2-1 3-1v1h-1c-1 1-2 3-3 4v1l-1 1h-1l-1 2c-1-1-1-2-1-3h-1l-1 1v-5c0-1 0-2-1-3h0v-2l1-1v-1l2-1 2 1c0-1 0-2 1-3 1 0 1 1 1 1h1l1-1z" class="H"></path><path d="M301 198h1c0 2-1 4-1 6 0-1 0-2-1-3h0v-2l1-1z" class="S"></path><path d="M305 204l1 3h1v1l-1 1h-1l-1 2c-1-1-1-2-1-3h-1c0-1 0-1 1-2 0 1 0 1 1 1l1-3z" class="C"></path><path d="M303 208l1 1v-1c1-1 2 0 3 0l-1 1h-1l-1 2c-1-1-1-2-1-3z" class="D"></path><path d="M309 194v2h1c-2 2-3 3-3 6 0 0 1 0 1 1 1 0 2-1 3-1v1h-1c-1 1-2 3-3 4h-1l-1-3 1-2v-2h0l-2 1h0v-1c0-2 2-3 3-5h0 1l1-1z" class="B"></path><path d="M306 202h1v2l1-1h2c-1 1-2 3-3 4h-1l-1-3 1-2z" class="R"></path><defs><linearGradient id="c" x1="304.052" y1="225.743" x2="295.852" y2="216.124" xlink:href="#B"><stop offset="0" stop-color="#292829"></stop><stop offset="1" stop-color="#444443"></stop></linearGradient></defs><path fill="url(#c)" d="M302 208h1c0 1 0 2 1 3l1-2c1 3 0 5 0 7v3c-1 2-2 4-1 7v2c-2 1-6 1-8 0-2 0-4 0-6 1-2 0-5-1-8-1-1 0-2 1-3 1l-1-1c-1 1-2 1-3 1l3-2v-1c3-2 1-6 2-8l1-3h1 1s0-1 1-1v2 1h1v2c1-1 0-2 1-2v-2h1l1 1 1-1v-3c1-1 2-1 2-1 1 0 1 0 1 1v3c0 1 0 1 1 1v-4c1 1 2 1 2 1 1 0 1 0 1-1h2l2-1c1 0 1-1 1-2l1-1z"></path><path d="M297 220h1v3h-1v-3zm4-3h0c1 1 0 1 0 2v2h0-1v-1c0-1 0-2 1-3z" class="P"></path><path d="M298 212l2-1c0 1-1 2-1 3-1 0-2 0-2 2h-1-1v-2-1c1 0 1 0 1-1h2z" class="V"></path><path d="M302 208h1c0 1 0 2 1 3-1 1-1 2-1 2v-1c-1 1-1 2-2 3v-1h-1c0 1-1 2-1 4h-1v-2h1v-2h0c0-1 1-2 1-3 1 0 1-1 1-2l1-1z" class="P"></path><defs><linearGradient id="d" x1="296.055" y1="221.887" x2="289.852" y2="216.278" xlink:href="#B"><stop offset="0" stop-color="#444344"></stop><stop offset="1" stop-color="#5f5e5e"></stop></linearGradient></defs><path fill="url(#d)" d="M293 212c1 1 2 1 2 1v1 2h1c0 3-2 6-1 9h-2c-2 0-2 2-4 2h0l1-1h0v-2-1c0-4-1-7 2-11v3c0 1 0 1 1 1v-4z"></path><path d="M284 217h1v2c1-1 0-2 1-2v-2h1l1 1 1-1v-3c1-1 2-1 2-1 1 0 1 0 1 1-3 4-2 7-2 11v1 2h0l-1 1h0c-3 0-8 0-10 2l-1-1c-1 1-2 1-3 1l3-2v-1c3-2 1-6 2-8l1-3h1 1s0-1 1-1v2 1z" class="V"></path><path d="M287 224l2-1h0c0 1 1 1 1 1v2h0c-1-1-3 0-4 0v-2l1 1h0v-1z" class="D"></path><path d="M278 227h1s1-1 2-1h1c1-1 3 0 3 0h1c1 0 3-1 4 0h0 0l-1 1h0c-3 0-8 0-10 2l-1-1c-1 1-2 1-3 1l3-2z" class="L"></path><path d="M284 217h1v2c1-1 0-2 1-2v-2h1l1 1 1-1v-3c1-1 2-1 2-1 1 0 1 0 1 1-3 4-2 7-2 11v1s-1 0-1-1h0l-2 1v-1c1-1 0-2 0-3v-2h-1v3h-1c0-1-1-3-1-4z" class="R"></path><path d="M370 346c3 1 7 4 10 6 0 0 2 1 3 1h0c2 1 5 4 7 6v3h0c-2 0-4-2-6-3v1l4 3c1 1 2 1 3 3l4 3-2 1 1 2h-1v1h0v1c1 0 1 1 1 1 2 1 4 2 5 4-3-1-6-4-9-5l-1 1c-2-1-3-3-5-4l-10-8c-3-3-8-6-10-10h2l-2-2-2-2h3 1 0c1 0 2 1 3 1 0-1 0-2-1-2h2v-2z" class="Q"></path><path d="M378 360c0-2-5-4-5-5 1 0 3 0 5 1v1 1c1 0 1 1 2 2h-2z" class="J"></path><path d="M366 349c1 0 2 1 3 1 2 1 3 2 4 3v1l-2-1v1h-1l-1-1-1-1-1-1-1 1v2-1l-2-2-2-2h3 1 0z" class="C"></path><defs><linearGradient id="e" x1="380.119" y1="364.366" x2="393.329" y2="369.157" xlink:href="#B"><stop offset="0" stop-color="#8e8c8c"></stop><stop offset="1" stop-color="#b3b3b3"></stop></linearGradient></defs><path fill="url(#e)" d="M378 360h2c0 1 5 5 5 5l2-1c1 1 2 1 4 2l4 3-2 1 1 2h-1c-2-1-4-3-6-3-1-1-3-2-4-3s-2-3-4-4h0v-1l-1-1z"></path><path d="M385 365l2-1c1 1 2 1 4 2l4 3-2 1-8-5z" class="g"></path><defs><linearGradient id="f" x1="373.553" y1="364.398" x2="377.974" y2="360.611" xlink:href="#B"><stop offset="0" stop-color="#535152"></stop><stop offset="1" stop-color="#696969"></stop></linearGradient></defs><path fill="url(#f)" d="M366 353v1-2l1-1 1 1 1 1h-1c0 1 1 2 2 3 2 2 4 3 6 4l6 6c1 1 2 3 4 4h1v-1c2 0 4 2 6 3v1h0v1c1 0 1 1 1 1 2 1 4 2 5 4-3-1-6-4-9-5l-1 1c-2-1-3-3-5-4l-10-8c-3-3-8-6-10-10h2z"></path><path d="M387 369c2 0 4 2 6 3v1h0v1c1 0 1 1 1 1l-7-5v-1z" class="F"></path><path d="M370 346c3 1 7 4 10 6 0 0 2 1 3 1h0c2 1 5 4 7 6v3h0c-2 0-4-2-6-3v1l4 3c1 1 2 1 3 3-2-1-3-1-4-2l-2 1s-5-4-5-5c-1-1-1-2-2-2v-1-1l5 3c-2-2-5-4-8-6-2-1-3-3-5-5v-2z" class="a"></path><path d="M183 174c0-1 0-2 1-4h0c1 0 2-1 2-2h1 1c0-1 0-1 0 0 2 0 3-1 5 0 0 0 1 0 1 1s2 1 3 2c1 0 1 1 2 2l3 3v2c1 0 1 0 2 1h-2c1 1 1 1 2 1l-1 1v1h-3 0c0 1 1 1 1 2h0c0 1 0 1-1 1-1 1 0 1-1 1 1 0 2 0 3 1h-2c-2 1-5 3-8 3l-1 1 1 1c-1 0-1 0-2 1-1 0-1 1-1 3v2l1 1h0l-1 1 1 1h1-1l1 1v1h3l-2 2c0-2-1-1-2-3h0-2v2h-1l-2-7-1-3v-8c-1-4-1-8-1-12z" class="H"></path><path d="M188 202c0-2 0-4-1-6 0-2-1-3-1-5 1-1 0-2 0-3 0-2 1-4 2-6l-2 6c2-1 5-5 7-5v1c-1 1-2 1-2 2l-3 2c-1 1-1 3-1 4s1 2 1 3c0 2 0 5 2 7v-1l1 1v1h3l-2 2c0-2-1-1-2-3h0-2z" class="N"></path><path d="M195 187h3 2c-2 1-5 3-8 3l-1 1 1 1c-1 0-1 0-2 1-1 0-1 1-1 3v2l1 1h0l-1 1 1 1h1-1v1c-2-2-2-5-2-7 0-1-1-2-1-3s0-3 1-4c0 1 1 1 1 2 1-1 2-2 3-2h0c1 0 2-1 3-1z" class="K"></path><path d="M200 182c0 1 1 1 1 2h0c0 1 0 1-1 1-1 1 0 1-1 1 1 0 2 0 3 1h-2-2-3c-1 0-2 1-3 1h0c-1 0-2 1-3 2 0-1-1-1-1-2l3-2c0-1 1-1 2-2l5-1 2-1z" class="O"></path><path d="M193 184l5-1h1 0l-1 1h-1c0 1 0 1 1 1-1 0-2 0-3 1h-2-2c0-1 1-1 2-2z" class="J"></path><path d="M200 182c0 1 1 1 1 2h0c0 1 0 1-1 1-1 1 0 1-1 1 1 0 2 0 3 1h-2-2-3c1 0 3-1 4-2h-1c-1 0-1 0-1-1h1l1-1h0-1l2-1z" class="B"></path><path d="M183 174c0-1 0-2 1-4h0c1 0 2-1 2-2h1 1c0-1 0-1 0 0 2 0 3-1 5 0 0 0 1 0 1 1s2 1 3 2c1 0 1 1 2 2l3 3v2c1 0 1 0 2 1h-2c1 1 1 1 2 1l-1 1v1h-3 0l-2 1-5 1v-1c-2 0-5 4-7 5l2-6 2-1h0c1-1 2-2 3-2 2-1 3-1 5-1h0l-1-1h-4c0 1-1 1-2 1 0-1 4-2 5-2h2v-1c-1 0-1-1-2-2h0-2 0c-1 1-2 1-2 1-2 0-4 1-5 3v1c-1 2-2 2-1 4 0 1-1 1-1 2h0c0 1-1 2-1 2-1-4-1-8-1-12z" class="D"></path><path d="M190 171c1 0 3-1 4 0h1c1 2 2 2 3 2 1 1 1 2 2 3-1 1-3 0-4 0h2v-1c-1 0-1-1-2-2h0-2 0c-1-1-1-1-1-2h-3z" class="G"></path><path d="M195 179c1 0 3 0 4 1h2v1c-1 0-3 0-4 1h0l-4 1c-2 0-5 4-7 5l2-6 2-1 5-2z" class="K"></path><path d="M195 179c1 0 3 0 4 1h2v1c-1 0-3 0-4 1h0l-1-1c-1 0-2 0-3 1h-1v-1c1-1 3-1 4-1h1v-1h-2z" class="B"></path><defs><linearGradient id="g" x1="183.986" y1="172.835" x2="188.903" y2="176.478" xlink:href="#B"><stop offset="0" stop-color="#626162"></stop><stop offset="1" stop-color="#797878"></stop></linearGradient></defs><path fill="url(#g)" d="M183 174l2-3v-1c2-2 4-2 6-2 0 1-1 1-1 1h0l-2 3c1 0 1 0 2-1h3c0 1 0 1 1 2h0 0c-1 1-2 1-2 1-2 0-4 1-5 3v1c-1 2-2 2-1 4 0 1-1 1-1 2h0c0 1-1 2-1 2-1-4-1-8-1-12z"></path><path d="M455 351c3 0 7-1 10 0h1 2 1 4c1 3 3 6 5 8 1 1 3 3 5 4v1c-1 0-2 0-2 1-2-1-3-2-5-4v1c0 1 1 2 2 2l-1 1-2-1v1c0 1 2 2 3 3l-2 1-3-3h-2c-4-2-7-2-11-2v-1h-1c-4 0-8 0-12 2-3 0-5 0-7-2h-1c-2-2-2-3-2-6h0c2-3 3-4 6-5 4-1 6-1 10-1h2z" class="I"></path><path d="M466 351h2c0 1 1 2 2 3v1h-1l-1-1c-1-1-3-2-3-3h1z" class="Z"></path><path d="M468 351h1c0 1 1 2 2 3 2 2 3 4 4 6h0-1c-2-1-4-3-5-5h1v-1c-1-1-2-2-2-3z" class="K"></path><path d="M469 351h4c1 3 3 6 5 8 1 1 3 3 5 4v1c-1 0-2 0-2 1-2-1-3-2-5-4l-1-1c-1-2-2-4-4-6-1-1-2-2-2-3z" class="a"></path><path d="M461 351l2 1c1 0 3 2 3 3 1 0 1 1 1 1 1 0 1 1 2 1l3 4v1l-14-7c0-1 2-3 3-4z" class="O"></path><path d="M446 355c3-1 6-1 9-1l3 1 14 7c1 1 2 1 3 2v1c0 1 2 2 3 3l-2 1-3-3c-1-1-1-1-1-2h1 0l-1-1c-3-2-7-2-10-3-1-1-1-1-3-2l-1-1c-2-1-5-2-8-1h0l-1-1c-1 0-1 1-1 1-1 0-1 0-2-1z" class="P"></path><defs><linearGradient id="h" x1="452.547" y1="359.42" x2="440.672" y2="353.306" xlink:href="#B"><stop offset="0" stop-color="#a09ea0"></stop><stop offset="1" stop-color="#bfbebe"></stop></linearGradient></defs><path fill="url(#h)" d="M443 352c4-1 6-1 10-1h2c2 0 4 1 6 0-1 1-3 3-3 4l-3-1c-3 0-6 0-9 1-2 1-4 2-6 4v3 1h-1c-2-2-2-3-2-6h0c2-3 3-4 6-5z"></path><path d="M453 351h2c2 0 4 1 6 0-1 1-3 3-3 4l-3-1 1-1h1v-1h-1c-1 0-2 0-3-1h0z" class="J"></path><path d="M440 362v-3c2-2 4-3 6-4 1 1 1 1 2 1 0 0 0-1 1-1l1 1h0c3-1 6 0 8 1l1 1c2 1 2 1 3 2 3 1 7 1 10 3l1 1h0-1c0 1 0 1 1 2h-2c-4-2-7-2-11-2v-1h-1c-4 0-8 0-12 2-3 0-5 0-7-2v-1z" class="G"></path><defs><linearGradient id="i" x1="465.665" y1="365.71" x2="440.546" y2="358.881" xlink:href="#B"><stop offset="0" stop-color="#939092"></stop><stop offset="1" stop-color="#bdbebe"></stop></linearGradient></defs><path fill="url(#i)" d="M462 360c3 1 7 1 10 3l1 1h0-1c0 1 0 1 1 2h-2c-4-2-7-2-11-2v-1h-1c-4 0-8 0-12 2-3 0-5 0-7-2v-1c1 0 2 1 3 1 2 0 5-2 7-2 4-1 8-1 12-1z"></path><path d="M459 363c4-1 9-1 12 0h1l1 1h0-1c0 1 0 1 1 2h-2c-4-2-7-2-11-2v-1h-1z" class="T"></path><defs><linearGradient id="j" x1="272.882" y1="223.914" x2="268.188" y2="212.103" xlink:href="#B"><stop offset="0" stop-color="#6a696a"></stop><stop offset="1" stop-color="#898888"></stop></linearGradient></defs><path fill="url(#j)" d="M255 203c1 0 1-1 2-2 0 1 0 1 1 1h0c0 1 0 1-1 2l2 1v-1h1v1h2c1 1 1 1 1 2v3h1 0v-2-1c1-2 1-2 3-3l-1-2c1 0 2 0 2 1l2-1c0 2-1 2 1 3v3h1v-1c1 1 1 1 1 2h1v-2h1v1c1-1 1-2 2-3v2h1v5l1-1 1-1 1 1v3l1-1v2h-1l-1 3c-1 2 1 6-2 8v1l-3 2c-1 1-1 1-2 1v-1l-1-2-1 2h-3s-1-1-2-1-2 1-3 1-1-1-2-1-2 0-3 1v1c-1-1-1-1-1-2l-1-4c-1-2-1-4-1-6l-2-6c1-1 1-3 2-4h0v-5z"></path><path d="M274 219v-3h1v3c0 1 0 1-1 1v-1zm-5-7v3 1 2h-1v-1c0-2-1-3 1-5z" class="I"></path><g class="N"><path d="M273 224c-1-2-1-4 0-6l1 1v1c1 0 1 0 1-1h1 1v4l-3 3h-1v-1-1z"></path><path d="M274 209v-2h1v1c1-1 1-2 2-3v2h1v5l-1 1v4 2h-1v-7 1l-1-1v-1-1l-1 2v-3z"></path></g><path d="M276 212v-2h1v3 4 2h-1v-7z" class="R"></path><path d="M278 212l1-1 1-1 1 1v3l1-1v2h-1l-1 3c-1 2 1 6-2 8v1l-3 2c-1 1-1 1-2 1v-1l-1-2c1-1 1-2 1-2v1h1l3-3v-4-2-4l1-1z" class="I"></path><path d="M273 229h1 0c1-1 2-3 3-4v-1-1h1v3 1l-3 2c-1 1-1 1-2 1v-1z" class="C"></path><path d="M268 203l2-1c0 2-1 2 1 3v3h1v-1c1 1 1 1 1 2h1v3c0 1 0 2-1 3h-1 0l-1 1v-1c-1-1-1-2-1-3h-1l-1-1 1 1c-2 2-1 3-1 5v1h-1v1 1l-1-1-1-1v1h-2c-1 0-1 1-2 1 0 1 0 0-1 1v-2h1c1 0 1-1 1-1v-1c1-1 2 0 2-2-1 0-1-1-1-2 1-1 1-2 1-3h0v-2-1c1-2 1-2 3-3l-1-2c1 0 2 0 2 1z" class="K"></path><path d="M270 212l2-2h0c1 2 0 3 0 5h0l-1 1v-1c-1-1-1-2-1-3z" class="I"></path><path d="M268 203l2-1c0 2-1 2 1 3v3c-1-1-2-1-2-1-1 0-1 1-2 1s0-1 0-2v-2l-1-2c1 0 2 0 2 1z" class="F"></path><path d="M266 202c1 0 2 0 2 1h0v2l-1 1h0v-2l-1-2z" class="d"></path><path d="M264 210v-1c1 1 1 2 2 3v2s0 1 1 1c0-1 1-1 0-2 0-1-1-1-1-2s0-1 1-2h1v2l1 1c-2 2-1 3-1 5v1h-1v1 1l-1-1-1-1v1h-2c-1 0-1 1-2 1 0 1 0 0-1 1v-2h1c1 0 1-1 1-1v-1c1-1 2 0 2-2-1 0-1-1-1-2 1-1 1-2 1-3z" class="Q"></path><path d="M265 218c0-1 0-1 1-1l1 1v1 1l-1-1-1-1z" class="S"></path><defs><linearGradient id="k" x1="261.058" y1="226.144" x2="261.511" y2="210.506" xlink:href="#B"><stop offset="0" stop-color="#636263"></stop><stop offset="1" stop-color="#838282"></stop></linearGradient></defs><path fill="url(#k)" d="M255 203c1 0 1-1 2-2 0 1 0 1 1 1h0c0 1 0 1-1 2l2 1v-1h1v1h2c1 1 1 1 1 2v3h1c0 1 0 2-1 3 0 1 0 2 1 2 0 2-1 1-2 2v1s0 1-1 1h-1v2c1-1 1 0 1-1 1 0 1-1 2-1h2v-1l1 1-1 1-1 1c1 1 1 2 2 2 1 1 2 1 2 3 1-1 1-1 1-2l1 1 2-2s0 1 1 1v1s0 1-1 2l-1 2h-3s-1-1-2-1-2 1-3 1-1-1-2-1-2 0-3 1v1c-1-1-1-1-1-2l-1-4c-1-2-1-4-1-6l-2-6c1-1 1-3 2-4h0v-5z"></path><path d="M256 224l1-1-1-5c-1-1-1-2-1-3l-1-1c0-1 1-2 1-2 1-1 1 0 2 1 0 1 0 2 1 2 1 4 0 6 0 9v4h-1l-1-4z" class="H"></path><path d="M255 203c1 0 1-1 2-2 0 1 0 1 1 1h0c0 1 0 1-1 2l2 1v-1h1v1h2c1 1 1 1 1 2v3h1c0 1 0 2-1 3-1 0-1-1-2-2h-1l-1 2v-1c0-1-1-2-1-3l-1-1h-1c0 1 1 2 1 2v3c-1-1-1-2-2-1 0 0-1 1-1 2l1 1c0 1 0 2 1 3l1 5-1 1c-1-2-1-4-1-6l-2-6c1-1 1-3 2-4h0v-5z" class="W"></path><path d="M259 204h1v1h2v2l-2 1c-1-1-1-2 0-3l-1-1z" class="O"></path><path d="M262 205c1 1 1 1 1 2v3h1c0 1 0 2-1 3-1 0-1-1-2-2h1v-4-2z" class="N"></path><path d="M255 203c1 0 1-1 2-2 0 1 0 1 1 1h0c0 1 0 1-1 2l2 1-2 2h-1l-1 1h0v-5z" class="a"></path><path d="M256 207c0-1 0-2 1-3h0l2 1-2 2h-1z" class="O"></path><path d="M204 180c1 0 1 0 2 1 0 0-1 0-1 1h1l1-1c2 0 4 1 6 1 2 3 5 7 7 9 2 3 5 5 7 8h1v1l2 1v3c1 2 2 3 1 5 0 1 1 1 1 2-1 1-1 2-2 2l-1 1h-2l1-1s-1 0-1-1h-2c0-1-1-1-1-1-1-1-3-2-3-3-1-1 0-2 0-3h0c-1-2-3-2-4-2l-1-1h1 2v-1c-1-1-1-1-2-1h-4v-1h4c-1-1-1-1-3-2h-3-2c-1 0-2 0-4 1v-1c-4 1-8 3-11 6h-3v-1l-1-1h1-1l-1-1 1-1h0l-1-1v-2c0-2 0-3 1-3 1-1 1-1 2-1l-1-1 1-1c3 0 6-2 8-3h2c-1-1-2-1-3-1 1 0 0 0 1-1 1 0 1 0 1-1h0c0-1-1-1-1-2h0 3v-1l1-1z" class="h"></path><path d="M227 199h1v1h0c1 1 1 2 1 3h0c0 2 1 4 1 5l1 1c0 1-1 1-1 1l-1 1v2h-1v-3s1 0 1-1h1v-1c-1 1-1 1-2 1-1-1 1-2 1-3 0-2-1-3-1-4s0-2-1-3z" class="b"></path><path d="M218 195l1 1v3h1l-1 1c1 1 1 1 2 1h2v1c-1 0-1 1-2 1-1-1-1-1-2 0l-2-1h2v-1c-1-1-1-1-2-1h-4v-1h4 0 1 0c0-1 0-1-1-1h0c0-1 0-1 1-1h0v-2z" class="c"></path><path d="M228 200l2 1v3c1 2 2 3 1 5 0 1 1 1 1 2-1 1-1 2-2 2l-1 1h-2l1-1h0 1v-2l1-1s1 0 1-1l-1-1c0-1-1-3-1-5h0c0-1 0-2-1-3h0z" class="X"></path><path d="M209 191l-1-1v-1l-2-2h0 2l5 2v-1h2c0 1 1 1 1 1v1c-1 1-1 0-2 2 1 1 2 2 3 2l1 1v2h0c-1 0-1 0-1 1h0c1 0 1 0 1 1h0-1 0c-1-1-1-1-3-2l1-1v-1c0-1 0-1-1-2l-1 1c-1 0-1 0-2 1h-1c-1-1 1-1 1-2-1 0-2 0-3-1h0l1-1z" class="T"></path><path d="M208 187l5 2v-1h2c0 1 1 1 1 1v1c-1 1-1 0-2 2-1-1-2-1-3-1h0v-1h1v-1c-1 0-3-1-4-1v-1z" class="c"></path><path d="M211 191c1 0 2 0 3 1s2 2 3 2l1 1v2h0c-1 0-1 0-1 1h0c1 0 1 0 1 1h0-1 0c-1-1-1-1-3-2l1-1v-1c0-1 0-1-1-2-2 0-2-1-3-2z" class="e"></path><path d="M204 180c1 0 1 0 2 1 0 0-1 0-1 1h1c2 0 4 1 5 3 1 0 1 1 2 1v2h0v1l-5-2h-2 0l2 2v1l1 1h-5c0-1 1-1 2-1v-1c-2 0-3-1-4-1h-3c-3 1-5 2-7 4l-1-1 1-1c3 0 6-2 8-3h2c-1-1-2-1-3-1 1 0 0 0 1-1 1 0 1 0 1-1h0c0-1-1-1-1-2h0 3v-1l1-1z" class="D"></path><path d="M211 185c1 0 1 1 2 1v2l-5-2c1-1 2-1 3-1z" class="e"></path><path d="M204 180c1 0 1 0 2 1 0 0-1 0-1 1h1c2 0 4 1 5 3-1 0-2 0-3 1-2 0-3-1-4-1h-2c1-1 2-1 3-2v-1h-2v-1l1-1z" class="U"></path><path d="M200 182h0 3 2v1c-1 1-2 1-3 2h2l1 1c-1 1-2 1-3 2h-3c-3 1-5 2-7 4l-1-1 1-1c3 0 6-2 8-3h2c-1-1-2-1-3-1 1 0 0 0 1-1 1 0 1 0 1-1h0c0-1-1-1-1-2z" class="E"></path><path d="M202 188c1 0 2 1 4 1v1c-1 0-2 0-2 1h5l-1 1h0c1 1 2 1 3 1 0 1-2 1-1 2h1c1-1 1-1 2-1l1-1c1 1 1 1 1 2v1l-1 1h-3-2c-1 0-2 0-4 1v-1c-4 1-8 3-11 6h-3v-1l-1-1h1-1l-1-1 1-1h0l-1-1v-2c0-2 0-3 1-3 1-1 1-1 2-1 2-2 4-3 7-4h3z" class="F"></path><path d="M202 188c1 0 2 1 4 1v1c-1 0-2 0-2 1h5l-1 1h-3-6c-2 1-5 3-7 4v-1c3-2 6-3 9-4v-1c-2-1-4 2-6 1 1-1 4-1 5-2l-1-1h3z" class="B"></path><path d="M192 196c2-1 5-3 7-4h6c-2 1-3 1-4 1 0 1 0 1-1 1h0 1c2-1 4 0 6-1l-7 3h-2c-3 1-5 3-9 4 1-1 3-2 3-4h0z" class="a"></path><path d="M205 192h3 0c1 1 2 1 3 1 0 1-2 1-1 2h1c1-1 1-1 2-1l1-1c1 1 1 1 1 2v1l-1 1h-3-2c-1 0-2 0-4 1v-1l1-1c-1-1-4 0-6 0h0l7-3c-2 1-4 0-6 1h-1 0c1 0 1 0 1-1 1 0 2 0 4-1z" class="V"></path><path d="M214 193c1 1 1 1 1 2v1l-1 1h-3-2s-1 0-1-1h4l-1-1c1-1 1-1 2-1l1-1z" class="X"></path><path d="M319 315c11-1 21 0 31 1 4 1 8 3 12 3l1 1c0 1-1 2-1 3-1 1-1 1-1 2-1 1-1 1-1 2 0 0 0 1-1 1h-1l-1 3v4h-1 0c-1 0-1 1-2 1l-3-1-13-5c-7-3-14-5-21-7 0-1-1-2-1-3 1-1 2-4 3-5z" class="j"></path><path d="M362 319l1 1c0 1-1 2-1 3-1 1-1 1-1 2-1 1-1 1-1 2 0 0 0 1-1 1h-1l-1 3v4h-1 0c-1 0-1 1-2 1l-3-1c0-1 0-1 1-1s2 0 2-1c1-1 1-2 2-4 0-1 1-3 2-5 1 0 1-1 2-1h0 1c0-2 1-2 1-4z" class="X"></path><defs><linearGradient id="l" x1="202.218" y1="306.632" x2="212.622" y2="297.58" xlink:href="#B"><stop offset="0" stop-color="#403f40"></stop><stop offset="1" stop-color="#575757"></stop></linearGradient></defs><path fill="url(#l)" d="M184 284l1-1v-1l1-1h1c1 1 3 1 4 1h2c1 0 2-1 3-1h0l1 2h2c1 1 1 2 1 3v1l1 1c0 1 0 1-1 1 0 2 2 2 3 3l-1 1v1c1 1 5 4 7 4l2 1h0 1c1 0 2 1 3 2h2s0-1 1-1c0 0 1 1 2 1v1l2 1c-1 1-1 2-2 2l1 1h-1-1c-2-1-3 0-4 0h0c1 1 1 2 2 3v1l-10-1h0-18-4c0-1 0-2-1-2 0-1 2-1 2-2l-1-1h0 0v-1h-2l1-1c0-2 0-2 1-3l1-1c-1 0-1 0-1-1v-1c0-1-1-1-1-2 0 1 0 1 1 2h-2l-1-2h0l1-1h-3l-1-1h0-1v-1l1-2h2l1-1-1-1c1-1 2-1 3-2 0 0-1 0-1-1h1z"></path><path d="M200 301h2v1 1h-1l-2-1v-1h1z" class="L"></path><path d="M207 302c1-1 2 0 3 1h0c0 2 3 3 4 5v1h-2c0-1-1-2-1-2l-3-3s1 0 1-1l-2-1h0z" class="P"></path><path d="M207 309c0-2-2-2-3-3h2l2-2 3 3s1 1 1 2h-5z" class="c"></path><path d="M215 301h2s0-1 1-1c0 0 1 1 2 1v1l2 1c-1 1-1 2-2 2-1-2-4-2-6-4h1z" class="T"></path><path d="M203 307l1-1c-1 0-1 0-1-1h1 1l-1-2 1-1-1-1h0l3 1h0l2 1c0 1-1 1-1 1l-2 2h-2c1 1 3 1 3 3h-18c4-2 10 1 14-1v-1z" class="U"></path><path d="M186 300l1-1s1-1 1-2h0v-1c1 0 4-3 5-3 0 1 1 1 2 2v2c0 1 1 1 1 2l1 1c1 1 1 2 1 3 1 1 4 3 5 4h0v1c-4 2-10-1-14 1h-4c0-1 0-2-1-2 0-1 2-1 2-2l-1-1h0 0v-1h-2l1-1c0-2 0-2 1-3l1 1z" class="b"></path><path d="M184 302c0-2 0-2 1-3l1 1h2c1 0 2 1 4 1h0c-1 1-2 1-3 1l-1 1h0c-1-1-2-1-3-1l1 1-1 1h0v-1h-2l1-1z" class="X"></path><path d="M184 302c0-2 0-2 1-3 1 1 2 1 3 2-1 1-3 1-4 1z" class="L"></path><path d="M184 284l1-1v-1l1-1h1c1 1 3 1 4 1h2c1 0 2-1 3-1h0l1 2h2c1 1 1 2 1 3v1l1 1c0 1 0 1-1 1 0 2 2 2 3 3l-1 1v1l-1 1 1 1s0 1 1 2l-1 1c-1 0-2-1-3-1h1l-1-1c-1 0-1 1-2 2v1l-1-1c0-1-1-1-1-2v-2c-1-1-2-1-2-2-1 0-4 3-5 3v1h0c0 1-1 2-1 2l-1 1-1-1 1-1c-1 0-1 0-1-1v-1c0-1-1-1-1-2 0 1 0 1 1 2h-2l-1-2h0l1-1h-3l-1-1h0-1v-1l1-2h2l1-1-1-1c1-1 2-1 3-2 0 0-1 0-1-1h1z" class="D"></path><path d="M195 292c1-1 2-1 3-1v1c0 1-1 1-2 1 0 0 0-1-1-1z" class="C"></path><path d="M200 287l-1 1c-1 0-2-1-3-1l-3-3h1 0c2 0 4 1 6 2v1z" class="P"></path><path d="M184 284l1-1v-1l1-1h1c1 1 3 1 4 1l1 2 1 1h-1l1 1c0 1 0 1-1 1 0 1-1 1-1 2-1 0-2 0-3-1-1 0-1-1-2-1h0l-1-1-1-1s-1 0-1-1h1z" class="C"></path><path d="M187 281c1 1 3 1 4 1l1 2v1c-1 0-3 1-4 0v-1c-1-1-1-2-1-3z" class="G"></path><path d="M185 296l1-1s2-1 2-2l-1-1c1 0 1 0 2 1 2-1 3-1 4-2v-1h1c0 1 1 1 1 2 1 0 1 1 1 1 1 1 1 1 1 2 1 0 1 1 2 1v1h0c-1 0-1 1-2 2v1l-1-1c0-1-1-1-1-2v-2c-1-1-2-1-2-2-1 0-4 3-5 3v1h0c0 1-1 2-1 2l-1 1-1-1 1-1c-1 0-1 0-1-1v-1z" class="P"></path><defs><linearGradient id="m" x1="190.235" y1="288.845" x2="181.422" y2="288.756" xlink:href="#B"><stop offset="0" stop-color="#4e4e4f"></stop><stop offset="1" stop-color="#696868"></stop></linearGradient></defs><path fill="url(#m)" d="M184 285l1 1 1 1h0c1 0 1 1 2 1 1 1 2 1 3 1 0 0 0 1-1 1v1c-1 1-2 1-3 1s-1-1-2-1c0 1 0 1 1 2-2 0-2 0-3 1h1c0 1 0 1 1 2h-2l-1-2h0l1-1h-3l-1-1h0-1v-1l1-2h2l1-1-1-1c1-1 2-1 3-2z"></path><path d="M180 293c1-1 2-1 3-1-1-1-1-1-2-1v-1h1c1 0 2 0 3 1 0 1 0 1 1 2-2 0-2 0-3 1h1c0 1 0 1 1 2h-2l-1-2h0l1-1h-3z" class="V"></path><path d="M194 203c3-3 7-5 11-6v1c2-1 3-1 4-1h2 3c2 1 2 1 3 2h-4v1h4c1 0 1 0 2 1v1h-2-1l1 1c1 0 3 0 4 2h0c0 1-1 2 0 3 0 1 2 2 3 3 0 0 1 0 1 1h2c0 1 1 1 1 1l-1 1h2l1-1 1 1v1c0 1 1 3 2 4 0 1 2 4 2 5s0 2-1 3c1 1 2 1 2 2v1c-1 0-2-1-2-1-1-2-3-4-4-5 0 0 0-1-1-1 0-1-1-2-2-3 0-1-1-1-1-2-1-1-2-1-2-2h-4c0-1 0-2-1-2l-1 1h-1v1h-3c0 1-1 1-2 1h0l-1 2h-3-4-1s-1 0-1 1c1 1 1 1 2 1l1-1h1l-1 1v1l6 3v1l-5-2-4-2c0 1-1 1-2 1h0l-1 1-2-1-1 1h-1-1c-1 0-1 1-2 1l-2-1c-2-2-2-3-2-5 0 0-1 1-2 1v-1-6l1 5c1 1 2 0 3 0-1-2-1-4-1-6-1-3-1-5-2-8h1v-2h2 0c1 2 2 1 2 3l2-2z" class="D"></path><path d="M197 223h1c1-1 1-1 2 0l-1 1-2-1z" class="L"></path><path d="M209 212l2 1-1 1c-1 0-3 1-4 1l-1-1c2 0 3-1 4-2z" class="P"></path><path d="M203 219h1l-1-1-2 1h0l2-1c-1-1-1-1-2-1 1 0 3 0 4-1h3c1 0 2 0 2 1h2l-1 2h-3-4-1z" class="L"></path><path d="M210 210c1 0 2-1 3 0 1 0 2 0 2 1-1 0-2 0-3 1h-3c-1 1-2 2-4 2h0c-1 0-2 0-3 1-1 0-2 1-3 2h-1-1c1-1 4-3 5-4 2-1 3-1 5-2 1 0 2 0 2-1h1z" class="G"></path><path d="M186 219v-6l1 5c1 1 2 0 3 0h2v-2l1 1v1h1c2 1 4 2 6 2v1c-2 0-4-1-6-1 1 1 2 1 3 1l-1 1h-1c0 1 0 1-1 2-1 0-1 1-2 1l-2-1c-2-2-2-3-2-5 0 0-1 1-2 1v-1z" class="Q"></path><path d="M188 219h3v1h-1l1 1h0c1 0 1 1 2 1h2c0 1 0 1-1 2-1 0-1 1-2 1l-2-1c-2-2-2-3-2-5z" class="V"></path><defs><linearGradient id="n" x1="221.89" y1="214.607" x2="210.516" y2="213.056" xlink:href="#B"><stop offset="0" stop-color="#2b2b2b"></stop><stop offset="1" stop-color="#454445"></stop></linearGradient></defs><path fill="url(#n)" d="M210 209c4-1 5 0 8 2 2 2 4 3 6 5h-4c0-1 0-2-1-2l-1 1h-1v1h-3c0 1-1 1-2 1h0-2c0-1-1-1-2-1l2-2 1-1-2-1h3c1-1 2-1 3-1 0-1-1-1-2-1-1-1-2 0-3 0l-1-1h1z"></path><path d="M217 203c1 0 3 0 4 2h0c0 1-1 2 0 3 0 1 2 2 3 3 0 0 1 0 1 1h2c0 1 1 1 1 1l-1 1h2l1-1 1 1v1c0 1 1 3 2 4 0 1 2 4 2 5s0 2-1 3c1 1 2 1 2 2v1c-1 0-2-1-2-1-1-2-3-4-4-5 0 0 0-1-1-1 0-1-1-2-2-3 0-1-1-1-1-2-1-1-2-1-2-2-2-2-4-3-6-5-3-2-4-3-8-2h-1c-4-1-9 3-11 4-1 2-4 3-4 5h-1v-1c1-1 1-3 2-4l3-3v-1c1 0 1-1 2-2 2-1 3-1 5-2 2 0 6-1 8-1l1 1h-1c2 1 2 1 4 1l1-1v1l1-1-2-2z" class="V"></path><path d="M234 227c-1-1 0-2 0-3-1-1-2-3-3-4l2-1c0 1 2 4 2 5s0 2-1 3z" class="L"></path><path d="M225 216l1-1v-2h0l1 1h2 1v1h-3 0l2 2v1l-1 1c-1-1-3-2-3-3z" class="P"></path><path d="M205 205c2 0 6-1 8-1l1 1h-1c-1 1-2 1-4 1-4 0-8 2-11 4v-1c1 0 1-1 2-2 2-1 3-1 5-2z" class="S"></path><path d="M198 213c0-1 1-2 2-3 3-2 7-3 10-3l1 1-1 1h-1c-4-1-9 3-11 4z" class="Q"></path><path d="M217 203c1 0 3 0 4 2h0c0 1-1 2 0 3 0 1 2 2 3 3 0 0 1 0 1 1h2c0 1 1 1 1 1l-1 1-1-1h0v2l-1 1c-2-3-6-6-9-8h-1c-2-1-4-1-6-2 2 0 3 0 4-1 2 1 2 1 4 1l1-1v1l1-1-2-2z" class="b"></path><path d="M218 205v1h1l-1 1c-1-1-2-1-3 0l1 1h-1c-2-1-4-1-6-2 2 0 3 0 4-1 2 1 2 1 4 1l1-1z" class="T"></path><defs><linearGradient id="o" x1="198.376" y1="213.89" x2="192.884" y2="200.837" xlink:href="#B"><stop offset="0" stop-color="#5d5c5d"></stop><stop offset="1" stop-color="#807f7f"></stop></linearGradient></defs><path fill="url(#o)" d="M194 203c3-3 7-5 11-6v1c2-1 3-1 4-1h2 3c2 1 2 1 3 2h-4v1h4c1 0 1 0 2 1v1h-2-1l1 1 2 2-1 1v-1l-1 1c-2 0-2 0-4-1h1l-1-1c-2 0-6 1-8 1-2 1-3 1-5 2-1 1-1 2-2 2v1l-3 3c-1 1-1 3-2 4l-1-1v2h-2c-1-2-1-4-1-6-1-3-1-5-2-8h1v-2h2 0c1 2 2 1 2 3l2-2z"></path><path d="M201 203c1 1 2 1 3 2-4 1-6 3-9 5 1-3 4-5 6-7z" class="W"></path><defs><linearGradient id="p" x1="209.004" y1="199.435" x2="201.569" y2="197.862" xlink:href="#B"><stop offset="0" stop-color="#716e71"></stop><stop offset="1" stop-color="#8b8c8a"></stop></linearGradient></defs><path fill="url(#p)" d="M209 197h2v1l-8 2c-1 0-1 0-2 1l-6 3 3-3 4-2c1-1 2-1 3-1 2-1 3-1 4-1z"></path><path d="M211 197h3c2 1 2 1 3 2h-4v1h4c1 0 1 0 2 1v1h-2-1l1 1 2 2-1 1v-1l-1 1c-2 0-2 0-4-1h1l-1-1c-2 0-6 1-8 1h-1 0c-1-1-2-1-3-2 1-1 2-1 4-2l-2-1 8-2v-1z" class="C"></path><path d="M217 200c1 0 1 0 2 1v1h-2-1l1 1 2 2-1 1v-1c-1 0-2-1-2-2-1 0-3-1-4-1v-1h4l1-1z" class="X"></path><path d="M205 201c2 0 4-1 7-1l-6 3h3 0c-1 1-3 1-5 2h0c-1-1-2-1-3-2 1-1 2-1 4-2z" class="N"></path><path d="M230 201c2 1 4 3 5 4h1 0c2 3 4 6 6 10l1-1h0l1 1h0v-1-1h1l1 1v-1l1-1c0-2-1-4-1-5v-3 1 1l1 1 1-1h0c0 2 0 3 1 4 1 0 2 0 2 1 1 1 1 1 2 1l2 6c0 2 0 4 1 6l1 4c0 1 0 1 1 2v-1c1-1 2-1 3-1s1 1 2 1 2-1 3-1 2 1 2 1h3l1-2 1 2v1c-1 1-2 1-3 1-2 1-7 0-10 0h-29-17l-3 1h1c-2 2-1 8-2 10h1 1c1 0 2 0 2-1v1l4-1c1 1 1 1 2 1-4 1-8 2-11 3h0-2 0l-1-4v-1c0-1-3-2-3-3 1 0 1 0 1-1v-1c-1-1-1-1 0-2h0c-1-1-3-4-4-5l-4-2c1-1 0-1 0-2l1-1 2 1 1-1h0c1 0 2 0 2-1l4 2 5 2v-1l-6-3v-1l1-1h-1l-1 1c-1 0-1 0-2-1 0-1 1-1 1-1h1 4 3l1-2h0c1 0 2 0 2-1h3v-1h1l1-1c1 0 1 1 1 2h4c0 1 1 1 2 2 0 1 1 1 1 2 1 1 2 2 2 3 1 0 1 1 1 1 1 1 3 3 4 5 0 0 1 1 2 1v-1c0-1-1-1-2-2 1-1 1-2 1-3s-2-4-2-5c-1-1-2-3-2-4v-1l-1-1c1 0 1-1 2-2 0-1-1-1-1-2 1-2 0-3-1-5v-3z" class="b"></path><path d="M197 223l2 1c0 2 1 1 2 3h-1c-1-1-2-1-4-1 1-1 0-1 0-2l1-1z" class="X"></path><path d="M218 215l1-1c1 0 1 1 1 2h4c0 1 1 1 2 2l-2 1v1c-1-1-2-2-3-2l-3-3z" class="U"></path><path d="M245 227v-2h2l2 2h0 1v-2h1l2 3h-2c-1 0-1 1-1 1-1 0-3-1-4-2v2c0-1-1-2-1-2z" class="D"></path><path d="M200 223h0c1 0 2 0 2-1l4 2h-3v1l2 1h0-1c-1 0-1 0-3 1h0c-1-2-2-1-2-3l1-1z" class="U"></path><path d="M231 215c1 0 2 1 2 2l3 5 2 3h0l3 6c-2-2-4-5-6-7 0-1-2-4-2-5-1-1-2-3-2-4z" class="F"></path><path d="M236 222c1-1 2-1 2-2 1 0 2 2 3 3 0 1 1 2 1 3h1v-1c1 0 1 1 2 1v1h0s1 1 1 2v1h-1c0-1 0 0-1-1-2-1-3-3-5-4h-1 0l-2-3z" class="V"></path><path d="M224 220v1c-1 1-4 2-4 4v2c2 2 6 1 9 2h-9c-2-2-3-4-4-5 1-1 3-2 4-3 1 0 3 0 3-1h1z" class="U"></path><path d="M209 245v-10c0-1 0-3 1-4h4l-3 1h1c-2 2-1 8-2 10h1 1c1 0 2 0 2-1v1l4-1c1 1 1 1 2 1-4 1-8 2-11 3h0z" class="J"></path><path d="M212 217h0c1 0 2 0 2-1h3l1 1v1h-2-3v1c-1 1-1 1-3 1l-1 1h0c2 1 4 3 6 3 1 2 3 4 4 5-2 0-4-1-5-2s-2-1-3-1v-1l-6-3v-1l1-1h-1l-1 1c-1 0-1 0-2-1 0-1 1-1 1-1h1 4 3l1-2z" class="U"></path><path d="M226 218c0 1 1 1 1 2 1 1 2 2 2 3 1 0 1 1 1 1 1 1 3 3 4 5h-5 0c-3-1-7 0-9-2v-2c0-2 3-3 4-4v-1-1l2-1z" class="D"></path><path d="M223 223h1c0 1 2 1 2 2l-1 1c-1 0-2 0-3-1h0l1-2z" class="V"></path><path d="M227 225l-3-5c2 1 4 2 5 4v1s0 1-1 1l-1-1z" class="R"></path><path d="M230 224c1 1 3 3 4 5h-5 0c-3-1-7 0-9-2h4 2c1-1 1-2 1-2l1 1h1c1 0 1-1 1-2z" class="T"></path><path d="M230 201c2 1 4 3 5 4h1 0c2 3 4 6 6 10 0 1 0 1 1 2l1 3 1 5v1c-1 0-1-1-2-1v1h-1c0-1-1-2-1-3-1-1-2-3-3-3 0 1-1 1-2 2l-3-5c0-1-1-2-2-2v-1l-1-1c1 0 1-1 2-2 0-1-1-1-1-2 1-2 0-3-1-5v-3z" class="E"></path><path d="M236 205h0c2 3 4 6 6 10 0 1 0 1 1 2l1 3 1 5v1c-1 0-1-1-2-1v1h-1c0-1-1-2-1-3 0-3-2-5-2-8l1-1c-1-2-2-4-3-5l-2-4h1z" class="H"></path><path d="M230 201c2 1 4 3 5 4l2 4h-1-1v1h1c1 1 1 2 1 3s-1 1-1 2h-1v-1l-1-1c0 1 0 2 1 3-1 0-1 1-2 1 0-1-1-2-2-2v-1l-1-1c1 0 1-1 2-2 0-1-1-1-1-2 1-2 0-3-1-5v-3z" class="D"></path><path d="M232 211c0 1 1 2 1 3h0-2l-1-1c1 0 1-1 2-2z" class="P"></path><path d="M246 204v1 1l1 1 1-1h0c0 2 0 3 1 4 1 0 2 0 2 1 1 1 1 1 2 1l2 6c0 2 0 4 1 6l1 4h-1s-1 0-2 1h-1v-1l-2-3h-1v2h-1 0l-2-2h-2v2h0v-1-1l-1-5-1-3c-1-1-1-1-1-2l1-1h0l1 1h0v-1-1h1l1 1v-1l1-1c0-2-1-4-1-5v-3z" class="B"></path><path d="M249 214h1v1c0 1 0 2-1 3h0l-1-3 1-1z" class="N"></path><path d="M249 210c1 0 2 0 2 1 1 1 1 1 2 1l2 6h-1-1c-1-1-1-1-1-2-1-2 0-3-1-5h-1l-1-1z" class="Q"></path><path d="M250 219v-1h1l1 1c1 2 1 3 1 4v2h-1 0c0-1-1-1-1-1h-1c0 1 0 2-1 3l-2-2h-2v2h0v-1-1-1c1 0 2 0 3-1 1 0 1-3 1-4h1z" class="R"></path><path d="M250 219v-1h1l1 1c1 2 1 3 1 4h-1s0-1-1-1v1h-1v-4z" class="Q"></path><defs><linearGradient id="q" x1="253.17" y1="226.894" x2="254.191" y2="218.713" xlink:href="#B"><stop offset="0" stop-color="#5a585a"></stop><stop offset="1" stop-color="#7b7a7a"></stop></linearGradient></defs><path fill="url(#q)" d="M253 218h1 1c0 2 0 4 1 6l1 4h-1s-1 0-2 1h-1v-1l-2-3h-1v2h-1 0c1-1 1-2 1-3h1s1 0 1 1h0 1v-2c0-1 0-2-1-4l1-1z"></path><path d="M209 272v-1h1c2 0 4 0 6 2l1 1 2 2c2 2 4 4 6 5 1 1 3 1 4 1 3 2 8 2 11 1h0l4-1c1 0 1 1 2 1 0 1-1 1-1 2-1 1-1 3-2 5 0 0-1 0-1 1v1h-1l1 1c0 1-1 2-1 2 1 0 2-1 3 0 0 1 0 1-1 2h1c0 1 2 3 2 3-1 0-1 1-2 1l1 1 1-1c0 1 1 1 1 2v3h0s-1 0-1 1c1 1 3 0 3 3 0 1 1 3 0 4h0c0-1-1-2-1-4h0c-1-1-2-2-3-2l2 2c0 1 0 1 1 2v2l-1-1h0c-1-1-2-1-2-2s0-1-1-1-2 1-2 1h-1c-1 0-1 0-2 1l-3-1-11-1h-2-6v-1c-1-1-1-2-2-3h0c1 0 2-1 4 0h1 1l-1-1c1 0 1-1 2-2l-2-1v-1c-1 0-2-1-2-1-1 0-1 1-1 1h-2c-1-1-2-2-3-2h-1 0l-2-1c-2 0-6-3-7-4v-1l1-1c-1-1-3-1-3-3 1 0 1 0 1-1l-1-1v-1c0-1 0-2-1-3h-2l-1-2h0c-1 0-2 1-3 1h0c-1-1-2-1-2-2h0v-1-1c2-1 3-1 4-1v-1-2h1 3c0 1 0 2 2 2v-1h4l1-1c1 0 2 0 3-1v-1z" class="I"></path><path d="M227 302h-1-1v-1h2 2l-2 1z" class="E"></path><path d="M226 295h2c-1 1-1 1-2 1h-2c1-1 1-1 2-1z" class="G"></path><path d="M222 301c1 0 1-1 2-1 1 1 1 2 1 2v1c-2 0-2-1-3-2zm-3-4v-1c1 0 3 0 4 1l-1 1h-1l-2-1z" class="N"></path><path d="M215 290h0c1 2 4 2 5 3h0l1 1h1 1l-1 1h-3c-1 0-2-2-3-3-1 0-1 0-2-1 1 0 1-1 1-1z" class="H"></path><path d="M216 286h2l-1 1c0 1 1 1 1 2h-2-1-2l-1-1v-1c1 0 3 0 4-1h0z" class="N"></path><path d="M229 301h2v2h1 0 2 0l-1 1 1 1 4-1v1l-2 1c-1-1-2 0-2 0-1 0-1-1-1-1h-3l-3-3 2-1z" class="G"></path><path d="M221 301h1c1 1 1 2 3 2-1 2 0 3 1 4h-3l-2-1-1-1c1 0 1-1 2-2l-2-1 1-1z" class="E"></path><path d="M222 303c0 1 1 1 2 2v1h-1v1l-2-1-1-1c1 0 1-1 2-2z" class="D"></path><path d="M237 308c-1 1-2 1-3 1v-1s1 0 1-1c-2-1-3 0-5 0l-1-1c-1 0-2-1-3-2h0 1c1 0 1 1 3 1h0 3s0 1 1 1c0 0 1-1 2 0h4l-1 1c-1 0-2 0-2 1h0 0z" class="Q"></path><path d="M226 295l1-1c-1 0-1-1-2-1h-1c0-1 0-1-1-2h-2v-1c1-1 2 0 3 0h1v1c2 0 3-1 4-1 0 1-1 1-2 1v1h2v1s-1 0-2 1c2 0 4 1 6 1h0c-1 1-2 1-3 2h0c-1 0-1-1-2-2h-2z" class="H"></path><path d="M224 290h1v1c2 0 3-1 4-1 0 1-1 1-2 1v1h2v1s-1 0-2 1c0-1-1-1-1-1v-2c-1 0-2 0-3-1h0 1z" class="M"></path><path d="M213 279c2 0 3 2 6 2l-1 2h-1c0 1 1 1 2 1 0 1 1 1 1 1h0 3v1c-1 0-2 0-2 1h-1c-1-1-1-1-2-1h-2c-2 0-4-1-7-1 2-1 5-1 6-2h0-3 0c0-1 1-1 2-2 0-1 0-1-1-2z" class="O"></path><path d="M217 310v-1c-1-1-1-2-2-3h0c1 0 2-1 4 0h1 1l2 1h3 0c2 1 4 0 6 1-1 0-2 1-3 1h-1s0 1-1 1h-2-2-6z" class="T"></path><path d="M223 310c1 0 1 0 2-1 1 0 2-1 3 0 0 0 0 1-1 1h-2-2z" class="G"></path><path d="M238 305c2-1 4-1 5-2l1 1c0 1-1 1-1 1v1l1-1 1 1c0 1-1 1-1 2h1l2 2c0 1 0 1 1 2v2l-1-1h0c-1-1-2-1-2-2s0-1-1-1-2 1-2 1h-1c-1 0-1 0-2 1l-3-1c1 0 2 1 3 0v-2c-1-1-2 0-2-1h0 0c0-1 1-1 2-1l1-1h-4l2-1z" class="F"></path><path d="M237 308h0 0c0-1 1-1 2-1v1h4 0l-2 2h0v1c-1 0-1 0-2 1l-3-1c1 0 2 1 3 0v-2c-1-1-2 0-2-1z" class="M"></path><path d="M221 280c1 0 1 1 2 1 1 1 2 1 3 2h0c1 1 2 1 3 2-1 1-1 1-1 2l2-1c0 1 0 1 1 2h0c1 0 1-1 1-2h2c1 1 1 1 2 1h1l-4 2c-1 0-2 0-3 1h-1c-1 0-2 1-4 1v-1l2-1c1 0 0 0 1-1l-1 1h-2-5-2c0-1-1-1-1-2l1-1c1 0 1 0 2 1h1c0-1 1-1 2-1v-1h-3 0s-1 0-1-1c-1 0-2 0-2-1h1l1-2s2 1 2 0v-1z" class="Y"></path><path d="M218 286c1 0 1 0 2 1h1 2c0 1-1 1-2 1l-1 1h-2c0-1-1-1-1-2l1-1z" class="F"></path><path d="M191 278c2-1 3-1 4-1v1h1c0 1 0 2 1 2 1 1 5 4 7 4l1-1c1 0 2 1 4 1h0l-1 1v1h0 1v1c1 1 2 1 1 2v1h0l1 1c0 2-1 1 1 2s4 3 7 4l2 1-2 2 2 1-1 1v-1c-1 0-2-1-2-1-1 0-1 1-1 1h-2c-1-1-2-2-3-2h-1 0l-2-1c-2 0-6-3-7-4v-1l1-1c-1-1-3-1-3-3 1 0 1 0 1-1l-1-1v-1c0-1 0-2-1-3h-2l-1-2h0c-1 0-2 1-3 1h0c-1-1-2-1-2-2h0v-1-1z" class="C"></path><path d="M219 297l2 1-2 2s-1 0-2-1v-1h2v-1zm-10 1l-1-1c1-1 2 0 3 0h1c-1 1-1 1-1 2l-2-1z" class="E"></path><path d="M209 272v-1h1c2 0 4 0 6 2l1 1 2 2c2 2 4 4 6 5l1 2c-1-1-2-1-3-2-1 0-1-1-2-1v1c0 1-2 0-2 0-3 0-4-2-6-2 1 1 1 1 1 2-1 1-2 1-2 2h-1c-1-1-2-1-3-1l1 2c-2 0-3-1-4-1l-1 1c-2 0-6-3-7-4-1 0-1-1-1-2h-1v-1-1-2h1 3c0 1 0 2 2 2v-1h4l1-1c1 0 2 0 3-1v-1z" class="J"></path><path d="M213 275l-1-1c-1 0-1 0-1-1h3l1 1c1 1 1 1 2 1l-1 1h-2l-1-1z" class="a"></path><path d="M209 272v-1h1c2 0 4 0 6 2h-5-1c-1 1-1 1-2 1 0 1-1 1-1 2l-2-1 1-1c1 0 2 0 3-1v-1z" class="M"></path><path d="M213 275l1 1h2l1-1c0 2 1 3 3 4h1l-5-1c-2-1-4 0-5 1l-1-1s-1 0-1-1h2 0v-1c1 0 2 0 2-1z" class="O"></path><path d="M211 279c1-1 3-2 5-1l5 1v1 1c0 1-2 0-2 0-3 0-4-2-6-2h-2z" class="f"></path><path d="M209 276h1l1 1h0-2c0 1 1 1 1 1l1 1h2c1 1 1 1 1 2-1 1-2 1-2 2h-1c-1-1-2-1-3-1h-1v-1c1 0 1 0 2-1h-2l1-1h1c-1-1-2-1-3-1v-1h0c1 0 1 0 2-1h1z" class="M"></path><path d="M207 280h2c1 0 1 1 2 2v1c-1-1-2-1-3-1h-1v-1c1 0 1 0 2-1h-2z" class="Q"></path><path d="M196 274h3c0 1 0 2 2 2v-1h4l2 1h2-1c-1 1-1 1-2 1h0v1c1 0 2 0 3 1h-1l-1 1h2c-1 1-1 1-2 1v1h1l1 2c-2 0-3-1-4-1l-1 1c-2 0-6-3-7-4-1 0-1-1-1-2h-1v-1-1-2h1z" class="E"></path><path d="M202 280l3 1h1v1h-1c-1 0-3 0-4-1v-1h1z" class="C"></path><path d="M195 278c2-1 3-1 5 0l2 1h4v-1c1 0 2 0 3 1h-1l-1 1-2 1-3-1c-2 0-4-2-6-2h-1z" class="S"></path><defs><linearGradient id="r" x1="198.646" y1="279.002" x2="202.084" y2="273.161" xlink:href="#B"><stop offset="0" stop-color="#605e5f"></stop><stop offset="1" stop-color="#777"></stop></linearGradient></defs><path fill="url(#r)" d="M196 274h3c0 1 0 2 2 2v-1h4l2 1h2-1c-1 1-1 1-2 1h0v1 1h-4l-2-1c-2-1-3-1-5 0v-1-1-2h1z"></path><path d="M225 281c1 1 3 1 4 1 3 2 8 2 11 1h0l4-1c1 0 1 1 2 1 0 1-1 1-1 2-1 1-1 3-2 5 0 0-1 0-1 1v1h-1l1 1c0 1-1 2-1 2 1 0 2-1 3 0 0 1 0 1-1 2h1c0 1 2 3 2 3-1 0-1 1-2 1l1 1 1-1c0 1 1 1 1 2v3h0s-1 0-1 1c1 1 3 0 3 3 0 1 1 3 0 4h0c0-1-1-2-1-4h0c-1-1-2-2-3-2h-1c0-1 1-1 1-2l-1-1-1 1v-1s1 0 1-1l-1-1c-1 1-3 1-5 2v-1l-4 1 1-1c0-1 1-1 2-1h0v-1 1h-5c0-1 1-1 2-2-1 0-2 0-3-1h0c0-1 1-1 1-2l-1-1h-1 0c1-1 2-1 3-2h0c-2 0-4-1-6-1 1-1 2-1 2-1v-1h-2v-1c1 0 2 0 2-1h1c1-1 2-1 3-1l4-2h-1c-1 0-1 0-2-1h-2c0 1 0 2-1 2h0c-1-1-1-1-1-2l-2 1c0-1 0-1 1-2-1-1-2-1-3-2h0l-1-2z" class="M"></path><path d="M237 303l-3-1h0c1-1 1-1 2-1h1 0c1-1 2-1 3-2h1 0c0 1 1 1 2 1-1 1 0 1-1 1-2 1-2 2-4 3l-4 1 1-1c0-1 1-1 2-1h0v-1 1z" class="W"></path><path d="M236 296v1c2 0 5-2 6-1-1 2-3 3-4 4h-4-1c1-1 2-1 3-1v-1c-1 0-2 0-3-1 1 0 2-1 3-1zm-10-13h2c1 1 5 2 6 2h1 2 0l6-2c0 1-2 3-3 4h-3-1c-1 0-1 0-2-1h-2c0 1 0 2-1 2h0c-1-1-1-1-1-2l-2 1c0-1 0-1 1-2-1-1-2-1-3-2z" class="F"></path><path d="M233 289h0c2 0 6-1 7 0-1 1-2 2-4 2v1h1c1-1 2-1 3 0 0 1 0 1-1 1-1 1-3 1-4 2 1 0 2 0 4-1h1 0c-1 1-3 2-4 2s-2 1-3 1h-2-1 0c1-1 2-1 3-2h0c-2 0-4-1-6-1 1-1 2-1 2-1v-1h-2v-1c1 0 2 0 2-1h1c1-1 2-1 3-1z" class="Z"></path><path d="M237 292c-1 1-2 1-3 1s-1-1-1-1v-1h3v1h1z" class="J"></path><path d="M231 316l4-1 3 1c1 0 1 1 1 1 2 0 4 1 6 1l2 1c0 1 1 1 2 1 1 1 0 1 2 1v1h-1l-3 3h-1l1-1v-1c-1 1-5 1-6 2l1 1c0 1-1 1-1 2 1 1 1 1 0 2h0c-4 1-7 3-11 5-7 3-14 7-19 12-1 1-3 2-4 3h-2-1c-1-2-1-3-1-5l-2 1-2 1h0l1-1-2-1c-1 0-1-1-2-1h0c-1-1-1-3-2-5v-1l-3 1-1 1c-2 2-3 3-4 5l-1-1 2-2h-1c-7 3-14 9-19 14l-7 7c-1 1-2 3-3 3 7-10 18-18 28-25 0-1 0-2 1-3v-1l-2 1h0c0-2 2-3 3-4l-1-1h0l-2 2c0-1 1-2 1-3 0 0 0-1-1-1v-1h0c0-2 2-3 2-5h0l2-1-1 1v1c1-1 2-2 2-3h3c6-2 12-3 18-4 5-1 10-2 14-2 2-1 3-1 5-1h2z" class="f"></path><path d="M190 328v3c1 1 1 3 1 5h0 0c1 0 1 1 2 1l-4 2c0-1 0-2 1-3v-2h-1c-1 1-1 2-1 3-1 1-1 1-1 2v1l-2 1c0-1 0-2 1-3v-1l-2 1h0c0-2 2-3 3-4v-1c0-1 1-1 1-2h1l-1-1 2-2z" class="S"></path><path d="M239 317c2 0 4 1 6 1l2 1c0 1 1 1 2 1 1 1 0 1 2 1v1h-1s0-1-1-1l-2 1c-2 1-4 1-5 1h-3v-1h0c-1-1-1-1-2-1h0v-2-1h2v-1z" class="N"></path><path d="M237 319c1 1 3 1 4 1s2 0 3 1h-2v2h-3v-1h0c-1-1-1-1-2-1h0v-2z" class="E"></path><path d="M189 323c1 1 1 2 1 3 1 1 2 1 2 2v1c1 1 2 2 2 3 1 2-2 3 1 4l-2 1c-1 0-1-1-2-1h0 0c0-2 0-4-1-5v-3l-2 2 1 1h-1c0 1-1 1-1 2v1l-1-1h0l-2 2c0-1 1-2 1-3 0 0 0-1-1-1v-1h0c0-2 2-3 2-5h0l2-1-1 1v1c1-1 2-2 2-3z" class="P"></path><path d="M185 332c1-1 2-2 2-4 1 0 1-1 2-1l1 1-2 2 1 1h-1c0 1-1 1-1 2v1l-1-1h0l-2 2c0-1 1-2 1-3z" class="G"></path><path d="M202 335c7-4 15-6 24-8 3-1 7-2 10-2-5 2-11 5-16 8-6 3-11 7-17 10l-3 3-2-1c-1 0-1-1-2-1h0c-1-1-1-3-2-5v-1l3-1 1-1 4-1z" class="j"></path><path d="M198 336l4-1h-1c0 2-1 3-2 5h-1v2h1l1 2h0v-1c1 0 1 1 2 0h1l-3 3-2-1c0-1 1-1 1-2l-2-1v-2c0-1 0-2 1-4z" class="e"></path><path d="M197 337l1-1c-1 2-1 3-1 4v2l2 1c0 1-1 1-1 2-1 0-1-1-2-1h0c-1-1-1-3-2-5v-1l3-1z" class="L"></path><path d="M197 337l1-1c-1 2-1 3-1 4v2c-1-1-2-2-2-3 0 0 1 0 2-1v-1z" class="X"></path><path d="M241 325l1 1c0 1-1 1-1 2 1 1 1 1 0 2h0c-4 1-7 3-11 5-7 3-14 7-19 12-1 1-3 2-4 3h-2-1c-1-2-1-3-1-5 11-9 24-16 38-20zm-10-9l4-1 3 1c1 0 1 1 1 1v1h-2v1 2h0c1 0 1 0 2 1h0v1h-2c-2 1-4 2-6 2-13 2-25 6-36 11-3-1 0-2-1-4 0-1-1-2-2-3v-1c0-1-1-1-2-2 0-1 0-2-1-3h3c6-2 12-3 18-4 5-1 10-2 14-2 2-1 3-1 5-1h2z" class="h"></path><path d="M189 323h3v4 1c0-1-1-1-2-2 0-1 0-2-1-3z" class="e"></path><path d="M231 316l4-1 3 1c1 0 1 1 1 1v1h-2v1 2h0c1 0 1 0 2 1h0v1h-2c-2 1-4 2-6 2v-1h1v-1-1 1l1-1v-1h-2v-1c0-1 1-1 1-1h2l-1-1c-1 0-4 0-4-1-2-1-3 0-5 0 2-1 3-1 5-1h2z" class="L"></path><path d="M231 316l4-1 3 1c1 0 1 1 1 1v1h-2v1 2h0c1 0 1 0 2 1h0v1h-2l1-1h0c-1 0-3-1-4-1s-1-1-1-1h1c1 0 1 0 2-1-1 0-1-1-1-1l-1-1h-4c-1 0-1-1-1-1h2z" class="C"></path><path d="M231 316l4-1 3 1c1 0 1 1 1 1v1h-2v-1c-1 0-3 0-3-1h-3z" class="S"></path><path d="M184 240h1 1 0l-1 2h1c1-1 1-1 1-2h0v1h2 1 2l2 1c1 0 1 1 2 0l1 1c0 1 2 2 3 2l3 3h0 2l2-1-1-2v-4l1 4h0 2 0c3-1 7-2 11-3 2 0 4 0 6 1h-5v1h-1v1h0c-1 1-1 1-3 1h0c1 2 5 4 7 5 1 1 1 1 1 2h0 0-4v1h1v1l-2 1h2 5c-5 1-9 3-14 5l-4 2v1h-1v1l1-1h2v-1l2 1c1 0 2-1 3-2h1 1 1l-1 1 1 1c-1 3 0 6 0 9l2 2-2 1-2-2-1-1c-2-2-4-2-6-2h-1v1 1c-1 1-2 1-3 1l-1 1h-4v1c-2 0-2-1-2-2h-3-1v2 1c-1 0-2 0-4 1v1 1h0c0 1 1 1 2 2h0-2c-1 0-3 0-4-1h-1l-1 1v1l-1 1v-2l-1 1v-1l1-1c1-1 1-1 1-2l-2-2h0s-1 0-2 1l-1-1c-1 0-2 1-4 1 1-1 3-2 4-2v-1h-2v-1h1c-1-1-1-1-2-1v-1h0-1-2-1l-1 1h0c0-1 1-2 1-2 0-1 0-2 1-3 1 0 1-3 2-4-1 0-2 1-3 2l2-6c1-1 1-2 1-3 1 0 0-1 0-1 1-2 2-3 3-4l3-2h-3c0-1 1-2 2-3 1-2 2-3 3-4h-1-1l2-2v-1z" class="M"></path><path d="M196 253h3v1h-2l-1 1-1-1 1-1z" class="B"></path><path d="M182 272c-1 0-1-1 0-1 1-1 1-1 2-1v1c-1 1-2 1-2 1zm-4-4h4v1c-2 0-3 0-4 1v-2z" class="J"></path><path d="M191 262l-1-1h-1s0-1 1-1 2 1 4 1c-2 1-2 1-3 1z" class="B"></path><path d="M197 254s1 0 1 1l-2 1h-2v-1h2l1-1z" class="K"></path><path d="M192 251l2 1c-1 0-3 1-4 1 0-1-1-1-1-2h3z" class="F"></path><path d="M176 272c1-1 2-1 3-1s1 0 1 1v1l-1 1c-1-1-1-1-2-1v-1h0-1z" class="J"></path><path d="M184 260c1 0 2-1 3-1l1 1c-1 1-2 1-4 2 0-1-1-1-1-1l1-1z" class="F"></path><path d="M185 266c-2 0-3 1-5 1v-1c1 0 3-1 4-1 1-1 1-1 2-1h0c1 0 2 0 2 1l-3 1z" class="J"></path><path d="M182 259h2v1l-1 1h-1c1 1 1 1 2 1-2 1-3 1-5 1h0l2-3 1-1z" class="H"></path><path d="M192 251c2-1 3 0 5 0l1 1h1v1h0-3c-1-1-1-1-2-1l-2-1z" class="W"></path><path d="M184 262h1v1c-2 1-4 1-5 2s-2 2-3 2v-1l1-1c0-1 0-1 1-2 2 0 3 0 5-1z" class="I"></path><path d="M176 264v1l1 1v1l1 1v2c-1 0-2 1-4 1h0l1-1-1-2c1 0 1-3 2-4z" class="F"></path><path d="M193 245h2c2 1 3 1 4 2v1h-1l-1-1c-1 0-2 0-3-1h-1v1c1 1 1 1 2 1v1h-2-1v1h-2c1-1 1-1 2-1l-3-3h0-1l1-1h2 2z" class="W"></path><path d="M175 260c2 0 2-1 4-2v1h0 2 1l-1 1-2 3h0c-1 1-1 1-1 2l-1 1-1-1v-1c-1 0-2 1-3 2l2-6z" class="Z"></path><path d="M179 263l-2-1c0-1 0-1 1-2h3l-2 3z" class="d"></path><path d="M186 264v-2h4 1l-1 1h3l2 2h-6c1 1 2 1 2 1l-2 2-1-1h-1-2v-1l3-1c0-1-1-1-2-1h0z" class="Q"></path><path d="M186 264l2-1h3v1 1h-3c0-1-1-1-2-1z" class="M"></path><path d="M184 243h3 0c0 1-1 1-1 2v1h1v1c-1 0-2 0-2 1 1 0 1 1 2 0h1 0l-3 3c0-1 0-1-1 0-1-1-2-1-2-1h-3c0-1 1-2 2-3 1-2 2-3 3-4z" class="F"></path><path d="M196 261h2c1 0 2 2 3 3h-1 0v1l-2 2c0-1-1-1-1-1h-2v1l-1-1h-3s-1 0-2-1h6l-2-2h-3l1-1c1 0 1 0 3-1h2z" class="E"></path><path d="M200 265s-1 1-2 0c-1 0-2 0-2-1v-1c2 0 2 0 4 1h0v1z" class="D"></path><path d="M184 240h1 1 0l-1 2h1c1-1 1-1 1-2h0v1h2 1 2l2 1c1 0 1 1 2 0l1 1c0 1 2 2 3 2-1 1-3 0-5 0h-2-2-2l-1-1h1v-1h-2-3-1-1l2-2v-1z" class="B"></path><path d="M191 245v-1-2h1s1 0 2 1l-1 2h-2z" class="H"></path><path d="M182 250s1 0 2 1c1-1 1-1 1 0l-2 1 1 2c-1 0-2 1-2 2v1c0 1-2 1-3 2v-1c-2 1-2 2-4 2 1-1 1-2 1-3 1 0 0-1 0-1 1-2 2-3 3-4l3-2z" class="J"></path><path d="M179 254h1 2l-3 3-1-1h0l1-2z" class="Y"></path><path d="M182 250s1 0 2 1c1-1 1-1 1 0l-2 1-1 2h-2-1v-2l3-2z" class="a"></path><path d="M195 245c2 0 4 1 5 0l3 3h0v1h2 1c1 1 2 1 3 2v1h2c0 1-1 1-1 1-1 0-2 1-3 2h-3s-1 1-2 1l-1-1s0 1-1 1c0 0-1 0-2-1 1 0 1 0 1-1h0v-1h0v-1h-1c1-1 1-1 1-2h-1-2 0v-1c1-1 1-1 2-1h1v-1c-1-1-2-1-4-2z" class="H"></path><path d="M199 252c2 0 3 0 5 1 1 0 1 0 1 1h0c-2 0-4 0-6-1v-1z" class="F"></path><path d="M212 250l2 1 2 1c-1 1-1 1-2 1v2h-4c0 1 1 1 1 1v1c-3 0-3 2-5 3h-2c1 2 2 3 4 3l-2 1h-1-4 0c-1-1-2-3-3-3h-2c0-1 1-2 1-3h-1v-1h1c2 0 3 0 5-1h0c1 0 2-1 2-1h3c1-1 2-2 3-2 0 0 1 0 1-1h-2v-1l3-1z" class="I"></path><path d="M201 264c1-1 1-2 2-2s1 0 2 1v1h-4z" class="N"></path><path d="M201 258s2 1 2 0c1 0 2-1 2-1 1 1 1 1 1 3h-2c-2 0-3 1-6-1h1l2-1z" class="V"></path><path d="M212 250l2 1 2 1c-1 1-1 1-2 1v2h-4c0 1 1 1 1 1v1c-3 0-3 2-5 3 0-2 0-2-1-3 0 0-1 1-2 1 0 1-2 0-2 0h2c1-1 1-1 2-1h1l-2-2h0 3c1-1 2-2 3-2 0 0 1 0 1-1h-2v-1l3-1z" class="R"></path><path d="M216 252c1 1 1 1 2 1v-1c2 0 5 0 7 1h-4v1h1v1l-2 1h2 5c-5 1-9 3-14 5l-4 2h-1 0c-2 0-3-1-4-3h2c2-1 2-3 5-3v-1s-1 0-1-1h4v-2c1 0 1 0 2-1z" class="N"></path><path d="M208 263c0-1-1-2-1-3 2 0 2 0 3 1h3l-4 2h-1z" class="I"></path><path d="M216 252c1 1 1 1 2 1v-1c2 0 5 0 7 1h-4v1h1v1l-2 1c-1 1-3 1-5 2 0 0-1 1-2 1-1-1-2-1-2-2v-1s-1 0-1-1h4v-2c1 0 1 0 2-1z" class="B"></path><path d="M211 257c1 0 1 0 2-1h-1v-1c2 0 4 0 5-1 0 0-1 1-1 2h0c-1 0-2 0-2 1 0 0 1 0 1 1 0 0-1 1-2 1-1-1-2-1-2-2z" class="W"></path><path d="M218 253v-1c2 0 5 0 7 1h-4v1h1v1l-2 1c-1 1-3 1-5 2 0-1-1-1-1-1 0-1 1-1 2-1h0c0-1 1-2 1-2h2l-1-1z" class="F"></path><path d="M206 241l1 4h0 2 0c3-1 7-2 11-3 2 0 4 0 6 1h-5v1h-1v1h0c-1 1-1 1-3 1h0c1 2 5 4 7 5 1 1 1 1 1 2h0 0c-2-1-5-1-7-1v1c-1 0-1 0-2-1l-2-1-2-1-3 1c-1-1-2-1-3-2h-1-2v-1h2l2-1-1-2v-4z" class="K"></path><path d="M212 250h1s1 0 1-1c1 0 1 1 2 1 1 1 2 1 3 1v1c-2 0-3-1-5-1l-2-1z" class="B"></path><path d="M224 251c1 1 1 1 1 2h0 0c-2-1-5-1-7-1v1c-1 0-1 0-2-1l-2-1c2 0 3 1 5 1h0 1c1-1 2-1 2-1l1 1 1-1z" class="J"></path><path d="M206 241l1 4h0 2 0c3-1 7-2 11-3 2 0 4 0 6 1h-5v1h-1v1h0c-1 1-1 1-3 1h0c-1 0-1-1-2-1l-8 2-1-2v-4z" class="X"></path><path d="M215 245c2-1 3-1 5-1v1h0c-1 1-1 1-3 1h0c-1 0-1-1-2-1zm2 17h1 1l-1 1 1 1c-1 3 0 6 0 9l2 2-2 1-2-2-1-1c-2-2-4-2-6-2h-1v1 1c-1 1-2 1-3 1l-1 1h-4v1c-2 0-2-1-2-2h-3-1v2 1c-1 0-2 0-4 1v1 1h0c0 1 1 1 2 2h0-2c-1 0-3 0-4-1h-1l-1 1v1l-1 1v-2l-1 1v-1l1-1c1-1 1-1 1-2l-2-2h0v-1h-2v-3l1-1s1 0 2-1h1l-1-1h-1v-1c1-1 3-1 5-2l1 1 2-2h3l1 1v-1h2s1 0 1 1l2-2v-1h0 1 0 4 1l2-1h0 1v1h-1v1l1-1h2v-1l2 1c1 0 2-1 3-2h1z" class="B"></path><path d="M192 270h2l1 1v1h-1l-1-1-1-1zm17 2l-1-1-2 1-1-1c1-1 2-1 3-1l2 1h-1v1z" class="N"></path><path d="M196 270h2l5 2h0-2s-1 1-1 0c-2 0-3 0-4-2z" class="C"></path><path d="M200 266c1 0 1 0 2 1l1 1c1 0 1 0 1 1h0-3-1-2l-1-1c2 0 2-1 3-2z" class="Q"></path><path d="M191 266h3l1 1v-1h2s1 0 1 1l-1 1c-1 0-1 0-2-1-1 1-4 1-6 1l2-2z" class="G"></path><path d="M188 270c0-1 0-1 1-1s1 1 1 2l2-1 1 1-1 1 1 1h-2c-1 1-2 1-3 1l1-2h-4v-1c1 0 2-1 3-1z" class="R"></path><g class="Q"><path d="M189 272h3l1 1h-2c-1 1-2 1-3 1l1-2z"></path><path d="M184 270c2 0 2-1 4 0-1 0-2 1-3 1v1h4l-1 2-1-1-2 2h2v1h0c-2 1-2 1-4 1h0v-1h-2v-3l1-1s1 0 2-1h1l-1-1z"></path></g><path d="M185 272h4l-1 2-1-1-2 2h2v1h-2-3c2-1 2-2 3-3v-1z" class="B"></path><path d="M200 264h1 0 4l-1 1c1 1 1 0 2 1s2 1 3 1c0 1 0 1 1 1 0 1 1 1 1 1 2 1 4 2 5 2l1 3-1-1c-2-2-4-2-6-2l-2-1h-3v-1h2v-1c-1 0-1 0-2-1-1 0-1 0-2-1-1 0-1 0-2-1h-1v-1h0z" class="G"></path><path d="M208 263h1v1h-1v1l1-1h3c1 1 2 2 2 3v1c1 1 2 2 2 3-1 0-3-1-5-2 0 0-1 0-1-1-1 0-1 0-1-1-1 0-2 0-3-1s-1 0-2-1l1-1h1l2-1h0z" class="T"></path><path d="M208 263h1v1h-1v1l1-1h3c1 1 2 2 2 3v1h-2c-1-1-2-1-3-2-1 0-1-1-1-1h-2v-1l2-1h0z" class="X"></path><path d="M217 262h1 1l-1 1 1 1c-1 3 0 6 0 9l2 2-2 1-2-2-1-3c0-1-1-2-2-3v-1c0-1-1-2-2-3h-3 2v-1l2 1c1 0 2-1 3-2h1z" class="e"></path><path d="M217 262h1 1l-1 1 1 1c-1 3 0 6 0 9-2-3-3-6-4-9 1-1 1-2 2-2z" class="D"></path><path d="M191 273c2 0 3 0 5 1h-1v2 1c-1 0-2 0-4 1v1 1h0c0 1 1 1 2 2h0-2c-1 0-3 0-4-1h-1l-1 1v1l-1 1v-2l-1 1v-1l1-1c1-1 1-1 1-2l-2-2c2 0 2 0 4-1h0v-1h-2l2-2 1 1c1 0 2 0 3-1z" class="S"></path><path d="M191 278h0v-2c2 0 2 1 4 0v1c-1 0-2 0-4 1z" class="E"></path><path d="M190 274h4v1l-2 1-2-2z" class="B"></path><defs><linearGradient id="s" x1="295.293" y1="204.694" x2="366.188" y2="179.132" xlink:href="#B"><stop offset="0" stop-color="#0f0f0e"></stop><stop offset="1" stop-color="#3c3b3d"></stop></linearGradient></defs><path fill="url(#s)" d="M313 200c6-5 10-11 14-17 6-9 13-19 23-24l6-3c1 0 2 0 3 1 1 3 1 5 1 8v8c0 2-2 8-1 10-1 6-2 12-5 18l-1 1v1l-1-1v-2c1-1 1-2 1-2v-1l-1 1h0c0 1-1 2-2 2 0 1 0 1 1 2h-1v1c-1 0-2 0-3-1l1-1h1c0-1-1-1-1-2h-2-3c-4-1-6 0-10 2l-3 1-5 4c-6 5-10 10-14 15-2 2-6 9-7 9l3-6c-1 1-2 1-2 2 0 0 0 1-1 1v1h0 0v-2c-1-3 0-5 1-7v-3c0-2 1-4 0-7h1l1-1v-1c1-1 2-3 3-4h1v-1c1 0 2-1 2-2z"></path><path d="M325 196h0 2v1l-1 1h0l-1 1v1l-1 1h0c1 0 1 0 2-1 1 1 1 2 1 3l-2 2c0-1 0-2-1-3-1 0-3 0-4 1h-1c1 0 2-1 2-2 1-2 4-3 5-4l-1-1z" class="e"></path><path d="M332 186v-1 1h2c0 1-1 1-1 1v1h1c0 1-1 1-2 1 0 1 0 1-1 2 0 0-2 1-3 2h-1c1 0 3 0 5-1h0 2 0c-1 1-2 1-4 2 2 1 4 0 6 0 0 1-1 1-1 1h0l-4 2c-2 0-3 1-4 1-1 1-2 1-2 2v-1l1-1h0l1-1v-1h-2 0-2c0-1 1-2 2-3 1 0 2-1 2-2 2-1 3-2 5-3 0-1-1-1-2-2h2z" class="U"></path><path d="M325 196h-2c0-1 1-2 2-3 1 1 1 1 2 1l1 1c1 0 2 1 3 1 1-1 2-2 4-1l-4 2c-2 0-3 1-4 1-1 1-2 1-2 2v-1l1-1h0l1-1v-1h-2 0z" class="b"></path><path d="M310 203h1v1l2 3-3 6h-1c0-1 1-2-1-3h-2c1 2 1 1 2 2 1 2-1 6-2 7h-1v2 2c0 1-1 2-1 3-1-3 0-5 1-7v-3c0-2 1-4 0-7h1l1-1v-1c1-1 2-3 3-4z" class="e"></path><path d="M310 203h1v1c-1 2-2 3-3 5h-2l1-1v-1c1-1 2-3 3-4z" class="P"></path><path d="M337 176l1 1h0l-1 1h1l1 1h-3v1h1 2c0 1-1 2-2 2v1c1 0 2 0 3-1h2v1h2 0l1 1h-6 3c0 1 0 0-1 1h-1l-4 1v1h1v1h-1v2c-2 0-3 1-4 1v1h0c-2 1-4 1-5 1h1c1-1 3-2 3-2 1-1 1-1 1-2 1 0 2 0 2-1h-1v-1s1 0 1-1h-2v-1 1c-1-1-3-1-3-1 0-2 1-3 2-4v-1l3-3v1 1c1-2 2-3 3-3z" class="V"></path><path d="M339 184c0 1-1 1-2 1v-1c1-1 3-1 5-1h2 0l1 1h-6z" class="N"></path><path d="M337 176l1 1h0c-2 1-2 2-3 2 0 0 0 1-1 1 0 1-1 1-2 2l-2 1c1 0 4-1 6-1-1 1-3 2-5 2l1 1c1 0 2-1 3-1v1h-3v1c-1-1-3-1-3-1 0-2 1-3 2-4v-1l3-3v1 1c1-2 2-3 3-3z" class="X"></path><path d="M327 198c1 0 2-1 4-1 2 1 4 0 6 1l2-1h4c1 0 1 0 2 1-1 1-1 1-2 1-4-1-6 0-10 2l-3 1-5 4c-6 5-10 10-14 15-2 2-6 9-7 9l3-6 6-8c3-4 8-8 12-11l2-2c0-1 0-2-1-3-1 1-1 1-2 1h0l1-1c0-1 1-1 2-2z" class="K"></path><path d="M327 198c1 0 2-1 4-1 2 1 4 0 6 1-1 0-3 1-4 2l-3 1-3 2c0-1 0-2-1-3-1 1-1 1-2 1h0l1-1c0-1 1-1 2-2z" class="T"></path><path d="M326 200h3l1 1-3 2c0-1 0-2-1-3z" class="X"></path><path d="M327 198c1 0 2-1 4-1 2 1 4 0 6 1-1 0-3 1-4 2l-1-1c-1-1-2 0-4 0l-1-1z" class="D"></path><path d="M349 185c1 0 2 0 3 1l1 1v1l-1 1 1 1 1 1h0c0 1 0 2 1 3l-2 3-1 1h0c0 1-1 2-2 2 0 1 0 1 1 2h-1v1c-1 0-2 0-3-1l1-1h1c0-1-1-1-1-2h-2-3c1 0 1 0 2-1-1-1-1-1-2-1h-4l-2 1c-2-1-4 0-6-1l4-2h0s1 0 1-1c-2 0-4 1-6 0 2-1 3-1 4-2h0-2v-1c1 0 2-1 4-1v-2h1v-1h-1v-1l4-1h1 8z" class="H"></path><path d="M354 191c0 1 0 2 1 3l-2 3-1 1v-3c1-1 1-3 2-4z" class="B"></path><path d="M337 192c4-1 6-2 9-1h3v1h-1-4c-2 0-7 2-9 1h1c0-1 1-1 1-1z" class="J"></path><path d="M334 192v-1h1 2v1s-1 0-1 1h-1c2 1 7-1 9-1h0c-1 1-2 1-3 1v1h0c-1 1-1 2-2 3l-2 1c-2-1-4 0-6-1l4-2h0s1 0 1-1c-2 0-4 1-6 0 2-1 3-1 4-2h0z" class="I"></path><path d="M337 187h-1v-1l4-1h1 8l2 1v1 1h0l-1 1h-4-4c0 1-1 1-1 1l-4 1h-2-1v1h-2v-1c1 0 2-1 4-1v-2h1v-1z" class="K"></path><path d="M346 189v-1h5l-1 1h-4z" class="F"></path><path d="M337 187c1 0 3 1 4 1s1 0 1 1c-2 0-4 0-6 1v-2h1v-1z" class="E"></path><path d="M337 187h-1v-1l4-1v1h0c1 0 3 0 5 1-1 1-2 1-3 2h0c0-1 0-1-1-1s-3-1-4-1z" class="B"></path><path d="M337 176l-1-1 1-1c1-3 3-3 5-5 2-1 3-2 4-4 2-1 4-2 5-3s2-2 4-2c0 0 1 0 1-1h1l1 2c0 2 0 2-1 4l2 1h0c1-1 1 0 1-1v8c0 2-2 8-1 10-1 6-2 12-5 18l-1 1v1l-1-1v-2c1-1 1-2 1-2v-1l2-3c-1-1-1-2-1-3h0l-1-1-1-1 1-1v-1l-1-1c-1-1-2-1-3-1h-8c1-1 1 0 1-1h-3 6l-1-1h0-2v-1h-2c-1 1-2 1-3 1v-1c1 0 2-1 2-2h-2-1v-1h3l-1-1h-1l1-1h0l-1-1z" class="L"></path><path d="M357 165c0-1-1-1-1-2l2-2c0 2 0 2-1 4z" class="U"></path><defs><linearGradient id="t" x1="357.933" y1="183.204" x2="351.208" y2="185.452" xlink:href="#B"><stop offset="0" stop-color="#616062"></stop><stop offset="1" stop-color="#797978"></stop></linearGradient></defs><path fill="url(#t)" d="M355 179l1-1c0-1 1-1 1-1 1-2 0-2-1-4l2-1v3 5c-1 4-2 9-3 14-1-1-1-2-1-3h0l-1-1-1-1 1-1v-1-2c0-1 1-2 1-2 1-2 1-3 1-4z"></path><path d="M346 168c1 0 2-1 3-2l1-1c1 1 1 1 2 0 1 0 1-1 1-1 1 0 1-1 2-1v1l-1 1h0 1c1 0 2 1 3 1l-1 1v1h1-4v-1h0l-1-1h0l-1 2h-1c-1 0-2 1-3 1h-1v2c-2 1-3 2-4 3v1h0v1l-1 1h-1c-1-1-2-1-3-1v-1c1-1 1-1 1-2 1-1 3-1 4-2h0c0-2 2-2 3-3z" class="G"></path><path d="M341 177c-1-1-2-1-3-1v-1c1-1 1-1 1-2 1-1 3-1 4-2h0c0-2 2-2 3-3l1 2-2 1c-2 0-4 2-5 4h0c1 0 2 0 2 1v1h-1z" class="C"></path><path d="M347 171v-2h1c1 0 2-1 3-1h1l1-2h0l1 1h0v1h4v1 3l-2 1c1 2 2 2 1 4 0 0-1 0-1 1l-1 1c0 1 0 2-1 4 0 0-1 1-1 2v2l-1-1c-1-1-2-1-3-1h-8c1-1 1 0 1-1h-3 6l-1-1h0-2v-1h-2c-1 1-2 1-3 1v-1c1 0 2-1 2-2h-2-1v-1h3l-1-1h-1l1-1h0 3 1l1-1v-1h0v-1c1-1 2-2 4-3z" class="B"></path><path d="M351 178l1-2h1l1 1-1 1h-2z" class="H"></path><path d="M346 173h1c2-1 3 0 4 0v1h-1l-1 1c-2 0-2 0-3-1v-1h0z" class="R"></path><path d="M352 179h1v3c-2 0-3 1-4 1h-2v-1h1c1 0 3-1 4-3z" class="N"></path><path d="M347 171v-2h1c1 0 2-1 3-1h1l1-2h0l1 1h0v1h4v1 3l-2 1c1 2 2 2 1 4 0 0-1 0-1 1l-1 1c0-2 1-4 1-5v-1-1h0s-1-1-2-1l-1 1c-1 0-2-1-3-1h-3z" class="R"></path><path d="M343 175c2 1 4 2 6 2v1h2-1l1 1c-1 0-4 0-5 1v1c-2 1-5-1-6 1-1 1-2 1-3 1v-1c1 0 2-1 2-2h-2-1v-1h3l-1-1h-1l1-1h0 3 1l1-1v-1z" class="H"></path><path d="M336 180h10v1c-2 1-5-1-6 1-1 1-2 1-3 1v-1c1 0 2-1 2-2h-2-1zm7-5c2 1 4 2 6 2v1h0-1c-3-1-6 1-9 1l-1-1h-1l1-1h0 3 1l1-1v-1z" class="E"></path><path d="M218 241h13c6 1 13 1 17 5 5 4 6 11 7 16v71-3c-1 1-1 2-1 4 0-2 1-5 0-6v6l-1-1v-1c-2 1-3 3-4 5l-1-1 1-1h-1l1-1c1-1 1-1 1-2s0-1 1-2v-1l-1-1h-2c-1 1-1 1-2 1h0l2-2h-3 0c0 1-1 1-2 1l1 1-1 1h0-1-1 0c1-1 1-1 0-2 0-1 1-1 1-2l-1-1c1-1 5-1 6-2v1l-1 1h1l3-3h1v-1c-2 0-1 0-2-1-1 0-2 0-2-1 1-1 2-1 2-3v-2h0 0c1-1 0-3 0-4 0-3-2-2-3-3 0-1 1-1 1-1h0v-3c0-1-1-1-1-2l-1 1-1-1c1 0 1-1 2-1 0 0-2-2-2-3h-1c1-1 1-1 1-2-1-1-2 0-3 0 0 0 1-1 1-2l-1-1h1v-1c0-1 1-1 1-1 1-2 1-4 2-5 0-1 1-1 1-2-1 0-1-1-2-1l-4 1h0c-3 1-8 1-11-1-1 0-3 0-4-1-2-1-4-3-6-5l2-1-2-2c0-3-1-6 0-9l-1-1 1-1h-1-1-1c-1 1-2 2-3 2l-2-1v1h-2l-1 1v-1h1v-1l4-2c5-2 9-4 14-5h-5-2l2-1v-1h-1v-1h4 0 0c0-1 0-1-1-2-2-1-6-3-7-5h0c2 0 2 0 3-1h0v-1h1v-1h5c-2-1-4-1-6-1-1 0-1 0-2-1z" class="O"></path><path d="M252 323l1-1c0 3 1 8 0 11v-1c-2 1-3 3-4 5l-1-1 1-1h-1l1-1c1-1 1-1 1-2s0-1 1-2v-1s1 0 1-1h0-2v-1c1-1 1-2 2-4h0z" class="c"></path><defs><linearGradient id="u" x1="261.035" y1="296.76" x2="243.767" y2="305.875" xlink:href="#B"><stop offset="0" stop-color="#282c28"></stop><stop offset="1" stop-color="#3a363c"></stop></linearGradient></defs><path fill="url(#u)" d="M253 277v3 42l-1 1v-4c-1 0-1-1 0-2v-6-4c0-5 0-11-1-16 1-3 1-8 1-11h-1 0c1-1 1-1 2-1v-2z"></path><defs><linearGradient id="v" x1="252.752" y1="253.813" x2="243.056" y2="264.199" xlink:href="#B"><stop offset="0" stop-color="#161514"></stop><stop offset="1" stop-color="#2e3032"></stop></linearGradient></defs><path fill="url(#v)" d="M235 243c5 1 10 2 14 6 2 3 2 5 3 8 2 7 2 16 1 23v-3c-1 0-1-1-1-1v-1c0-3-1-5-1-8v-1c0-2 0-5-1-7 0-5-2-8-6-10-3-4-7-3-10-5l1-1z"></path><path d="M251 280h1c0 3 0 8-1 11 1 5 1 11 1 16v4 6c-1 1-1 2 0 2v4h0c-1 2-1 3-2 4v1h2 0c0 1-1 1-1 1l-1-1h-2c-1 1-1 1-2 1h0l2-2h-3 0c0 1-1 1-2 1l1 1-1 1h0-1-1 0c1-1 1-1 0-2 0-1 1-1 1-2l-1-1c1-1 5-1 6-2v1l-1 1h1l3-3h1v-1c-2 0-1 0-2-1-1 0-2 0-2-1 1-1 2-1 2-3v-2h0 0c1-1 0-3 0-4 0-3-2-2-3-3 0-1 1-1 1-1h0v-3c0-1-1-1-1-2l-1 1-1-1c1 0 1-1 2-1 0 0-2-2-2-3h-1c1-1 1-1 1-2-1-1-2 0-3 0 0 0 1-1 1-2l-1-1h1v-1c0-1 1-1 1-1 1-2 1-4 2-5 0-1 1-1 1-2-1 0-1-1-2-1h0l4-1h0l3-1h0z" class="E"></path><path d="M249 314h0l1 1h0c1 1 1 2 0 3v2l1 1v1-1c-2 0-1 0-2-1-1 0-2 0-2-1 1-1 2-1 2-3v-2h0z" class="B"></path><path d="M251 280h1c0 3 0 8-1 11 1 5 1 11 1 16v4 6l-1-10c-1 0-1-1-1-1v-1c-1-1 0-2 0-3h-1c0-2 1-2 0-3v-1-1c-1-1-1-2-2-2h-1 0c0-1 1-1 1-2h0-2c-1 0 0 0-1-1 1-1 1-2 2-4 2-1 2-2 2-3 1-1 1-2 1-3l-1-1h0l3-1h0z" class="T"></path><path d="M251 280h1c0 3 0 8-1 11 1 5 1 11 1 16-2-4 0-8-1-12-1-2-1-1-3-1v-1c1-1 2-1 2-2s0-1 1-1v-4h-1c1-2 2-4 1-6z" class="U"></path><path d="M226 243h9 0l-1 1c3 2 7 1 10 5 4 2 6 5 6 10 1 2 1 5 1 7v1c-1-1-2-3-3-4l-1 2-2-3c-1-2-3-3-5-4-3-1-5-1-8-2h-5-5-2l2-1v-1h-1v-1h4 0 0c0-1 0-1-1-2-2-1-6-3-7-5h0c2 0 2 0 3-1h0v-1h1v-1h5z" class="P"></path><path d="M245 256c1 0 2 1 2 2 2 2 3 6 4 8v1c-1-1-2-3-3-4 0-1-1-2-1-3v-2c-1 0-2-2-2-2z" class="L"></path><path d="M226 243h9 0l-1 1c3 2 7 1 10 5-2-1-5-2-8-3-4-2-10 0-15-2v-1h5z" class="e"></path><path d="M220 245h3l3 1c4 1 9 1 13 3 3 2 4 4 6 7 0 0 1 2 2 2v2c0 1 1 2 1 3l-1 2-2-3 1-1c0-2-2-4-3-6l-1-1s-1-1-2-1c-3-3-4-3-8-4-2-1-4-1-5-1h-1c-2 0-5-1-6-3h0z" class="E"></path><path d="M217 246h0c2 0 2 0 3-1 1 2 4 3 6 3h1c1 0 3 0 5 1 4 1 5 1 8 4 1 0 2 1 2 1l1 1c1 2 3 4 3 6l-1 1c-1-2-3-3-5-4-3-1-5-1-8-2h-5-5-2l2-1v-1h-1v-1h4 0 0c0-1 0-1-1-2-2-1-6-3-7-5z" class="O"></path><path d="M243 255c1 2 3 4 3 6l-1 1c-1-2-3-3-5-4-3-1-5-1-8-2h5c1 0 1 0 2 1h0 2c0 1 1 1 2 1v-1-2z" class="K"></path><path d="M225 253h1c0 1 0 1 1 1 0-1-1-1-1-2 2-1 6 0 8 0 2 1 3 2 3 3h0 0c-1 0-2-1-3-1l-1 1h-4-1c-2 1-4-1-6 1h-2l2-1v-1h-1v-1h4 0z" class="d"></path><path d="M217 246h0c2 0 2 0 3-1 1 2 4 3 6 3h1c1 0 3 0 5 1 4 1 5 1 8 4 1 0 2 1 2 1-1 0-2-1-3-2h-1c-4-2-8-2-13-2-1-1-3-1-4-2-2-1-2-2-4-2z" class="H"></path><path fill="#fff" d="M227 256h5c3 1 5 1 8 2 2 1 4 2 5 4l2 3 1-2c1 1 2 3 3 4 0 3 1 5 1 8v1s0 1 1 1v2c-1 0-1 0-2 1l-3 1h0l-4 1h0l-4 1h0c-3 1-8 1-11-1-1 0-3 0-4-1-2-1-4-3-6-5l2-1-2-2c0-3-1-6 0-9l-1-1 1-1h-1-1-1c-1 1-2 2-3 2l-2-1v1h-2l-1 1v-1h1v-1l4-2c5-2 9-4 14-5z"></path><path d="M228 266c1 1 1 1 1 2h0c0 1-1 1-1 2v-1c-1 1-1 1-2 1h0c0-2 1-3 2-4z" class="i"></path><path d="M236 270h0c1 0 1 0 2 1-1 2-1 3-4 4l2-5zm2-1v-2h1 2c1 1 2 1 3 3-2-1-2 0-4 0 0-1 0-1-1-2l-1 1z" class="g"></path><path d="M236 269v-3h2c0-1 0-1 1-1l2 2h-2-1v2 2c-1-1-1-1-2-1h0v-1z" class="i"></path><path d="M244 270h0v2c1 0 1-1 2-1l1 1c-1 2-2 3-3 5-2 1-4 3-7 4-2 0-4 0-6-1h5c2-2 3-3 5-4 1-2 3-4 3-6zm-20 6c-2-3-3-5-4-8l3-4c2 0 5 0 7 1s1 4 2 6h0c0 2 1 4 1 6-3-1-4-5-5-7 0-1 1-1 1-2h0c0-1 0-1-1-2v-1c-3 1-5 1-7 3h0c1 1 0 2 1 3 0 0 1 0 1 1s0 1 1 1v3z" class="g"></path><path d="M234 259h0 1c0 4 1 7 1 10v1l-2 5v2h-1 0c0-2-1-4-1-6v-7c1-2 1-3 2-5z" class="h"></path><path d="M235 259c2 0 5 2 7 3 2 2 5 5 5 8 1 0 0 1 0 1v1h0l-1-1c-1 0-1 1-2 1v-2h0 0c-1-2-2-2-3-3l-2-2c-1 0-1 0-1 1h-2v3c0-3-1-6-1-10z" class="Z"></path><path d="M219 264c4-2 7-3 12-3 0 1 0 1-1 2 1 0 2 0 2 1v7h0c-1-2 0-5-2-6s-5-1-7-1l-3 4c1 3 2 5 4 8 2 2 4 3 7 4 2 1 4 1 6 1h-4-2c-1 1-1 1-2 1s-3 0-4-1c-2-1-4-3-6-5l2-1-2-2c0-3-1-6 0-9z" class="O"></path><path d="M221 275c2 3 5 5 10 6-1 1-1 1-2 1s-3 0-4-1c-2-1-4-3-6-5l2-1zm6-19h5c3 1 5 1 8 2l3 3-1 1c-2-1-5-3-7-3h-1 0c-1 2-1 3-2 5 0-1-1-1-2-1 1-1 1-1 1-2-5 0-8 1-12 3l-1-1 1-1h-1-1-1c-1 1-2 2-3 2l-2-1v1h-2l-1 1v-1h1v-1l4-2c5-2 9-4 14-5z" class="U"></path><path d="M219 264c1-1 1-2 2-3 3-3 8-3 12-3l1 1c-1 2-1 3-2 5 0-1-1-1-2-1 1-1 1-1 1-2-5 0-8 1-12 3z" class="H"></path><defs><linearGradient id="w" x1="252.281" y1="272.57" x2="241.157" y2="271.175" xlink:href="#B"><stop offset="0" stop-color="#0b0b0b"></stop><stop offset="1" stop-color="#2b2a2a"></stop></linearGradient></defs><path fill="url(#w)" d="M240 258c2 1 4 2 5 4l2 3 1-2c1 1 2 3 3 4 0 3 1 5 1 8v1s0 1 1 1v2c-1 0-1 0-2 1l-3 1h0l-4 1h0l-4 1h0c-3 1-8 1-11-1 1 0 1 0 2-1h2 4c3-1 5-3 7-4 1-2 2-3 3-5h0v-1s1-1 0-1c0-3-3-6-5-8l1-1-3-3z"></path><path d="M248 263c1 1 2 3 3 4 0 3 1 5 1 8v1l-5-11 1-2z" class="C"></path><path d="M244 277h1v-1c1 0 2-3 3-3s1 1 1 2l-1 1v1 1l-1 1-2 1c-1 0-3 1-5 1v1h-3v1c1 0 3-1 3 0h0c-3 1-8 1-11-1 1 0 1 0 2-1h2 4c3-1 5-3 7-4z" class="P"></path><path d="M101 306c7-40 18-79 40-114 5-8 11-15 17-23 26-31 65-54 106-58 40-3 77 13 106 38 27 22 45 54 57 85 7 19 13 38 17 57 3 13 5 25 6 38l3-2c0-2-1-5-1-8l-4-24 1-2v1l1 1c2 5 2 11 3 16 0 0 3 13 3 15l1 1-3 1v-1c0 1 1 1 0 2 0 1-1 2-2 3h0c-1 2 0 3 0 5l1 1c1 1 1 2 2 3 1 0 3 1 4 1l-1-1c1-2 2-3 3-4 1 0 2-1 3 0h1c1 0 2 0 3 1l-1 1c1 1 1 2 2 3 0 0 1 0 2 1l-1 1c-1 1-2 2-2 3s-1 2-2 2h0v1c-2 0-4 1-5 0-1 0-1-1-1-1v-1c-1-1-5-3-6-4-2-2-4-4-7-5h1c1 0 1 0 1-1 0-2-1-5 0-7v-4 2c-1 0-1-2-1-3h-1v2-3l-1-10c-1-2-1-3-2-5-4-27-11-52-21-77-18-46-49-88-95-108-19-8-40-12-60-11-16 1-31 6-46 11-13 7-25 14-37 23-25 19-44 47-57 76-4 8-7 16-10 25-5 15-10 31-12 47l-4 24c0 3 0 6-1 8l-1-1v-4-2h0l-1-1c0-4 2-10 2-14z" class="L"></path><path d="M453 311s3 13 3 15h-1-1l-1-15z" class="B"></path><path d="M453 343l-3-3c-1-2-1-7 0-9 0-1 2-3 4-4 0 1 1 1 0 2 0 1-1 2-2 3h0c-1 2 0 3 0 5l1 1c1 1 1 2 2 3 1 0 3 1 4 1l-1-1c1-2 2-3 3-4 1 0 2-1 3 0h1c1 0 2 0 3 1l-1 1c1 1 1 2 2 3 0 0 1 0 2 1l-1 1c-1 1-2 2-2 3s-1 2-2 2h0v1c-2 0-4 1-5 0-1 0-1-1-1-1v-1l1-2h-1c-2 0-3-1-4-2-1 0-2-1-3-1z" class="Z"></path><path d="M465 339l1-1 1 1c0 2 0 2-1 3s-2 3-4 4h0-1-1c-2 0-3-1-4-2-1 0-2-1-3-1l2-1 5 3c1 0 2 0 3-1 2-1 3-3 3-4l-1-1z" class="M"></path><path d="M461 337c1 0 2-1 3 0h1c1 0 2 0 3 1l-1 1c1 1 1 2 2 3 0 0 1 0 2 1l-1 1c-1 1-2 2-2 3s-1 2-2 2h0v1c-2 0-4 1-5 0-1 0-1-1-1-1v-1l1-2h1 0c2-1 3-3 4-4s1-1 1-3l-1-1-1 1h-2c0-1-1-1-2-2z" class="W"></path><path d="M469 342s1 0 2 1l-1 1c-1 1-2 2-2 3s-1 2-2 2l-1-1 4-3v-3zM118 240h1 1v1l-1 1v2s0 1-1 1v4c-5 15-10 31-12 47l-4 24c0 3 0 6-1 8l-1-1v-4-2h0c1-15 4-29 7-43 3-12 7-26 11-38z" class="F"></path><path d="M222 123h0c-1 2-6 3-8 4h2c1 0 1-1 2-1h1 1c0-1 1-1 2-1-13 7-25 14-37 23-25 19-44 47-57 76-4 8-7 16-10 25v-4c1 0 1-1 1-1v-2l1-1v-1h-1-1l9-21c12-26 29-49 51-68l11-9c1-1 3-3 5-4s5-3 7-4l14-8c2-1 4-2 7-3z" class="K"></path><path d="M222 123c32-14 68-15 101-2 13 5 26 12 37 21 30 22 50 53 64 87 9 22 16 46 21 70 2 9 3 18 4 28v2c-1 0-1-2-1-3h-1v2-3l-1-10c-1-2-1-3-2-5-4-27-11-52-21-77-18-46-49-88-95-108-19-8-40-12-60-11-16 1-31 6-46 11-1 0-2 0-2 1h-1-1c-1 0-1 1-2 1h-2c2-1 7-2 8-4h0z" class="O"></path><path d="M241 330h1 1 0l1-1-1-1c1 0 2 0 2-1h0 3l-2 2h0c1 0 1 0 2-1h2l1 1v1c-1 1-1 1-1 2s0 1-1 2l-1 1h1l-1 1c-2 1-6 5-7 7-1 1-3 2-4 4l-8 12v1c0 1-1 2-1 4-1 2-2 5-3 8h0v3c0 2-2 4-3 6h0 0c0-2 1-3 1-5l-1-1c-1 2-2 3-4 4l-1-1h0c-1 1-2 3-2 4-1 0-1 1-1 1v1h-1 0c1 1 1 2 1 2l1 1c1-1 1-1 3-1 0 3-1 6-2 8v4h-1c-1-1 0-3-1-3 0 0-1 2-1 3v5 1l-2 1h-1l-1 2c0 1 0 1-1 0v-1h-1 0c-1-1 0-3-1-5 0-1-1-1-1-1-1 1-1 1-2 1h0c0-3 1-6 2-9 0-1 0-2-1-4 0 2 0 3-1 4l1-7h-1-1s-1 0-2-1h0v-1l-1 1c0 2-2 3-3 5h-1c0-1 1-2 1-3 0-3 1-6 2-9 0-1-1-1-2-2l-1 2-1 3v3h0c0 1 0 2-1 3v3l-2 6c-1 1-1 3-2 3h0v1l-1 3h0v1c-1 0-1 1-1 1v5l1 1h-1s1 2 0 2c-1 2-1 5-2 6-1 0-1-1-2-1v-1c-1 1-1 1-1 2l-2 3v2h0c0-1-1-2-1-2h-1s0 1-1 2h0v-3c1-1 1-3 1-4h1l1-1c-1-3 0-5 1-7v1h1c0-2 1-3 1-5v-1l-1-1c0-1 0-1-1-2v-1c-1 0-2 1-3 2 0-1 0-2 1-3s0-3 1-4l1-3c0-1-1-1-1-1v-1-3h-1c0-1 1-2 1-2 0-1 0-2-1-2 0-1 0-2-1-2v-1l3-3h0c1-1 1-2 1-3 0-2-1-2-2-3 5-6 9-15 16-19 0-1 1-2 2-3l1-1 2-1 2-1c0 2 0 3 1 5h1 2c1-1 3-2 4-3 5-5 12-9 19-12 4-2 7-4 11-5z" class="I"></path><path d="M209 391c0 1 1 1 1 1l-1 2s-1 0-1-1 0-1 1-2z" class="H"></path><path d="M196 375l1-2h1v2s0 1 1 2h-1c0-1-1-1-2-2z" class="N"></path><path d="M186 377l4-1-1 2h-1c-1 0-1 0-2 1s-1 1-2 1l1-1 1-2h0z" class="H"></path><path d="M211 364l3-3h1l-1 2-1 2h-1-1v1l-2 1c1-2 1-2 2-3z" class="V"></path><path d="M186 377c0-2 1-3 2-5 1 1 2 2 2 3h1l-1 1h0l-4 1z" class="W"></path><g class="G"><path d="M200 384c1-1 2-3 3-5 0-1 1-1 1-3h1 0c1 3-2 6-2 9h-1s-1 0-2-1z"></path><path d="M204 385c0-1 1-3 1-5l1 1 1 2v-2-1c1 0 1-1 1-2 2 1 2 1 3 2 0 1 0 1-1 2v1c0 1-1 2-2 3l-2 4v1h0v1c-1-1-1-1 0-2 0 0-1-1-1-2 1-1 2-3 2-4h-1-1l-1 4c0 2 0 3-1 4l1-7z"></path></g><path d="M211 398v-6h1c0-1-1-1-1-2s0-2 1-3c1 0 1 0 2-1l1 1c1-1 1-1 3-1 0 3-1 6-2 8 0-1 0-1-1-2-1 1-2 2-3 4v1s0 1-1 1z" class="H"></path><path d="M193 360c1 1 0 2 0 3s0 1-1 1c-1 1-1 3-2 4h1 0s1 1 0 1c0 1 0 3-1 4v2c0-1-1-2-2-3v-1h-3l1-1c0-1 1-1 1-2h-1 0l2-4 1-1h1 0l3-3z" class="N"></path><path d="M187 368h2 0 1c-1 1-1 3-2 3h-3l1-1c0-1 1-1 1-2z" class="J"></path><path d="M216 360c0 1-1 2-1 3s2 2 2 3l-1 1v1h3v1h-1c-1 0-2 2-2 3-1 1-2 4-3 6 0 1 0 2 1 3v1c-1-1-1-1-1-2h-2v-1-4h0l3-6c0-1-1-2-1-3 1-1 1-2 1-3l1-2 1-1z" class="E"></path><path d="M213 378v-1h-1c2-2 2-5 3-7 0-1 1-2 1-3v1h3v1h-1c-1 0-2 2-2 3-1 1-2 4-3 6z" class="V"></path><path d="M204 388l1-4h1 1c0 1-1 3-2 4 0 1 1 2 1 2-1 1-1 1 0 2-1 3-1 4 1 6h0c1 1 1 1 2 3 1-1 2-1 2-3 1 0 1-1 1-1v-1c1-2 2-3 3-4 1 1 1 1 1 2v4h-1c-1-1 0-3-1-3 0 0-1 2-1 3v5 1l-2 1h-1l-1 2c0 1 0 1-1 0v-1h-1 0c-1-1 0-3-1-5 0-1-1-1-1-1-1 1-1 1-2 1h0c0-3 1-6 2-9 0-1 0-2-1-4z" class="S"></path><path d="M207 398c1 1 1 1 2 3-1 0 0 1 0 1v1l-2 3v-8z" class="G"></path><path d="M182 381l2-1v1c0 1 0 1-1 2v1c1 0 1 0 1 1v1h1v-1l1 1c0 1 0 2-1 4h1l1-1c0 2-1 5-2 8 0 1 0 2-1 3 0 2 0 3-1 4h0v-1l-1-1c0-1 0-1-1-2v-1c-1 0-2 1-3 2 0-1 0-2 1-3s0-3 1-4l1-3c0-1-1-1-1-1v-1-3c0-1 1-2 1-3 1 0 1-1 1-2z" class="M"></path><path d="M181 400c1-1 2-2 2-3v-2h1c1 1 1 1 1 2s0 2-1 3c0 2 0 3-1 4h0v-1l-1-1c0-1 0-1-1-2z" class="J"></path><path d="M180 386c0-1 1-2 1-3v3c1 1 2 2 1 3 1 0 2 1 3 0v1l-3 6c0 1-1 2-1 3-1 0-2 1-3 2 0-1 0-2 1-3s0-3 1-4l1-3c0-1-1-1-1-1v-1-3z" class="F"></path><path d="M180 386c0-1 1-2 1-3v3c1 1 2 2 1 3v1s0 1 1 1h0c0 1 0 1-1 2-1-1-1-2-1-3v-1h-1v-3z" class="W"></path><defs><linearGradient id="x" x1="196.993" y1="354.619" x2="177.312" y2="379.906" xlink:href="#B"><stop offset="0" stop-color="#7c7b7b"></stop><stop offset="1" stop-color="#a4a3a3"></stop></linearGradient></defs><path fill="url(#x)" d="M196 351c0-1 1-2 2-3 0 2 0 3-1 3v1c1 1 1 1 0 2v1 1h1l-1 2c-1 1-1 2-1 2h-1c0-1 1-2 0-3-1 1-2 2-2 3l-3 3h0-1l-1 1-2 4h0 1c0 1-1 1-1 2l-1 1h3v1c-1 2-2 3-2 5h0l-1 2-1 1h0l-2 1c0 1 0 2-1 2 0 1-1 2-1 3h-1c0-1 1-2 1-2 0-1 0-2-1-2 0-1 0-2-1-2v-1l3-3h0c1-1 1-2 1-3 0-2-1-2-2-3 5-6 9-15 16-19z"></path><path d="M182 381v-2l1-1 2 1-1 1h0l-2 1z" class="J"></path><path d="M185 371h3v1c-1 2-2 3-2 5h0l-1 2-2-1c0-2 2-5 2-7z" class="O"></path><path d="M183 378l1-1h2l-1 2-2-1z" class="F"></path><path d="M196 351c0-1 1-2 2-3 0 2 0 3-1 3v1c1 1 1 1 0 2v1 1h1l-1 2c-1 1-1 2-1 2h-1c0-1 1-2 0-3-1 1-2 2-2 3l-3 3h0c1-3 3-7 5-10 1-1 1-1 1-2z" class="E"></path><path d="M196 351c0-1 1-2 2-3 0 2 0 3-1 3v1c1 1 1 1 0 2v1 1h1l-1 2h-1c-1-1 1-3 0-5h-1c1-1 1-1 1-2z" class="G"></path><defs><linearGradient id="y" x1="191.956" y1="394.151" x2="183.04" y2="392.694" xlink:href="#B"><stop offset="0" stop-color="#7a7979"></stop><stop offset="1" stop-color="#a1a0a1"></stop></linearGradient></defs><path fill="url(#y)" d="M184 400c1-1 2-1 2-2v-1h1v-1c0-2 1-3 2-4v-1c0-2 1-4 1-6 0-3 0-6 1-8v-1c1-3 3-5 5-7h1c0 2 0 2-1 3 0 1 0 1-1 2v2 1l-1 3v3h0c0 1 0 2-1 3v3l-2 6c-1 1-1 3-2 3h0v1l-1 3h0v1c-1 0-1 1-1 1v5l1 1h-1s1 2 0 2c-1 2-1 5-2 6-1 0-1-1-2-1v-1c-1 1-1 1-1 2l-2 3v2h0c0-1-1-2-1-2h-1s0 1-1 2h0v-3c1-1 1-3 1-4h1l1-1c-1-3 0-5 1-7v1h1c0-2 1-3 1-5h0c1-1 1-2 1-4z"></path><path d="M184 406h1l1 1h0v2h-1l-1 1-1-1h0l2-2h0l-1-1z" class="J"></path><path d="M179 416v1c1 0 2-2 2-2v-1l2-2h1v1c-1 1-1 3-1 4v-1c-1 1-1 1-1 2l-2 3v2h0c0-1-1-2-1-2h-1s0 1-1 2h0v-3c1-1 1-3 1-4h1z" class="H"></path><defs><linearGradient id="z" x1="209.896" y1="362.645" x2="197.995" y2="366.623" xlink:href="#B"><stop offset="0" stop-color="#585757"></stop><stop offset="1" stop-color="#706f6f"></stop></linearGradient></defs><path fill="url(#z)" d="M201 346l2-1c0 2 0 3 1 5h1 2c1-1 3-2 4-3h1l-3 3c-1 1-4 3-4 5l2 1h3c1 0 2 1 2 2v1h0 1 3v1l-1 1h-1l-3 3c-1 1-1 1-2 3 0 1-1 1-2 2-2 0-1-1-3-1v2c-1 1-1 2-1 4h0-1-1l-2-3v-6h-1l-1 1v-1h0c0-3 1-4 2-6v-2l-1-1h-1v-1-1c1-1 1-1 0-2v-1c1 0 1-1 1-3l1-1 2-1z"></path><path d="M204 370c-1 0-1 0-2-1v-2h1l1 1v2z" class="V"></path><path d="M197 354h1l2-2c1 0 1 0 1 1l1 2c0 1 0 2 1 3h2l-1 1h-1c0-1-1-1-1-2v1l1 1v1h-1-1l-1-1v1h0c-1 0-1 0-1-1v-2l-1-1h-1v-1-1z" class="C"></path><path d="M197 356l2-1c1 0 1 1 2 2 0 1 0 1-1 2v1h0c-1 0-1 0-1-1v-2l-1-1h-1z" class="E"></path><path d="M207 356h3c1 0 2 1 2 2v1h0 1 3v1l-1 1h-1l-3 3-2 2-2-2 1-3h0 2 0v-1c-1-1-1-2-2-2s-1 0-1-1h0c-1 1-2 0-2 0h0l1-1h1 0z" class="T"></path><path d="M201 346l2-1c0 2 0 3 1 5h1 2c1-1 3-2 4-3h1l-3 3c-1 1-4 3-4 5l2 1h0-1l-1 1h0v1h-2c-1-1-1-2-1-3l-1-2c0-1 0-1-1-1l-2 2h-1c1-1 1-1 0-2v-1c1 0 1-1 1-3l1-1 2-1z" class="P"></path><path d="M201 346l2-1c0 2 0 3 1 5h1 2l-3 4c0-2-2-2-2-4l-1-1v-3h0z" class="b"></path><path d="M241 330h1 1 0l1-1-1-1c1 0 2 0 2-1h0 3l-2 2h0c1 0 1 0 2-1h2l1 1v1c-1 1-1 1-1 2s0 1-1 2l-1 1h1l-1 1c-2 1-6 5-7 7-1 1-3 2-4 4l-8 12v1c-1 1-1 2-2 4l-1 2-1 2c0 1-1 3-1 4h-1v-1h-1c-1 0-1 1-2 1-1-1-1-2-1-3v-1h-3v-1l1-1c0-1-2-2-2-3s1-2 1-3v-1h-3-1 0v-1c0-1-1-2-2-2h-3l-2-1c0-2 3-4 4-5l3-3h-1c5-5 12-9 19-12 4-2 7-4 11-5z" class="h"></path><path d="M220 361v-1-1h0v-2s1 0 1-1h0v2c1 0 1-1 2-1h2l1 1c1 1 2 1 3 1h0v1c-1 1-1 2-2 4l-1 2v-1c-1 0-3 0-3-2l-1-1v-1h-2z" class="b"></path><path d="M222 362l1-1c1 1 1 1 1 2v1h2 0 1l-1 2v-1c-1 0-3 0-3-2l-1-1z" class="U"></path><path d="M243 333v1c-2 1-4 3-6 5-2 1-3 2-4 3-5 4-9 9-13 14h0c-1 1-2 3-2 4l1 2h1v-1h2v1l1 1c0 2 2 2 3 2v1l-1 2c0 1-1 3-1 4h-1v-1h-1c-1 0-1 1-2 1-1-1-1-2-1-3v-1h-3v-1l1-1c0-1-2-2-2-3s1-2 1-3v-1l1-2c1 0 1 0 1-1l7-8 3-3c3-2 6-6 9-8l3-3c1 0 2-1 3-1z" class="D"></path><path d="M225 368h-1c-1 1-1 1-2 1l-1-1c-1-1-1-1-1-2h0c-2-2-3-3-4-5 1-2 2-4 4-5h0c-1 1-2 3-2 4l1 2h1v-1h2v1l1 1c0 2 2 2 3 2v1l-1 2z" class="P"></path><path d="M241 330h1 1 0l1-1-1-1c1 0 2 0 2-1h0 3l-2 2h0c1 0 1 0 2-1h2l1 1v1c-1 1-1 1-1 2s0 1-1 2l-1 1c-1 0-2-2-3-2 0 0-1 0-2 1v-1c-1 0-2 1-3 1l-3 3c-3 2-6 6-9 8l-3 3-7 8c0 1 0 1-1 1l-1 2h-3-1 0v-1c0-1-1-2-2-2h-3l-2-1c0-2 3-4 4-5l3-3h-1c5-5 12-9 19-12 4-2 7-4 11-5z" class="h"></path><path d="M211 355c1-1 1-1 2 0v-1c-1-1-2-2-2-3 1 1 3 3 5 4 1 1 2 1 2 1 0 1 0 1-1 1s-1 0-3 1h-1 0v-3h-2z" class="c"></path><path d="M209 350c-1 2-2 3-4 4 2 1 3 0 3 1l1-1h1l1 1h2v3h0 1c2-1 2-1 3-1l-1 2h-3-1 0v-1c0-1-1-2-2-2h-3l-2-1c0-2 3-4 4-5z" class="X"></path><path d="M241 330h1 1 0l1-1-1-1c1 0 2 0 2-1h0 3l-2 2h0c1 0 1 0 2-1h2l1 1v1c-1 1-1 1-1 2s0 1-1 2l-1 1c-1 0-2-2-3-2 0 0-1 0-2 1v-1c0-1 1-1 1-2-3 1-5 1-7 2-7 2-13 6-19 10-2 1-4 3-6 4h-1c5-5 12-9 19-12 4-2 7-4 11-5z" class="B"></path><path d="M251 329v1c-1 1-1 1-1 2s0 1-1 2l-1 1c-1 0-2-2-3-2 0 0-1 0-2 1v-1c0-1 1-1 1-2 1 0 2-1 4-1h1c1-1 1-1 2-1z" class="P"></path><defs><linearGradient id="AA" x1="208.068" y1="450.471" x2="174.479" y2="411.411" xlink:href="#B"><stop offset="0" stop-color="#5f5f5f"></stop><stop offset="1" stop-color="#878786"></stop></linearGradient></defs><path fill="url(#AA)" d="M196 375c1 1 2 1 2 2-1 3-2 6-2 9 0 1-1 2-1 3h1c1-2 3-3 3-5l1-1v1h0c1 1 2 1 2 1h1 1l-1 7c1-1 1-2 1-4 1 2 1 3 1 4-1 3-2 6-2 9h0c1 0 1 0 2-1 0 0 1 0 1 1 1 2 0 4 1 5h0 1v1c1 1 1 1 1 0l1-2h1c1 2 1 4 1 6v1l-1 1c1 1 1 1 0 2h0v5l-1 3c1 1 1 2 0 3v-2h-1c0 1-1 2 0 2v1c1 1 0 2 0 2v1 1h0c1 1 0 3 0 4v1-2h-2l-1 2c1 2 1 4 0 5l1 1c0 1-1 2-1 2h0c-2 1-2 2-3 3l2-1 1 1c1 2 0 8 0 9v3 2c0 1 1 1 1 2 1 1 1 4 1 5s0 2 1 3v2 2 2h1c0 1-1 4 0 5v3h0c1 1 1 3 2 3v2 1c0 2 1 6 2 8 0 1 0 2 1 3 0 2 1 4 1 6h0 0v3 1c-1-1-1-2-1-3s-1-1-1-2c-1-1-1-2-2-4-1-1-1-2-1-3l-1 2h-2v-2l-1-3-1-3h-2l-1-3c0-3-4-8-6-11v2l-1 1-1-1v-1c-1 0-2-1-2-2l-1-1h0-1c-1-3-3-5-4-8l-1-3c-1-1-1-1-1-3l-1 4c0-2-1-2-2-3l-1-1v-1l1-1v-5c-1-1-1-2-2-3l2-2c-1-1-1-2-1-4-1-2-1-6-1-8v-2l-1 1h0v-3c1-2 1-4 0-6l1-2c0-2-1-3 0-5h0v-2l2-3c0-1 0-1 1-2v1c1 0 1 1 2 1 1-1 1-4 2-6 1 0 0-2 0-2h1l-1-1v-5s0-1 1-1v-1h0l1-3v-1h0c1 0 1-2 2-3l2-6v-3c1-1 1-2 1-3h0v-3l1-3 1-2z"></path><path d="M202 431c0 1 1 3 0 4v1l-1-1h0c0-2 0-3 1-4z" class="G"></path><path d="M188 421h1v4c-1 0-1 1-1 1v1l-1-2v2 3h0v-5c0-1 1-3 1-4z" class="H"></path><path d="M203 426v-2-1l-1-1c1-1 0-3 1-4h1v1l1-1v2c-1 1-1 2-1 3h-1c1 2 1 1 0 3zm-21-8c1 1 0 2 0 4 0 1 0 2-1 3 0 1 0 2-1 3 0-2-1-3 0-5h0v-2l2-3z" class="I"></path><path d="M191 465l1-1h0v-2h0v-3-1c1 1 1 3 1 4v1c0-1 0-1 1-1v-6-3c-1-2 0-5 0-7h1v4 4 7h0v-1h-1v3l-1 1-1 1h0-1z" class="C"></path><path d="M181 425c1 1 1 2 2 4 0 1-1 1-1 2 0 2 1 4 0 6-2 2-1 5-2 8 1 1 1 2 1 3-1-2-1-6-1-8v-2l-1 1h0v-3c1-2 1-4 0-6l1-2c1-1 1-2 1-3z" class="N"></path><defs><linearGradient id="AB" x1="178.171" y1="448.771" x2="186.082" y2="462.249" xlink:href="#B"><stop offset="0" stop-color="#807f7d"></stop><stop offset="1" stop-color="#a09ea1"></stop></linearGradient></defs><path fill="url(#AB)" d="M180 445l2 1v-2-1c-1-1-1-1 0-2h0 0c0 3 1 5 1 7 1 2 0 6 1 8 0 4 1 7 2 11-1-1-1-1-1-3l-1 4c0-2-1-2-2-3l-1-1v-1l1-1v-5c-1-1-1-2-2-3l2-2c-1-1-1-2-1-4 0-1 0-2-1-3z"></path><path d="M180 454l2-2v5c-1-1-1-2-2-3z" class="F"></path><path d="M184 456v-1-1-2h1v1l1-1h1c0 1 1 1 1 2 1 0 1 0 1 1 1 1 1 2 2 4 0 1-1 2 0 3-1 1-2 1-2 2s1 5 1 5c1 1 1 1 1 2 0 2 1 3 2 5l4 4v2l-1 1-1-1v-1c-1 0-2-1-2-2l-1-1h0-1c-1-3-3-5-4-8l-1-3c-1-4-2-7-2-11z" class="G"></path><path d="M187 470c1 1 3 0 3 1v1c0 1 1 2 1 3 1 2 2 3 2 4l-1-1h0-1c-1-3-3-5-4-8z" class="B"></path><defs><linearGradient id="AC" x1="194.374" y1="488.639" x2="220.878" y2="464.097" xlink:href="#B"><stop offset="0" stop-color="#3f3f3f"></stop><stop offset="1" stop-color="#5d5d5d"></stop></linearGradient></defs><path fill="url(#AC)" d="M196 448c0-1 1-2 1-3l1-1h0v4h1c0-1 0-2 1-2h1v4c1-1 1-1 1-2h1v-1l2-1 1 1c1 2 0 8 0 9v3 2c0 1 1 1 1 2 1 1 1 4 1 5s0 2 1 3v2 2 2h1c0 1-1 4 0 5v3h0c1 1 1 3 2 3v2 1c0 2 1 6 2 8 0 1 0 2 1 3 0 2 1 4 1 6h0 0v3 1c-1-1-1-2-1-3s-1-1-1-2c-1-1-1-2-2-4-1-1-1-2-1-3l-1 2h-2v-2l-1-3-1-3h-2l-1-3c0-3-4-8-6-11l-4-4c-1-2-2-3-2-5 0-1 0-1-1-2 0 0-1-4-1-5s1-1 2-2v3h1 0l1-1 1-1v-3h1v1h0v-7-4c0 1 0 1 1 1v-3z"></path><path d="M195 450c0 1 0 1 1 1v-3 13c0-2 0-5-1-7v-4z" class="G"></path><path d="M199 457h1l1 1h0-1c0 1 0 3 1 4v4l1-1h0c0 2 0 2-1 3v-1h-1c0 1 1 2 1 3v3 3c-2-4-1-7-2-11-1-3-1-5 0-8z" class="C"></path><path d="M206 487c1-1 1-1 1-2 1 1 0 1 0 2 0 0 1 1 2 1v4c1 2 1 5 2 7v1l-1 2h-2v-2l-1-3-1-3c0-2-2-6-1-8l1 1z" class="L"></path><path d="M206 494c0-2-2-6-1-8l1 1c1 3 2 6 3 10h-2l-1-3z" class="X"></path><defs><linearGradient id="AD" x1="217.477" y1="424.366" x2="180.548" y2="401.627" xlink:href="#B"><stop offset="0" stop-color="#696868"></stop><stop offset="1" stop-color="#888788"></stop></linearGradient></defs><path fill="url(#AD)" d="M196 375c1 1 2 1 2 2-1 3-2 6-2 9 0 1-1 2-1 3h1c1-2 3-3 3-5l1-1v1h0c1 1 2 1 2 1h1 1l-1 7c1-1 1-2 1-4 1 2 1 3 1 4-1 3-2 6-2 9h0c1 0 1 0 2-1 0 0 1 0 1 1 1 2 0 4 1 5h0 1v1c1 1 1 1 1 0l1-2h1c1 2 1 4 1 6v1l-1 1c1 1 1 1 0 2h0v5l-1 3c1 1 1 2 0 3v-2h-1c0 1-1 2 0 2v1c1 1 0 2 0 2v1 1h0c1 1 0 3 0 4v1-2h-2l-1 2c1 2 1 4 0 5l1 1c0 1-1 2-1 2h0c-2 1-2 2-3 3v1h-1c0 1 0 1-1 2v-4c0-1 1-2 2-3h-1l1-1v-1-1-4h-1 0v-1c1-1 0-3 0-4v-1l1-1h-1 0 0c0-2 0-2 1-3 1-2 1-1 0-3h1c0-1 0-2 1-3v-2h0 0v-2h-1l1 1h-1c-1-2 0-5 0-7-1 3-2 5-2 8-1 1-2 1-2 4v4c-1 1-1 2-1 4-1 2 0 9-2 11h-1 0c-1-1 0-5 0-6v-3-2c0 1 0 4-1 5-1 2 0 6 0 7v2h-1c-1 0-1-1-2-1v-2s0 1-1 1v-1h-1c0-1 0-3 1-3 0-4 0-7 1-10 0-1 0-3 1-4-1 0-1 0-1-1v-4h1l-1-1h0 0c0-2 1-4 0-6-1-1-2-3-2-4v-1s0 1-1 2v-3h-1l1-1h0c-1-1-1-2 0-2v-2-2-1h0c1 0 1-2 2-3l2-6v-3c1-1 1-2 1-3h0v-3l1-3 1-2z"></path><path d="M197 423l-1-1c0-1 0-3 1-4h1v1c0 1 0 1-1 1v1 2z" class="Q"></path><path d="M200 384c1 1 2 1 2 1h1c0 1-1 2-2 4h-1v-5h0z" class="S"></path><path d="M206 436h0c0-1 1-5 1-6h1c1 1 1 2 1 4h-2l-1 2z" class="G"></path><path d="M199 406h1v2c1 1 1 4 0 5l-1-1s-1 0-1-1h0l1-1v-4zm-2 15c1 2 0 5 0 7 0 3 0 5-1 7v-3-2l1-7v-2z" class="S"></path><path d="M192 428c0 1-1 3 0 4l1 1-1 1c0 2 0 6-1 7h0-1c0-1 0-3 1-3 0-4 0-7 1-10z" class="Q"></path><path d="M207 406h1v1c0 3-1 6-1 8 0 1 0 1 1 2v1c-1 0 0 1-1 2v-1s-1-2-1-3c0-2-1-4 0-5 1-2 1-3 1-5z" class="M"></path><path d="M198 394c0 1 1 1 1 2-1 4-1 8-1 12 0 2 0 4-1 6l-1 3c0-1 0-3-1-4v-1-2-1h0v-6h0c1 0 1 0 1-1l2-8z" class="R"></path><path d="M204 388c1 2 1 3 1 4-1 3-2 6-2 9v1c0 2-1 2-2 3h0v1-2h-1v2l-1-1c0-3 1-7 3-10 0-1 0-2 1-3s1-2 1-4z" class="B"></path><path d="M199 384c1 1 1 1 1 2-1 1-1 1 0 2h-1v1 2c0 1 0 2-1 3l-2 8c-1 0-1-1-2-2v-2-1c-1-1 0-7 1-8h1c1-2 3-3 3-5z" class="H"></path><path d="M208 407c1 1 1 1 1 0l1-2h1c1 2 1 4 1 6v1l-1 1c1 1 1 1 0 2h0v5l-1 3h-1v-4l-1 2h-1v-1c1-1 0-2 1-2v-1c-1-1-1-1-1-2 0-2 1-5 1-8z" class="Q"></path><path d="M209 419c0-3-1-7 0-10h1v4h1 0c-1 2-1 5 0 7l-1 3h-1v-4z" class="N"></path><path d="M330 202l3-1c4-2 6-3 10-2h3 2c0 1 1 1 1 2h-1l-1 1c1 1 2 1 3 1v-1c0 1 1 2 1 2 1 3 1 5 1 7h1 1l1 1c1 3 2 8 1 11h0v1c-1 1-1 3-2 4-1 2-1 3-3 3 0 3 2 4 3 6 0 1 0 1 1 2h1l4 2-1 1c-1-1-2-1-3-2l-1-1-1 1h0c1 1 1 2 1 3l2 2c0 1 1 1 1 1 1 0 1 1 2 1h0-2v1c0 1 1 1 1 1 0 1 0 2 1 2v1c1 2 4 5 5 5l1 1h1v1l-2-1-1 1c1 0 1 0 1 1l-1 1h1l2 4 1 1-1 1v2l-1 1 1 1v4c0 1 1 4 1 4 1 1 1 2 1 3h1c0 2 0 3 1 5v1h-1c0 1 1 1 1 2l3 3-1 1c1 1 1 3 3 5-1 0-1 1-2 1v5h-1c-1 0-1 1-2 1l1 1 1 1c-1 1-1 3-1 4v1-3h0c-2 0-3 0-4 1h0v1l-1-1c-3 0-5 0-8-1l-9-1c-9 0-19 0-28 1-1 0-2 0-3-1v-1c1-1 3-2 4-3 0-1 1-3 2-4 1-2 2-2 3-3v-1h-1c-1 1-2 1-3 0h-2 0l1-1 1-1h1 1c1-1 2-1 4-2v-1l1-1c1-1 1-2 2-2v-2h1c1 0 1-1 2-1 0-1 1-2 2-2-1-1-1-1-1-2h-5c-1-1-1-1-1-2h2c0-1 0-1-1-1v-1l2-2-1-1h0v-1c-2 0-3 1-5 2l3-4v-1c-1 0-1 1-2 2h0 0v-3c-2 5-4 8-9 10-2 1-5 1-7 1h0 1c1-1 1-1 1-2l1 1h0v-1c0-1 1-1 1-2l-2-2-1-9v-1-5h1l-1-1-2 1 1-1-1-1-1 1-4 2-3 3v-1-3c1-4 5-9 9-11 2-1 5-2 7-3h0l2-2h-1 0l1-1h-1c1-1 5-1 7-1-2-1-3 0-5-1h4v-1h2 10c1 0 2 0 4-1-1-1-1-3-1-5v-2l1 1v-1c-2-2-35-1-40-1l1-1c1 0 5-7 7-9 4-5 8-10 14-15l5-4z" class="h"></path><g class="e"><path d="M337 286c1 0 1-1 2-1v-1c1-1 1-2 1-3s0-1 1-1h0c1 1 1 0 1 1l-1 1c0 1 1 1 1 2-1 1-3 2-4 3l-1-1zm10-64h-1l-1-1h-3l-1 1c-1 0-1 0-2-1 1-1 3-1 4-2 0 0-1 0-1-1h1 1v1 1h0l3 2h0z"></path><path d="M341 266l1-1c1 0 2 1 3 2l1-1v1 2l1 1c-1 0-1 0-2-1v-2c-1 0-2 1-3 2l-1-1c1-1 1-1 1-2h-1z"></path></g><path d="M342 234v-2l1 1 2 8-1 1h-1v-3c-1-1-1-3-1-5z" class="d"></path><path d="M351 249h1v1l-1 1v1h0l1-1h1l-1 1c-1 1-2 1-4 1-1-1-1-1-1-2l1-1c1-1 2-1 3-1z" class="c"></path><path d="M351 223c1-1 2-1 3-1h0l1-1h0 0c1 0 0 0 1-1v3 1c-1 1-1 3-2 4v-2h0c0-1 0-1 1-2v-2h0l-1 1c0 1-1 1-1 2v1c-1 0-1-1-3 0h0l-1-1c1 0 1-1 2-1v-1h0z" class="b"></path><path d="M345 216l1 1 1 1s1 0 1 1c1 1 2 1 3 1 0 1 0 1-1 2-1 0-1 1-3 0h0l-3-2h0v-1c1 0 1 0 1-1h0v-1-1z" class="c"></path><path d="M354 212h1c1 3 2 8 1 11h0v-3c-1 1 0 1-1 1h0 0l-1 1h0c-1 0-2 0-3 1l-1-1c1-1 1-1 1-2h1c1 0 1 0 3-1 0-2 0-2-1-4h0c-1-1-1-1 0-3z" class="U"></path><path d="M331 245c1 0 3-1 4 0 1 0 3 1 4 1v1 1c-1 0-1-2-3-1 0 1-1 1-1 2h0 3c-1 1-2 1-3 1l-1-1h0l1-2-1 1c-2 0-2 0-3 1l-3 1h-1l1-1c2-1 4-2 5-4h-2z" class="c"></path><path d="M343 239v3h-11-4c-2-1-3 0-5-1h4v-1h2 10c1 0 2 0 4-1z" class="a"></path><path d="M326 260c1 0 2 0 2 1h0l1-1h1l1 1c1 0 2 1 3 1 0 0 1 0 1 1h0 0c-2 2-3 2-4 4 0 1-1 2-2 2 0-1 1-3 0-5h0c-1-2-2-3-3-4z" class="e"></path><path d="M331 267c0-1 0-3-1-4 0-1 0-2-1-2l1-1 2 2h1c1 1 1 1 2 1-2 2-3 2-4 4z" class="U"></path><path d="M338 212h5 1 1c0 1 1 1 1 2h1c0 1 0 1 1 2 0 0 0 1 1 1l-1 2c0-1-1-1-1-1l-1-1-1-1v1c-1 0-1 0-2-1-1 0-1 0-1-1l1 1 1-1-1-1c-1 1-1 1-2 1l-2-1v-1l1-1h-3 0 1z" class="b"></path><path d="M346 214h1c0 1 0 1 1 2 0 0 0 1 1 1l-1 2c0-1-1-1-1-1l-1-1-1-1h-1v-1h2v-1z" class="P"></path><path d="M355 243h0v-1c-2 0-3-1-4-3v-1c-1 0-1-1-1-2s0-1-1-2h0v-1-1-1h2c0 3 2 4 3 6 0 1 0 1 1 2h1l4 2-1 1c-1-1-2-1-3-2l-1-1-1 1h0c1 1 1 2 1 3z" class="c"></path><path d="M340 274h0 2 2c0-1 1-2 1-2v2h-1c0 1 0 1-1 2h-1l1 1c-1 1-1 2-2 3-1 0-1 0-1 1s0 2-1 3v1c-1 0-1 1-2 1l-1 1c-1 1-2 2-3 2v1l-1 1h-1c1-1 1-2 2-2v-2h1c1 0 1-1 2-1 0-1 1-2 2-2 1-1 1-2 1-3l1-1h0c0-2 0-2-1-3h-1l-1-1c1 0 2-1 3-1h-1l1-1z" class="X"></path><path d="M340 275h1c0 1-1 2-2 2h-1l-1-1c1 0 2-1 3-1z" class="L"></path><path d="M330 202h1c1 1 1 1 2 1-1 1-2 1-3 2h4 4l-1 1v1h1l1 2v1 1l-1 1h-1c-2 0-3 0-5-1h1c1 0 1 0 2 1v-2-1c-1 0-1 0-2-1v-2h-4c-2 0-2 1-4 0l5-4z" class="X"></path><path d="M353 211h1l1 1h-1c-1 2-1 2 0 3h0c1 2 1 2 1 4-2 1-2 1-3 1h-1c-1 0-2 0-3-1l1-2c-1 0-1-1-1-1-1-1-1-1-1-2v-2h0 1 1v1c2 0 3-1 4-2z" class="V"></path><path d="M353 211h1l1 1h-1l-1 1c-1 1-1 2-2 3h-1c0-1-1-2-1-3 2 0 3-1 4-2z" class="S"></path><path d="M347 212h0 1c0 1 0 1-1 2 1 1 1 1 2 1 0 1 0 2 1 2 0 0 1 1 2 1s1-1 2-1v-2h0c1 2 1 2 1 4-2 1-2 1-3 1h-1c-1 0-2 0-3-1l1-2c-1 0-1-1-1-1-1-1-1-1-1-2v-2z" class="D"></path><path d="M354 215c1 2 1 2 1 4-2 1-2 1-3 1h-1c-1 0-2 0-3-1l1-2c1 1 3 2 5 2v-1-3z" class="L"></path><path d="M317 263c0-2-1-3-1-4 2-1 6-1 8 0 1 0 1 1 2 1h0c1 1 2 2 3 4h0c1 2 0 4 0 5h0v-3l-1-1c-1-1-2-1-3-1h-3-1c-1 0-1-1-2-1s-1 0-2 1v-1z" class="M"></path><path d="M317 263l1-1h1c2-1 2-1 4-1l1 1c0 1 0 1 1 2h-3-1c-1 0-1-1-2-1s-1 0-2 1v-1z" class="J"></path><path d="M331 291h1l1-1v-1c1 0 2-1 3-2l1-1 1 1v1c-1 1-2 2-3 2 1 1 1 1 2 1 0 1-2 2-2 2-1 1-2 2-4 3-1 0-2 2-3 3-1 0-2 1-3 2 1-2 2-2 3-3v-1h-1c-1 1-2 1-3 0h-2 0l1-1 1-1h1 1c1-1 2-1 4-2v-1l1-1zm0-42c1-1 1-1 3-1l-1 1c1 1 1 1 2 1l1 1v1c1 0 2 1 3 1v1h0l2 1h-4c1 1 1 1 2 1 2 1 3 0 4 1 0 1-1 1-1 2 1 0 1 0 1-1h2c0 1 0 1 1 1v1c0 1-1 2-2 2l-1 1h0-4l-3-1c1 0 1-1 1-2h2c0-1 0-1 1-1h0v-2h-1c-1-1-1-1-3-1h0c0-1 1-1 1-2h-1l-2-1c0-1 0-1 1-1v-1c-1 0-2-1-3-1 0-1-1-1-1-1z" class="b"></path><path d="M340 257h2v1l-1 1h0 3 1l-2 2h0 1v1l-1 1h0-4l-3-1c1 0 1-1 1-2h2c0-1 0-1 1-1h0v-2z" class="X"></path><path d="M337 260h2 3v1c-1 0-2 0-3 1v1l-3-1c1 0 1-1 1-2zm1 6l1-1 1 1h1 1c0 1 0 1-1 2l1 1v1l1 1h0v1c-1 1-2 1-3 2l-1 1h1c-1 0-2 1-3 1s-1-1-2-1l1-1h2v-2c-1-1-2 0-3 0v-1l1-1c-1-1-2-1-4-1 2-2 3-3 6-3z" class="L"></path><path d="M338 266l1-1 1 1h1 1c0 1 0 1-1 2l1 1v1l1 1h0v1c-1 1-2 1-3 2l-1 1h1c-1 0-2 1-3 1s-1-1-2-1l1-1h2 0 2v-1h-1 0l-1-1h3 0 1v-1h-1c0-1 1-1 1-2h-4v-1h2 0v-1c-1 0-1-1-2-1z" class="P"></path><path d="M367 279h0v-1l-1-2-1-2v-1c0-1 0-1-1-1 0-1 0-1-1-2s-3-3-4-3-1-1-2-1v-2-1h-1 0 2v-1h0 1s1 0 1-1h1c0 2 1 4 3 5h1v-1h0v-1h1-1v-3l2 4 1 1-1 1v2l-1 1 1 1v4c0 1 1 4 1 4 1 1 1 2 1 3-1-1-1-2-2-3z" class="b"></path><path d="M331 249s1 0 1 1c1 0 2 1 3 1v1c-1 0-1 0-1 1l2 1h1c0 1-1 1-1 2h0c2 0 2 0 3 1h1v2h0c-1 0-1 0-1 1h-2c0 1 0 2-1 2l-6-3 1-1h3v-1h0-2v-2h-1 0c0-1 0-1-1-1l-1-1 2-1-1-1h-3c-1 0-1 1-2 1h-3l3-1 2-1h1l3-1z" class="P"></path><path d="M331 255c1 0 3-1 4 0h0s0 1-1 1v1h-2v-2h-1z" class="D"></path><path d="M331 249s1 0 1 1c1 0 2 1 3 1v1c-1 0-1 0-1 1l2 1h-3 1c0-1-1-1-1-2-1 0-1 0-1-1h0c-1 0-2-1-3 0h0c0-1-1-1-1-1l3-1z" class="U"></path><path d="M334 257l3 1v1h-2v1h2c0 1 0 2-1 2l-6-3 1-1h3v-1z" class="D"></path><path d="M332 269c2 0 3 0 4 1l-1 1v1c1 0 2-1 3 0v2h-2l-1 1c1 0 1 1 2 1l1 1h1c1 1 1 1 1 3h0l-1 1c0 1 0 2-1 3-1-1-1-1-1-2h-5c-1-1-1-1-1-2h2c0-1 0-1-1-1v-1l2-2-1-1h0v-1c-2 0-3 1-5 2l3-4v-1l1-2z" class="V"></path><path d="M331 272l3-1c1 1 1 2 1 3s-1 1-1 1v1l-1-1h0v-1c-2 0-3 1-5 2l3-4z" class="R"></path><path d="M338 277h1c1 1 1 1 1 3h0l-1 1c0 1 0 2-1 3-1-1-1-1-1-2v-1-1h-2v-1-1c1-1 2-1 3-1z" class="P"></path><path d="M358 248c0 1 1 1 1 1 0 1 0 2 1 2v1c1 2 4 5 5 5l1 1h1v1l-2-1-1 1c1 0 1 0 1 1l-1 1h1v3h1-1v1h0v1h-1c-2-1-3-3-3-5l1 1c-1-1-1-1-1-2h1 0l1-1c-3 0-3 2-5 3-1-1-2 0-3-1v-1-1l-1-1h-2v-1h2l-1-1v-1h0-1v-2s1 0 2-1h1 1l-1-1h2c0-1 1 0 1 0v-3z" class="U"></path><path d="M353 256l1-1c1-1 2-1 4-1l2 2c-1 1-1 1-2 1h-1-2v1h2l-1 1h-1l-1-1h-2v-1h2l-1-1z" class="b"></path><path d="M311 259l-1-1c2-3 5-6 9-7 2-1 3-1 6 0l-3 1h3c1 0 1-1 2-1h3l1 1-2 1 1 1c1 0 1 0 1 1h0 1v2h2 0v1h-3l-1 1c-1-1-1-1-2-1-4-2-9-1-13-1h-1l-2 1-1 1z" class="E"></path><path d="M331 255h0 1v2h2 0v1h-3c0-1 1-1 1-2-2-1-2 0-3 0l2-1z" class="V"></path><path d="M314 257v-1c0-1 1-1 2-2 1 0 3-1 4-1-2 1-3 2-5 4h-1z" class="B"></path><path d="M322 252h3c1 0 1-1 2-1h3l1 1-2 1 1 1c1 0 1 0 1 1l-2 1c-1-1-2-1-3-2l2-1-1-1s-1 0-1 1c-1 0-1-1-2-1s-3 2-5 2c1-1 3-1 3-2z" class="D"></path><path d="M320 246c1 0 5-2 6-1 0 0-1 0-1 1h0 0 2c2 0 3-1 4-1h2c-1 2-3 3-5 4l-1 1-2 1c-3-1-4-1-6 0-4 1-7 4-9 7l1 1-4 2-3 3v-1-3c1-4 5-9 9-11 2-1 5-2 7-3h0z" class="X"></path><path d="M307 261v-1c0-2 2-4 4-6s5-4 8-6c1 0 2-1 4-1v1h1 0 1 1c1 0 1 0 2 1l-1 1-2 1c-3-1-4-1-6 0-4 1-7 4-9 7l1 1-4 2z" class="L"></path><path d="M317 264c1-1 1-1 2-1s1 1 2 1h1 3c1 0 2 0 3 1l1 1v3 1c-2 5-4 8-9 10-2 1-5 1-7 1h0 1c1-1 1-1 1-2l1 1h0v-1c0-1 1-1 1-2v-2l1-3v-1c0-3 0-5-1-7z" class="g"></path><path d="M318 272h0c1 0 2-1 3-2h1c0 1 0 3-1 4 0 1-1 2-2 3-1 0-1 0-2-2l1-3zm3 4c1-1 2-5 3-6s2-1 3-1v1 2c-2 2-3 4-5 5l-1-1z" fill="#fff"></path><path d="M317 264c1-1 1-1 2-1s1 1 2 1h1 3c1 0 2 0 3 1l1 1v3 1c-2 5-4 8-9 10-2 1-5 1-7 1h0 1c1-1 1-1 1-2l1 1h0v-1c0-1 1-1 1-2v-2c1 2 1 2 2 2 0 1 0 1 1 1l1-2 1 1c2-1 3-3 5-5 0 0 1-1 1-2 1-1 0-1 0-2l-1-2c-2 1-6 1-7 1-1-1-1 0-1 0 0 1-1 2-1 4h0c0-3 0-5-1-7z" class="O"></path><path d="M317 277v-2c1 2 1 2 2 2 0 1 0 1 1 1l1-2 1 1c-1 1-2 2-4 2-1 0-1 1-2 1v-1c0-1 1-1 1-2z" class="i"></path><path d="M367 279c1 1 1 2 2 3h1c0 2 0 3 1 5v1h-1c0 1 1 1 1 2l3 3-1 1c1 1 1 3 3 5-1 0-1 1-2 1v5h-1c-1 0-1 1-2 1l1 1 1 1c-1 1-1 3-1 4v1-3h0c-2 0-3 0-4 1h0v1l-1-1c-3 0-5 0-8-1h4s2-3 2-4c1-1 2-1 2-1v-2c0-2 0-5 1-7 0-1 1-3 1-4-1-2-1-5-2-7v-6z" class="e"></path><path d="M371 290l3 3-1 1-1-2-1 1h0v3c1 1 1 2 1 4h-1 1v4h-3-1c0-1 1-3 2-4l1 1v-1c0-1-1-1-1-1v-8c1 0 1-1 1-1z" class="C"></path><path d="M372 304v-4h-1 1c0-2 0-3-1-4v-3h0l1-1 1 2c1 1 1 3 3 5-1 0-1 1-2 1v5h-1l-1-1z" class="K"></path><path d="M372 304l1 1c-1 0-1 1-2 1l1 1 1 1c-1 1-1 3-1 4v1-3h0c-2 0-3 0-4 1h0v1l-1-1c1-1 0-5 1-7h1 3z" class="G"></path><path d="M369 307l2-2v1l1 1-1 1h-1l-1-1z" class="W"></path><path d="M368 312c0-2 0-4 1-5l1 1h1l1-1 1 1c-1 1-1 3-1 4v1-3h0c-2 0-3 0-4 1h0v1z" class="B"></path><path d="M330 202l3-1c4-2 6-3 10-2h3 2c0 1 1 1 1 2h-1l-1 1c1 1 2 1 3 1v-1c0 1 1 2 1 2 1 3 1 5 1 7h1c-1 1-2 2-4 2v-1h-1-1 0v2h-1c0-1-1-1-1-2h-1-1-5l1-1v-1-1l-1-2h-1v-1l1-1h-4-4c1-1 2-1 3-2-1 0-1 0-2-1h-1z" class="L"></path><path d="M349 207c0 1 0 2 1 3-1 1-2 1-3 2h0c0-1-1-1-1-1l2-1v-1h-3l2-2 1 1 1-1z" class="E"></path><path d="M345 209h-3c-1 0-1 0-1-1l1-1s-1 0-2-1h0 5c1 1 0 1 1 1h1l-2 2z" class="D"></path><path d="M351 204c1 3 1 5 1 7h1c-1 1-2 2-4 2v-1h-1-1c1-1 2-1 3-2-1-1-1-2-1-3l-2-1c1-1 1-1 2-1v-1h2z" class="B"></path><path d="M349 212v-1h3 1c-1 1-2 2-4 2v-1z" class="N"></path><defs><linearGradient id="AE" x1="337.255" y1="197.809" x2="348.287" y2="205.357" xlink:href="#B"><stop offset="0" stop-color="#616161"></stop><stop offset="1" stop-color="#787878"></stop></linearGradient></defs><path fill="url(#AE)" d="M330 202l3-1c4-2 6-3 10-2h3 2c0 1 1 1 1 2h-1l-1 1c1 1 2 1 3 1v-1c0 1 1 2 1 2h-2v1c-1 0-1 0-2 1-1 0-2-1-3-1-1-1-2-1-4-1-1 1-2 0-2 0-2 1-3 1-4 1h-4c1-1 2-1 3-2-1 0-1 0-2-1h-1z"></path><path d="M347 202h0c0 1-1 1-2 1h-1v-1c1-1 3-1 5-1h-1l-1 1z" class="B"></path><path d="M338 204h-2v-1c2-1 4-2 6-2h1l-1 1 1 2h-3c-1 1-2 0-2 0z" class="S"></path><path d="M285 77c1 0 2 0 3 1v1h3c1 0 1 0 1 1 3 1 5 1 7 3h0c1 0 1 1 1 2h1s0 1 1 2v1c0 1 1 1 1 2l1-1c0-1 1-1 2-2 1 1 2 3 3 3 1-1 0-2 1-3 1 1 2 1 3 2l1-1h0l1 1h3l2 1h2c1 0 2 0 4 1 5 2 11 4 15 7-1 0-3-1-4-2h-1v1c1 1 3 2 3 3v2l-1 1h-1-1l1 1-1 1-1 1h0c0 1-1 2-1 3s1 1 2 2l-2 2-1 1c1 1 1 1 2 1s1 1 2 1c1-1 2-1 2-1l1 1v1c0 1 1 1 1 2h0l6 4c5 2 9 5 13 7l4 4c1 0 2 1 2 1h3c1 2 2 2 4 3 0 1 1 1 1 2 2 1 2 2 4 3 0-1 1-1 1-2l1-1v-2c1 0 2-1 2-1 2-1 2-1 3-1-1 1-3 2-4 3v1h1l2 1c2 1 4 1 5 3l1 1 2 2h1c1-1 4-4 4-5v-2h0c-1-1-1-2-2-3 2 1 4 2 5 2l2 2c0 1 1 2 1 3s1 2 1 4c0 0 1 1 1 2h0c1 2 3 5 4 5 1 2 2 3 4 4h0 1l7 8c1 1 3 3 3 4l4 5 1 3c1 3 2 5 3 7v5l1 4h0v6 11c0 3 1 6 1 9l1 4 5 13 1 3c1 2 2 3 3 4v1l-2-1v1l1 1-1 1c0 1 0 1-1 1v-1 6c1 2 1 3 0 4l1 6c2 9 5 17 7 26 0 1 0 3 1 5l-1-1v-1l-1 2 4 24c0 3 1 6 1 8l-3 2c-1-13-3-25-6-38-4-19-10-38-17-57-12-31-30-63-57-85-29-25-66-41-106-38-41 4-80 27-106 58-6 8-12 15-17 23-22 35-33 74-40 114l-1-1c0-2 1-7 2-8h0v-1h-2c1-1 1-2 1-3 0-2 1-4 2-6v-6l5-22v-1-2-1h-1c0-2 1-4 1-5-1-1-1-1-1-2h0l3-10-1 1v-1l-1 1h0l-2 2v-4l1-3 2-2c1-2 3-4 4-6l2-6 1-4 1-6 2-13-1-3h0c-1 1-1 4-1 6-1-2-1-3-2-4 0-1 1-3 1-4s0-2 1-2l1-1 1 1v-1-1c2-1 3-2 4-2l2 2 2 1 1-1c1-1 1-2 1-2 1-1 2-1 3-2-2-1-3-3-5-3 1-1 1-2 2-4l1-1v2 2 1h2 0l2 2c1 0 1 0 2-1s2-1 2-2c1-1 2-2 3-4 1-1 1-1 3-1l3-4c1-2 3-4 4-6l2-2c2-1 3-4 5-6 5-5 10-9 15-14v1c0 1 0 1 1 1 1-1 2-3 4-4v1c3-1 5-3 7-4h1c4-2 8-6 12-8 7-4 13-8 20-11 3-1 5-2 8-3 1-1 3-2 4-3 4-1 9-2 13-3v1h2l22-3c1-1 2-3 3-3l2-2-1-1h0c0-1 0-2-1-3h0-1 0c-1 1-2 1-2 2l-1-1h0l1-1v1l1-1c1-2 1-4 1-7 1 1 2 1 3 1v-2l1 1c1-1 2 0 3-1v-2h1 3c0-1 0-1-1-2 0-1 0-2 1-3l1 2 2-1 1-2v-1l1-1c1-1 1-2 0-4z" class="O"></path><path d="M111 247s0 1 1 1l-4 11v-1-2-1l1-5 2-3z" class="T"></path><path d="M394 169c1 1 2 1 3 1h0c1 2 2 3 3 5s4 5 6 8l-2 1c-1-2-3-4-4-6l-6-9z" class="D"></path><path d="M384 156c5 4 9 9 13 14-1 0-2 0-3-1-1 0-2-2-3-2l-8-10 1-1z" class="C"></path><path d="M119 224l1 2-8 22c-1 0-1-1-1-1l8-23z" class="P"></path><path d="M230 113c4-1 9-2 13-3v1h2l-18 6-1-1c1-1 3-2 4-3zm42-12c1 1 2 1 2 2 2-1 2 0 4 0l2 2h2c2 1 2 1 3 2l1 1h-19c1-1 2-3 3-3l2-2v-1-1z" class="T"></path><path d="M278 103l2 2c-1 0-1 1-2 1l-1-1 1-2z" class="V"></path><path d="M274 103c2-1 2 0 4 0l-1 2c-1 0-1 0-1 1l-1-1-1-2z" class="R"></path><path d="M272 101c1 1 2 1 2 2l1 2c-2 1-3 0-5 0l2-2v-1-1z" class="K"></path><path d="M272 101c1 1 2 1 2 2v1h-1l-1-1v-1-1z" class="F"></path><path d="M404 184l2-1 3 6c1 1 2 2 2 3l14 27-1-1v1l-20-35zm35 72l2 1v1l1 6c2 9 5 17 7 26 0 1 0 3 1 5l-1-1v-1l-1 2c-2-13-6-25-10-37l1-1v-1z" class="P"></path><path d="M439 256l2 1v1l1 6c-2-2-2-4-3-7v-1z" class="E"></path><path d="M424 219v-1l1 1 6 14c1 2 2 4 2 6l2 2c1 3 1 5 2 8l2 7v1l-1 1-14-39z" class="U"></path><path d="M143 181h1 1 0c-10 14-18 29-25 45l-1-2c3-7 6-14 10-21 4-8 9-16 14-22z" class="T"></path><path d="M272 91l1 1c1-1 2 0 3-1v-2h1l2 1h0v4h0v1c1 2 2 3 3 5 0 0-1 0-1-1h-1 0s-1 0-2-1v1l1 2v1l3 3h-2l-2-2c-2 0-2-1-4 0 0-1-1-1-2-2v1 1l-1-1h0c0-1 0-2-1-3h0-1 0c-1 1-2 1-2 2l-1-1h0l1-1v1l1-1c1-2 1-4 1-7 1 1 2 1 3 1v-2z" class="I"></path><path d="M270 99h0c1-2 1-5 2-5 1 1 0 2 1 3-1 0-1 1-1 1 0 1 0 2-1 3v1c0-1 0-2-1-3h0z" class="N"></path><path d="M278 99c-1-1-1-2-1-3 0 0 1 0 1-1v-1l1 1c1 2 2 3 3 5 0 0-1 0-1-1h-1 0s-1 0-2-1v1z" class="H"></path><path d="M272 101l1-2c0-1 1-1 1-2h1c2 2 2 3 4 5l3 3h-2l-2-2c-2 0-2-1-4 0 0-1-1-1-2-2z" class="M"></path><path d="M411 192l1-1h1 0c1 2 2 4 3 5l3 6c1 2 2 4 3 5l1 1c2 1 1 3 3 3l4 8c0-1 0-1-1-2h0v-1c1 1 1 2 2 2h1c1 2 1 5 3 7l5 13 1 3c1 2 2 3 3 4v1l-2-1v1l1 1-1 1c0 1 0 1-1 1v-1 6c1 2 1 3 0 4v-1l-2-1-2-7c-1-3-1-5-2-8l-2-2c0-2-1-4-2-6l-6-14-14-27z" class="Z"></path><path d="M437 249c2 3 3 5 4 8l-2-1-2-7z" class="I"></path><path d="M423 208c2 1 1 3 3 3l4 8 2 6c1 1 1 2 2 3-1 2-1 2 0 3l3 10c0 1 1 1 1 2l-1 1c-2-9-6-18-10-27-1-3-3-5-4-9z" class="K"></path><path d="M430 219c0-1 0-1-1-2h0v-1c1 1 1 2 2 2h1c1 2 1 5 3 7l5 13 1 3c1 2 2 3 3 4v1l-2-1v1l1 1-1 1c0 1 0 1-1 1v-1l-7-20c-1-1-1-2-2-3l-2-6z" class="X"></path><path d="M226 116l1 1c-18 7-34 16-48 28l-9 9c-9 8-17 17-25 27h0-1-1c4-5 7-10 12-14l-1-2-3 3-3 3-1 1v-1c1-2 3-4 4-6l2-2c2-1 3-4 5-6 5-5 10-9 15-14v1c0 1 0 1 1 1 1-1 2-3 4-4v1c3-1 5-3 7-4h1c4-2 8-6 12-8 7-4 13-8 20-11 3-1 5-2 8-3z" class="U"></path><defs><linearGradient id="AF" x1="170.197" y1="155.67" x2="163.823" y2="151.558" xlink:href="#B"><stop offset="0" stop-color="#67666d"></stop><stop offset="1" stop-color="#7c7b77"></stop></linearGradient></defs><path fill="url(#AF)" d="M178 142c3-1 5-3 7-4-4 5-9 8-13 12l-12 12c-2 1-3 3-5 5l-1-2c4-4 8-8 11-12 5-4 9-7 13-11z"></path><path d="M173 143v1c0 1 0 1 1 1 1-1 2-3 4-4v1c-4 4-8 7-13 11-3 4-7 8-11 12l-3 3-3 3-1 1v-1c1-2 3-4 4-6l2-2c2-1 3-4 5-6 5-5 10-9 15-14z" class="Z"></path><path d="M278 99v-1c1 1 2 1 2 1h0 1c0 1 1 1 1 1 3 1 7 2 10 3l13 3c2 1 6 1 8 3h0c0-1 1-1 1-1 1 0 2 0 3-1 0 0 1 1 1 2 1 0 2 1 3 1h1c1 1 3 2 4 2s1 0 2 1h1v-1l2-2v-1l1 1h1l1-1c0 1 1 1 2 2l-2 2-1 1c1 1 1 1 2 1s1 1 2 1c1-1 2-1 2-1l1 1v1c0 1 1 1 1 2h0l6 4h0-1l-1 1c1 0 2 1 3 1l6 4c3 1 5 4 8 5 1 1 3 4 5 4 0 1 0 1 1 2h-1l17 16-1 1c-5-6-11-11-17-16-11-8-22-15-34-20-3-1-6-3-9-4-12-4-24-7-37-9l-1-1c-1-1-1-1-3-2l-3-3v-1l-1-2z" class="X"></path><path d="M335 121h4c-1 0-1 0 0-1h1 0c0 1 1 1 2 1l1 1c1 0 2 1 3 1l-1 1c1 0 2 1 3 1l6 4c3 1 5 4 8 5 1 1 3 4 5 4 0 1 0 1 1 2h-1c-4-2-7-5-11-8-7-4-14-8-21-11z" class="Z"></path><path d="M278 99v-1c1 1 2 1 2 1h0 1c0 1 1 1 1 1 3 1 7 2 10 3l13 3c2 1 6 1 8 3h-1c1 1 2 1 3 2h0 1-8l-12-3c-6-1-13-2-17-7l-1-2z" class="J"></path><defs><linearGradient id="AG" x1="314.263" y1="109.26" x2="337.624" y2="122.841" xlink:href="#B"><stop offset="0" stop-color="#8a8a8b"></stop><stop offset="1" stop-color="#b2afb1"></stop></linearGradient></defs><path fill="url(#AG)" d="M317 107s1 1 1 2c1 0 2 1 3 1h1c1 1 3 2 4 2s1 0 2 1h1v-1l2-2v-1l1 1h1l1-1c0 1 1 1 2 2l-2 2-1 1c1 1 1 1 2 1s1 1 2 1c1-1 2-1 2-1l1 1v1c0 1 1 1 1 2h0l6 4h0-1c-1 0-2-1-3-1l-1-1c-1 0-2 0-2-1h0-1c-1 1-1 1 0 1h-4l-27-10h8-1 0c-1-1-2-1-3-2h1 0c0-1 1-1 1-1 1 0 2 0 3-1z"></path><path d="M335 115c1 0 1 1 2 1 1-1 2-1 2-1l1 1v1c0 1 1 1 1 2h0l-6-2v-2z" class="T"></path><path d="M317 107s1 1 1 2c1 0 2 1 3 1h1c1 1 3 2 4 2s1 0 2 1h1v-1l2-2v-1l1 1h1l1-1c0 1 1 1 2 2l-2 2-1 1c1 1 1 1 2 1v2c-3-1-7-3-10-4h-1c-3-1-6-1-8-2h-1 0c-1-1-2-1-3-2h1 0c0-1 1-1 1-1 1 0 2 0 3-1z" class="C"></path><path d="M329 112l2-2 1 3h-2-1v-1z" class="D"></path><path d="M334 109c0 1 1 1 2 2l-2 2c-1-1-2-1-2 0l-1-3v-1l1 1h1l1-1z" class="R"></path><path d="M313 109h0l12 4h-1c-3-1-6-1-8-2h-1 0c-1-1-2-1-3-2h1z" class="O"></path><path d="M147 171v1l1-1 3-3 3-3 1 2c-5 4-8 9-12 14-5 6-10 14-14 22-4 7-7 14-10 21l-8 23-2 3-1 5h-1c0-2 1-4 1-5-1-1-1-1-1-2h0l3-10-1 1v-1l-1 1h0l-2 2v-4l1-3 2-2c1-2 3-4 4-6l2-6 1-4 1-6 2-13-1-3h0c-1 1-1 4-1 6-1-2-1-3-2-4 0-1 1-3 1-4s0-2 1-2l1-1 1 1v-1-1c2-1 3-2 4-2l2 2 2 1 1-1c1-1 1-2 1-2 1-1 2-1 3-2-2-1-3-3-5-3 1-1 1-2 2-4l1-1v2 2 1h2 0l2 2c1 0 1 0 2-1s2-1 2-2c1-1 2-2 3-4 1-1 1-1 3-1l3-4z" class="F"></path><path d="M141 176c1-1 1-1 3-1l-8 10v-3c1-1 2-1 2-2 1-1 2-2 3-4z" class="D"></path><path d="M117 218c2-2 2-5 4-7 0 2-1 3-1 4h0c0 1-1 3-1 4-1 1-2 3-3 4v1l-1-1 2-5z" class="N"></path><path d="M119 210c0 1 0 1 1 1l1-1c0-1-1-2 0-3 0 0 1 0 2 1-1 0-2 3-2 3-2 2-2 5-4 7l-1-2 1-6h2z" class="T"></path><path d="M113 232c1 3-2 8-3 10 0 2-1 4-1 5v3l-1 5h-1c0-2 1-4 1-5-1-1-1-1-1-2h0l3-10c1 0 3-5 3-6z" class="O"></path><path d="M129 177l1-1v2 2 1h2 0l2 2c1 0 1 0 2-1v3l-7 11h-1-1l1-1s0-1 1-2h1v-2h1v-1-1h1l1-1h-3-1-1c1-1 1-2 1-2 1-1 2-1 3-2-2-1-3-3-5-3 1-1 1-2 2-4z" class="C"></path><path d="M130 188l3-3h0v3h-3z" class="S"></path><path d="M116 216l1 2-2 5h0c0 2 0 4-1 5 0 2-1 3-1 4s-2 6-3 6l-1 1v-1l-1 1h0l-2 2v-4l1-3 2-2c1-2 3-4 4-6l2-6 1-4z" class="U"></path><path d="M107 234c1 1 1 2 1 3l-1 1 1 1-2 2v-4l1-3z" class="G"></path><path d="M115 223c0 2 0 4-1 5 0 2-1 3-1 4s-2 6-3 6l-1 1v-1l-1 1c0-1 1-2 2-3 1-4 3-8 5-13z" class="B"></path><path d="M119 189v-1c2-1 3-2 4-2l2 2 2 1 1-1h1 1 3l-1 1h-1v1 1h-1v2h-1c-1 1-1 2-1 2l-1 1h1 1l-6 11v1c-1-1-2-1-2-1-1 1 0 2 0 3l-1 1c-1 0-1 0-1-1h-2l2-13-1-3h0c-1 1-1 4-1 6-1-2-1-3-2-4 0-1 1-3 1-4s0-2 1-2l1-1 1 1v-1z" class="I"></path><path d="M119 197h0c1 0 1 2 2 3l-2 2v1c1 0 2-1 2 0v1h-1c0 1-1 1-1 1h0v5h-2l2-13z" class="D"></path><path d="M122 196c0 1 1 1 1 2l1-1h1c1 1 1 1 2 0v-1h0v-1h1l-1 1h1 1l-6 11c0-1-1-3-1-4l2 1v-1-1h-1v-2c0-2 0-2-2-3l1-1z" class="E"></path><path d="M119 189v-1c2-1 3-2 4-2l2 2 2 1 1-1h1 1 3l-1 1h-1v1 1h-1v2h-1c-1 1-1 2-1 2h-1v1h0v1c-1 1-1 1-2 0h-1l-1 1c0-1-1-1-1-2l-1 1c0-1-1-1-1-2v-1l-2-2c0-1 0-1 1-2v-1z" class="R"></path><path d="M119 189c1 1 2 1 3 1 0-1 0-1 1-2h0v2c-1 1-1 0-2 0-1 2-1 2-1 4h0l2-1h0v3l-1 1c0-1-1-1-1-2v-1l-2-2c0-1 0-1 1-2v-1z" class="D"></path><path d="M119 189v-1c2-1 3-2 4-2l2 2 2 1c1 0 2 0 2 1h-2c-1 0-2-1-2-2h0-2 0c-1 1-1 1-1 2-1 0-2 0-3-1z" class="X"></path><path d="M285 77c1 0 2 0 3 1v1h3c1 0 1 0 1 1 3 1 5 1 7 3h0c1 0 1 1 1 2h1s0 1 1 2v1c0 1 1 1 1 2l1-1c0-1 1-1 2-2 1 1 2 3 3 3 1-1 0-2 1-3 1 1 2 1 3 2l1-1h0l1 1h3l2 1h2c1 0 2 0 4 1 5 2 11 4 15 7-1 0-3-1-4-2h-1v1c1 1 3 2 3 3v2l-1 1h-1-1l1 1-1 1-1 1h0c0 1-1 2-1 3l-1 1h-1l-1-1v1l-2 2v1h-1c-1-1-1-1-2-1s-3-1-4-2h-1c-1 0-2-1-3-1 0-1-1-2-1-2-1 1-2 1-3 1 0 0-1 0-1 1h0c-2-2-6-2-8-3l-13-3c-3-1-7-2-10-3-1-2-2-3-3-5v-1h0v-4h0l-2-1h3c0-1 0-1-1-2 0-1 0-2 1-3l1 2 2-1 1-2v-1l1-1c1-1 1-2 0-4z" class="I"></path><path d="M301 99h1c1 0 1 0 1 1l-1 1c-1 0-1-1-1-2z" class="G"></path><path d="M307 92h1l1 1v1l-3 1v-2s1 0 1-1z" class="F"></path><path d="M318 103c-1 0-1-1-1-1l1-2c1 1 1 1 2 1v2h-1-1zm-12-8l3-1 1 2-2 2c-1-1-1-2-2-3z" class="J"></path><path d="M321 97c1 0 1 0 2 2-1 0-1 1-1 1l-2 1c-1 0-1 0-2-1l3-3z" class="a"></path><path d="M303 90l1-1c0 1 0 1 1 2v1 2c-1 0-1-1-1-1-1 1-2 1-2 2-1 1-3 2-4 3-1-1-1-1-1-2h2l3-3v-1c1-1 1-1 1-2z" class="C"></path><path d="M312 95c1 1 2 1 3 1 0 1 0 1-1 2l1 1h0l-2 2h0-1c-1 1-2 1-3 2 0-1 1-2 2-3-1 0-1 0-2-1 0-1 1-2 2-3h0l1-1z" class="J"></path><path d="M312 95c1 1 2 1 3 1 0 1 0 1-1 2-1 0-1 1-2 1h-1v-3l1-1z" class="a"></path><path d="M313 93c1 0 1 0 1 1 0 0 1 0 1 1v1h1l1-1 1 1v-1c1 0 1 0 2 1-1 1-1 1-2 1l-1 1 1 1-3 3c-1 0-1 0-2-1l2-2h0l-1-1c1-1 1-1 1-2-1 0-2 0-3-1l1-2z" class="F"></path><path d="M321 97l1-2v1c1 1 1 1 2 1h0c1 0 1 1 2 1v2h2l-1 2h1c-1 0-2 1-3 2v1-2l-1 1h-1c0-1 0-1-1-1v-3h0s0-1 1-1c-1-2-1-2-2-2z" class="K"></path><path d="M318 103h1c0 2-1 3-2 4s-2 1-3 1c0 0-1 0-1 1h0c-2-2-6-2-8-3h1c3 1 3-1 5-2 1 0 2 2 3 2l1-1c1 0 3-1 3-2zm-14-14c0-1 1-1 2-2 1 1 2 3 3 3 1-1 0-2 1-3 1 1 2 1 3 2v2h0c0 1-1 1-1 2h1l-1 2-1 1h0-1l-1-2v-1l-1-1h-1-2v-1c-1-1-1-1-1-2z" class="E"></path><path d="M309 93s0 1 1 1c0-1 1-1 1-2h-1v-2c2 0 2 0 3 1h0c0 1-1 1-1 2h1l-1 2-1 1h0-1l-1-2v-1z" class="N"></path><path d="M313 89l1-1h0l1 1h3l2 1h2c1 0 2 0 4 1 5 2 11 4 15 7-1 0-3-1-4-2h-1v1c-1-1-2-1-4-1h0l-1-1c-1 0-2 1-3 1-1 1-1 1-2 1 0-1 2-1 1-3h-1c-1 0-1 1-2 1l-1-2v-1l-1-1c-1 0-1 1-1 1h0c-2-1-3 0-5-1v-1l-1-1c-1 1-1 1-1 2h-1 0v-2z" class="L"></path><defs><linearGradient id="AH" x1="298.855" y1="104.375" x2="299.696" y2="97.352" xlink:href="#B"><stop offset="0" stop-color="#626262"></stop><stop offset="1" stop-color="#7a7979"></stop></linearGradient></defs><path fill="url(#AH)" d="M294 97v-1l1-1s1 1 2 1c0 1 0 1 1 2h1v-1c1 1 2 1 2 2s0 2 1 2v1c0 1 1 1 2 1 0 1 1 2 2 3h-1l-13-3v-1l1-1h1v-3-1z"></path><path d="M294 97c1 1 2 1 2 2h0v1c-1 0-1 0-2 1v-3-1z" class="D"></path><path d="M329 99h1c1 0 1-1 2-1l1 1c1 0 1-1 2-1l1 1c0 1 0 1-1 2 0 1 0 1 1 2h-1l1 2-1 1h0c0 1-1 2-1 3l-1 1h-1l-1-1c0-1-1-1-1-1v-1h2 0v-1c-1 0-2 0-3 1h-1-1l-2-2v-1c1-1 2-2 3-2h-1l1-2h-2v-2c1 0 2 0 2 1h1z" class="G"></path><path d="M326 98c1 0 2 0 2 1h1l-1 2h0c1 0 1 0 1-1h1v3l-2-1h-1l1-2h-2v-2z" class="Q"></path><path d="M329 107l1-3c1 0 1-1 2-1v-1l1 1v1 1 3h-1 0v-1h0v-1c-1 0-2 0-3 1z" class="S"></path><path d="M325 105v-1c1-1 2-2 3-2l2 1-2 4h-1l-2-2z" class="O"></path><path d="M333 104l2-1 1 2-1 1h0c0 1-1 2-1 3l-1 1h-1l-1-1c0-1-1-1-1-1v-1h2v1h0 1v-3-1z" class="H"></path><path d="M322 100h0v3c1 0 1 0 1 1h1l1-1v2l2 2h1 1c1-1 2-1 3-1v1h0-2v1s1 0 1 1v1l-2 2v1h-1c-1-1-1-1-2-1s-3-1-4-2h-1c-1 0-2-1-3-1 0-1-1-2-1-2 1-1 2-2 2-4h1v-2l2-1z" class="I"></path><path d="M326 109l-1-1 1-1h1c0 1 1 1 0 2h-1z" class="G"></path><path d="M322 107c1 0 1 1 2 1v1h-1l-2 1v-1c0-1 0-1 1-2z" class="M"></path><path d="M330 108s1 0 1 1v1l-2 2v-4h1z" class="G"></path><path d="M322 100h0v3c1 0 1 0 1 1h1v2h-1c-1-1 0-1-1-2-1 0-1-1-2-1v-2l2-1z" class="N"></path><path d="M285 77c1 0 2 0 3 1v1h3c1 0 1 0 1 1 3 1 5 1 7 3h0c1 0 1 1 1 2h1s0 1 1 2v1c0 1 1 1 1 2s0 1-1 2v1l-3 3h-2c-1 0-2-1-2-1l-1 1v1 1 3h-1l-1 1v1c-3-1-7-2-10-3-1-2-2-3-3-5v-1h0v-4h0l-2-1h3c0-1 0-1-1-2 0-1 0-2 1-3l1 2 2-1 1-2v-1l1-1c1-1 1-2 0-4z" class="S"></path><path d="M291 89h3l1 3c-1 0-2-1-3-2l-1-1zm6 3h-1c0-1-1-2-2-4h3c1 1 3 2 4 3s1 1 1 2c-1 0-1 0-1-1h-4z" class="M"></path><path d="M297 88c1 1 3 2 4 3h-3c-1-1-1-1-1-3z" class="Q"></path><path d="M297 83h2c1 0 1 1 1 2h1s0 1 1 2v1c-2 1-3 0-5-1 0-1-1-1-2-1l-1-2 2-1h1z" class="F"></path><path d="M300 85h1s0 1 1 2v1c-2 1-3 0-5-1 1-1 1-1 1-2 1 1 1 1 0 1l1 1h1v-2z" class="W"></path><path d="M296 83h1c0 1 1 1 1 2s0 1-1 2c0-1-1-1-2-1l-1-2 2-1z" class="K"></path><path d="M286 90h1c1 0 1 2 2 2l1 1c-1 1-1 2-2 3h-1l-2 1v-2l-1-3v-1c1-1 1-1 2-1z" class="C"></path><path d="M284 91c1-1 1-1 2-1v2 1 2h1v1l-2 1v-2l-1-3v-1z" class="D"></path><path d="M279 94c1 0 1-1 2-1v-2h1v1h1v-2l1 1v1l1 3v2l2-1h1c0 2 0 3 1 4 1 0 1 1 2 1 0 0 1 0 1 1v1c-3-1-7-2-10-3-1-2-2-3-3-5v-1h0z" class="H"></path><path d="M284 92l1 3c0 1-1 1-1 2l-1-1v-3l1-1z" class="W"></path><path d="M279 95v-1c1 2 2 3 3 5 1 0 2 1 3 1 1-1 1-1 0-2v-1l2-1h1c0 2 0 3 1 4 1 0 1 1 2 1 0 0 1 0 1 1v1c-3-1-7-2-10-3-1-2-2-3-3-5z" class="G"></path><path d="M291 89l1 1c1 1 2 2 3 2v1h2 1l-1-1h4c0 1 0 1 1 1l-3 3h-2c-1 0-2-1-2-1l-1 1v1 1 3h-1l-1 1c0-1-1-1-1-1-1 0-1-1-2-1-1-1-1-2-1-4 1-1 1-2 2-3l2-1v-1l-1-1v-1z" class="Q"></path><path d="M293 101c0-1-1-2-1-3 0 0 0-1 1-1 0 0 0 1 1 1h0v3h-1z" class="E"></path><path d="M285 77c1 0 2 0 3 1v1h3c1 0 1 0 1 1 3 1 5 1 7 3h0-2-1l-2 1 1 2h-2l1 1c-1 1-1 1-3 1h-2c-1 1-1 1-2 1l-1-1c-1 1-2 1-3 2v2h-1v-1h-1v2c-1 0-1 1-2 1v-4h0l-2-1h3c0-1 0-1-1-2 0-1 0-2 1-3l1 2 2-1 1-2v-1l1-1c1-1 1-2 0-4z" class="H"></path><path d="M292 80c3 1 5 1 7 3h0-2-1c-1-1-2-1-3 0-1-1-2-2-3-2l-1-1h3z" class="J"></path><path d="M289 83v-1l1-1v1l1 1c1 2 1 3 0 5h-2 0c-1-1-1-2-1-3v-1l1-1z" class="I"></path><path d="M285 77c1 0 2 0 3 1v1h3c1 0 1 0 1 1h-3l1 1-1 1v1l-1 1c-1-1-2-1-4-2l1-1c1-1 1-2 0-4z" class="N"></path><path d="M285 81l1 1 2-1v1l1 1-1 1c-1-1-2-1-4-2l1-1z" class="O"></path><path d="M284 83v-1c2 1 3 1 4 2v1c0 1 0 2 1 3h0c-1 1-1 1-2 1l-1-1c-1 1-2 1-3 2v2h-1v-1h-1v2c-1 0-1 1-2 1v-4h0l-2-1h3c0-1 0-1-1-2 0-1 0-2 1-3l1 2 2-1 1-2z" class="M"></path><path d="M284 83v-1c2 1 3 1 4 2v1c-1 1-2 1-3 1s-2 0-2-1l1-2z" class="J"></path><path d="M284 83l2 2-1 1c-1 0-2 0-2-1l1-2z" class="O"></path><path d="M347 123c5 2 9 5 13 7l4 4c1 0 2 1 2 1h3c1 2 2 2 4 3 0 1 1 1 1 2 2 1 2 2 4 3 0-1 1-1 1-2l1-1v-2c1 0 2-1 2-1 2-1 2-1 3-1-1 1-3 2-4 3v1h1l2 1c2 1 4 1 5 3l1 1 2 2h1c1-1 4-4 4-5v-2h0c-1-1-1-2-2-3 2 1 4 2 5 2l2 2c0 1 1 2 1 3s1 2 1 4c0 0 1 1 1 2h0c1 2 3 5 4 5 1 2 2 3 4 4h0 1l7 8c1 1 3 3 3 4l4 5 1 3c1 3 2 5 3 7v5l1 4h0v6 11c0 3 1 6 1 9l1 4c-2-2-2-5-3-7h-1c-1 0-1-1-2-2v1h0c1 1 1 1 1 2l-4-8c-2 0-1-2-3-3l-1-1c-1-1-2-3-3-5l-3-6c-1-1-2-3-3-5h0-1l-1 1c0-1-1-2-2-3l-3-6c-2-3-5-6-6-8s-2-3-3-5h0c-4-5-8-10-13-14l-17-16h1c-1-1-1-1-1-2-2 0-4-3-5-4-3-1-5-4-8-5l-6-4c-1 0-2-1-3-1l1-1h1 0z" class="T"></path><path d="M403 165c1 1 2 1 3 2h1c0 1 1 2 1 2h0c1 1 1 2 1 3-3-1-4-4-6-7z" class="N"></path><path d="M408 177l-3-3v-1c1 1 2 1 2 1 1 1 2 0 2 0 1 1 2 1 2 2h2 1v3h-1l-2-2h-3z" class="Q"></path><path d="M396 160c0-1 0-2-1-2l1-1 3 5c0 1 1 2 2 3h0c1 1 3 3 3 5h0l-8-10z" class="B"></path><path d="M408 177h3l2 2h2 1 0c1 1 1 1 2 1v1h-2v2 1h-3c-2-2-4-4-5-7z" class="R"></path><path d="M413 184h3c0 1 0 2 1 3l1 1v1c1 0 2 0 2 1v1h0 1 3s0-1 1-1h2 0c-1 1-1 1-2 1s-1 1-2 1l1 1c0 1-1 1-1 2l1 1h-1l-1 1v1 1c2 2 2 4 4 5h0c1 0 1 1 2 1v1c-1 3 1 4 1 6h0c-2-2-3-4-4-6-1-1-2-2-2-3h0c-1-2-1-4-2-5-1-2-5-6-5-7l1-1c-2-1-2-4-4-6z" class="G"></path><path d="M422 197v-2-1c0-1-1-1-2-1l1-1h1v1l1-1 1 1c0 1-1 1-1 2l1 1h-1l-1 1z" class="B"></path><path d="M424 186c1-1 2-1 3 0h2c1 1 2 3 3 4v1l1 4h0v6 11c0 3 1 6 1 9l1 4c-2-2-2-5-3-7s-3-4-3-6-2-3-1-6v-1c-1 0-1-1-2-1h0c-2-1-2-3-4-5v-1-1l1-1h1l-1-1c0-1 1-1 1-2l-1-1c1 0 1-1 2-1s1 0 2-1h0-2c-1 0-1 1-1 1h-3-1 0v-1l1-2h1c0-1 1-1 2-2z" class="G"></path><path d="M427 195h2v2h1c0 2 1 6 0 7h0v-2-1l-1 1h-1v-4c0-1 0-2-1-3z" class="B"></path><path d="M430 197v-3h1c1 2 1 3 1 5v1c0 1 0 5-1 6h0l-1-2c1-1 0-5 0-7z" class="K"></path><path d="M433 195h0v6 11c-2-1-2-1-2-3v-3h0c1-1 1-5 1-6v-1h0c1-1 1-3 1-4z" class="N"></path><path d="M428 204c1 1 2 1 2 3v2h1c0 2 0 2 2 3 0 3 1 6 1 9l1 4c-2-2-2-5-3-7s-3-4-3-6-2-3-1-6v-1-1z" class="B"></path><path d="M424 186c1-1 2-1 3 0h2c1 1 2 3 3 4v1l1 4c0 1 0 3-1 4h0c0-2 0-3-1-5h-1v3h-1v-2h-2c0-1-1-1-2-1v1c0 1 0 1 1 1 1 2 1 3 1 4v2c0 1 0 1 1 2v1c-1 0-1-1-2-1h0c-2-1-2-3-4-5v-1-1l1-1h1l-1-1c0-1 1-1 1-2l-1-1c1 0 1-1 2-1s1 0 2-1h0-2c-1 0-1 1-1 1h-3-1 0v-1l1-2h1c0-1 1-1 2-2z" class="R"></path><path d="M424 186c1-1 2-1 3 0h2c1 1 2 3 3 4v1l1 4c0 1 0 3-1 4h0c0-2 0-3-1-5v-1c-2 0-1 1-3 1l-1-1h-1v-1h2 0 1l-1-1c1-1 1-1 1-2s-1-1-1-2h-3v1h2c-1 1-2 1-2 1-1-1-1-2-1-3z" class="P"></path><path d="M413 159h1l7 8c1 1 3 3 3 4l4 5 1 3c1 3 2 5 3 7v5-1c-1-1-2-3-3-4h-2c-1-1-2-1-3 0s-2 1-2 2h-1l-1 2c0-1-1-1-2-1v-1l-1-1c-1-1-1-2-1-3v-1-2h2v-1c-1 0-1 0-2-1h0-1-2 1v-3h-1-2c0-1-1-1-2-2l1-1 1-1h0c1 0 1 0 2-1h-1v-1h2v-1c1 0 2-1 2-2h1v-1l-1-1 1-1c-1-1-1-2-2-3l-2-2z" class="I"></path><path d="M424 171l4 5 1 3c0 1-1 1-2 1h0v-1c0-1 0-2-1-2 0-1-1-2-1-3h-2l1-1v-2z" class="P"></path><path d="M416 179c2-1 3 0 5-1h0l-1-1v-1c0-1 0-2 1-3h0l1 1 2 2v2h0l2-1c1 0 1 1 1 2v1h0c-1-1-2-1-2-1l-2 2c-2 1-3 3-5 4l3 3-1 2c0-1-1-1-2-1v-1l-1-1c-1-1-1-2-1-3v-1-2h2v-1c-1 0-1 0-2-1h0z" class="C"></path><path d="M423 181l2-2s1 0 2 1h0 0c1 0 2 0 2-1 1 3 2 5 3 7v5-1c-1-1-2-3-3-4h-2c-1-1-2-1-3 0s-2 1-2 2h-1l-3-3c2-1 3-3 5-4z" class="M"></path><path d="M423 181l2-2s1 0 2 1h0 0c1 0 2 0 2-1 1 3 2 5 3 7-1 0-1-1-1-1-1-1-2-3-4-3 0 0 0-1-1-1h0-2-1z" class="L"></path><path d="M413 159h1l7 8c1 1 3 3 3 4v2l-1-1h-2 0l-1-1v-1l-1-1c-1 2-2 3-2 5-2 1-3 1-4 1v1h-2c0-1-1-1-2-2l1-1 1-1h0c1 0 1 0 2-1h-1v-1h2v-1c1 0 2-1 2-2h1v-1l-1-1 1-1c-1-1-1-2-2-3l-2-2z" class="C"></path><path d="M419 169h0v-2c1 0 1 0 2 1 0 1 0 1-1 2l-1-1z" class="G"></path><path d="M417 164c1 1 1 2 1 4s-1 3-2 4c-2 1-3 1-5 1v-1h0c1 0 1 0 2-1h-1v-1h2v-1c1 0 2-1 2-2h1v-1l-1-1 1-1z" class="M"></path><path d="M347 123c5 2 9 5 13 7l4 4c1 0 2 1 2 1 10 8 19 16 27 25 9 10 17 22 24 34l6 12c1 2 2 3 3 5-2 0-1-2-3-3l-1-1c-1-1-2-3-3-5l-3-6c-1-1-2-3-3-5h0-1l-1 1c0-1-1-2-2-3l-3-6c-2-3-5-6-6-8s-2-3-3-5h0c-4-5-8-10-13-14l-17-16h1c-1-1-1-1-1-2-2 0-4-3-5-4-3-1-5-4-8-5l-6-4c-1 0-2-1-3-1l1-1h1 0z" class="J"></path><path d="M397 170c4 2 7 7 10 11h-1 0c-1-2-2-3-3-4s-1-2-2-3h-1v1c-1-2-2-3-3-5z" class="M"></path><path d="M380 138c1 0 2-1 2-1 2-1 2-1 3-1-1 1-3 2-4 3v1h1l2 1c2 1 4 1 5 3l1 1 2 2h1c1-1 4-4 4-5v-2h0c-1-1-1-2-2-3 2 1 4 2 5 2l2 2c0 1 1 2 1 3s1 2 1 4c0 0 1 1 1 2h0c1 2 3 5 4 5 1 2 2 3 4 4h0l2 2c1 1 1 2 2 3l-1 1 1 1v1h-1c0 1-1 2-2 2v1h-2v1h1c-1 1-1 1-2 1h0l-1 1-1-1c0-1 0-2-1-3h0s-1-1-1-2h-1c-1-1-2-1-3-2h0l-1-1-1 1h0c-1-1-2-2-2-3l-3-5-1 1c1 0 1 1 1 2-2-2-4-5-7-6l-3-3c-1-2-3-5-6-6 0 0 0-1-1-1 1-1 1-2 0-3h0l1-1v-2z" class="R"></path><path d="M399 146h-1v-4h1l1 2-1 2zm-2 1c0 1 0 1 1 2 0 1-2 2-3 3v2h-1v-1c0-1 0-1 1-2v-2l2-2z" class="B"></path><path d="M389 144l1 1v2l3 2-1 1c-1 0-1 0-2-1v1l1 1-1 1c-1 0-1-2-2-2l1-1c-1-2-1-2-2-2v-1c1 0 2-1 2-2h0z" class="I"></path><path d="M392 150l1-1-3-2v-2l2 2h1 0 2c1 0 1-1 2-1h0v1l-2 2c-1 1-3 2-4 2l1-1z" class="D"></path><path d="M400 150c0-1 1-1 2-1h1v-1l2 2c-1 1-2 1-2 2v-1c-2 0-3 1-3 2l-3 3v1c-1-1-1-1-2-1l1-1c0-2 0-3 2-4l2-1z" class="N"></path><path d="M395 137c2 1 4 2 5 2l2 2c0 1 1 2 1 3s1 2 1 4c0 0 1 1 1 2h0 0l-2-2v1h-1c-1 0-2 0-2 1 0-2-1-3-1-4l1-2 1-1c0-1 0-1-1-2s-1-1-3-1h0c-1-1-1-2-2-3z" class="L"></path><path d="M397 156l3-3c0-1 1-2 3-2v1c1 1 1 3 2 3v1c1 0 2 1 3 2 0 0 1 0 1 1h1v1l-1 1-6-3v-1c-1 0-2-1-3-1 1 1 1 2 2 3l-1 1c-2-1-3-2-4-4z" class="F"></path><path d="M397 156l3-3c0-1 1-2 3-2v1c1 1 1 3 2 3v1-1c-1 0-2-1-3-2-1 0-2 1-3 2l1 1h0c1 1 1 2 2 3l-1 1c-2-1-3-2-4-4z" class="U"></path><path d="M405 150h0c1 2 3 5 4 5 1 2 2 3 4 4h0l2 2c1 1 1 2 2 3l-1 1c-2-1-4-3-5-3-1-1-1-1-2-1l1-1v-1h-1c0-1-1-1-1-1-1-1-2-2-3-2v-1c-1 0-1-2-2-3 0-1 1-1 2-2z" class="J"></path><path d="M400 156c1 0 2 1 3 1v1l6 3c1 0 1 0 2 1 1 0 3 2 5 3l1 1v1h-1c0 1-1 2-2 2v1h-2v1h1c-1 1-1 1-2 1h0l-1 1-1-1c0-1 0-2-1-3h0s-1-1-1-2h-1c-1-1-2-1-3-2h0l-1-1-1 1h0c-1-1-2-2-2-3 1-1 1-1 2-1v-1l1-1c-1-1-1-2-2-3z" class="M"></path><path d="M402 159v1h2c1 1 1 1 2 1s2 1 3 2v1h-1c0-1-1-1-2 0v-1-1h-2 0-1c-1 1-1 1-2 0v-1-1l1-1z" class="D"></path><path d="M401 161v1c1 1 1 1 2 0h1 0v1s1 2 2 2c0 0 1 0 1 1h4l1 2-1 1h-1-2 0s-1-1-1-2h-1c-1-1-2-1-3-2h0l-1-1-1 1h0c-1-1-2-2-2-3 1-1 1-1 2-1z" class="C"></path><path d="M403 158l6 3c1 0 1 0 2 1 1 0 3 2 5 3l1 1v1h-1c0 1-1 2-2 2v1h-2v1h1c-1 1-1 1-2 1h0l-1 1-1-1c0-1 0-2-1-3h2 1l1-1-1-2h0v-1c-1-4-5-4-8-6v-1z" class="B"></path><path d="M408 169h2l1 1h0v2h0l-1 1-1-1c0-1 0-2-1-3z" class="J"></path><path d="M288 79l1-2c1 0 2 1 3 1v-1c1 1 1 1 2 1h0 1 1v-1l1 1c0 1 1 2 2 2h1c1-1 1 0 3 0l9 1c3 0 7 1 10 2 9 3 19 6 28 11 5 2 9 5 13 6 1 1 2 2 3 2 8 5 16 10 24 16l4 4c1 2 4 4 6 5l5 6c1 1 2 1 3 2 1 3 3 4 5 6 2 3 4 5 7 8 0 0 1 1 2 1 0 1 1 2 1 2 2 2 6 6 6 8 1 1 2 3 2 4h1s0 1 1 1l2 2v1 1c1 0 1 0 1 1l2 4 1 1c1 2 2 4 4 5 1 1 4 6 4 8 1 0 0 1 1 1 0 2 1 3 2 5l4 8c1 1 2 3 2 4l8 17c1 3 2 5 3 8 0 1 1 2 1 3l11 36c1 2 1 5 2 7l3 17c1 2 1 5 1 7 1 9 3 17 3 25 0 2-1 3 0 5l-1 2c0 1 0 1-1 2h-1s0-1 1-1v-2c-1 0-1 2-2 2l-1-1v3h-1l-1 2h0c-2 0-3-1-3-2l-2 2-3-2-3-2c-1-1-3-2-5-3h-4c-2 2-5 4-6 6h1c0 1-1 1-1 2h0l-1-1h-1l-1-1c0-2-1-3 0-5h0c1-1 2-2 2-3 1-1 0-1 0-2v1l3-1-1-1c0-2-3-15-3-15-1-5-1-11-3-16-1-2-1-4-1-5-2-9-5-17-7-26l-1-6c1-1 1-2 0-4v-6 1c1 0 1 0 1-1l1-1-1-1v-1l2 1v-1c-1-1-2-2-3-4l-1-3-5-13-1-4c0-3-1-6-1-9v-11-6h0l-1-4v-5c-1-2-2-4-3-7l-1-3-4-5c0-1-2-3-3-4l-7-8h-1 0c-2-1-3-2-4-4-1 0-3-3-4-5h0c0-1-1-2-1-2 0-2-1-3-1-4s-1-2-1-3l-2-2c-1 0-3-1-5-2 1 1 1 2 2 3h0v2c0 1-3 4-4 5h-1l-2-2-1-1c-1-2-3-2-5-3l-2-1h-1v-1c1-1 3-2 4-3-1 0-1 0-3 1 0 0-1 1-2 1v2l-1 1c0 1-1 1-1 2-2-1-2-2-4-3 0-1-1-1-1-2-2-1-3-1-4-3h-3s-1-1-2-1l-4-4c-4-2-8-5-13-7l-6-4h0c0-1-1-1-1-2v-1l-1-1s-1 0-2 1c-1 0-1-1-2-1s-1 0-2-1l1-1 2-2c-1-1-2-1-2-2s1-2 1-3h0l1-1 1-1-1-1h1 1l1-1v-2c0-1-2-2-3-3v-1h1c1 1 3 2 4 2-4-3-10-5-15-7-2-1-3-1-4-1h-2l-2-1h-3l-1-1h0l-1 1c-1-1-2-1-3-2-1 1 0 2-1 3-1 0-2-2-3-3-1 1-2 1-2 2l-1 1c0-1-1-1-1-2v-1c-1-1-1-2-1-2h-1c0-1 0-2-1-2h0c-2-2-4-2-7-3 0-1 0-1-1-1h-3z" class="Y"></path><path d="M469 245l5 17h-2v-2l-1-2-2-1v-1c-1-2-1-3-1-4h2c-1-2-1-5-1-7z" class="R"></path><path d="M468 252h2l1 6-2-1v-1c-1-2-1-3-1-4z" class="U"></path><path d="M465 239h0c0-1 0-2-1-3l-2-6c0-2-2-4-1-6l8 21c0 2 0 5 1 7h-2l-2-5c0-2-1-4-2-5 0-1 0-2 1-3z" class="C"></path><path d="M478 274l6 34c-1 2-1 3-1 5l-2-5v-5l-3-17c0-2-1-5-1-7l1-5z" class="E"></path><path d="M469 257l2 1 1 2v2h2l4 12-1 5c0 2 1 5 1 7 0 1 0 2-1 3v1h0c0-2-1-5-2-8l-1-2-1-1c0-2-1-4-2-5 2-2 0-3 0-4v-2-1c-1-1-1-2-2-3l1-2-1-1-1-3 1-1z" class="X"></path><path d="M470 262c1 1 1 2 2 3l-1 2c-1-1-1-2-2-3l1-2z" class="P"></path><path d="M469 257l2 1 1 2h-1l-1-1v1s-1 0-1 1l-1-3 1-1z" class="b"></path><path d="M471 268c1 1 2 3 2 4l2 10-1-2-1-1c0-2-1-4-2-5 2-2 0-3 0-4v-2z" class="G"></path><path d="M472 262h2l4 12-1 5c-3-6-3-11-5-17z" class="V"></path><path d="M446 198h1 0 2c3 5 6 12 8 17 2 3 3 6 4 9-1 2 1 4 1 6l2 6c1 1 1 2 1 3h0c-1 1-1 2-1 3 1 1 2 3 2 5v5c-1-2-1-3-2-5-1-1-1-4-2-5l-1-2c-1-2-2-5-2-8l-1-4c0-1 0-1-1-1l2-2-13-27z" class="R"></path><path d="M461 230c1 2 2 5 3 7l1 2c-1 1-1 2-1 3 0-2-2-4-2-6v-1h0c0-1 0-1-1-2v-3z" class="L"></path><path d="M459 225l2 5v3l-1-1h-1l-1-4c0-1 0-1-1-1l2-2z" class="U"></path><path d="M459 232h1l1 1c1 1 1 1 1 2h0v1c0 2 2 4 2 6 1 1 2 3 2 5v5c-1-2-1-3-2-5-1-1-1-4-2-5l-1-2c-1-2-2-5-2-8z" class="X"></path><defs><linearGradient id="AI" x1="306.292" y1="91.31" x2="321.306" y2="77.548" xlink:href="#B"><stop offset="0" stop-color="#666367"></stop><stop offset="1" stop-color="#828380"></stop></linearGradient></defs><path fill="url(#AI)" d="M288 79l1-2c1 0 2 1 3 1v-1c1 1 1 1 2 1h0 1 1v-1l1 1c0 1 1 2 2 2h1c4 2 8 2 12 3 8 2 17 4 25 8h-2-4l-4-1c-1 0-1 0-1 1-2-1-3-1-4-1h-2l-2-1h-3l-1-1h0l-1 1c-1-1-2-1-3-2-1 1 0 2-1 3-1 0-2-2-3-3-1 1-2 1-2 2l-1 1c0-1-1-1-1-2v-1c-1-1-1-2-1-2h-1c0-1 0-2-1-2h0c-2-2-4-2-7-3 0-1 0-1-1-1h-3z"></path><path d="M317 87l10 3c-1 0-1 0-1 1-2-1-3-1-4-1h-2l-2-1-1-1h1l-1-1h0z" class="b"></path><path d="M301 85v-1c2-1 3 0 4 1 3 0 5 0 8 1 1 0 3 0 4 1h0l1 1h-1l1 1h-3l-1-1h0l-1 1c-1-1-2-1-3-2-1 1 0 2-1 3-1 0-2-2-3-3-1 1-2 1-2 2l-1 1c0-1-1-1-1-2v-1c-1-1-1-2-1-2z" class="X"></path><path d="M301 85v-1c2-1 3 0 4 1h0c-1 0-2 0-3-1l-1 1h1c1 1 1 1 2 1h0l1 1v-1h0 1v1c-1 1-2 1-2 2l-1 1c0-1-1-1-1-2v-1c-1-1-1-2-1-2z" class="U"></path><path d="M481 303v5l2 5c0-2 0-3 1-5 0 2 1 4 0 5l2 9 1 4h1c0 2-1 3 0 5l-1 2c0 1 0 1-1 2h-1s0-1 1-1v-2c-1 0-1 2-2 2l-1-1v3h-1l-1 2h0c-2 0-3-1-3-2l-2 2-3-2c1-1 1-1 2-1l1-4 2 1 1-9c1-3 0-6 1-9v-1-5h0c0-2 1-3 1-5z" class="P"></path><path d="M481 303v5c0 1 0 4-1 5h0v-5h0c0-2 1-3 1-5z" class="X"></path><path d="M484 308c0 2 1 4 0 5v3 2 6c-1-3-1-8-1-11 0-2 0-3 1-5z" class="R"></path><path d="M478 336v-2h1 1v-4c1-1 1-3 1-5 0-1 0-3 1-4 0 2-1 3 0 5 0 1 0 4-1 5v1c-1 1 0 3-1 4 0 1 0 1 1 2h0c-2 0-3-1-3-2z" class="L"></path><path d="M484 313l2 9 1 4h1c0 2-1 3 0 5l-1 2c0 1 0 1-1 2h-1s0-1 1-1v-2c-1 0-1 2-2 2l-1-1c1-3 1-6 1-9v-6-2-3z" class="I"></path><path d="M486 322l1 4h1c0 2-1 3 0 5l-1 2c-2-3-1-8-1-11z" class="O"></path><path d="M480 314c2 2 2 4 2 7-1 1-1 3-1 4 0 2 0 4-1 5v4h-1-1v2l-2 2-3-2c1-1 1-1 2-1l1-4 2 1 1-9c1-3 0-6 1-9z" class="C"></path><path d="M476 331l2 1c-1 1-1 2-2 3h-1l1-4z" class="E"></path><path d="M471 280l2-1 1 1 1 2c1 3 2 6 2 8h0v-1c1-1 1-2 1-3l3 17c0 2-1 3-1 5h0v5 1c-1 3 0 6-1 9l-1 9-2-1v-1-4-3l-1-1v-1c0-1 0-3-1-5 0-2 0-2 1-4l-1-1c0-1 1-1 1-1v-2c0-1-1-1-1-3h2l-1-5-1-5-1-8-2-7z" class="B"></path><path d="M478 303v-1h0 0c0 1 0 1 1 2v2c0 1 0 2 1 2v5 1c-1 3 0 6-1 9 0-3 1-5 0-8 0-1-1-2-1-3 0-3-1-6 0-9z" class="E"></path><path d="M474 311c0-1 1-1 1-1v-2c0-1-1-1-1-3h2c1 6 1 12 0 19 0 2 1 4 0 6v-4-3l-1-1v-1c0-1 0-3-1-5 0-2 0-2 1-4l-1-1z" class="L"></path><defs><linearGradient id="AJ" x1="477.588" y1="290.196" x2="472.145" y2="290.326" xlink:href="#B"><stop offset="0" stop-color="#686868"></stop><stop offset="1" stop-color="#817f80"></stop></linearGradient></defs><path fill="url(#AJ)" d="M471 280l2-1 1 1 1 2c1 3 2 6 2 8h0v-1c1-1 1-2 1-3l3 17c0 2-1 3-1 5h0c-1 0-1-1-1-2v-2c-1-1-1-1-1-2h0 0v1c-1-3-2-7-3-10l-1 2-1-8-2-7z"></path><path d="M474 280l1 2c1 3 2 6 2 8h-1c0-1-1-1-1-2l-1-8z" class="R"></path><path d="M478 286l3 17c0 2-1 3-1 5l-3-18v-1c1-1 1-2 1-3z" class="b"></path><path d="M457 227c1 0 1 0 1 1l1 4c0 3 1 6 2 8l1 2c1 1 1 4 2 5 1 2 1 3 2 5v-5l2 5c0 1 0 2 1 4v1l-1 1 1 3 1 1-1 2c1 1 1 2 2 3v1 2c0 1 2 2 0 4-2-2-3-5-6-6-1-1-2-1-3-2h-3l-1 1v-1h-2c-2-1-3-2-5-2-1-1-1-2-1-3h0-2l-1 1h1l-2 2-5-15c1 0 1 0 1-1l1-1-1-1v-1l2 1v-1c-1-1-2-2-3-4l-1-3v-3-1l1 1h0l1 1c1 1 1 2 2 3h1c1 1 3 4 4 5h1l-1-1 1-2-3-3h2l2 2v-1l1-1v-3-2c1-2 3-4 4-5l1-1h0z" class="I"></path><path d="M466 247l2 5c0 1 0 2 1 4v1l-1 1 1 3 1 1-1 2v-1c-2-3-3-7-7-9v-3h1l1 1v-5c1 2 1 3 2 5v-5z" class="C"></path><path d="M466 247l2 5c0 1 0 2 1 4v1l-1 1-2-6v-5z" class="e"></path><path d="M448 261c0-1-1-2-1-3h0l6 2v1c1 0 2 0 4 1 1 0 3 0 4 1l5 2 2 1c1 1 2 4 3 5-3-3-6-5-10-6h0c-2 1-3 1-5 1-2-1-3-2-5-2-1-1-1-2-1-3h0-2z" class="a"></path><path d="M453 261c1 0 2 0 4 1 1 0 3 0 4 1-1 0-2 1-4 0h0-3c0-1-1-1-1-1l-1 1v-1h1v-1h0z" class="d"></path><path d="M448 261c0-1-1-2-1-3h0l6 2v1h0v1h-1v1l2 1c2 0 5 0 6 1h1c-2 1-3 1-5 1-2-1-3-2-5-2-1-1-1-2-1-3h0-2z" class="g"></path><path d="M457 253l2 3c1 1 2 2 3 2 2 2 5 4 6 7h0c-1 0-1 0-1-1-1 0-1 0-2-1 1 1 1 2 1 2l-5-2c-1-1-3-1-4-1-2-1-3-1-4-1v-1-1h-1c1 0 1-1 2-1h1v-1h1c-1-1-2-2-4-3h1c1 0 2 1 3 1 0-1 0-1 1-2z" class="J"></path><path d="M455 257h1 1c2 2 4 3 6 5-3 0-6-1-10-3h-1c1 0 1-1 2-1h1v-1z" class="f"></path><path d="M441 249c1 0 1 0 1-1l1-1c1 2 1 4 3 4h1c1 1 2 1 3 2 1 0 2 0 3 1h-1c2 1 3 2 4 3h-1v1h-1c-1 0-1 1-2 1h1v1l-6-2h0c0 1 1 2 1 3l-1 1h1l-2 2-5-15z" class="b"></path><path d="M448 256l-2-1v-2h0v-1l3 1h1c1 0 1 1 2 1 2 1 3 2 4 3h-1v1h-1c-1 0-1 1-2 1-1-1-3-2-4-3z" class="a"></path><path d="M448 256v-1c3 0 5 2 7 2v1h-1c-1 0-1 1-2 1-1-1-3-2-4-3z" class="g"></path><defs><linearGradient id="AK" x1="460.566" y1="241.626" x2="450.95" y2="236.263" xlink:href="#B"><stop offset="0" stop-color="#9a999a"></stop><stop offset="1" stop-color="#c1c0c0"></stop></linearGradient></defs><path fill="url(#AK)" d="M457 227v1c-2 5-4 9-2 14v1l3 7c1 3 4 5 5 7l-1 1c-1 0-2-1-3-2l-2-3h-1c-1-2-2-5-3-7h-3s-1 0-1-1v-1h1l-1-1 1-2-3-3h2l2 2v-1l1-1v-3-2c1-2 3-4 4-5l1-1z"></path><path d="M447 238h2l2 2v-1c0 1 1 1 1 2v1c1 4 4 7 6 10 0 2 1 2 1 4l-2-3h-1c-1-2-2-5-3-7h-3s-1 0-1-1v-1h1l-1-1 1-2-3-3z" class="G"></path><path d="M450 241c1 2 2 3 3 5h-3s-1 0-1-1v-1h1l-1-1 1-2z" class="Y"></path><path d="M458 228l1 4c0 3 1 6 2 8l1 2c1 1 1 4 2 5v5l-1-1h-1v3h0l-3-3c-2-4-3-9-4-13 0-4 2-7 3-10z" class="a"></path><path d="M460 244l-1-2v-1c1-1 1-1 2-1l1 2c1 1 1 4 2 5v5l-1-1h-1v3h0l-3-3c1-1 1-2 1-4h-2l2-3z" class="W"></path><path d="M460 244l-1-2v-1c1-1 1-1 2-1l1 2c-1 0-1 1-1 1 0 1 1 2 1 3 1 1 2 3 1 4h0 0c-1 0-1 0-2-1s-1-3-1-5zm-20-6v-3-1l1 1h0l1 1c1 1 1 2 2 3h1c1 1 3 4 4 5v1c0 1 1 1 1 1h3c1 2 2 5 3 7h1c-1 1-1 1-1 2-1 0-2-1-3-1-1-1-2-1-3-1-1-1-2-1-3-2h-1c-2 0-2-2-3-4l-1-1v-1l2 1v-1c-1-1-2-2-3-4l-1-3z" class="f"></path><path d="M444 245l5 3c1 0 3 1 5 3-1-2-2-4-4-5h3c1 2 2 5 3 7l-1-1c-4-2-8-4-11-6v-1z" class="W"></path><path d="M440 238v-3-1l1 1h0l1 1c1 1 1 2 2 3h1c1 1 3 4 4 5v1c-1 0-2-2-3-3h-1l-3-3h-1v2l-1-3z" class="M"></path><path d="M443 247l-1-1v-1l2 1c3 2 7 4 11 6l1 1h1c-1 1-1 1-1 2-1 0-2-1-3-1-1-1-2-1-3-1-1-1-2-1-3-2h-1c-2 0-2-2-3-4z" class="Y"></path><path d="M427 169c1 2 3 4 4 5l1-1 10 18c1 1 4 7 4 7l13 27-2 2h0l-1 1c-1 1-3 3-4 5v2 3l-1 1v1l-2-2h-2l3 3-1 2 1 1h-1c-1-1-3-4-4-5h-1c-1-1-1-2-2-3l-1-1h0l-1-1v1 3l-5-13-1-4c0-3-1-6-1-9v-11-6h0l-1-4v-5c-1-2-2-4-3-7l-1-3v-2c-1-1-1-3-1-5z" class="b"></path><path d="M453 217c1 1 2 3 2 4-1 0-1 0-2 1h-1l1-1-1-2c1 0 1-1 1-2z" class="T"></path><path d="M441 192c0 2 1 3 2 5v3c0-2-1-3-2-4-1 0-1-1-1-2h0l1-2z" class="I"></path><path d="M447 205c2 4 5 8 6 12 0 1 0 2-1 2h-1s0 1-1 1v-1c1-1 1-1 0-2h0v-2c0-1-1-1-1-1v-3l-1-1c-1-1-1-2-1-2h0v-1-2z" class="L"></path><path d="M441 196c1 1 2 2 2 4v-3c1 2 3 5 4 8v2c-1-1-1-2-3-2h0v1 1c0 1-1 1-2 2l1-4c-1-1-1 0-2-2v-2-5z" class="C"></path><g class="N"><path d="M441 196c1 1 2 2 2 4v5c-1-1-1 0-2-2v-2-5z"></path><path d="M437 186l4 6-1 2h0c0 1 0 2 1 2v5h-1 0v3h0-1l-1-1v-1h-1 0-1c-1-3 1-7-1-10h1 2c-1-2-1-4-1-6z"></path></g><path d="M440 194c0 1 0 2 1 2v5h-1 0v3h0l-1-1c0-2 0-7 1-9z" class="O"></path><path d="M438 192v6 3h-1 0c0-3 0-6-1-9h2z" class="F"></path><path d="M437 186l4 6-1 2-1-1c-1 2 0 4-1 5v-6c-1-2-1-4-1-6z" class="Q"></path><path d="M445 215h0c1-2 3-2 4-2-1-1-1-1-2-1v-1l1-1 1 1v3s1 0 1 1v2h0c1 1 1 1 0 2v1c1 0 1-1 1-1h1l1 2-1 1 1 1c-1 0-1 0-2 1h-1v1l-1-1-1 2v-1-2-1h-1c-1 0-1-1-1-1l1-1v-1l-2-1v-3z" class="R"></path><path d="M448 222l2-1v2s-1 0-1 1l-1 2v-1-2-1z" class="C"></path><path d="M445 215h0c1-2 3-2 4-2-1-1-1-1-2-1v-1l1-1 1 1v3s1 0 1 1v2c-1 0-1 2-3 2-1-2 1-1 1-4h-1-1-1z" class="G"></path><path d="M442 209c1-1 2-1 2-2v-1-1h0c2 0 2 1 3 2v1h0s0 1 1 2l-1 1v1c1 0 1 0 2 1-1 0-3 0-4 2h0v3l2 1v1l-1-1h-1-1-1l-1-2h-1c0-3 1-6 1-8z" class="I"></path><path d="M442 209c1-1 2-1 2-2v-1-1h0c2 0 2 1 3 2v1h0l-3 2-1 1v2h1c-1 1-1 1-1 2v1l-1 1h0-1c0-3 1-6 1-8z" class="V"></path><path d="M452 222h1c1-1 1-1 2-1l-1 2h2v1h0l-2 1h0l1 1c1 1 0 1 1 2-1 1-3 3-4 5h-1-2l-4-2v-2-1l-2-2-1-1 1-1c1 0 1 2 3 2v-1h1l1-2v2 1l1-2 1 1v-1h1c1-1 1-1 2-1l-1-1z" class="E"></path><path d="M445 229h1c1 1 1 1 3 2h3c0 1-1 1-1 2h-2l-4-2v-2z" class="I"></path><defs><linearGradient id="AL" x1="426.423" y1="180.215" x2="437.324" y2="181.464" xlink:href="#B"><stop offset="0" stop-color="#818181"></stop><stop offset="1" stop-color="#9f9d9f"></stop></linearGradient></defs><path fill="url(#AL)" d="M427 169c1 2 3 4 4 5 3 4 4 8 6 12 0 2 0 4 1 6h-2-1v-1c-1 0-2-3-2-4v4 4h0l-1-4v-5c-1-2-2-4-3-7l-1-3v-2c-1-1-1-3-1-5z"></path><defs><linearGradient id="AM" x1="439.525" y1="227.4" x2="445.859" y2="226.15" xlink:href="#B"><stop offset="0" stop-color="#9c9b9c"></stop><stop offset="1" stop-color="#bab9ba"></stop></linearGradient></defs><path fill="url(#AM)" d="M433 195v-4-4c0 1 1 4 2 4v1c2 3 0 7 1 10h1 0 1v1l1 1h1 0v-3h0 1v2c1 2 1 1 2 2l-1 4c0 2-1 5-1 8h1l1 2h1 1 1l1 1-1 1s0 1 1 1h1v1l-1 2h-1v1c-2 0-2-2-3-2l-1 1 1 1 2 2v1 2l4 2h2 1v2 3l-1 1v1l-2-2h-2l3 3-1 2 1 1h-1c-1-1-3-4-4-5h-1c-1-1-1-2-2-3l-1-1h0l-1-1v1 3l-5-13-1-4c0-3-1-6-1-9v-11-6z"></path><path d="M441 217h1l1 2-1 2h0c0 1 1 2 2 3h1l1 1v1c-2 0-2-2-3-2l-1 1 1 1h-1c-2-3-1-6-1-9h0z" class="D"></path><path d="M443 219h1 1 1l1 1-1 1s0 1 1 1h1v1l-1 2h-1l-1-1h-1c-1-1-2-2-2-3h0l1-2z" class="Q"></path><path d="M445 224h-1c-1-1-2-2-2-3h0 2c1 1 1 2 1 3z" class="S"></path><path d="M440 204v-3h0 1v2c1 2 1 1 2 2l-1 4c0 2-1 5-1 8h0c0-2-1-4-1-7l-1 1c0 1 0 2-1 4-1-2-1-3-1-5l1-6v-1l1 1h1 0z" class="K"></path><path d="M440 204v-3h0 1v2 5c-1-1-1-3-1-4z" class="a"></path><path d="M438 204c1 1 2 5 2 6l-1 1c0 1 0 2-1 4-1-2-1-3-1-5l1-6z" class="F"></path><path d="M443 226l2 2v1 2l4 2h2 1v2 3l-1 1v1l-2-2h-2c0-1-1-2-1-2-2-3-3-6-4-10h1z" class="L"></path><path d="M445 231l4 2h2 1v2 3l-1-2h-2l-1-1v1h-1v-1h1c0-1 0-1-1-1-1-1-1-2-2-3z" class="R"></path><path d="M451 233h1v2c-2 0-2-1-3-2h2z" class="D"></path><path d="M437 219l1-1c0 6 1 11 4 16 1 1 2 3 3 5h-1c-1-1-1-2-2-3l-1-1h0l-1-1v1 3l-5-13-1-4c2 2 3 5 4 6l-1-4v-4z" class="H"></path><path d="M433 195v-4-4c0 1 1 4 2 4v1c2 3 0 7 1 10h1 0 1v1 1l-1 6c0 2 0 3 1 5v3l-1 1v4l1 4c-1-1-2-4-4-6 0-3-1-6-1-9v-11-6z" class="F"></path><path d="M437 202h0 1v1 1l-1 6h0l-1-1c1-2 1-5 1-7z" class="Q"></path><path d="M436 209l1 1h0c0 2 0 3 1 5v3l-1 1v4c-1-1-2-3-2-5 1-1 1-3 1-5v-4z" class="M"></path><path d="M435 218c1-1 1-3 1-5 0 2 0 3 1 4v2h0v4c-1-1-2-3-2-5z" class="J"></path><path d="M433 201c1 2 1 3 1 5 1 1 0 2 0 3s0 4 1 5v4c0 2 1 4 2 5l1 4c-1-1-2-4-4-6 0-3-1-6-1-9v-11z" class="f"></path><path d="M441 248v1l5 15 2-2h-1l1-1h2 0c0 1 0 2 1 3 2 0 3 1 5 2h2v1l1-1h3c1 1 2 1 3 2 3 1 4 4 6 6 1 1 2 3 2 5l-2 1 2 7 1 8 1 5 1 5h-2c0 2 1 2 1 3v2s-1 0-1 1l1 1c-1 2-1 2-1 4 1 2 1 4 1 5v1l1 1v3 4 1l-1 4c-1 0-1 0-2 1l-3-2c-1-1-3-2-5-3h-4c-2 2-5 4-6 6h1c0 1-1 1-1 2h0l-1-1h-1l-1-1c0-2-1-3 0-5h0c1-1 2-2 2-3 1-1 0-1 0-2v1l3-1-1-1c0-2-3-15-3-15-1-5-1-11-3-16-1-2-1-4-1-5-2-9-5-17-7-26l-1-6c1-1 1-2 0-4v-6z" class="F"></path><path d="M460 323s1 1 0 1l1 3h1v2s-1 0-1 1v1c-2 2-5 4-6 6h-1c1-2 3-4 5-5h0l1-2-2-5c1-1 2-1 2-2z" class="C"></path><path d="M460 323s1 1 0 1c0 2 0 4 1 5v1 1h-1v-1l-2-5c1-1 2-1 2-2z" class="U"></path><path d="M448 261h2 0c0 1 0 2 1 3h-1-2c1 0 1 1 1 1 1 3 1 10 4 11h0c-1 1-1 1-1 2 1 0 1-1 1 0s0 1 1 2h0v2h0l-1-1h-1v2c1 0 1 1 2 1l1 1h-2v1c1 0 2 0 3-1h1c-1 2-2 3-3 3 0 1 1 1 2 1l-2 2 1 1h0c0 1 1 1 1 1l-1 1v1c1 1 1 1 1 2h0c1 0 1 1 1 1l1 1c-1 1-1 1-1 3v2c0 1 1 1 1 2 0 2 0 2 1 3l1 1v1c0 1 0 2 1 3v1h1c1 1 2 1 3 1h0s-1 1-2 1-1-1-2-1v1c0 1 0 2-1 2h-1v1h0c0 1 0 1 1 1v2c0 1-1 1-2 2-1-7-1-15-3-22l-5-25c-1-5-3-9-4-14l2-2h-1l1-1z" class="X"></path><path d="M459 319c0-1 0-2-1-2h0c1-1 1-1 1-2s0-1-1-1h0v-1h1v-1l-1-1v-1h1s0 1 1 1h0c0 1 0 2 1 3v1h1c1 1 2 1 3 1h0s-1 1-2 1-1-1-2-1v1c0 1 0 2-1 2h-1z" class="C"></path><path d="M465 311c1-1 3-1 4-1s2 0 3 1l1 1 1-1 1 1c-1 2-1 2-1 4 1 2 1 4 1 5v1l1 1v3 4 1l-1 4c-1 0-1 0-2 1l-3-2c-1-1-3-2-5-3h-4v-1c0-1 1-1 1-1v-2h-1l-1-3c1 0 0-1 0-1v-2c-1 0-1 0-1-1h0v-1h1c1 0 1-1 1-2v-1c1 0 1 1 2 1s2-1 2-1h0c-1 0-2 0-3-1h-1v-1c-1-1-1-2-1-3h2 3z" class="G"></path><path d="M471 319v1h-2v1c1 1 1 2 1 3v1h-1v-3h-2v-1l2-1 2-1z" class="T"></path><path d="M470 334h0c1-2 1-2 2-3s0-2 0-3l1 1v6 1l-3-2z" class="C"></path><path d="M462 320c1 1 2 1 3 1h2v1c-1 0-1 0-1 1s1 1 2 1v1c-1 1-1 1-3 1 0 0 0-1-1-1v-1h0c-1-1-2-2-2-4z" class="I"></path><path d="M473 329h1v-2l1-1v-4l1 1v3 4 1l-1 4c-1 0-1 0-2 1v-1-6z" class="P"></path><path d="M470 315c2 1 2 1 2 2s0 1-1 2l-2 1-2 1h-2c-1 0-2 0-3-1 0 0-1-1-1-2v-1-1c1 0 1 1 2 1s2-1 2-1h1c1-1 3-1 4-1z" class="Q"></path><path d="M462 320c1-1 2 0 3-1 2 0 3 0 4 1l-2 1h-2c-1 0-2 0-3-1z" class="N"></path><path d="M465 311c1-1 3-1 4-1s2 0 3 1l1 1 1-1 1 1c-1 2-1 2-1 4l-2-1-2-2v1 1c-1 0-3 0-4 1h-1 0c-1 0-2 0-3-1h-1v-1c-1-1-1-2-1-3h2 3z" class="M"></path><path d="M465 311v1c0 1-1 1-1 2h-1c0-2 1-2 1-3h-2 0 3z" class="I"></path><path d="M462 311h0c1 1 1 1 0 2v1l-1 1v-1c-1-1-1-2-1-3h2z" class="W"></path><path d="M465 311c1-1 3-1 4-1s2 0 3 1c0 1-1 2-1 2h-2c-1-1-2-1-4-1v-1z" class="B"></path><path d="M474 311l1 1c-1 2-1 2-1 4l-2-1-2-2v1 1c-1 0-3 0-4 1h-1 0c-1 0-2 0-3-1 2 0 5 0 7-2h2s1-1 1-2l1 1 1-1z" class="R"></path><path d="M451 264c2 0 3 1 5 2h2v1l1-1h3c1 1 2 1 3 2 3 1 4 4 6 6 1 1 2 3 2 5l-2 1 2 7-1 1h0-1 0l-1-1-2-4c-1 0-2 2-3 2-1 1-2 1-3 1-2 1-5 2-6 3-1 0-2 0-2-1 1 0 2-1 3-3h-1c-1 1-2 1-3 1v-1h2l-1-1c-1 0-1-1-2-1v-2h1l1 1h0v-2h0c-1-1-1-1-1-2s0 0-1 0c0-1 0-1 1-2h0c-3-1-3-8-4-11 0 0 0-1-1-1h2 1z" class="P"></path><path d="M458 266v1 1c2 0 3-2 4-1-1 1-1 1-2 1 0 0 0 1-1 1v2c-1 0-3 1-5 1v-1c1-2 3-4 4-5z" class="Z"></path><path d="M468 276c2 3 3 8 4 12h0-1l-3-6-3-6h3z" class="S"></path><path d="M453 276c0-1 0-2-1-3v-1h1s0 1 1 1c0 0 1 0 1 1h2c1 0 1 1 2 1h0c-1 1-2 1-3 2 0 1-1 1-2 2v1c-1-1-1-1-1-2s0 0-1 0c0-1 0-1 1-2h0z" class="M"></path><path d="M453 276c0-1 0-2-1-3v-1h1s0 1 1 1c0 0 1 0 1 1h2c-1 0-2 0-3 1l1 1-2 2c0-1 0 0-1 0 0-1 0-1 1-2h0z" class="H"></path><path d="M464 274c-1 0-1-1-2-1v1c-2 0-1-1-2-1h-2v-1h1c1-1 2-1 3-1s2 1 3 1l2 2 1 2h-3-1v-2z" class="B"></path><path d="M467 274l1 2h-3-1v-2l2 1 1-1z" class="G"></path><path d="M466 271c3 3 4 5 5 9l2 7-1 1c-1-4-2-9-4-12l-1-2-2-2 1-1z" class="c"></path><path d="M453 276c-3-1-3-8-4-11 0 0 0-1-1-1h2c1 1 4 3 6 3 0 1 0 1-1 1l-1 2h-1v-1c0-1-1-1-1-2h-1l1 1v1 1l1 2h-1v1c1 1 1 2 1 3z" class="E"></path><defs><linearGradient id="AN" x1="472.734" y1="273.529" x2="459.155" y2="272.215" xlink:href="#B"><stop offset="0" stop-color="#8d8b8b"></stop><stop offset="1" stop-color="#b5b5b7"></stop></linearGradient></defs><path fill="url(#AN)" d="M458 267l1-1h3c1 1 2 1 3 2 3 1 4 4 6 6 1 1 2 3 2 5l-2 1c-1-4-2-6-5-9l-1-1c-2-1-4 0-6 1v-2c1 0 1-1 1-1 1 0 1 0 2-1-1-1-2 1-4 1v-1z"></path><path d="M454 280v-1c1-1 2-1 2-2 1-1 2-1 3-2l2 2h0c1-1 1-1 2 0 0 0 0 1-1 1h-1v1c1 0 1 1 3 1l1-1v1 2h1c1 0 1 0 2 1-1 0-2 2-3 2-1 1-2 1-3 1-2 1-5 2-6 3-1 0-2 0-2-1 1 0 2-1 3-3h-1c-1 1-2 1-3 1v-1h2l-1-1c-1 0-1-1-2-1v-2h1l1 1h0v-2h0z" class="S"></path><path d="M454 280l2-2c1 1 1 1 2 1 1 1 1 0 2 0 0 1-1 1-1 1-1 1-1 0-1 1l-4 1v-2z" class="H"></path><path d="M458 281c1 0 0 0 0 1h3 1l-3 3h-1v-3h-2l-1 2c0 1 0 1 1 1-1 1-2 1-3 1v-1h2l-1-1c-1 0-1-1-2-1v-2h1l1 1h0l4-1z" class="C"></path><path d="M468 283l2 4 1 1h0 1 0l1-1 1 8 1 5 1 5h-2c0 2 1 2 1 3v2s-1 0-1 1l-1 1-1-1c-1-1-2-1-3-1s-3 0-4 1h-3-2v-1l-1-1c-1-1-1-1-1-3 0-1-1-1-1-2v-2c0-2 0-2 1-3l-1-1s0-1-1-1h0c0-1 0-1-1-2v-1l1-1s-1 0-1-1h0l-1-1 2-2c1-1 4-2 6-3 1 0 2 0 3-1 1 0 2-2 3-2z" class="R"></path><path d="M470 302c1 1 2 1 3 1h0-2v3c-1 0-1 0-2-1 1-1 1-1 1-3z" class="C"></path><path d="M475 300l1 5h-2c0 2 1 2 1 3v2s-1 0-1 1l-1 1-1-1c-1-1-2-1-3-1h0v-1h1c1 1 2 1 3 1 1-1 1-1 0-2 0-1 1-1 1-2 0 0-1 0-1-1h2v-1l-2-1h0 0c0-1 0-1-1-1h0v-1-1c2 1 2 1 3 1v-1z" class="D"></path><path d="M472 288l1-1 1 8 1 5v1c-1 0-1 0-3-1v1 1h0c1 0 1 0 1 1h0c-1 0-2 0-3-1-1 0-1 0-1-1 2-1 2-2 3-2l1 1h0c1-1 0-2 0-3s0-3-1-4v-3c-1-1-1-1-1-2h0 1 0z" class="L"></path><path d="M465 300c0-1 0 0 1-1 0-1 1-2 2-3l2 1v1c1 0 1 0 2 1l1-1v-1c0 1 1 2 0 3h0l-1-1c-1 0-1 1-3 2 0 1 0 1 1 1 0 2 0 2-1 3h0c-1 0-1 1-2 1v-1-1l2-2v-2h-1c-1 1-1 3-2 4v-2h0l-1-1v-1z" class="G"></path><path d="M465 300c0-1 0 0 1-1 0-1 1-2 2-3l2 1v1 1h-1l-1-1c-1 1-2 3-2 4l-1-1v-1z" class="Q"></path><path d="M462 301c1-3 3-4 5-6 2 0 2 0 3 1v1l-2-1c-1 1-2 2-2 3-1 1-1 0-1 1v1l1 1h0c-1 2-2 2-2 3v1 1 1h0 1 1 1l1-1h0 2 0v2h0-1v1h0c-1 0-3 0-4 1h-3-2v-1l-1-1c-1-1-1-1-1-3 0-1-1-1-1-2l2-2c0-1 0-2 1-3l2 2z" class="B"></path><path d="M468 307h2 0v2h0-1v1h0c-1 0-3 0-4 1h-3-2v-1l9-1c0-1 0-1-1-2z" class="G"></path><path d="M459 302c0-1 0-2 1-3l2 2c-1 1-1 2-1 4l1 1v1c-1 1-1 1-2 1h0v-1-1-1c0-2-1-2-1-3z" class="M"></path><path d="M462 301c1-3 3-4 5-6 2 0 2 0 3 1v1l-2-1c-1 1-2 2-2 3-1 1-1 0-1 1h0c-1 1-2 2-2 4h-1l1 2h-1l-1-1c0-2 0-3 1-4z" class="D"></path><path d="M468 283l2 4 1 1c0 1 0 1 1 2v3c1 1 1 3 1 4v1l-1 1c-1-1-1-1-2-1v-1-1c-1-1-1-1-3-1-2 2-4 3-5 6l-2-2c-1 1-1 2-1 3l-2 2v-2c0-2 0-2 1-3l-1-1s0-1-1-1h0c0-1 0-1-1-2v-1l1-1s-1 0-1-1h0l-1-1 2-2c1-1 4-2 6-3 1 0 2 0 3-1 1 0 2-2 3-2z" class="H"></path><path d="M470 287l1 1c0 1 0 1 1 2v3c1 1 1 3 1 4v1l-1 1c-1-1-1-1-2-1v-1-1c1 0 1 0 1-1l-2-2c-1-1-2-1-3 0h0l-1-1c1-1 0-2 0-3 1-1 2 0 3 0s2-2 2-2z" class="I"></path><path d="M469 293c1 0 1-1 1-1l-2-2h0 1c1 0 2 1 2 2l1 1c1 1 1 3 1 4v1l-1 1c-1-1-1-1-2-1v-1-1c1 0 1 0 1-1l-2-2z" class="R"></path><path d="M455 292h0c1 1 5-1 6-1s2-1 2-1l1-1v1c-1 1-1 1-2 1v3h1 1c-1 1-2 2-3 4l-1 1c-1 1-1 2-1 3l-2 2v-2c0-2 0-2 1-3l-1-1s0-1-1-1h0c0-1 0-1-1-2v-1l1-1s-1 0-1-1h0z" class="C"></path><path d="M461 295h-1c-1-1-2-1-3-1v-1s1 0 1-1l4-1v3l-1 1z" class="M"></path><path d="M462 294h1 1c-1 1-2 2-3 4l-1 1c-1 1-1 2-1 3l-2 2v-2c0-2 0-2 1-3l-1-1s0-1-1-1h0c0-1 0-1-1-2v-1l2 2h1c0-1 0-1 1-1h0c0 1-1 1 0 2v1c1 0 1-1 2-1v-1s-1 0 0-1h0l1-1z" class="D"></path><path d="M327 90l4 1h4 2c3 1 7 3 10 5 11 4 21 11 31 18 4 3 7 5 10 7 14 11 25 24 36 38 1 1 2 2 3 4 4 5 7 11 11 16 4 6 8 12 11 19h-2 0-1s-3-6-4-7l-10-18-1 1c-1-1-3-3-4-5 0 2 0 4 1 5v2l-4-5c0-1-2-3-3-4l-7-8h-1 0c-2-1-3-2-4-4-1 0-3-3-4-5h0c0-1-1-2-1-2 0-2-1-3-1-4s-1-2-1-3l-2-2c-1 0-3-1-5-2 1 1 1 2 2 3h0v2c0 1-3 4-4 5h-1l-2-2-1-1c-1-2-3-2-5-3l-2-1h-1v-1c1-1 3-2 4-3-1 0-1 0-3 1 0 0-1 1-2 1v2l-1 1c0 1-1 1-1 2-2-1-2-2-4-3 0-1-1-1-1-2-2-1-3-1-4-3h-3s-1-1-2-1l-4-4c-4-2-8-5-13-7l-6-4h0c0-1-1-1-1-2v-1l-1-1s-1 0-2 1c-1 0-1-1-2-1s-1 0-2-1l1-1 2-2c-1-1-2-1-2-2s1-2 1-3h0l1-1 1-1-1-1h1 1l1-1v-2c0-1-2-2-3-3v-1h1c1 1 3 2 4 2-4-3-10-5-15-7 0-1 0-1 1-1z" class="E"></path><path d="M367 118h1l1 1c-1 0-1 1-2 2-1-1-1-1-1-2-1 0-1 0-2-1h3z" class="D"></path><path d="M359 118c2-1 3-2 5-2 0 1-2 3-3 3v1c-1-2-1-2-2-2z" class="B"></path><path d="M354 107h4l1 1v1h-2l-3 1c0-1 0-2-1-2l1-1z" class="K"></path><path d="M425 166h1c0-1-1-2-1-3v-1 1c2 3 5 6 7 10l-1 1c-1-1-3-3-4-5l-1-1c0-1 0-1-1-2z" class="U"></path><path d="M371 117h2c1 1 1 2 1 2v1 1 2h0c-1 0-1 1-2 1h-3l1-2c1-1 1-2 1-3v-2z" class="W"></path><path d="M371 119l2 1c-1 1-1 2-1 2h-2c1-1 1-2 1-3z" class="M"></path><path d="M355 113c0 1 0 1 1 2h0c0 1 0 2 1 3h1 1c1 0 1 0 2 2-1 0-2 1-2 2h0v-1c-1-1-2-1-3-1l-2-2h0v-1c0-1 0-2-1-3l2-1z" class="H"></path><path d="M371 117c-2 0-3-1-4-2h-2-1v-2h0c-2 0-3 1-4 1h-1c0 1-1 1-1 1-1 0-1 0-2-1 1-1 1-2 2-2s1 1 2 0v-1h-2c-1 0-1 0-2 1h0-1 0c0-1 1-2 2-2h2c1-1 1 0 2 0v1h1c2 1 2 1 3 0l7 4 1 2h-2z" class="Q"></path><path d="M394 130c5 3 9 8 13 13 7 7 14 15 18 23 1 1 1 1 1 2l-9-11c-3-5-7-9-11-14h-3v1c0-1-1-2-1-3s0-1 1-2c0-1-3-2-4-3l-6-4h0v-2-1l1 1z" class="X"></path><path d="M402 141c0-1 0-1 1-2l3 4h-3v1c0-1-1-2-1-3z" class="R"></path><path d="M403 144v-1h3c4 5 8 9 11 14l9 11 1 1c0 2 0 4 1 5v2l-4-5c0-1-2-3-3-4l-7-8h-1 0c-2-1-3-2-4-4-1 0-3-3-4-5h0c0-1-1-2-1-2 0-2-1-3-1-4z" class="K"></path><path d="M403 144v-1h3c4 5 8 9 11 14h-1c-2-2-4-3-5-5-1-1-3-3-3-4l-2-2v1h0c1 2 3 4 5 6l-1 1c-1-2-2-3-3-4h-1c1 2 2 3 3 4v1c-1 0-3-3-4-5h0c0-1-1-2-1-2 0-2-1-3-1-4z" class="H"></path><path d="M369 124h3c1 0 1-1 2-1h0 0 1 1c0 1 0 1-1 2 1 0 1 1 1 1v1 2s-1 1-1 2c0 2 1 3 3 4l1 1h0 0l1 1v1 2l-1 1c0 1-1 1-1 2-2-1-2-2-4-3 0-1-1-1-1-2-2-1-3-1-4-3h-2v-2c-1 0-2-1-2-2l-1-1s0-1-1-1h0l1-2-1-1h1c1 0 1 1 2 2v-1c0-1 2-2 3-3z" class="S"></path><path d="M371 132h1c0-1 0-1 1-2h1v1h1c0 2 1 3 3 4l1 1h0 0l1 1v1h-2c-2 0-2-1-3-2h-1c0-1 0-1-1-2h1 0 1v-1h-1-2-1v-1z" class="H"></path><path d="M374 136h1c1 1 1 2 3 2h2v2l-1 1c0 1-1 1-1 2-2-1-2-2-4-3 0-1-1-1-1-2l-1-1h0v-1h2 0z" class="B"></path><path d="M369 124h3c1 0 1-1 2-1h0 0 1 1c0 1 0 1-1 2 1 0 1 1 1 1v1 2s-1 1-1 2h-1v-1h-1c-1 1-1 1-1 2h-1v-1h-1-1c0-1 0-2-1-2h0l1-1-3-1c0-1 2-2 3-3z" class="M"></path><path d="M375 125c1 0 1 1 1 1v1c-1 0-2 0-3-1 1-1 1-1 2-1z" class="J"></path><path d="M369 128c1-1 2-1 3-2l1 1v2h-1l-1-1h-1c0 1 0 2 1 3h-1-1c0-1 0-2-1-2h0l1-1z" class="T"></path><path d="M365 111c-1-1-2-2-4-3 0 0-1 0-2-1v-1h3l1 1 1 1c3 1 5 2 7 4 9 5 16 11 23 18l-1-1v1 2l-2-2c-1 0-1 0-2-1l-1 1h-1 0c-1-1-1-1-2-1l-1 1v-1h-2-1v-1h0v-1h-1v1h-1v-1h-1c-1 0-1 1-2 1v1-2-1s0-1-1-1c1-1 1-1 1-2h-1-1 0v-2-1-1s0-1-1-2l-1-2-7-4z" class="b"></path><path d="M385 124c2 1 4 2 6 4 0 0 1 1 2 1v1 2l-2-2c-3-1-5-4-6-6z" class="U"></path><path d="M372 115c2 1 4 2 5 3 3 1 5 4 8 6 1 2 3 5 6 6-1 0-1 0-2-1l-1 1h-1 0c-1-1-1-1-2-1l-1 1v-1h-2-1v-1h0v-1h-1v1h-1v-1h-1c-1 0-1 1-2 1v1-2-1s0-1-1-1c1-1 1-1 1-2h-1-1 0v-2-1-1s0-1-1-2l-1-2z" class="D"></path><path d="M381 125c2 0 5 3 6 4v1c-1-1-1-1-2-1l-1 1v-1h-2-1v-1h0s1 0 1-1l-1-2z" class="V"></path><path d="M374 119c2 1 3 1 5 3h0c0 1 1 2 2 3l1 2c0 1-1 1-1 1v-1h-1v1h-1v-1h-1c-1 0-1 1-2 1v1-2-1s0-1-1-1c1-1 1-1 1-2h-1-1 0v-2-1-1z" class="E"></path><path d="M340 114h-1v-1c1 0 3-1 4-1h3c2-1 5-1 7-1l2 2-2 1c1 1 1 2 1 3v1h0l2 2c1 0 2 0 3 1v1h0c1 0 1 1 1 1 0 1 0 2 1 3v4h0c1 0 2 1 3 2l1-1c0 1 1 2 2 2v2h2-3s-1-1-2-1l-4-4c-4-2-8-5-13-7l-6-4h0c0-1-1-1-1-2v-1l-1-1 1-1z" class="D"></path><path d="M359 122h0c1 0 1 1 1 1 0 1 0 2 1 3v4c-1-1-1-2-2-2-1-1-2-1-2-2s1 0 2-1h-1c0-1 1-1 1-2v-1h0z" class="B"></path><path d="M354 118l2 2c1 0 2 0 3 1v1h0l-2 2c-1 1-1 1-2 1s-2 0-2-1c0-2 0-4 1-6z" class="F"></path><path d="M356 120c1 0 2 0 3 1v1h0l-2 2c0-1-1-1-2-1h-1 0l1-1c0-1 1-1 1-2h0z" class="K"></path><path d="M340 114l5-1 1 1-1 1c1 1 3 0 4 0l-2 1v1h0v1l1 2v2s-1 0-1-1v-1c-1-1-1-1-1-2l-1 1h-3v-1h1c1 0 1-1 1-1-2 0-2-1-4 0v-1l-1-1 1-1z" class="I"></path><path d="M340 114l5-1 1 1-1 1h-4l-1 1-1-1 1-1z" class="L"></path><path d="M349 115c1 0 2 1 3 2l1-1v1l1 1h0c-1 2-1 4-1 6-1 0-1 0-2-1l-2-1h-1v-2l-1-2v-1h0v-1l2-1z" class="E"></path><path d="M349 122l1-2 2 1v2h-1l-2-1z" class="W"></path><path d="M349 115c1 0 2 1 3 2l1-1v1 2h-2c-1 0-1-1-2-1h0l-2-1h0v-1l2-1z" class="H"></path><path d="M340 114h-1v-1c1 0 3-1 4-1h3c2-1 5-1 7-1l2 2-2 1c1 1 1 2 1 3v1l-1-1v-1l-1 1c-1-1-2-2-3-2s-3 1-4 0l1-1-1-1-5 1z" class="O"></path><path d="M345 113c3 0 5 0 8 1 1 1 1 2 1 3v1l-1-1v-1l-1 1c-1-1-2-2-3-2s-3 1-4 0l1-1-1-1z" class="D"></path><path d="M376 129v-1c1 0 1-1 2-1h1v1h1v-1h1v1h0v1h1 2v1l1-1c1 0 1 0 2 1h0 1l1-1c1 1 1 1 2 1l2 2h0l6 4c1 1 4 2 4 3-1 1-1 1-1 2l-2-2c-1 0-3-1-5-2 1 1 1 2 2 3h0v2c0 1-3 4-4 5h-1l-2-2-1-1c-1-2-3-2-5-3l-2-1h-1v-1c1-1 3-2 4-3-1 0-1 0-3 1 0 0-1 1-2 1v-1l-1-1h0 0l-1-1c-2-1-3-2-3-4 0-1 1-2 1-2z" class="I"></path><path d="M386 136h1c-2 1-3 1-4 2v2h-1-1v-1c1-1 3-2 4-3h1z" class="N"></path><path d="M386 136c1-1 1-1 2-1s1-1 2-1c2 1 3-1 4 1 0 1 0 1-1 1h-6-1z" class="Q"></path><path d="M387 130h1l1-1c1 1 1 1 2 1l2 2h0c-2 1-3 0-4-1l-2 2-1-1c0-1 0-1 1-2z" class="C"></path><path d="M399 136c1 1 4 2 4 3-1 1-1 1-1 2l-2-2c-1 0-3-1-5-2h-2v-1h1c1 1 1 1 2 1s2-1 3-1zm-18-8v1h1 2v1l1-1v4h-2-1v1 1c-1 0-1 1-2 1v1l-1-1c1 0 1-1 1-2v-2-1c0-1 0-2 1-3z" class="E"></path><path d="M381 128v1h1 2v1h-2v1h-2c0-1 0-2 1-3z" class="C"></path><path d="M376 129v-1c1 0 1-1 2-1h1v1h1v-1h1v1h0c-1 1-1 2-1 3v1 2c0 1 0 2-1 2h0 0l-1-1c-2-1-3-2-3-4 0-1 1-2 1-2z" class="S"></path><path d="M378 134c-1-1-1-1-1-2h3v1c-1 1-1 1-2 1z" class="N"></path><path d="M380 132v2c0 1 0 2-1 2h0 0l-1-1h0v-1c1 0 1 0 2-1v-1h0z" class="B"></path><path d="M384 141l1-2 1 1h1v-2l1-1c1 0 2 1 2 1l1 1-2 1 1 1v2h2c1-1 1-1 1-3l-1-1h1c1 0 1 0 2-1h0l1 1h-1l1 1h1 0v2c0 1-3 4-4 5h-1l-2-2-1-1c-1-2-3-2-5-3z" class="M"></path><path d="M327 90l4 1h4 2c3 1 7 3 10 5h-1c4 2 7 3 10 5v1c1 0 1 1 2 1 1 1 2 1 2 1l-5-1v1h0l1 1-2 2h0 0l-1 1c1 0 1 1 1 2h-4c1 1 2 1 3 1-2 0-5 0-7 1h-3c-1 0-3 1-4 1v1h1l-1 1s-1 0-2 1c-1 0-1-1-2-1s-1 0-2-1l1-1 2-2c-1-1-2-1-2-2s1-2 1-3h0l1-1 1-1-1-1h1 1l1-1v-2c0-1-2-2-3-3v-1h1c1 1 3 2 4 2-4-3-10-5-15-7 0-1 0-1 1-1z" class="K"></path><path d="M341 110c1 0 1-1 2 0l1 1c-2 0-3 1-5 1h-1l1-1s1 0 1-1h1z" class="F"></path><path d="M339 100h3 1v1h1v1l-1 1 1-1c-1 1-1 2-2 3h1l-1 1-1-1c-2 1-4 3-5 5v1c-1-1-2-1-2-2s1-2 1-3h0l1-1 1-1-1-1h1 1l1-1v-2z" class="G"></path><path d="M339 100h3 1c-1 1-2 2-2 3h-2v1l1 1-1 1c-1 0-1-1-2-2l-1-1h1 1l1-1v-2z" class="H"></path><path d="M355 104l1 1-2 2h0 0l-1 1c1 0 1 1 1 2h-4c-2 0-4 1-6 1l-1-1c-1-1-1 0-2 0l-1-1c2-1 4-2 6-2h1c0-1 1-1 2-1 2 0 4 0 6-2z" class="N"></path><path d="M343 110c3-1 7-2 10-2 1 0 1 1 1 2h-4c-2 0-4 1-6 1l-1-1z" class="O"></path><path d="M327 90l4 1h4 2c3 1 7 3 10 5h-1c4 2 7 3 10 5v1c1 0 1 1 2 1 1 1 2 1 2 1l-5-1v1h0c-2 2-4 2-6 2-1 0-2 0-2 1h-1s1-1 2-1c1-1 4-1 5-2l-1-1-2 1v-1l1-1c-1 0-2 1-3 0h-1v1l-1 1c-1 0-1-1-2-2h0l-1 1 1-1v-1h-1v-1h-1-3c0-1-2-2-3-3v-1h1c1 1 3 2 4 2-4-3-10-5-15-7 0-1 0-1 1-1z" class="e"></path><path d="M336 97v-1h1c1 1 3 2 4 2s2 1 4 1h1c1 1 2 2 2 3h-1v1l-1 1c-1 0-1-1-2-2h0l-1 1 1-1v-1h-1v-1h-1-3c0-1-2-2-3-3z" class="C"></path><path d="M331 91h4 2c3 1 7 3 10 5h-1c4 2 7 3 10 5v1l-25-11z" class="B"></path><path d="M254 334v-6c1 1 0 4 0 6 0-2 0-3 1-4v3 124 31c0 5 1 10 0 15-1 6-2 12-4 18l-2-1c-4 8-12 11-20 13-6 1-12 2-18 1 2-1 5 1 7 0 1-1 1 0 2 0v-1-2l-1-1c0-1-1-3-1-4l-1-1v3c1 0 1 1 1 1v2h-1c-1 0-1-1-1-1v-1c-1-1-1-1-1-2 0-2-1-2-1-4h0l-2-5-2-3h0l-3-3-1-2-1-3c1 1 1 1 2 1 0-2-2-6-2-9 2 1 1-1 3 1v2h2l1-2c0 1 0 2 1 3 1 2 1 3 2 4 0 1 1 1 1 2s0 2 1 3v-1-3h0 0c0-2-1-4-1-6-1-1-1-2-1-3-1-2-2-6-2-8v-1-2c-1 0-1-2-2-3h0v-3c-1-1 0-4 0-5h-1v-2-2-2c-1-1-1-2-1-3s0-4-1-5c0-1-1-1-1-2v-2-3c0-1 1-7 0-9l-1-1-2 1c1-1 1-2 3-3h0s1-1 1-2l-1-1c1-1 1-3 0-5l1-2h2v2-1c0-1 1-3 0-4h0v-1-1s1-1 0-2v-1c-1 0 0-1 0-2h1v2c1-1 1-2 0-3l1-3v-5h0c1-1 1-1 0-2l1-1v-1c0-2 0-4-1-6l2-1v-1-5c0-1 1-3 1-3 1 0 0 2 1 3h1v-4c1-2 2-5 2-8-2 0-2 0-3 1l-1-1s0-1-1-2h0 1v-1s0-1 1-1c0-1 1-3 2-4h0l1 1c2-1 3-2 4-4l1 1c0 2-1 3-1 5h0 0c1-2 3-4 3-6v-3h0c1-3 2-6 3-8 0-2 1-3 1-4v-1l8-12c1-2 3-3 4-4 1-2 5-6 7-7l1 1c1-2 2-4 4-5v1l1 1z" class="e"></path><g class="b"><path d="M232 363l1-2h1c1 1 1 1 1 2h-3z"></path><path d="M235 363c1 0 1 0 2 1 0 0 1 0 0 1h-1c-1 0-1-1-1-2z"></path></g><path d="M226 528c1 1 1 2 1 3h-1c-1-1-2-1-2-2v-1c1 1 1 1 2 1v-1z" class="c"></path><path d="M245 483c0-1 1 0 1 0 1 1 1 2 0 3h0c-1-1-2-1-2-2l1-1z" class="U"></path><path d="M237 368h1c1 0 1 0 2 1 1 0 1 1 1 1h-1c0 1-1 1-1 2h-1l-1-4z" class="b"></path><path d="M248 396h0c0 2-2 3-2 5v4-3h-1v1h-1c0-1 1-2 1-2h0v-1c-1-1 0-1 0-2h1c1 0 1-1 2-2z" class="T"></path><path d="M224 528c0-1-1-2-2-3h0v-1l2-1c1 2 1 3 2 5v1c-1 0-1 0-2-1z" class="U"></path><path d="M245 380h0c1 1 0 3 1 4v1 4c0 1 0 2 1 2 0 1-1 1-1 2h-1l-1-1c0-1 0-2 1-2v-1-1-1-1-1h0v-3c0-1-1-1 0-2z" class="c"></path><path d="M210 482l1 1-1-2h1c-1-1 0-2 0-3h1l-1 1c0 1 1 3 2 5h0l1 2 1 1c0 2 1 2 1 3h0c-1-1-2-2-2-3-1-1-2-3-3-4 0 2 2 3 2 4 1 2 2 3 2 4-1 0-1-1-1-1-1-1-2-2-2-3s0-1-1-2v-1l-1 1h0v-3z" class="T"></path><path d="M212 491v1h1v2 1c1 1 0 1 1 2h0c0 1 0 2 1 2v1 1h0l1 1c0 1 1 3 1 4 1 1 2 3 3 4-1 2-2 2-3 4 0-2 0-4-1-6 0-2-1-4-1-6-1-1-1-2-1-3-1-2-2-6-2-8zm21-21h1l1 2s1 0 1 1c0-2-1-2 1-4 1 1 1 2 2 3v1c1 1 0 2 0 3-1-1-2-4-2-5v2 1c0 1 1 2 2 3 0 1 1 3 1 4v1s-1 0-1-1h0c0-1 0-1-1-1v-1-2c-1 0-1-1-1-1l-1-2v1s0 1-1 1c-2-1-2-4-2-6z" class="X"></path><path d="M253 497h1v-4c1 2 0 7 1 10-1 6-2 12-4 18l-2-1c3-8 4-15 4-23z" class="Y"></path><path d="M220 510v3c1 1 2 2 2 4l-1 1s0 1 1 1c1 1 1 3 2 4l-2 1v1h0c1 1 2 2 2 3-1 0-1-1-2-1v1c-1 1-1 2-1 3-1-1-2-4-2-5h0c1 0 2-1 2-1l-2-6h0l-2-5c1-2 2-2 3-4z" class="c"></path><path d="M241 372l1-2h1v2h1 1v5h-1c0 1 1 2 0 3v1h-1c0 1 0 1 1 2v2h0-1l-1-4h0l-1 1v-1c0-2 1-5 0-7l-1 1c-1 1-2 2-2 3h0-1c0-1 1-1 1-2-1-1-1-2-1-3l1-1h1c0-1 1-1 1-2h1v2z" class="U"></path><path d="M241 381h1c0-2 0-4 1-5 1 0 1 1 1 1 0 2 0 3-1 4h-1 0l-1 1v-1z" class="P"></path><path d="M241 372l1-2h1v2h1l-1 2s0 2-1 2v-3l-1-1z" class="b"></path><path d="M242 456v-1c1-1 0-2 0-2-1-1 0-7 0-8h0v-1c1 0 0-2 0-3h0-1 0c0-1 0-1-1-1 1-1 1-1 1-2h0v3l1-1v-1-2h0c1 1 0 1 0 1 0 1 0 1 1 2v4 2h0v1l1-1h1l1-2v-2-1l1 1h0v2l-1 1c0 1 0 2-1 3v3c1 1 1 0 0 1h0v-1l-1-1v-1c1 0 1-1 1-2h-1 0s0 1-1 1c1 1 0 2 1 3h-1 0s-1 1 0 1c0 1 1 1 1 2l-1 1s0 1 1 1c0 2-1 2-1 3v2h0l-1 1c1 2 1 3 1 5h0c0 1-1 1-1 2v2 1l-1-1v-1l1-1h0v-2-1h0c1-3-2-7 0-10z" class="X"></path><path d="M218 480c1 1 1 2 2 3s2 4 3 6c0 0 0-1-1-1v-4c0-3-2-7-1-10l2 6 1 1c0 1 0 2 1 3v2c1 1 0 4 1 5v1c1 1 1 1 1 2s1 3 1 4h0 0c-1-1-1-1-1-2h-1v-1s-1 0-1-1c0 0-1-1-1-2v-1 1h0v2 1 1c-1-1-1-2-1-2v-1-1c-1-1-1-3-2-4v-1l-1-1v-1c0-1 0-1-1-1 0-1-1-3-1-4z" class="c"></path><path d="M212 503c1 2 1 3 2 4 0 1 1 1 1 2s0 2 1 3v-1-3h0 0c1 2 1 4 1 6l2 5h0l2 6s-1 1-2 1h0c-1-1-1-2-2-3 0 1 0 1-1 2h0l-2-2h0l-2-5v-3h0c1 1 1 2 1 3h1v-1c0-2 1-3 1-5 0-1-1-2-1-3-1-2-2-4-2-5v-1z" class="L"></path><path d="M216 512v-1-3h0 0c1 2 1 4 1 6l2 5c-2-3-3-4-3-7zm26-91c0-1 1-1 1-2v3 1 1c-1 1 0 3-1 4v2c-1 1-1 2-2 2v5 2 1 1 2h0v2l-1 1h0c-1-1-2 0-2-2v-3-1h0c1 0 1 0 1-1 0 0 0-1-1-1v-2h0v3c0 1-1 2-1 2v1h0v4c-1-1-1-3-1-5 2-2 1-8 1-11 2-2 3-4 6-5 0-1 1-3 0-4z" class="T"></path><path d="M205 499c2 1 1-1 3 1v2h2l1-2c0 1 0 2 1 3v1c0 1 1 3 2 5 0 1 1 2 1 3 0 2-1 3-1 5v1h-1c0-1 0-2-1-3h0v3l-2-3h0l-3-3-1-2-1-3c1 1 1 1 2 1 0-2-2-6-2-9z" class="E"></path><path d="M205 499c2 1 1-1 3 1v2c1 3 2 6 3 10 0 1 1 2 0 3h-1 0l-3-3-1-2-1-3c1 1 1 1 2 1 0-2-2-6-2-9z" class="N"></path><path d="M206 510h2c1 1 2 3 2 5l-3-3-1-2z" class="Q"></path><path d="M226 479c1 0 1 0 2 1v1l1-1s0-1 1-1c1 1 1 2 2 3v-1c0-1-1-2-1-3h0l-1-1c1-1 0-3-1-4-1 0 0-2 0-3h-1l-2-3v-1-1-2c1 1 1 4 2 5v-2h0c0-1 0-2 1-2 0 1 1 3 1 4 1-1 2-3 3-4l1 2c0 1 1 2 0 4h-1c0 2 0 5 2 6 0 1 1 1 1 2h1 0-2l1 1c0 1 1 1 1 1l-1 1c0 1-1 1-1 1h-1c0 1 2 2 1 3l-1-1v-1 1c0 1 0 1 1 2v1 1l2-2c0-1 1-1 1-2h1 0c-1 2-3 4-3 5l1 1v2h-1v-2h0-1v1 1 1-1h-1v1h0v-2-1c-1-1-1-1-1-2h0c-1-1 0-2-1-3h0c0-1 0-2-1-2v-2c-1 0-1-1-1-1-1 1-1 1-1 2h-1c-1-1-2-2-2-3z" class="P"></path><path d="M234 466c0 1 1 2 0 4h-1v-2h0l1-2z" class="U"></path><path d="M229 359l8-12c1-2 3-3 4-4 1-2 5-6 7-7l1 1c-1 0-2 1-3 2h0c-2 3-5 5-6 7l-3 3c-3 4-5 7-7 12 0 0 1 0 1 1v2h1v-1h3c0 1 0 2 1 2h1v3l1 4-1 1c0-1-1-2-1-2 0-1 1-1 1-2-1 0-2 2-2 3s0 2-1 3v-1h0c0-2 0-4-1-6l-1-1v2c0 1 0 1 1 2 0 1-1 4-1 5s-1 3-1 4l-1 1v-1-4c-1-1-2-3-3-3 0 0-1 1-2 1v-2h0c1-3 2-6 3-8 0-2 1-3 1-4v-1z" class="P"></path><path d="M254 334v-6c1 1 0 4 0 6 0-2 0-3 1-4v3 124 31c0 5 1 10 0 15-1-3 0-8-1-10v4h-1l1-163z" class="a"></path><path d="M232 376c0-1 1-4 1-5-1-1-1-1-1-2v-2l1 1c1 2 1 4 1 6h0v1c1-1 1-2 1-3s1-3 2-3c0 1-1 1-1 2 0 0 1 1 1 2s0 2 1 3c0 1-1 1-1 2h1 0c0-1 1-2 2-3l1-1c1 2 0 5 0 7v1l1-1h0l1 4v2h1 0l-1 1v1l-1 1c-1 0-2-1-2-2-1 0-1-1-1-2h1v-1h0 0c-1 1-1 2-2 3v3h0v3 1-2h0c1-1 1-2 1-3h-1 1c1 1 1 1 0 2v1h1 0v1l1 1v-1h2 0 1c1 0 1 1 1 1v1s0 1-1 1v1 1h0c-1 2 1 5 0 6v2c-1 2-3 2-2 4v2c1 1 0 2 1 3v2-2-2l1-1 1 1h0c1 1 1 2 0 3 0 1-1 1-1 2h0c1 1 0 3 0 5h0v1h0 0v-1c1-1 0-2 1-3v-1c1-1 0-1 1-2h0v1 2c-1 1-1 1-1 3v1c1 0 1 0 0 1h0v4l-1 1v-3h-1c0 1 1 3 0 4-1-2-1-3 0-5v-1-1-2c1-1 1-3 1-4h-1c0 1-1 1-1 2 0-1 1-3 0-4v-1c0-1-1-1-1-2h0c-1 0-1 1-2 1v-1h-1-1s0-1-1-1h-1c-1-2 0-4 1-6h0v-1c0-1 1-1 1-2h0 0 1v-1-5c-1-1-1-1-1-2h0v-1l-1 2h-1v-2c-1-3 0-7 0-10h0c1-1 1-3 1-4h-3 0c-1-2 0-3-1-5z" class="L"></path><path d="M235 385c0 1 0 2 1 3h0c1-2 1-5 2-7h1c1 2 0 4-1 6s-1 4-1 7v2h0 0v-1l-1 2h-1v-2c-1-3 0-7 0-10z" class="D"></path><path d="M222 381h0c1-2 3-4 3-6v-3 2c1 0 2-1 2-1 1 0 2 2 3 3v4 1l1-1c0-1 1-3 1-4 1 2 0 3 1 5h0 3c0 1 0 3-1 4h0c0 3-1 7 0 10v2h1l1-2v1h0c0 1 0 1 1 2v5 1h-1 0 0c0 1-1 1-1 2v1h0c-1 2-2 4-1 6h1c1 0 1 1 1 1v1h1 1 0c0 1 0 1 1 1v2h1c0 2-1 3-1 4l1 2-1 1-1-1c-1 0-1 0-1 1l-1-1v-1l-1-1v-1-2-2c0-1-1-1-1-1-1-1-1-2-1-2h-2 0c-1-1-1-2-3-2v-1c-1-1 0-1 0-2l1-1v-2l1-1c-1 0-2-1-2-1 0-1-1-2-1-2h-2 0v-1l-1 2c0-2 2-6 2-8v-3l-1-1-1 1h-1c0-1 0-2-1-3h0c1-1 1-3 1-3v-2l1-1v-1-3l1-1v-1c-1 1-2 4-2 5l-1 2h0-1c-1-1 0-2 0-3z" class="V"></path><path d="M232 376c1 2 0 3 1 5h0 0c0 1 0 2-1 3h0v-4h-1c0-1 1-3 1-4zm-2 30c2 1 1 3 2 4-1 0-2 0-2 1-1-1-1-1-1-2l1-1v-2zm6 1h-1c1-2 1-6 2-8h0v4h1v1h-1 0 0c0 1-1 1-1 2v1h0z" class="D"></path><path d="M223 384c0-1 1-4 1-5 1-1 1-2 2-3 1 1 1 2 1 2 1 1 1 2 2 2v2c-1 0-2-1-3-2 0 1-1 2-1 3v-1-3l1-1v-1c-1 1-2 4-2 5l-1 2h0z" class="E"></path><path d="M229 395c0 1 0 2 1 2s2 0 2-1c1 1 1 2 1 4h0c-1 0-1 0-1 1s-1 3-1 4c-1 0-2-1-2-1 0-1-1-2-1-2h-2c0-3 1-5 3-7z" class="I"></path><path d="M228 394c0-1-1-2 0-3l-2-1c0-2 0-3 2-5 0-1 0-1 1-2 0 1-1 2-1 3 0 0 1 3 2 3v1l1-1v-3h1 0c0 1 0 2 1 3 0 2 0 4-1 6v1c0 1-1 1-2 1s-1-1-1-2l-1-1z" class="D"></path><path d="M228 394h1v-3h1c1 2-1 3 1 5l1-1v1c0 1-1 1-2 1s-1-1-1-2l-1-1z" class="C"></path><path d="M229 412c2 0 2 1 3 2h0 2s0 1 1 2c0 0 1 0 1 1v2 2 1l1 1v1l1 1c0-1 0-1 1-1l1 1 1-1-1-2c0-1 1-2 1-4h-1v-2c-1 0-1 0-1-1h0-1-1v-1h1 1v1c1 0 1-1 2-1h0c0 1 1 1 1 2v1c1 1 0 3 0 4 1 1 0 3 0 4-3 1-4 3-6 5 0 3 1 9-1 11 0 2 0 4 1 5v1h0v2h1v-2h1 1l1 1c0 1 1 2 0 2l1 1c0 2 0 4 1 5-2 3 1 7 0 10 0 1 0 2-1 2-1 1-1 2-1 3v1h-1c-1-1-1-2-2-3-2 2-1 2-1 4 0-1-1-1-1-1l-1-2c1-2 0-3 0-4l-1-2c-1 1-2 3-3 4 0-1-1-3-1-4-1 0-1 1-1 2h0v2c-1-1-1-4-2-5v2 1 1l2 3h1c0 1-1 3 0 3 1 1 2 3 1 4l1 1h0c0 1 1 2 1 3v1c-1-1-1-2-2-3-1 0-1 1-1 1l-1 1v-1c-1-1-1-1-2-1h0c-1-2-1-3-1-5h0c0-1 0-2-1-2v-1-1l-1-6c-1-2-1-4-1-5l-1-2v-3h0c0 1 0 2 1 4 1-3 0-7 1-9v-1c0-1-1-1-1-2h0v-1-2-1c0-1 0-2 1-3 1 0 1-1 3 0h0c0-1 1-2 1-3v-6c0-2-1-4 0-5 0 1 1 3 1 4v-1-2c-1-2-1-3-1-5h0v-5c1-2 1-3 2-4z" class="D"></path><path d="M231 420h1v3h1 0v1 6c-1 2 0 4-1 6v-5-2c-1-1 0-2 0-3-1 0-1-1-1-1 1-2 1-4 0-5z" class="V"></path><path d="M236 419v2 1c-1 2-1 4-1 6-1 0 0 0-1-1 0-1 0-2-1-3v-1c0-1 0-2 1-3 0-1 1-1 2-1z" class="E"></path><path d="M234 420c0 1 1 2 1 3s0 3-1 4c0-1 0-2-1-3v-1c0-1 0-2 1-3z" class="G"></path><path d="M238 447h1l1 1c0 1 1 2 0 2l1 1c0 2 0 4 1 5-2 3 1 7 0 10 0 1 0 2-1 2-1 1-1 2-1 3 0-1-1-2-1-2h0v-10c0-2 0-3 1-4v-3s-1-2-1-3h1l-2-2z" class="T"></path><path d="M229 412c2 0 2 1 3 2h0 2s0 1 1 2c0 0 1 0 1 1v2c-1 0-2 0-2 1-1 1-1 2-1 3h0-1v-3h-1l1 1c-1 1-1 1-2 0l-1-1-1 1h-1 0 0v-5c1-2 1-3 2-4z" class="S"></path><defs><linearGradient id="AO" x1="245.427" y1="453.543" x2="194.168" y2="402.719" xlink:href="#B"><stop offset="0" stop-color="#4c4c4c"></stop><stop offset="1" stop-color="#777677"></stop></linearGradient></defs><path fill="url(#AO)" d="M214 384v-1s0-1 1-1c0-1 1-3 2-4h0l1 1c2-1 3-2 4-4l1 1c0 2-1 3-1 5h0c0 1-1 2 0 3h1 0l1-2c0-1 1-4 2-5v1l-1 1v3 1l-1 1v2s0 2-1 3h0c1 1 1 2 1 3h1l1-1 1 1v3c0 2-2 6-2 8l1-2v1h0 2s1 1 1 2c0 0 1 1 2 1l-1 1v2l-1 1c0 1-1 1 0 2v1c-1 1-1 2-2 4v5h0c0 2 0 3 1 5v2 1c0-1-1-3-1-4-1 1 0 3 0 5v6c0 1-1 2-1 3h0c-2-1-2 0-3 0-1 1-1 2-1 3v1 2 1h0c0 1 1 1 1 2v1c-1 2 0 6-1 9-1-2-1-3-1-4h0v3l1 2c0 1 0 3 1 5l1 6v1 1c1 0 1 1 1 2h0 0c-1 2-2 2-2 4h0v2l-2-6c-1 3 1 7 1 10v4c1 0 1 1 1 1-1-2-2-5-3-6s-1-2-2-3c-1 0-1-1-2-2 0 1 1 1 0 2-1-2-1-1-2-2-1 0-1-1-1-2h-1v2l-1-2-1 1v-1c-1-1-1-2-1-2v-1-2c-1-1-1-2-1-3s0-4-1-5c0-1-1-1-1-2v-2-3c0-1 1-7 0-9l-1-1-2 1c1-1 1-2 3-3h0s1-1 1-2l-1-1c1-1 1-3 0-5l1-2h2v2-1c0-1 1-3 0-4h0v-1-1s1-1 0-2v-1c-1 0 0-1 0-2h1v2c1-1 1-2 0-3l1-3v-5h0c1-1 1-1 0-2l1-1v-1c0-2 0-4-1-6l2-1v-1-5c0-1 1-3 1-3 1 0 0 2 1 3h1v-4c1-2 2-5 2-8-2 0-2 0-3 1l-1-1s0-1-1-2h0 1z"></path><path d="M212 466l1 2v2l-1 1h0v-1l-1-1v-1-1l1 1v-2z" class="D"></path><path d="M229 404s1 1 2 1l-1 1v2l-2-1c0-2 0-2 1-3z" class="R"></path><path d="M219 423h0 1v5h-1c-1-1 0-4 0-5z" class="C"></path><path d="M209 465h1v3c1 1 1 2 1 3l-1 1v1l-1 1v-1-2c-1-1-1-2-1-3h1c1-1 0-2 0-3z" class="D"></path><path d="M223 400c0 2 0 6-1 8 0 1 0 1-1 0h0v-3c0-1 0-1 1-2 0 0 0-1-1-1h0l2-2zm-17 56h1v2 3c1 1 1 2 1 3 1-1 1-1 0-2v-2c0-1 0-2-1-4h1c1 3 0 6 1 9 0 1 1 2 0 3h-1c0-1 0-4-1-5 0-1-1-1-1-2v-2-3z" class="G"></path><path d="M222 459c0 1 0 3 1 5l1 6h-1c0-1-1-3-2-5v-6h1z" class="T"></path><path d="M212 412c2 2 0 5 1 7 0 1 0 1 1 2h0l1 1s-1 0-1 1h0c1 1 0 2 0 3h0-1c-1 0-1 1-2 1l-1-1c1-1 1-2 0-3l1-3v-5h0c1-1 1-1 0-2l1-1z" class="E"></path><defs><linearGradient id="AP" x1="223.14" y1="397.715" x2="215.711" y2="402.019" xlink:href="#B"><stop offset="0" stop-color="#545454"></stop><stop offset="1" stop-color="#737372"></stop></linearGradient></defs><path fill="url(#AP)" d="M218 405v-1c1-1 1-2 1-3 0-3 1-7 2-9v3c0 1 1 2 1 2 1 1 1 2 1 3l-2 2h0c1 0 1 1 1 1-1 1-1 1-1 2v3-2h-1c0 1-1 2-2 2h0v-3z"></path><path d="M217 405h1v3h0c1 0 2-1 2-2h1v2h0v2c1 0 2 1 1 1 0 3 1 7 0 9h-1v-2l-1 1s0 1-1 1v-3c-1 2-1 3-2 5 0-1-1-4-1-6v-1h-1 0c-1-1-1-2-1-2 1-2 1-5 2-7v1c1-1 1-1 1-2z" class="Q"></path><path d="M219 417v-4-2c1 0 1 0 2 1l1-1v6l-1-1h0l-1-1v4s0 1-1 1v-3z" class="M"></path><path d="M214 384v-1s0-1 1-1c0-1 1-3 2-4h0l1 1c2-1 3-2 4-4l1 1c0 2-1 3-1 5-1 1-1 4-2 5 0 0 0 1-1 1v1h0v-2-1c1-1 1-2 1-3v-2l-1-1v1c0 1-1 1-1 2-1 0-2 0-2-1-1 1-1 1-1 2 2 1 2 1 2 2h0l1 1v4h2 0l-1-1c1 0 1 1 2 2h1l-1 1h0c-1 2-2 6-2 9 0 1 0 2-1 3v1h-1c0 1 0 1-1 2v-1-1c-1 1-1 2-1 3h-1l-1-4v-1-5c0-1 1-3 1-3 1 0 0 2 1 3h1v-4c1-2 2-5 2-8-2 0-2 0-3 1l-1-1s0-1-1-2h0 1z" class="G"></path><path d="M213 398h1c0 2 1 3 0 5h-1v-5z" class="C"></path><path d="M214 386s0-1-1-2h0 1c2 0 2 0 3 1v1h1c-2 0-2 0-3 1l-1-1z" class="R"></path><path d="M221 391h1l-1 1h0c-1 2-2 6-2 9 0 1 0 2-1 3v1h-1v-5c1-2 0-5 0-6 1-1 1-3 2-4 0 1 0 1 1 1h1z" class="S"></path><path d="M212 466c0-1 0-2-1-3v-5-1h1l1-1c1 0 2 1 2 2 1 1 1 2 1 4 1 1 2 2 2 4v2c1 2 1 3 1 5v-2h1v-5c-1-1 0-2-1-3h0c1-1 0-4 1-5 1 1 1 3 0 4v6c1 1 2 6 1 6-1 3 1 7 1 10v4c1 0 1 1 1 1-1-2-2-5-3-6s-1-2-2-3c-1 0-1-1-2-2 0 1 1 1 0 2-1-2-1-1-2-2-1 0-1-1-1-2h-1v2l-1-2s0-1 1-1h0l1-1c-1-1-1-2-1-3h0l1-1v-2l-1-2z" class="T"></path><path d="M213 470c1 1 2 6 2 8l-1-1-1-3h0c-1-1-1-2-1-3h0l1-1z" class="L"></path><path fill="#fff" d="M304 228h0 0v-1c1 0 1-1 1-1 0-1 1-1 2-2l-3 6-1 1c5 0 38-1 40 1v1l-1-1v2c0 2 0 4 1 5-2 1-3 1-4 1h-10-2v1h-4c2 1 3 0 5 1-2 0-6 0-7 1-6 1-11 3-15 8-1 0-1 0-1-1-7 9-7 21-7 31l-1 2v26c0 1 1 3 1 5v22 8 143c0 11 0 23 2 33h2c2 5 3 8 9 10 3 2 7 2 11 3 9 1 18 1 27 1-1 1-1 1-1 2v9h-77-49c-5 0-11 1-16 0v-1h2l-1-1h-2c1-2 3-6 5-8 0-1 1-1 1-1 6 1 12 0 18-1 8-2 16-5 20-13l2 1c2-6 3-12 4-18 1-5 0-10 0-15v-31-124-71c-1-5-2-12-7-16-4-4-11-4-17-5h-13l-4 1v-1c0 1-1 1-2 1h-1-1c1-2 0-8 2-10h-1l3-1h17 29c3 0 8 1 10 0 1 0 2 0 3-1 1 0 1 0 2-1 1 0 2 0 3-1l1 1c1 0 2-1 3-1 3 0 6 1 8 1 2-1 4-1 6-1 2 1 6 1 8 0z"></path><path d="M297 309c0 1 1 3 1 5v22-5h-1c-1-2 0-5 0-7v-15z" class="U"></path><path d="M229 234c5 0 11-1 16 0h-1c-6 1-14 0-20 0-3 0-7 0-10 1 1 1 1 3 2 4v1l1-1v1h3c-2 0-4 0-6 1 0 1-1 1-2 1v-8h17z" class="g"></path><path d="M208 544h106-8-17-65-2v1c-5 0-11 1-16 0v-1h2z" class="e"></path><path d="M297 331h1v5 8 143c-1-3 0-6-1-9h0v5-152z" class="h"></path><path d="M297 483v-5h0c1 3 0 6 1 9 0 11 0 23 2 33h2c2 5 3 8 9 10 3 2 7 2 11 3 9 1 18 1 27 1-1 1-1 1-1 2v9h-77-49v-1h2 65 17 8c2-1 6-1 8-1h25v-6h-8c-10 0-20 0-29-4-2-1-5-2-7-4-7-8-7-35-6-46z" class="j"></path><path d="M302 520c2 5 3 8 9 10 3 2 7 2 11 3 9 1 18 1 27 1-1 1-1 1-1 2-1-1-4 0-6 0-5 0-10 0-15-1-7 0-14-2-20-5-4-2-5-6-7-10h2z" class="a"></path><path d="M304 228h0 0v-1c1 0 1-1 1-1 0-1 1-1 2-2l-3 6-1 1c5 0 38-1 40 1v1l-1-1v2c0 2 0 4 1 5-2 1-3 1-4 1h-10-2v1h-4c2 1 3 0 5 1-2 0-6 0-7 1-6 1-11 3-15 8-1 0-1 0-1-1-7 9-7 21-7 31l-1 2c-1-5 0-10 0-15 1-8 3-17 10-22 9-8 23-8 34-7l-1-5h-95c-5-1-11 0-16 0h-17v8h-1-1c1-2 0-8 2-10h-1l3-1h17 29c3 0 8 1 10 0 1 0 2 0 3-1 1 0 1 0 2-1 1 0 2 0 3-1l1 1c1 0 2-1 3-1 3 0 6 1 8 1 2-1 4-1 6-1 2 1 6 1 8 0z" class="U"></path><path d="M218 232l25 1h-29c4 1 13-1 15 1h-17v8h-1-1c1-2 0-8 2-10 2 1 4 1 6 0z" class="c"></path><path d="M305 250v-1c6-6 13-8 22-9v1h-4c2 1 3 0 5 1-2 0-6 0-7 1-6 1-11 3-15 8-1 0-1 0-1-1z" class="Y"></path><path d="M304 228h0 0v-1c1 0 1-1 1-1 0-1 1-1 2-2l-3 6-1 1c5 0 38-1 40 1v1l-1-1v2-1h-1-2-6-90l-25-1c-2 1-4 1-6 0h-1l3-1h17 29c3 0 8 1 10 0 1 0 2 0 3-1 1 0 1 0 2-1 1 0 2 0 3-1l1 1c1 0 2-1 3-1 3 0 6 1 8 1 2-1 4-1 6-1 2 1 6 1 8 0z" class="a"></path><path d="M214 231h17c-5 1-11 0-16 1h3 0c-2 1-4 1-6 0h-1l3-1z" class="Z"></path><defs><linearGradient id="AQ" x1="291.549" y1="234.515" x2="287.139" y2="224.393" xlink:href="#B"><stop offset="0" stop-color="#19171b"></stop><stop offset="1" stop-color="#373836"></stop></linearGradient></defs><path fill="url(#AQ)" d="M304 228h0 0v-1c1 0 1-1 1-1 0-1 1-1 2-2l-3 6-1 1h-10-23c1 0 2 0 3-1 1 0 1 0 2-1 1 0 2 0 3-1l1 1c1 0 2-1 3-1 3 0 6 1 8 1 2-1 4-1 6-1 2 1 6 1 8 0z"></path><path d="M220 240c9 0 23-1 30 6 7 6 7 19 7 28v52 97 49c0 12 0 25-2 38-1 6-3 13-7 18-7 7-20 9-30 10h-6-1c-1 0-3 3-3 4l-1 1h-2c1-2 3-6 5-8 0-1 1-1 1-1 6 1 12 0 18-1 8-2 16-5 20-13l2 1c2-6 3-12 4-18 1-5 0-10 0-15v-31-124-71c-1-5-2-12-7-16-4-4-11-4-17-5h-13l-4 1v-1c2-1 4-1 6-1z" class="b"></path><path d="M211 534c6 1 12 0 18-1 8-2 16-5 20-13l2 1c-4 7-9 10-16 12-4 2-8 2-12 3h-13c0 1-1 3-2 4l-1 2h1l-1 1h-2c1-2 3-6 5-8 0-1 1-1 1-1z" class="Z"></path><defs><linearGradient id="AR" x1="420.324" y1="497.14" x2="132.496" y2="385.745" xlink:href="#B"><stop offset="0" stop-color="#0b0b0b"></stop><stop offset="1" stop-color="#414141"></stop></linearGradient></defs><path fill="url(#AR)" d="M306 251c4-5 9-7 15-8h1l-1 1h0 1l-2 2h0c-2 1-5 2-7 3-4 2-8 7-9 11v3 1l3-3 4-2 1-1 1 1-1 1 2-1 1 1h-1v5 1l1 9 2 2c0 1-1 1-1 2v1h0l-1-1c0 1 0 1-1 2h-1 0c2 0 5 0 7-1 5-2 7-5 9-10v3h0 0c1-1 1-2 2-2v1l-3 4c2-1 3-2 5-2v1h0l1 1-2 2v1c1 0 1 0 1 1h-2c0 1 0 1 1 2h5c0 1 0 1 1 2-1 0-2 1-2 2-1 0-1 1-2 1h-1v2c-1 0-1 1-2 2l-1 1v1c-2 1-3 1-4 2h-1-1l-1 1-1 1h0 2c1 1 2 1 3 0h1v1c-1 1-2 1-3 3-1 1-2 3-2 4-1 1-3 2-4 3v1c1 1 2 1 3 1-4 0-10 0-14 2-1 0-1 0-2 1 3 0 8-1 11-1-1 0-2 1-3 1h-2 3 2 2c-2 1-5 1-7 1h-3v1c2 1 4 0 6 0h4c-1 1-2 4-3 5 0 1 1 2 1 3 7 2 14 4 21 7-1 0-1 1-1 1l16 6 3 2c1 1 1 2 2 3h0 0c1 1 1 2 1 2 0 2 2 3 3 5h0l2 2 2 2h-2c2 4 7 7 10 10l10 8c2 1 3 3 5 4 4 2 8 4 11 7 1 1 3 2 3 4h1c1 1 2 2 3 4 3 4 7 7 11 11h-1-1l-2-2v1h0v2c1 2 3 4 3 6 2 2 3 3 4 5l2 2h2l5 10c1 3 2 6 2 9 0 2 2 5 3 7l3 7-2 5v2c-4 13-8 25-13 37-8 19-17 36-29 53l-6 8c-1 0-2 2-3 2v1l-1 1-1 1c-2 1-4 2-5 4v3h1c-2 6-8 9-10 15-1 1-2 2-4 3 0 0-1 1-2 1 1 1 1 2 1 3l-1 1h-1c-1 0-1 1-1 2h0c-1-1-1-1-2-1v2c0 1-1 2-2 3l-2-2h0c0-1 0-1-1-2l-1 1h-2 0v1c-1 1-3 2-5 4l1 2-1 1h3v1l-4 2-5 5c-2 1-5 3-7 3h-3-2s-1 1-1 2c-1 1-2 2-3 2h-2-2c1-1 1-1 1-2-1-1-1-1-2-1l1-1c-2 0-2 0-3 1l-8 2c1 0 1 0 1 1l-1 1c-1 0-2 1-2 1l-4 1c0 1-1 1-2 2 1 1 1 1 2 3 2 0 5-2 7-2 0 1 0 2-1 2l-9 11c-1 0-2 2-2 2-2 2-5 4-7 6h0l-2 3c-1 0-1 0-2-1 0 1-2 3-3 3h-1s-1 0-1 1l-2-2h1v-2h-4v1c0 1-1 1-1 1h-3c-1-2-3-2-5-3v1c-1 0-1-1-2 0l-1 1h0l2 2 1 1c0 1 1 3 1 4v1c-2-5-6-9-10-12l-3-2c-1-1-2-1-3-1h0l-2-1-4-3c-1-1-2-1-3-1-2 0-4 1-5 2-2 1-3 1-5 1 1 0 1-1 1-1v-1h-2-1-3s-1 1-2 1c-2 1-4 1-6 2l-2 1-2 1-1 1c-1 0-2 1-3 1l-2-1c-1 1-1 1-1 0l-10-1c-1 0-2-1-4-1l-6-2c-2 0-5-1-6-3-5-2-8-5-12-8-2-1-4-4-6-6-1-1-1-2-2-3s-1-2-1-3c1 1 3 3 5 4h1 1c1 0 1 0 2-1l-4-2c-5-3-7-7-8-12-2-3-2-7-2-10l1-15v1-3c0 1 1 2 1 3l1 1 1-2h1v1l2-2c1-4 1-9 2-13h0l1-2h-1 0c-2-1-3-2-3-3l-7-8c-3-3-5-6-8-9-15-21-26-44-35-68-2-6-4-12-6-19l-1 1-1-1c0-1-1-3-1-4l-2-7c-1-3-2-6-2-9-2-8-3-15-4-22l-1-6-1-5v-3c0-3-1-5-1-8v-9c0-1-1-2-3-3h0l-1-1c0-2 0-2 1-2l2-2c0-1 0-1 1-2l1 2c0 1 0 1 1 2h0c1 0 2-1 2-1 0-1 0-2-1-3h1c1 0 2 0 2-1l1 2c1 1 1 3 1 4l-1 2s1 1 1 2v4c0 11 1 22 3 33 1 2 1 5 2 7l2 10c0 2 1 4 1 6l1-1 1 2h0l2-2v1c1-1 1-1 1-2v-3-2-1l1-1v-4h0l1-1c2-2 4-7 5-10 0-2 0-4 1-6v-5c1-3 2-6 4-8l3-4h1l-1 3h0l3-4c2 0 4-1 6-2 0-1 1-1 2-1s1 0 1-1c2 0 5 1 6-1h0l1 1v1c1 0 1 1 2 1 0 1 1 2 1 3h0v1c0 1 0 1 1 2-1 1-1 2-1 3v2l1 1h0c0 2-1 4-1 7-1 1-1 3-1 5v3c-1 1 0 3 0 4 0 2-1 4-1 6s1 3 1 5c0 3-1 5 0 8 0 3 1 5 1 8 1 2 1 5 2 7 0 2 1 4 2 6 4 10 9 19 15 27 2 3 5 5 7 8l1 1-1 1 3 3h2l6 4h2l-1-1 1-1h0l3 3c1 1 5 3 6 3v-1c2 1 2 1 3 2l2-2h0l1 3 1 2 3 3h0l2 3 2 5h0c0 2 1 2 1 4 0 1 0 1 1 2v1s0 1 1 1h1v-2s0-1-1-1v-3l1 1c0 1 1 3 1 4l1 1v2 1c-1 0-1-1-2 0-2 1-5-1-7 0 0 0-1 0-1 1-2 2-4 6-5 8h2l1 1h-2v1c5 1 11 0 16 0h49 77v-9c0-1 0-1 1-2-9 0-18 0-27-1-4-1-8-1-11-3-6-2-7-5-9-10h-2c-2-10-2-22-2-33V344v-8-22c0-2-1-4-1-5v-26l1-2c0-10 0-22 7-31 0 1 0 1 1 1z"></path><path d="M256 565l1 1c0 1 0 2-1 3v-1-1l-1-1 1-1z" class="U"></path><path d="M144 399h1c0 2-1 3-1 5-1 0-1 0-1-1s0-2 1-4zm-7 26v2c0 1 1 1 1 2v4l-1 1c-1 1 0 4 0 6v-15z" class="P"></path><path d="M307 344h0c1 0 2 1 3 2v1 1h-1 0c-1-1 0-1-1-2 0 0-1 0-1-1h1l-1-1z" class="j"></path><path d="M187 520h2-1l-2-2c0-1 0 0 1 0l1 1c0 1 2 2 3 2v1c-1 0-2-1-4-1h0v-1z" class="D"></path><path d="M305 324h-1c-1 0-2-1-2-1v-6h0 1 1l-1 1v2c0 1 1 3 2 4l2 2h-1c0-1 0-1-1-2h0z" class="P"></path><path d="M205 507h0l1 3 1 2c0 1-1 1-1 1h0-1l-1-1h-1v-1l1 1v-1c0-1 0-1-1-2l2-2z" class="b"></path><path d="M155 500h0 0c-1-1-1-1-1-2l-1-1v-2h-1c1-1 1-1 1-2h0v3c1 0 0 0 1 1 0 0 2 2 3 2 0 1 1 1 1 2v1c1 1 1 1 1 2v1c-1-1-1-2-2-2h0c-1-1-1-2-2-3z" class="D"></path><path d="M408 408l1-2 1 2c2 3 3 5 4 8h0c1 2 1 2 1 4 0-2-1-3-2-4-2-3-3-6-5-8z" class="e"></path><path d="M124 452h1c2 1 1 4 2 6v2h-1v-2c-1 1-1 1-1 2-1-1 0-2-1-3v-2h-1 0v-1h1v-2z" class="E"></path><path d="M302 331h0c-1-1-1-4-1-5h0c0 1 0 2 1 2 0 0 1 0 1 1h1c1 1 3 2 4 3h-1-1l1 1h-1c-2 0-2-1-4-2z" class="U"></path><path d="M200 548c1-1 2-1 2-2l1 1h0c1 1 1 1 2 3 0 0-1 0-1 1h0-1c-2 0-2 0-4-2 0 0 0-1 1-1h0z" class="X"></path><path d="M200 548c1-1 2-1 2-2l1 1c-1 1-1 2-3 2h0v-1z" class="U"></path><path d="M303 320l1-1c2 2 4 2 5 3v1l2 1h-6c-1-1-2-3-2-4z" class="L"></path><path d="M308 317h-1-3c0-1 0-1 1-1h3l1-1c-1 0-2 0-3-1h1 5-3v1c2 1 4 0 6 0l-1 2h-5-1z" class="P"></path><path d="M148 388h1c1 0 1 1 2 1 0 1 1 2 1 3h0 0c-1 0-2 0-3-1h0-1l-1-1c-1 1-1 2-2 3l-1 1-3-3v-1c1 1 2 1 2 2 2-1 3-1 4-2 0 0 0-1 1-2z" class="L"></path><path d="M325 603v2c-2 1-4 3-6 4-1-1-4-1-6-1l12-5z" class="M"></path><path d="M307 332h1c1 0 2 1 2 1 1 1 2 1 3 2h0v1s1 2 2 3c1 0 2 2 3 2 3 3 6 5 8 8 0 1 1 2 1 2l1 1h0l-1 1v-1c-2-2-4-5-6-7h0 0l-6-6-1-1s-1 1-1 0v-1l-2-2h0c-1 0-1-1-2-2-1 0-1 0-2-1z" class="j"></path><path d="M411 407l1 1c2 5 5 9 9 13v1h0c-3-3-5-5-7-8v-1 1c1 1 1 1 1 2 1 0 1 0 1 1s1 2 1 2l1 1h0v1s0 1 1 1v1c-1 0-1-1-2-2 0-1 0-1-1-2v-1l-2-2c-1-3-2-5-4-8l1-1z" class="c"></path><path d="M303 292c2 0 2 2 2 2 1 1 2 1 3 1 0 1 1 2 1 2 1 2 2 3 3 4v1h-1s0 1-1 1l-3-4 1 1h0c1 0 2 1 2 2l1-1c-1 0-2-2-2-3l-1-1-2 1h-1s-1-1-1-2c-1-1-1-2-1-4z" class="U"></path><path d="M120 423h1v1h1v4 3c-1 1 0 3-1 4l-1 1h0 0s-1 0-1-1v-3c1-2 1-3 0-5v-2c0-1 1-1 1-1v-1z" class="E"></path><path d="M191 502h0l3 3c1 1 5 3 6 3v-1c2 1 2 1 3 2s1 1 1 2h0c-5-1-10-4-15-7h2l-1-1 1-1z" class="R"></path><path d="M361 351l-1-2c1 0 2 2 3 2h1l2 2h-2c2 4 7 7 10 10l10 8-1 1c-1-1-2-2-3-2s-1-1-1-1h1 0l-1-1c-2-1-3-3-5-4l-2-2h-1c-1-1-1-1-1-2-2-1-6-4-7-6l-1-1s-1-1-1-2z" class="P"></path><g class="R"><path d="M159 536c1-1 1 0 1 0h2l1 1h0l6 3c1 0 2 0 2 1v1l-1 1h-1c-1-1-2-1-2-2h-4c0-1 0 0-1-1h0s-1-1-1-2v-1c-1 0-1-1-2-1z"></path><path d="M165 536c-1-1-2-1-2-2v-1l3 3 1 1c1-1 3 0 4-1-1 0-1 0-1-1h1 0l1 1c1 1 4 2 5 2 2-1 2-2 3-3l1 1-4 4h0 0c-1-1-1-1-2-1h-1v1h2c1 0 2 2 2 2v1c-1-1-2-1-2-2h-1c-1 0-1 0-2-1h-1 0c-1-1-1 0-1-1h-1c-1 0-1 0-1-1l-1 1h-1c1 0 0 0 1-1-1-1-1-1-2-1l-1-1z"></path></g><path d="M179 551l-1 1h0c-1 0-1 0-2 1v1h-1v-2h0c-1 0-1 0-1 1h-1v-1h1v-3c1 0 1 0 2-1l1-1c-1-1-1-1-2-1l1-1c2 0 4 1 5 2h0c1 1 2 1 2 1 0 1-2 2-3 2h0l-1 1z" class="E"></path><path d="M353 337l3 2c1 1 1 2 2 3h0 0c1 1 1 2 1 2 0 2 2 3 3 5h0l2 2h-1c-1 0-2-2-3-2l1 2-1-1h-1-2 0v-1h1 1c-3-3-5-8-6-12z" class="X"></path><path d="M421 414v-1l2 2 3 6c2 4 4 9 5 14l2 10c-1-1-2-3-3-4 0-1-1-2-2-3v-1-2h0l1 1h0l1 1v-1h0c0-2-2-3-3-5l1-1c0-2-1-4-2-6-1 0 0-2-1-3v-1c-1-1-2-3-3-4v-1l-1-1z" class="e"></path><defs><linearGradient id="AS" x1="417.04" y1="419.449" x2="419.163" y2="411.129" xlink:href="#B"><stop offset="0" stop-color="#2e2c2c"></stop><stop offset="1" stop-color="#4a4a4b"></stop></linearGradient></defs><path fill="url(#AS)" d="M412 408c1 0 2 0 3 1v1h0c1 2 2 4 4 5h0v-1h1s1 1 1 2h0l1 1s0 1 1 2v1 1 1l1 2s1 2 2 2v1 1c1 0 1 1 1 2-1-1-1-2-2-2 0-1 0-1-1-2 0-1-1-1-1-2l-2-2v-1c-4-4-7-8-9-13z"></path><path d="M181 564v-1c1 0 1 0 2 1h4 1l-2-2h0 0c1 0 2 1 3 1h0l1 1-1 1h-3l1 1s0 1-1 0l2 2c0 1 0 1 1 2l1 1v1c0 1 1 2 2 2 1 1 1 1 2 1h-2c0 1-1 2-1 2-1-3-2-5-5-7l-1-1c-2 0-2-1-3-2-1 0-1-1-2-1 1-1 1-1 1-2z" class="U"></path><path d="M180 566c1-1 1-1 1-2 1 1 3 1 3 2h1c1 1 2 2 2 3l-1 1-1-1c-2 0-2-1-3-2-1 0-1-1-2-1z" class="L"></path><path d="M185 566c0 1 0 1-1 1l-1-1h0 1 1z" class="C"></path><path d="M302 339h1l1 1h0c1 1 1 1 1 2v1c2 1 3 5 4 7s2 4 2 6c1 0 1 1 1 2h0c0 1 0 0-1 1v-1c-1-1-1-2-2-3 0-1-1-2-1-3l-1-1h0v-1c-1-1-2-3-3-5v-1l-1-1-1-1v1l-1-1v-2h1v-1zm0-8c2 1 2 2 4 2 0 1 0 1-1 1v1l3 3 3 3c0 1 0 1 1 2h0c0 1 0 1 1 2 2 2 4 6 5 9 0 1 0 3 1 4v1h0l-1-2c-1-5-5-11-8-15v-1l-2-2v1l4 7 1 1c1 3 3 6 4 9h0v1 2l-1-2c0-1 0-2-1-3v-1l-2-4c-1-2-1-3-2-4s-1-1-1-2l-2-3-2-2-2-2h0 0c-1-1-1-2-1-3 0 0 0-1 1-1l-2-2z" class="j"></path><path d="M124 428l-1-1c-1-4 0-9 2-12 1-1 1-2 1-3 0 0 0 1 1 1h0c0-3 2-7 3-9h1c0 1 1 3 0 3 0 1 0 2-1 3v1c0 1 0 1-1 1v1 1c0 1-1 1-1 2-1-1-1 0-1-1v-1l-2 2v1c0 1-1 1-1 2v1 1h0c-1 2-1 4 0 6h0 0c1 1 1 1 2 1l1-1 1 1c0 2 1 6 0 8h0v-4c-1-1-1-2-1-4h0c0 2 0 3-1 5v-2-1-1h-1l-1-1zm188-125v-1h2-1v-1c-1-1-1-2-2-3-1-3-2-5-2-7l1-1-1-1v-2c-1 0-1 0-1-1v-1c0-1 1-1 1-2h6 1c-1 1-2 2-4 2 0 1 0 1 1 2h-2c0 1 1 2 1 3v1h-1c0 1 1 1 1 2v5h0c1 0 2 3 2 3l1 1c-1 1-2 1-3 1z" class="T"></path><path d="M315 283h1c-1 1-2 2-4 2 0 1 0 1 1 2h-2c0 1 1 2 1 3l-1-1c0-1-1-2-1-2h0c0-1 1-2 2-3 0 0 1 1 1 0 1 0 1 0 2-1z" class="C"></path><path d="M377 374l1 1c3 2 5 5 7 7 1 0 1 0 1 1 1 1 2 2 2 3h0l1 1v1c-1-1-1-1-2 0l4 3h-1l-3-2h-1l-1-1h0c0-1 0 0-1-1h0c0-1 0-1-1-1v-1h0l2 1h0c0-1-4-4-5-5-1 0-2-1-2-1h-2c-1-1-1-1-1-2v-1l1 2h2c1 1 1 1 2 1h1c-1 0-2-1-2-2h1c-1-1-3-2-3-4z" class="b"></path><path d="M137 397l1 2c0 1-1 1-1 2h-1 0c-1 2-2 4-2 5l1 1-1 2v2c0 1-1 2-1 3h0v2s-1 0-1 1 0 4-1 5v1s-1 0-1 1h-1v-1c1 0 1-1 1-1 1-1 1-2 1-3v-2c1-2 2-4 2-6h-1l-1 3v1c-1 1-1 2-2 3v3c-1 2 0 4-1 5-1-1-1-4 0-6v-2h0l1-2v-1c1-1 1-2 2-4 0 0 1-1 1-2l-1 1v-2h0c1 0 1 0 1-1s0-2 1-2l4-8z" class="D"></path><path d="M309 322l8 1c7 2 14 4 21 7-1 0-1 1-1 1l-13-3-12-3h3c4 1 10 3 14 3-6-2-12-4-18-4l-2-1v-1z" class="O"></path><defs><linearGradient id="AT" x1="143.505" y1="397.732" x2="136.964" y2="397.565" xlink:href="#B"><stop offset="0" stop-color="#3f3e3f"></stop><stop offset="1" stop-color="#575757"></stop></linearGradient></defs><path fill="url(#AT)" d="M137 397c0-2 1-3 1-5 1-1 1-1 3-1l3 3 1-1h1c-1 1-1 2-3 3 0 1-1 2-1 2-1 1 0 3 0 4 0 2 0 3-1 4v-1c-1-1-1-2-2-3h1v-1h-1c-1-1-1-1-1-2h0l-1-2z"></path><path d="M138 399c1-2 1-4 3-5 0 1 1 1 1 2 0 2-2 3-2 5h-1c-1-1-1-1-1-2z" class="X"></path><defs><linearGradient id="AU" x1="111.052" y1="445.475" x2="115.168" y2="443.119" xlink:href="#B"><stop offset="0" stop-color="#656567"></stop><stop offset="1" stop-color="#7b7a7a"></stop></linearGradient></defs><path fill="url(#AU)" d="M111 438l1 2h0l2-2v1c0 6 1 11 3 17h-1c0-1 0-1-1-2v-2c-1 0-1-1-1-2v-1h0c0 1-1 1 0 2v1c0 1 0 1 1 1v2 1c1 1 1 1 1 2h0c0 1 1 3 1 4s1 1 1 2v1 1c1 1 1 2 1 3h1v2h0l1 1c-1 1 0 1 0 2s1 1 1 1v1c1 3 3 5 3 8-1-2-2-5-4-8-2-7-5-15-7-22-2-5-3-10-4-15l1-1z"></path><path d="M315 315h4c-1 1-2 4-3 5 0 1 1 2 1 3l-8-1c-1-1-3-1-5-3 1 0 1 0 1-1h2l1-1h1 5l1-2z" class="X"></path><path d="M404 395h0c2 0 5 4 6 6l5 5c0 1 1 2 2 2 2 2 3 3 4 5v1l-2-2h-1l2 2h-1v1h0c-2-1-3-3-4-5h0v-1c-1-1-2-1-3-1l-1-1c-1-3-3-5-5-8h0l1-1-3-3z" class="P"></path><path d="M407 398l3 5 4 4c2 2 4 5 5 7v1h0c-2-1-3-3-4-5h0v-1c-1-1-2-1-3-1l-1-1c-1-3-3-5-5-8h0l1-1z" class="L"></path><path d="M316 283c1 0 3 0 4-1-1 1-1 2-2 3h-1v1s0 1 1 1l3 1 2 1-1 1h-2 0l1 2-1 1c-1 0-1 1-2 1s-1 0-2-1l-3-2h0l-1-1c0-1-1-2-1-3h2c-1-1-1-1-1-2 2 0 3-1 4-2z" class="E"></path><path d="M318 287h0v2h-1c-1 0-3-2-4-3 1 0 2-1 4-1v1s0 1 1 1z" class="I"></path><path d="M313 291v-2-1h1 0c1 1 5 2 5 3v1c-2 0-4-1-6-1h0z" class="S"></path><path d="M182 546c1 0 2 1 2 1h2 0c1 0 4-2 4-3h1c-1 0-1 0-1-1h0l1-1h-1c0-1 0-1 1-2l1 1c-1 0-1 0-1 1h1 0v1l-1 1h1l-2 2c-1 0-1 1-2 1-1 1-2 1-2 1h-1v1c1 0 1 0 2 1 0 0 1 0 1 1 2 0 3 1 4 1 1 1 1 1 2 1v1h-1v-1h-1c0 1 1 1 1 1h0-2 0-1c-1 0-2-1-2-1h-1-1-2 0c-1 0-1 0-2 1h0 0c1 1 2 2 3 2s2 2 3 2h1c0 1 1 1 1 2h-1-1c0-1-1-2-2-2v2c0-1 0-1-1-2h0v-1c-1 0-2-1-3-2h-1 0s0-1-1-1h0l1-1h1v-1h-1c0-1-1-1-2-1l1-1h0c1 0 3-1 3-2 0 0-1 0-2-1h0 1l-1-1h1z" class="D"></path><path d="M184 553l-2-2h1c1 0 3 1 4 1l1 1h-1-1-2z" class="U"></path><defs><linearGradient id="AV" x1="408.986" y1="396.486" x2="396.003" y2="398.475" xlink:href="#B"><stop offset="0" stop-color="#1e2120"></stop><stop offset="1" stop-color="#5e5b5c"></stop></linearGradient></defs><path fill="url(#AV)" d="M401 398c-1-2-3-3-4-5-2-2-3-4-4-6h0l1 1 1-1c2 2 4 5 5 7l2 2h2v-1l3 3-1 1h0c2 3 4 5 5 8l-1 1-1-2-1 2c-1-4-4-7-7-10z"></path><path d="M404 395l3 3-1 1h0l-4-3h2v-1z" class="C"></path><path d="M312 290l1 1h0l3 2c1 1 1 1 2 1s1-1 2-1h2l-1 2 2 1-1 1h0 0-1c0 1-1 2-2 2h-1l1 1-1 1v2c-1 1-1 1-2 1l-1-2-1-1s-1-3-2-3h0v-5c0-1-1-1-1-2h1v-1z" class="L"></path><path d="M317 295v2l-2-2v-1l2 1h0z" class="C"></path><path d="M320 293h2l-1 2 2 1-1 1h0 0-1l-4-2h0l1-1h0c1 0 1-1 2-1z" class="U"></path><path d="M320 293h2l-1 2-3-1h0c1 0 1-1 2-1z" class="D"></path><path d="M314 301v-1l2 2 1-1c0-3-2-2-3-4v-1c1 1 1 1 2 1 0 1 1 2 2 2h0l1 1-1 1v2c-1 1-1 1-2 1l-1-2-1-1z" class="C"></path><path d="M305 324h0c1 1 1 1 1 2h1l1-1v1h1 1 0 1c1 1 3 1 3 2 1 0 1 0 1 1h1l5 3 4 2v1l-1-1-1 1 2 1 1 1c1 0 1 1 1 1-1 0-2-1-3-1l-3-2c-1-1-3-3-5-3-1-1-2-2-3-2l-2-1h0v1c-1 0-2-1-3 0v-1h-1c0 1 1 1 1 1h1l2 2h1 0 1c0 1 1 1 1 2h1c3 2 6 5 9 6l2 2h-1s-1-1-2-1c-1-1-2-2-3-2l-9-6-5-4-1-1v-1h0c0-1-1-1-1-2l1-1z" class="c"></path><path d="M286 614c9-1 18-3 27-6 2 0 5 0 6 1-10 4-22 7-33 8l-1-1h-1l2-2z" class="J"></path><path d="M423 415h2l5 10c1 3 2 6 2 9 0 2 2 5 3 7l3 7-2 5v2c0-4-1-6-3-10l-2-10c-1-5-3-10-5-14l-3-6z" class="L"></path><path d="M390 388c-1 0 0 0-1-1v-1h2v1c-1-1-1-2-2-3 1 0 1 0 2-1h-1c0-1 0-1-1-1s-1-1-2-1h-1l-1-1c-1 0-3-2-4-3h-1c-1-1-3-3-3-5v-1h0l1-1-4-3-4-3c-1-1-3-2-4-4h1l2 2 4 3c1 0 1 1 1 1 1 1 3 2 3 3 1 0 1 1 2 1h1c0 1 0 2 1 3l-1 1c0 2 3 4 4 5h1 1 0c1 0 4 2 5 3 0 1 1 2 2 3l2 2-1 1-1-1h0c1 2 2 4 4 6 1 2 3 3 4 5-2 0-3-2-4-3h0v1h0-1l1 1 1-1v1l1 1 1 1v1c-1 0-2-1-3-2l-2-2c-1-2-2-2-3-4l-1-1c0-1-1-2-1-3z" class="P"></path><path d="M390 388v-1c1 0 2 2 3 2 0 1 2 4 2 5-1 0-2-2-3-2l-1-1c0-1-1-2-1-3z" class="L"></path><path d="M328 276c2-1 3-2 5-2v1h0l1 1-2 2v1c1 0 1 0 1 1h-2c0 1 0 1 1 2h-1-2l-1 1h2v1h-3-1l-1 1v2h-3v1h-1l-3-1c-1 0-1-1-1-1v-1h1c1-1 1-2 2-3 4-1 6-3 8-6z" class="B"></path><path d="M325 285l-4-1v-1c1 0 2-1 2-2h1 1c1-1 1-1 2-1h1c1-1 1-1 2-1v1l-1 1c-1 1-1 1-2 1h-1v1h1l-1 1-1 1z" class="M"></path><path d="M179 568v-1h-1v-1h1c1 1 2 1 3 1 1 1 1 2 3 2l1 1c3 2 4 4 5 7 0 0 1-1 1-2h2l2 1c0 1 0 2-1 3h0v3l1 1-1 1c-2-1-4-3-6-5l-3-3c-3-2-4-6-7-8z" class="F"></path><path d="M191 579c1-1 2-1 4-1v1 3c-1 0-2-1-3-2h0l-1-1h0z" class="K"></path><path d="M194 575l2 1c0 1 0 2-1 3h0v-1c-2 0-3 0-4 1v-2s1-1 1-2h2z" class="N"></path><path d="M380 370c1 0 2 1 3 2l1-1c2 1 3 3 5 4 4 2 8 4 11 7-1 1-3 0-5 0 0 0-1-1-2-1v1h0v3c-1-1-2-2-2-3-1-1-4-3-5-3h0-1-1c-1-1-4-3-4-5l1-1c-1-1-1-2-1-3z" class="D"></path><path d="M389 375c4 2 8 4 11 7-1 1-3 0-5 0 0 0-1-1-2-1v1c-2-1-4-3-5-4 1 0 3 1 4 2h2c-1-2-4-3-5-5z" class="G"></path><path d="M322 297h2c1 1 2 1 3 0h1v1c-1 1-2 1-3 3-1 1-2 3-2 4-1 1-3 2-4 3v1c1 1 2 1 3 1-4 0-10 0-14 2-1 0-1 0-2 1h0-1 0v-2h1c0 1 1 0 2 0h1v-1c1 1 2 1 3 0h2 1c0-1 2 0 3 0v-2h0 0l-2 1h-2c-1 0-2-1-2-2v-1-2h-1l1-1c1 0 2 0 3-1l1 2c1 0 1 0 2-1v-2l1-1-1-1h1c1 0 2-1 2-2h1 0z" class="X"></path><path d="M321 297h1c1 1 2 1 2 2v2h-2v1c0 1-2 2-3 4-1 0-1 1-1 1v1l-2 1h-2c1 0 2 0 3-1v-1c0-2 1-3 2-4h0v-1h0l1-1-1-1-1-1h1c1 0 2-1 2-2z" class="T"></path><path d="M319 300l1 1-1 1h0v1h0c-1 1-2 2-2 4v1c-1 1-2 1-3 1s-2-1-2-2v-1-2h-1l1-1c1 0 2 0 3-1l1 2c1 0 1 0 2-1v-2l1-1z" class="L"></path><path d="M147 526v-1l1 1c0 2 1 3 2 4l4 5 3 6c2 1 6 4 6 6 0 0 3 2 4 3h1c1 1 2 1 3 2v1h0-1 0c-1 0-2-1-2-1l-2-1v1c2 1 3 1 5 2h0c0-1 1-2 0-2v-1-1l1-1h-1l2-2v1c-1 1-1 2-1 4 0 1 0 1 1 2h-1 1 0 1v1h1 1l1-1c1 0 2 0 3-1v-1 2c-1 1-2 1-3 1h-1l1 1h0s-1 0-2 1h1 0c1 0 1 1 2 1h-4-2-2l-1 1h-1v-1h1c-3-4-8-8-10-12v-1c-1-2-3-5-4-7-3-4-6-8-8-12z" class="R"></path><path d="M172 558h2 4 1c1 0 1 1 1 1h2 1c1 1 2 1 3 1 1 1 1 1 2 1h0c1 1 1 1 3 2h-2 0c-1 0-2-1-3-1h0 0l2 2h-1-4c-1-1-1-1-2-1v1c0 1 0 1-1 2-1-1-1-1-2-1h-2v2c-2-1-3-3-4-4h0v-1c-1 1-1 2-1 3l-2-1h0c-1-1-2-1-2-2l1-2h-1l1-1h1l1-1h2z" class="P"></path><path d="M174 560c1 1 1 1 2 3h-1-3 0c0-1 0-1 1-2 1 0 1 0 1-1z" class="G"></path><path d="M169 559l1-1h2l2 2h0c0 1 0 1-1 1-1 1-1 1-1 2v-1c-1 1-1 2-1 3l-2-1h0c-1-1-2-1-2-2l1-2h-1l1-1h1z" class="N"></path><path d="M169 559l1-1h2l2 2c-1 0-1-1-2 0h-1l-1-1h-1z" class="Q"></path><path d="M169 559h1c0 2 0 3-1 5h0c-1-1-2-1-2-2l1-2h-1l1-1h1z" class="J"></path><path d="M181 546c-1 0-2-1-2-1v-2c0-1 0-1 1-2v-2h-1c1 0 1 0 2-1 1 0 2-1 3-1 1-1 2-1 3-1h0v-2l2-2h2 0l1-1c1 0 2 1 4 3h-1v1l-2 1h0l-2 2h0 1 1s0-1 1-1v1h-1c-1 0-1 1-2 1 0 1-1 1-1 1h-1v2h0-1 0s-1 1-2 1c0 0-1 0-1 1h-1 0v1h0c0 1 1 1 1 1l1-1h1c-1 2-1 2-3 2l-1-1h-1-1z" class="E"></path><path d="M332 282h5c0 1 0 1 1 2-1 0-2 1-2 2-1 0-1 1-2 1h-1v2c-1 0-1 1-2 2l-1 1v1c-2 1-3 1-4 2h-1-1l-1 1-2-1 1-2h-2l1-1-1-2h0 2l1-1-2-1h1v-1h3v-2l1-1h1 3v-1h-2l1-1h2 1z" class="V"></path><path d="M326 284h1 1c0 1 1 1 2 1v1l-7 1c1 1 1 1 2 1-1 1-1 1-2 1l-2-1h1v-1h3v-2l1-1z" class="I"></path><path d="M332 282h5c0 1 0 1 1 2-1 0-2 1-2 2-1 0-1 1-2 1l-2-2h1 2v-1c-1 0-2-1-3-1l-1-1h1z" class="L"></path><path d="M322 293h2v-1h-1v-1h3c0-1 0-1 1-1h3v-1c-1 0-2 0-3-1 1 0 3-1 4-1h2v2c-1 0-1 1-2 2l-1 1v1c-2 1-3 1-4 2h-1-1l-1 1-2-1 1-2z" class="T"></path><path d="M324 295s1 0 2-1 0-1 0-2c2 0 3-1 5-1l-1 1v1c-2 1-3 1-4 2h-1-1z" class="X"></path><path d="M138 399h0c0 1 0 1 1 2h1v1h-1c1 1 1 2 2 3v1 1c-1 2-2 4-4 7-1 2-2 8-2 11h1v-2h1v2 15 3c0 5 0 10 1 14v1h0v-2-1c1-1 0-2 0-4v-5h0c0 2 0 4 1 6 0 1-1 3 0 4h0v3h-1c-2-1-1-2-1-4h0c0-1-1-2-1-2v-1h0 1c0-1-1-1-1-2 0-3 1-13 0-14v-6c0-1 0-2 1-3 0-1 0-2-1-3v1 2c0 1-1 1-1 1v1l-1 4v3l-1 3c-1 1-1 3-1 5 1 2 1 4 1 5 1 1 0 5 0 6h0v-1c-1-1 0-3-1-4v-1-1-1h0l-1 1v1-1c-1-1 0-1-1-2v-1c1-1 0-2 1-2v3h0v-5h-1v1 3c0 2 1 6 0 8h-1l1 1v3 1h1v1c-1 1 0 1-1 2v-1c-1-1 0-3-1-4v-8-3l-1-1v-2s0 1 1 2v-2c0-1 1-2 2-2h0c1-1 1-2 2-3v-3s1 0 1-1-1-3 0-4v-1s0-1-1-1h0l1-1v-2c0-1 0-2 1-3v-3h0c1 0 0-1 1-1v-6h0v-1c0-1 1-1 1-2v-1-3-3c0-1 1-1 1-2z" class="L"></path><path d="M139 402c1 1 1 2 2 3v1 1c-2 0-2 1-3 2 0-2 1-5 1-7z" class="C"></path><path d="M305 250c0 1 0 1 1 1-4 6-5 11-6 18v8c-1 5-1 10-1 16 0 13 1 27 0 41v-1c0-1 1-4-1-5v10 6-8-22c0-2-1-4-1-5v-26l1-2c0-10 0-22 7-31z" class="a"></path><path d="M298 281v33c0-2-1-4-1-5v-26l1-2z" class="e"></path><path d="M393 382v-1c1 0 2 1 2 1 2 0 4 1 5 0 1 1 3 2 3 4h1c1 1 2 2 3 4 3 4 7 7 11 11h-1-1l-2-2v1h0v2c1 2 3 4 3 6-1 0-2-1-2-2l-5-5c-1-2-4-6-6-6h0v1h-2l-2-2c-1-2-3-5-5-7l-2-2v-3h0z" class="R"></path><path d="M403 386h1c1 1 2 2 3 4-3-1-4-2-5-4h1z" class="Q"></path><path d="M400 394h1l1-1h-1v-1c1 0 2 1 3 2 1 0 1 0 2 1v-2h0l1 1c0 3 3 4 3 7-1-2-4-6-6-6h0v1h-2l-2-2z" class="G"></path><path d="M407 394c2 2 4 5 7 6h0v2c1 2 3 4 3 6-1 0-2-1-2-2l-5-5c0-3-3-4-3-7z" class="D"></path><path d="M393 382v-1c1 0 2 1 2 1 2 0 4 1 5 0 1 1 3 2 3 4h-1l-1 1c2 2 3 3 4 5-4-3-8-6-12-10h0z" class="M"></path><path d="M395 382c2 0 4 1 5 0 1 1 3 2 3 4h-1l-1 1-6-5z" class="I"></path><path d="M178 497c-4-3-7-7-10-11-12-16-19-36-20-56 0-3 0-6 1-10v-6c0-2 1-4 2-7 0-3 1-6 1-9v2l1 1h0c0 2-1 4-1 7-1 1-1 3-1 5v3c-1 1 0 3 0 4 0 2-1 4-1 6s1 3 1 5c0 3-1 5 0 8 0 3 1 5 1 8 1 2 1 5 2 7 0 2 1 4 2 6 4 10 9 19 15 27 2 3 5 5 7 8l1 1-1 1z" class="G"></path><path d="M349 534h1v13c-12-1-25 0-37 0h-67l-22-1c-4 0-8 1-11 0-3 0-6 0-10 1h0l-1-1c1 0 2-2 3-3h2l1 1h-2v1c5 1 11 0 16 0h49 77v-9c0-1 0-1 1-2z" class="Y"></path><path d="M207 543l1 1h-2v1c5 1 11 0 16 0h49c-3 1-5 0-7 0h-14-43v1h6c-3 0-6 0-10 1h0l-1-1c1 0 2-2 3-3h2z" class="a"></path><path d="M133 391c2 0 4-1 6-2 0-1 1-1 2-1s1 0 1-1c2 0 5 1 6-1h0l1 1v1h-1c-1 1-1 2-1 2-1 1-2 1-4 2 0-1-1-1-2-2v1c-2 0-2 0-3 1 0 2-1 3-1 5l-4 8c0-1 1-2 1-3h-1-1-1v2h-1c-1 2-3 6-3 9h0c-1 0-1-1-1-1 0 1 0 2-1 3-2 3-3 8-2 12l1 1v4c-1 1 0 2 0 3v5h-1v-2-1-3c0-1-1-13 0-13v-1-2h-1 0-1l-3 6v4h0v-1-1h1c0 1 0 2-1 3 0 2 1 5 0 6v1c1 1 1 4 1 5v1h0c1-1 1-1 1-2h0v3l-1 1c0-1-1-1-1-2v-1-4c-1-1-1-3-1-5v2h0l-1-4v-4h0l1-1c2-2 4-7 5-10 0-2 0-4 1-6v-5c1-3 2-6 4-8l3-4h1l-1 3h0l3-4z" class="E"></path><path d="M148 388c-1 1-1 2-1 2-1 1-2 1-4 2 0-1-1-1-2-2v1c-2 0-2 0-3 1 0 2-1 3-1 5l-4 8c0-1 1-2 1-3h-1-1c1 0 1-1 2-1 0-3 1-5 2-7h0c0-1 1-1 1-2s2-2 3-3h3 4l1-1z" class="V"></path><path d="M298 344v-6-10c2 1 1 4 1 5v1 42 83 29c0 11 1 21 3 32h-2c-2-10-2-22-2-33V344z" class="d"></path><path d="M171 565c0-1 0-2 1-3v1h0c1 1 2 3 4 4v-2h2c1 0 1 0 2 1 1 0 1 1 2 1-1 0-2 0-3-1h-1v1h1v1c3 2 4 6 7 8l3 3c2 2 4 4 6 5l6 6h-1l1 2v2c0 1-1 1-2 2v1l-1-1h-1s0-1-1-1h-6l-1 1v1 1 1h-1-1-1v-1c-1-1-1-1-1-3 1-1 1-2 1-3 1-2 2-2 4-3l1-1-2-1h-1c-1-1-1-2-1-2-1 0-1 0-2 1v1h-2l1-1v-2-1c-1-1-2-2-2-4-1 1-2 0-2 0l-1-2h3c-4-4-8-8-11-12z" class="E"></path><path d="M190 592l3-2h0l1 1v1h-3c0 1 0 2-1 2s-1 1-2 2c-1-1-1-1-2-1l1-2c2 0 2 0 3-1z" class="Y"></path><path d="M191 582c1 1 5 4 5 5-1 1-1 1-2 1h-3 0l-2-1 2-2h0 1c-1-1-1-2-1-3z" class="W"></path><path d="M191 585l3 3h-3 0l-2-1 2-2h0z" class="I"></path><path d="M191 588l1 2c-1 0-1 0-2 1v1c-1 1-1 1-3 1l-1 2c1 0 1 0 2 1h-1l2 2v1h-1-1-1v-1c-1-1-1-1-1-3 1-1 1-2 1-3 1-2 2-2 4-3l1-1h0z" class="H"></path><path d="M187 596l2 2v1h-1-1l-1-2 1-1z" class="a"></path><g class="F"><path d="M191 588l1 2c-1 0-1 0-2 1v1c-1 1-1 1-3 1 1-1 2-2 3-4l1-1h0z"></path><path d="M191 592l1 1v-1l1 1h1c1-1 1 0 3-1 0-1 0-1-1-3l1-1c1 1 2 2 3 2l1 2v2c0 1-1 1-2 2v1l-1-1h-1s0-1-1-1h-6l-1 1v1 1l-2-2h1c1-1 1-2 2-2s1-1 1-2z"></path></g><path d="M171 565c0-1 0-2 1-3v1h0c1 1 2 3 4 4v-2h2c1 0 1 0 2 1 1 0 1 1 2 1-1 0-2 0-3-1h-1v1h1v1h-1c3 6 9 10 13 14h0c0 1 0 2 1 3h-1 0l-2 2h-1c-1-1-1-2-1-2-1 0-1 0-2 1v1h-2l1-1v-2-1c-1-1-2-2-2-4-1 1-2 0-2 0l-1-2h3c-4-4-8-8-11-12z" class="J"></path><path d="M182 577c1 1 2 1 3 2-1 1-2 1-3 0h0c-1 1-2 0-2 0l-1-2h3z" class="S"></path><path d="M182 579h0c1 1 2 1 3 0v1c1 0 2 1 3 1s1 1 2 1h1 0c0 1 0 2 1 3h-1 0l-2 2h-1c-1-1-1-2-1-2-1 0-1 0-2 1v1h-2l1-1v-2-1c-1-1-2-2-2-4z" class="V"></path><path d="M184 583s1 1 2 1l1 1c-1 0-1 0-2 1v1h-2l1-1v-2-1z" class="I"></path><path d="M185 580c1 0 2 1 3 1s1 1 2 1h1 0c0 1 0 2 1 3h-1 0c-2-1-4-3-6-5z" class="M"></path><path d="M161 499c1 0 2 1 2 2h1l2-2c1 0 1 1 2 1v1h0v-1h1 0c0 1 1 2 2 2v1l-2-1h0l2 2c1 2 4 7 6 7h1c1 1 1 1 2 1v1h1s0 1 1 1h0v-1l-3-4h1l3 3h1 0c3 1 4 3 6 4l-1 1v-1c0 1 0 1 1 2 0 0 0 1 1 1l1 1h-1c-3-2-6-4-8-6h0v1c1 0 1 0 1 1h1v1 1c-1-1-2-1-2-2h0l-1-1s-1 0-1-1l-1 1c2 2 5 3 7 5v1h0c2 0 3 1 4 1h1l1-1 1 1h0c-1 0-1 0-2 1 1 0 1 1 2 1h0c1 0 1 0 2 1h0 1v-2h0c1 1 1 1 2 1v-1-1h1v1 1 1s1 1 2 1c-1 1-1 2-2 3h0s1 1 1 2h-1l-1-1-1 1h0l1 1c0 1 0 1 1 2v1h0 0c-1 1-2 2-3 2v-1s0-1 1-1v-1c-1 1-2 1-3 2h-1s-1 1-1 0h0l2-1c1-1 2-1 3-1v-1h-1v-1s0-1 1-2v-1h-1 0l-1 1-1 1 1 1h0-1c0-2 0-2 1-4h-1-1-1c1 0 1-1 1-1l-1-1h0l-1-1v1c-1 0-1 0-1 1-2 0-2 1-4 1h0-1l-2-1h0l-1-1-1 1-1 1h-1l2-2-2-2c1-1 1-1 2-3h0 1c0-1 0-2-1-3l-3-1-2-2h-1c1 1 1 2 1 3v-1c-1 0-2 0-3 1h0c-1 0-2-1-3-1l-3-3c-1-2-2-2-2-4 0 0 1 0 1 1h1v-2s0-1-1-1c-1-1-1-2-1-4h0-1c-1-2-3-3-4-5h0z" class="T"></path><path d="M182 518c1 1 3 2 4 4l-1 1-2-1-1 1v2s1 0 1 1l-1 1-1 1h-1l2-2-2-2c1-1 1-1 2-3h0 1c0-1 0-2-1-3z" class="D"></path><path d="M378 565h1c-2 6-8 9-10 15-1 1-2 2-4 3 0 0-1 1-2 1 1 1 1 2 1 3l-1 1h-1c-1 0-1 1-1 2h0c-1-1-1-1-2-1v2c0 1-1 2-2 3l-2-2h0c0-1 0-1-1-2l-1 1h-2 0c-6 4-12 8-18 11-3 1-5 3-8 3v-2c8-4 16-8 23-13 3-2 6-4 9-7-1-1-2-3-2-5 0-1 0-1 1-2h1l1 2v-3c0-1 1-1 2-1h1l1-2c1-1 2-1 3-1v1h0c1-1 1-3 2-3 2-1 4-2 5-2 2-1 3-1 4 0l2-2z" class="J"></path><path d="M358 578v-3c0-1 1-1 2-1v1c0 1-1 3-1 3v1h-1v-1z" class="F"></path><path d="M355 592v-2l1-1c1 1 1 1 3 2h0c0 1-1 2-2 3l-2-2z" class="L"></path><path d="M359 589h0-1c2-2 4-3 5-5 1 1 1 2 1 3l-1 1h-1c-1 0-1 1-1 2h0c-1-1-1-1-2-1z" class="D"></path><path d="M361 574l1-2c1-1 2-1 3-1v1h0c-1 1-1 2-1 3-1 1-2 2-2 3-1 1-1 2-2 2v-2-1-1h1v-1-1z" class="a"></path><path d="M306 251c4-5 9-7 15-8h1l-1 1h0 1l-2 2h0c-2 1-5 2-7 3-4 2-8 7-9 11v3 1l3-3 4-2 1-1 1 1-1 1 2-1 1 1h-1v5 1l1 9 2 2c0 1-1 1-1 2v1h0l-1-1c0 1 0 1-1 2h-1 0-3c-2 0-3-1-4-1-1 1-1 1-2 0h0-1c0-1-1-1-1-2v-1c-1-1-1-3 0-4v-1h-1c0 1 0 1-1 2v2 4 10-4-9-8c1-7 2-12 6-18z" class="j"></path><path d="M303 271c0 2 0 2 1 4l1 2c2 1 4 3 6 3l2 1h0-3c-2 0-3-1-4-1s-3-1-4-2v-4c0-1 0-2 1-3z" class="U"></path><path d="M312 260l2-1 1 1h-1v5 1c-1-1-1-1-3-1 0 0 0 1-1 1l-1-1c-2 2-5 4-5 8v2c-1-2-1-2-1-4 0-1 0-2 1-2 0-1 1-2 2-4 2-1 4-3 6-5z" class="J"></path><path d="M312 260l2-1 1 1h-1v5-3c-1 0-5 2-6 3h-2c2-1 4-3 6-5z" class="I"></path><path d="M304 275v-2c0-4 3-6 5-8l1 1c1 0 1-1 1-1 2 0 2 0 3 1l1 9v-1c-1-1-1-1-1-2l-1-1c-1-1 0-2-1-2h-3c0 1-2 2-1 3v2c0 1-1 1-1 1v1l2 2h1l1 1v1c-2 0-4-2-6-3l-1-2z" class="i"></path><path d="M304 275v-2c0-4 3-6 5-8l1 1c1 0 1-1 1-1 2 0 2 0 3 1l1 9v-1c-1-3-1-5-4-7-1 0-2 1-3 2s-3 3-3 4v1 3l-1-2z" class="d"></path><path fill="#fff" d="M311 280v-1l-1-1h-1l-2-2v-1s1 0 1-1v-2c-1-1 1-2 1-3h3c1 0 0 1 1 2l1 1c0 1 0 1 1 2v1l2 2c0 1-1 1-1 2v1h0l-1-1c0 1 0 1-1 2h-1l-2-1z"></path><path d="M313 271l1 1c0 1 0 1 1 2v1l2 2c0 1-1 1-1 2v1h0l-1-1c0 1 0 1-1 2h-1l-2-1h3v-1l-1-2c0-1 0-1-1-2l1-2v-2z" class="g"></path><path d="M313 277c0-1 0-1-1-2l1-2h0c1 1 1 1 1 2s0 1-1 2z" class="i"></path><path d="M144 489h-1c1-1 1-2 2-3h0c1 0 1-1 1-1v-2h0c1 0 1 1 1 2h0l2 4s0 1 1 2v1l1 1v1l1 1c0 1 0 2 1 2l2 3c1 1 1 2 2 3h0c1 0 1 1 2 2s2 2 2 3c0-2-2-3-2-5s0-2-1-2v-2s0-1 1-1v-3c1 2 1 3 2 4h0 0c1 2 3 3 4 5h1 0c0 2 0 3 1 4 1 0 1 1 1 1v2h-1c0-1-1-1-1-1 0 2 1 2 2 4l3 3c1 0 2 1 3 1h0c1-1 2-1 3-1v1c0-1 0-2-1-3h1l2 2 3 1c1 1 1 2 1 3h-1 0c-1 2-1 2-2 3h0v2 1 1h-2c-2 0-1 2-3 1l-1-1-1 1v1 1l-1-1h0l-3-3-2-1h-1v-1h1c-1-2-3-2-4-4 0-1-1-2-2-3h0c-1-1-1-2-2-2h0l-1-1-1 1c0-1 0-1-1-2h1c-1-2-1-3-1-5-1-2-2-5-3-6-1-2-4-6-4-8 0-1 0-1-1-1 0-1-1-2-2-2 0 0-1-1-1-2h0l-1-1z" class="C"></path><path d="M165 504c0 1 1 2 0 3l-1-1h-1 0c1 2 2 4 2 6-2-2-3-5-4-7-2-2-1-3 0-6h0c1 2 3 3 4 5z" class="G"></path><path d="M167 525l2 2 1-1h1v-1c0-1 0-1 1-2l1 1 1 1h0c1 1 2 1 3 2h0 0l-1 1c-1 1-1 1-2 0l-1 1v1 1l-1-1h0l-3-3-2-1h-1v-1h1z" class="S"></path><path d="M174 528v-1-1c2 1 2 1 2 2-1 1-1 1-2 0z" class="B"></path><path d="M175 524l-1-1v-1c1-1 3-3 5-3l-1-2h1l3 1c1 1 1 2 1 3h-1 0c-1 2-1 2-2 3h0v2 1 1h-2c-2 0-1 2-3 1l-1-1c1 1 1 1 2 0l1-1h1l-3-3z" class="E"></path><path d="M175 524l-1-1c1 0 4 2 5 2v2h-1l-3-3z" class="S"></path><path d="M196 583c5 5 11 8 16 11 17 11 37 19 57 20 6 1 11 0 17 0l-2 2h1l1 1h-8c-12 0-24-2-35-5l-8-3c-2-1-4-2-6-2h0l1 2-1 1h-1-1v-1l-2 1v2h-1-1c-1-1-1-1-1-2h-2c-1 0-2-1-3-2l-6-3-2-2c-3-2-5-3-7-5-1 0-1-1-3-1v1h-2v-1h2v-1c1-1 2-1 2-2v-2l-1-2h1l-6-6 1-1z" class="K"></path><path d="M200 590h1c4 3 21 15 25 14h1v1h2 0v2h0l1 2-1 1h-1-1v-1l-2 1v2h-1-1c-1-1-1-1-1-2h-2c-1 0-2-1-3-2l-6-3-2-2c-3-2-5-3-7-5-1 0-1-1-3-1v1h-2v-1h2v-1c1-1 2-1 2-2v-2l-1-2z" class="J"></path><path d="M201 594c2 1 4 3 6 4 4 3 8 6 13 8h-1c-1 0-3-1-5-2h1-2c0-1-1-1-2-1h0l1 1-1 1-2-2c-3-2-5-3-7-5-1 0-1-1-3-1v1h-2v-1h2v-1c1-1 2-1 2-2z" class="P"></path><path d="M200 590h1c4 3 21 15 25 14h1v1h2 0v2h0l1 2-1 1h-1-1v-1l-2 1v2h-1-1c-1-1-1-1-1-2h-2c-1 0-2-1-3-2l-6-3 1-1-1-1h0c1 0 2 0 2 1h2-1c2 1 4 2 5 2h1l3 2c1-1 1-1 1-2-1-1-20-12-23-14l-1-2z" class="T"></path><path d="M225 610l-1-2 1-2c1 0 1-1 2-1h2 0v2h0l1 2-1 1h-1-1v-1l-2 1z" class="J"></path><defs><linearGradient id="AW" x1="156.494" y1="513.395" x2="149.641" y2="529.584" xlink:href="#B"><stop offset="0" stop-color="#595959"></stop><stop offset="1" stop-color="#727172"></stop></linearGradient></defs><path fill="url(#AW)" d="M126 486h0 1l2 5h0v-2l1 1c0-1 1-2 1-3 2 1 2 3 4 4 1 1 2 3 3 4 1 4 2 7 4 10 0 0 0-1 1-1v1c0 1 1 1 2 2l4 8 1 1 4-1c1-1 0-2 0-3l2 2c1 1 1 1 1 2l1-1 1 1h0c1 0 1 1 2 2h0c1 1 2 2 2 3 1 2 3 2 4 4h-1v1h1l2 1 3 3h0l1 1v-1l2 2h-1c0 1-1 1-1 1h2 1v-1-1l1 1c-1 0-1 1-1 1-2 1-2 1-3 2h-2 0-1c0 1 0 1 1 1-1 1-3 0-4 1l-1-1-3-3v1c0 1 1 1 2 2h-1-1l-1-1-5-3c-3-2-5-4-7-6-1-2-3-3-4-5-1 0-1-1-1-1l-1-1-1-1c0 1 1 2 1 3 1 0 1 1 1 2 1 0 1 0 1 1l1-1c0 1 1 2 2 3v1l-1-1-1-1v1l-5-7-16-33z"></path><path d="M169 527l3 3h0l-2 1-2-1v-1c0-1 0 0 1-1v-1z" class="Q"></path><path d="M131 487c2 1 2 3 4 4 1 1 2 3 3 4 1 4 2 7 4 10 0 0 0-1 1-1v1c0 1 1 1 2 2l4 8h0c1 1 1 2 2 3 1 2 2 3 3 5l3 3c0 1 0 1 1 1v1h-1c-1 0-2-2-3-2-1-2-4-5-5-7-1-1-1-2-2-2l-5-9v1 1h0l-1-2h-1c-3-2-6-7-8-11l-3-6h0v-2l1 1c0-1 1-2 1-3z" class="S"></path><path d="M141 508c-1-1-2-3-2-5 1 1 3 4 3 5v1 1h0l-1-2zm1-3s0-1 1-1v1c0 1 1 1 2 2l4 8h0c-1-1-1-2-2-3-2-3-4-5-5-7z" class="V"></path><path d="M131 483h-1 0-1l-1 1c0 1 0 1 1 1h0 0 0c1 0 2 0 2 1l-1 1v1h-1c0-1 0-1-1-1 0-1 0-1-1-2 0-1-2-3-2-4s-1-1-1-2v-1l-1-1v-1h-1v-1-1c-1-1-1-3-1-4-1 0 0 0-1-1v-2-1s0-2-1-3v-1c0-4-2-8-2-12v-1c0-2-1-3-1-4h1 0v1 3c1 1 0 2 1 3v3c0 1 0 1 1 2v1 2c0 1 0 1 1 1h0c1-1 2-6 1-8v-1h0v-6-4c0-1 1-1 2-1 0 1 1 4 1 5l-1 1v-1l-1 1v-2h1c0-1 0-2-1-3h0 0c0 2-1 5 0 7v2 9 1c0-1 1-1 1-1l1-1c0 1 1 1 1 2l-1 1c1 0 1 1 1 1v4l1 1h0v3 1h1v-2l1-1 1 1h0c1 0 1 1 1 1h2 1v-1-1l-1-11c1-1 1-1 2-1h1c0 1 0 1 1 1h0 0 0c-1 1 1 6 1 8v3 1c1 0 1 0 1 1s0 2 1 3h0v3 1c1 1 1 1 1 2h-1c0 2 0 3 1 4s1 3 2 4l3 6c0 1 0 2 1 2v-2c0-1 0-1-1-2h0 0c-1-1-1-2-1-3l1 1h0c0 1 1 2 1 2 1 0 2 1 2 2 1 0 1 0 1 1 0 2 3 6 4 8 1 1 2 4 3 6 0 2 0 3 1 5h-1l-2-2c0 1 1 2 0 3l-4 1-1-1-4-8c-1-1-2-1-2-2v-1c-1 0-1 1-1 1-2-3-3-6-4-10-1-1-2-3-3-4-2-1-2-3-4-4v-1-3z" class="E"></path><path d="M138 486h2 0c2 3 4 7 4 10 0-1-1-3-2-4s-2-1-2-2c-1-1-2-2-2-4z" class="I"></path><defs><linearGradient id="AX" x1="131.357" y1="493.998" x2="143.721" y2="493.736" xlink:href="#B"><stop offset="0" stop-color="#525155"></stop><stop offset="1" stop-color="#656562"></stop></linearGradient></defs><path fill="url(#AX)" d="M131 482h0c1-1 1-1 1 0 2 0 3 2 3 4 2 5 4 10 7 15 1 1 1 2 2 3 0 1 1 2 1 3-1-1-2-1-2-2v-1c-1 0-1 1-1 1-2-3-3-6-4-10-1-1-2-3-3-4-2-1-2-3-4-4v-1-3-1z"></path><path d="M131 482c0 2 1 3 2 4s2 3 2 5c-2-1-2-3-4-4v-1-3-1z" class="V"></path><path d="M146 492c1 0 2 1 2 2 1 0 1 0 1 1 0 2 3 6 4 8 1 1 2 4 3 6 0 2 0 3 1 5h-1l-2-2-1-1v2c0-1-1-1-1-2h-1v1h0-1l1-1v-1h0l-3-9h0v-1c-1-2-2-5-2-8zm21 70h0c0 1 1 1 2 2h0l2 1c3 4 7 8 11 12h-3l1 2s1 1 2 0c0 2 1 3 2 4v1 2l-1 1h2v-1c1-1 1-1 2-1 0 0 0 1 1 2h1l2 1-1 1c-2 1-3 1-4 3 0 1 0 2-1 3 0 2 0 2 1 3v1c-1 0-2 0-2-1h-1l1 1-1 1h-1c-1 1-1 1-2 1l1 1v1l2 3h-1v1c-1-1-2-1-3-2h-1v1c1 1 1 2 1 3 0 2-1 2-3 3h0-3l-1 1h-1-3c-5-3-7-7-8-12-2-3-2-7-2-10l1-15v1-3c0 1 1 2 1 3l1 1 1-2h1v1l2-2c1-4 1-9 2-13z" class="I"></path><path d="M179 587c1 1 1 1 2 1l-1 2v1h-1v-4z" class="G"></path><path d="M184 584v2l-1 1c-1 1-1 1-2 1s-1 0-2-1h1 0l1-1c1 0 2-1 3-2z" class="R"></path><path d="M170 597h2c1 1 1 3 2 2 1 1 3 3 3 5l-4-3v-1c-1 0-2-2-3-3z" class="F"></path><path d="M180 579s1 1 2 0c0 2 1 3 2 4v1c-1 1-2 2-3 2-2 1-2 1-4 0l-2-2c-1 0-1-1-2-2 0-1 0-1 1-2v-1h2 1 3z" class="Z"></path><path d="M177 579h1c0 1-1 2-1 2-1 1-1 2-1 3h1v2l-2-2c-1 0-1-1-2-2 0-1 0-1 1-2v-1h2 1z" class="d"></path><defs><linearGradient id="AY" x1="179.813" y1="599.955" x2="180.366" y2="590.419" xlink:href="#B"><stop offset="0" stop-color="#929293"></stop><stop offset="1" stop-color="#aeadad"></stop></linearGradient></defs><path fill="url(#AY)" d="M187 585s0 1 1 2h1l2 1-1 1c-2 1-3 1-4 3 0 1 0 2-1 3 0 2 0 2 1 3v1c-1 0-2 0-2-1h-1l1 1-1 1h-1c-1 1-1 1-2 1-2-2-4-6-4-9l1-1c1 0 2 1 2 2l1-1v-1-1l1-2c1 0 1 0 2-1h2v-1c1-1 1-1 2-1z"></path><path d="M183 587h2l-1 1-1 1v3 1 1 1l1 1h-1c-2-2 0-4-2-6h-1l1-2c1 0 1 0 2-1z" class="W"></path><path d="M187 585s0 1 1 2h1l2 1-1 1c-2 1-3 1-4 3 0 1 0 2-1 3h-2v-1-1-1-3l1-1 1-1v-1c1-1 1-1 2-1z" class="Z"></path><path d="M183 593l1-1c1-1 1-1 2 0 0 1 0 2-1 3h-2v-1-1z" class="d"></path><path d="M164 584l2-1c0-2 1-3 2-4 1 0 1 1 2 0 1 0 2-1 4-1v1c-1 0-2 0-3 1h0 3c-1 1-1 1-1 2 1 1 1 2 2 2v3 4c0 2 1 4 2 6l2 5v1c-2-2-3-4-5-6 0-1 0-2-1-3 0-1 0-4-1-5-1 1 0 4 0 5 1 1 2 3 2 3v2c-1 1-1-1-2-2h-2 0l-2 2v-3l-1 2h-1v-2c-1 1-1 1-3 1 0-1 1-2 1-3 0-2-1-3 0-5v-1c0-1 1-1 1-2v-1h-1s-1 1-1 2v2h-1c0-2 1-4 2-5z" class="Z"></path><path d="M164 584l2-1c0-2 1-3 2-4 1 0 1 1 2 0 1 0 2-1 4-1v1c-1 0-2 0-3 1h0c-2 1-3 2-3 4 0 1 1 1 1 1 1 1 0 3 0 4-1 0-1-1-1-1v-1h-1c-1 3 0 5 1 7l2 3-2 2v-3l-1 2h-1v-2c-1 1-1 1-3 1 0-1 1-2 1-3 0-2-1-3 0-5v-1c0-1 1-1 1-2v-1h-1s-1 1-1 2v2h-1c0-2 1-4 2-5z" class="P"></path><path d="M165 592c1 1 2 1 2 2l1 2-1 2h-1v-2h-1l1-1c-1-1-1-2-1-3z" class="E"></path><path d="M163 597c0-1 1-2 1-3 0-2-1-3 0-5h2c0 1 0 2-1 3 0 1 0 2 1 3l-1 1h1c-1 1-1 1-3 1z" class="X"></path><path d="M168 596v3l2-2h0c1 1 2 3 3 3v1l4 3 2 1h-1v1c1 1 1 2 1 3 0 2-1 2-3 3h0-3l-1 1h-1-3c-5-3-7-7-8-12v-1c0 1 0 1 1 2v-3-1c1 0 1-1 1-2l1 1c2 0 2 0 3-1v2h1l1-2z" class="W"></path><path d="M169 609v2l-2-1c-2 0-3-3-4-4v-1l-1-3h-1c1-2 1-2 1-3 1 1 1 1 1 2h1c0 3 2 6 5 8z" class="O"></path><path d="M168 596v3l2-2h0c1 1 2 3 3 3v1l4 3 2 1h-1v1l-5-2c-2 1-3 3-4 5-3-2-5-5-5-8h-1v-4c2 0 2 0 3-1v2h1l1-2z" class="P"></path><path d="M168 596v3s0 1-1 1-1 1-2 0v-1l-1-1v3h-1v-4c2 0 2 0 3-1v2h1l1-2z" class="L"></path><path d="M170 597h0c1 1 2 3 3 3v1c0 1 0 2-1 2-1 1-2 1-3 0v1l-1-1c0-1 0-2-1-3 1 0 1-1 1-1l2-2z" class="C"></path><path d="M168 603h1v-1l1-1h3v-1 1c0 1 0 2-1 2-1 1-2 1-3 0v1l-1-1z" class="D"></path><path d="M167 562h0c0 1 1 1 2 2h0l2 1c3 4 7 8 11 12h-3l1 2h-3-1-2v1h-3 0c1-1 2-1 3-1v-1c-2 0-3 1-4 1-1 1-1 0-2 0-1 1-2 2-2 4l-2 1c-1 1-2 3-2 5h1v-2c0-1 1-2 1-2h1v1c0 1-1 1-1 2v1c-1 2 0 3 0 5 0 1-1 2-1 3l-1-1c0 1 0 2-1 2v1 3c-1-1-1-1-1-2v1c-2-3-2-7-2-10l1-15v1-3c0 1 1 2 1 3l1 1 1-2h1v1l2-2c1-4 1-9 2-13z" class="K"></path><path d="M172 571l2 1-1 2c-1-1-1-2-1-3z" class="H"></path><path d="M161 578l1-2h1v1l-2 8-1-2c1 0 1-1 1-2v-3z" class="C"></path><path d="M164 584l2-5c0-1 1-1 1-2 0-2 0-4 1-6h0l1 1h-1c0 1 0 1 1 2 1 0 1 0 2-1v1s1 1 1 2h1c1 0 1 0 2 1v-1h1v3h-2v1h-3 0c1-1 2-1 3-1v-1c-2 0-3 1-4 1-1 1-1 0-2 0-1 1-2 2-2 4l-2 1z" class="S"></path><path d="M169 564l2 1c3 4 7 8 11 12h-3l1 2h-3-1v-3h-1v1c0-2-1-2-2-3l1-2-2-1-2-2c-1 1-1 1-2 1h0l1-1-1-2v-1c1 1 1 1 2 1h0v-1l-1-2h0z" class="V"></path><path d="M174 572c1 2 3 4 5 5l1 2h-3-1v-3h-1v1c0-2-1-2-2-3l1-2zm-15 2c0 1 1 2 1 3l1 1v3c0 1 0 2-1 2l1 2h0v3c-1 1-1 3-1 4v1c1-1 1-3 2-4h1v-2c0-1 1-2 1-2h1v1c0 1-1 1-1 2v1c-1 2 0 3 0 5 0 1-1 2-1 3l-1-1c0 1 0 2-1 2v1 3c-1-1-1-1-1-2v1c-2-3-2-7-2-10l1-15v1-3z" class="B"></path><path d="M159 574c0 1 1 2 1 3l1 1v3c0 1 0 2-1 2 0 3-2 8-1 10 0 1 1 1 1 1v4h1v1 3c-1-1-1-1-1-2v1c-2-3-2-7-2-10l1-15v1-3z" class="N"></path><path d="M159 574c0 1 1 2 1 3l1 1v3c-1 0-1-1-2-1v-3-3z" class="F"></path><path d="M100 369l1 2c1 1 1 3 1 4l-1 2s1 1 1 2v4c0 11 1 22 3 33 1 2 1 5 2 7l2 10c0 2 1 4 1 6 1 5 2 10 4 15 2 7 5 15 7 22 2 3 3 6 4 8l1 2 16 33 5 7c2 4 5 8 8 12 1 2 3 5 4 7v1c2 4 7 8 10 12h-1v1l-1 1h0c-2-1-3-2-3-3l-7-8c-3-3-5-6-8-9-15-21-26-44-35-68-2-6-4-12-6-19l-1 1-1-1c0-1-1-3-1-4l-2-7c-1-3-2-6-2-9-2-8-3-15-4-22l-1-6-1-5v-3c0-3-1-5-1-8v-9c0-1-1-2-3-3h0l-1-1c0-2 0-2 1-2l2-2c0-1 0-1 1-2l1 2c0 1 0 1 1 2h0c1 0 2-1 2-1 0-1 0-2-1-3h1c1 0 2 0 2-1z" class="P"></path><path d="M100 369l1 2c1 1 1 3 1 4l-1 2c-2 1-3 2-5 2-2-1-3-3-4-4v-1h0c1-1 2-2 3-2 0 1 0 1 1 2h0c1 0 2-1 2-1 0-1 0-2-1-3h1c1 0 2 0 2-1z" class="O"></path><path d="M92 374h0c1-1 2-2 3-2 0 1 0 1 1 2l1 1h0-4-1v-1z" class="d"></path><path d="M100 369l1 2c0 1 1 2 0 3l-3 2h-1v-1h0l-1-1h0c1 0 2-1 2-1 0-1 0-2-1-3h1c1 0 2 0 2-1z" class="Y"></path><path d="M106 431c-3-13-5-26-6-40-1-4-1-8-1-12h2c0 2 0 3 1 4h0c0 11 1 22 3 33 1 2 1 5 2 7-1 1-1 3-1 4v1c1 1 1 2 0 3z" class="F"></path><path d="M107 423l2 10c0 2 1 4 1 6 1 5 2 10 4 15 2 7 5 15 7 22 2 3 3 6 4 8l1 2 16 33 5 7c2 4 5 8 8 12 1 2 3 5 4 7v1c-7-8-13-16-18-25-3-5-6-10-9-16-7-13-13-27-18-42l-8-32c1-1 1-2 0-3v-1c0-1 0-3 1-4z" class="J"></path><path d="M142 519l5 7c2 4 5 8 8 12 1 2 3 5 4 7v1c-7-8-13-16-18-25 2 1 2 3 3 4l5 8c1 0 2 2 2 2 0-2-3-5-4-7-1-1-1-3-2-4-1-2-2-3-3-5h0z" class="N"></path><path d="M97 399l-1-10c-1-3-1-6-1-9h0 1v5h0l1-5h1c1 1 0 6 0 8l3 24 6 27c9 37 24 73 47 103 4 5 9 11 14 16v1l-1 1h0c-2-1-3-2-3-3l-7-8c-3-3-5-6-8-9-15-21-26-44-35-68-2-6-4-12-6-19l-1 1-1-1c0-1-1-3-1-4l-2-7c-1-3-2-6-2-9-2-8-3-15-4-22l-1-6-1-5c1-1 1-1 2-1z" class="F"></path><path d="M95 400c1-1 1-1 2-1l1 10v3-3 1l-1 1h0l-1-6-1-5z" class="X"></path><path d="M97 411h0l1-1v-1 3-3c2 11 4 22 7 33 1 4 2 8 3 11l-1 1-1-1c0-1-1-3-1-4l-2-7c-1-3-2-6-2-9-2-8-3-15-4-22z" class="T"></path><path d="M189 596l1-1h6c1 0 1 1 1 1h1l1 1h-2v1h2v-1c2 0 2 1 3 1 2 2 4 3 7 5l2 2 6 3c1 1 2 2 3 2h2c0 1 0 1 1 2h1 1v-2l2-1v1l2 2c1 1 5 5 5 7l3 3h0-1c0 1 0 1-1 2-1-1-2-1-3-1l-1-1c-1 1-3 2-4 3v1l-1 1v1l1 2h-1c1 1 2 1 3 1v2s-1 1-2 1c-2 1-4 1-6 2l-2 1-2 1-1 1c-1 0-2 1-3 1l-2-1c-1 1-1 1-1 0l-10-1c-1 0-2-1-4-1l-6-2c-2 0-5-1-6-3-5-2-8-5-12-8-2-1-4-4-6-6-1-1-1-2-2-3s-1-2-1-3c1 1 3 3 5 4h1 1c1 0 1 0 2-1l-4-2h3 1l1-1h3 0c2-1 3-1 3-3 0-1 0-2-1-3v-1h1c1 1 2 1 3 2v-1h1l-2-3v-1l-1-1c1 0 1 0 2-1h1l1-1-1-1h1c0 1 1 1 2 1v-1 1h1 1 1v-1-1-1z" class="S"></path><path d="M176 626c-2-1-2-2-3-3v-2h1c0 1 0 1 1 1 0 0 0 1 1 1h0l1-1h1v3c1 0 2 1 3 2h0c2 2 6 2 6 4h-1 0c-4-1-7-4-10-6v1z" class="V"></path><defs><linearGradient id="AZ" x1="184.447" y1="617.984" x2="186.342" y2="623.012" xlink:href="#B"><stop offset="0" stop-color="#5b5a5a"></stop><stop offset="1" stop-color="#747374"></stop></linearGradient></defs><path fill="url(#AZ)" d="M178 618h7c2 0 5-1 6 1 2-1 2-1 3-1h0c0 1-1 2-2 2h0-2c-1 0-1 0-2 1v1c-1 0-1 0-1 1h-1 0l-1 1c0-1-1 0-2 0h0l-1-1c0-2 1-2 1-3l-1-1h-1c-2 2 0 2 0 4h-1s-1-1-1-2 1-1 1-2h-1c-1 0-1 1-2 2-1 0-1 0-1-1v-1c1 0 2 0 2-1h0z"></path><path d="M163 612c1 1 3 3 5 4h1 1l1 2c1 0 1 0 3 1h-1l1 1-1 1v2c1 1 1 2 3 3 2 2 5 4 8 5v1c-5-2-8-5-12-8-2-1-4-4-6-6-1-1-1-2-2-3s-1-2-1-3z" class="D"></path><path d="M198 618h4 1 2 0c-1 1-1 2-2 3h0c0 1 1 1 1 1v2h0v2l-2 1v3 1h-1c-2-1-3-1-5 0l-1-1 1-1c-1 0-1-1-2-1h0v-1s1 0 1 1h0l1-1-1-1c-1-1-2-1-3-2v-1c1 0 2-1 3-1 0-1 1-2 2-3 0 0 1 0 1 1h1 0l-1-2z" class="G"></path><path d="M201 631v-2h-1l2-2v3 1h-1z" class="D"></path><path d="M209 612l4 1c2 1 4 0 6 0h1v1c-2 0-6 0-7 1s-1 1-2 1-1 0-2 1h0v-1c-1 1-2 1-2 0-1 0-1 1-2 1h1l-1 1h-2-1-4-4c-1 0-1 0-3 1-1-2-4-1-6-1h-7-2l-1 1-1 1-1-1h1c-2-1-2-1-3-1l-1-2c1 0 1 0 2-1l-4-2h3 1l1-1h3v1h0c1 0 1 1 2 1 2 0 6-1 8 0h5 2c1 0 2 0 3 1h2 1c1 0 2 0 2-1h4 0c1-1 1-1 2-1l-1-1h3z" class="K"></path><path d="M209 612l4 1c-1 1-4 1-6 1h-2 0c1-1 1-1 2-1l-1-1h3z" class="C"></path><path d="M172 615c4 2 9 2 13 2h20 1l-1 1h-2-1-4-4c-1 0-1 0-3 1-1-2-4-1-6-1h-7-2l-1 1-1 1-1-1h1c-2-1-2-1-3-1l-1-2c1 0 1 0 2-1z" class="U"></path><path d="M186 598v1h1 1c0 1 0 2 1 3h0c1 2 1 2 3 3l1 2c1 0 3 0 4 1 1 0 1 1 2 1s2 0 3 1v1c2 1 3 1 4 1l1 1c-1 0-1 0-2 1h0-4c0 1-1 1-2 1h-1-2c-1-1-2-1-3-1h-2-5c-2-1-6 0-8 0-1 0-1-1-2-1h0v-1h0c2-1 3-1 3-3 0-1 0-2-1-3v-1h1c1 1 2 1 3 2v-1h1l-2-3v-1l-1-1c1 0 1 0 2-1h1l1-1-1-1h1c0 1 1 1 2 1v-1z" class="B"></path><path d="M180 610c1 0 2 1 2 2h-4l2-2z" class="N"></path><path d="M178 606v-1h1c1 1 2 1 3 2 0 1-1 2-2 3l-2 2c-1 1-1 1-2 1h0v-1h0c2-1 3-1 3-3 0-1 0-2-1-3z" class="b"></path><path d="M186 608c2 1 4 3 7 2l2-1 7 2c2 1 3 1 4 1l1 1c-1 0-1 0-2 1l-9-1h-10-4l2-1c1 0 1-2 2-4z" class="U"></path><path d="M186 598v1h1 1c0 1 0 2 1 3h0c1 2 1 2 3 3l1 2c1 0 3 0 4 1 1 0 1 1 2 1s2 0 3 1v1l-7-2-2 1c-3 1-5-1-7-2-1-1-2-2-3-2l-2-3v-1l-1-1c1 0 1 0 2-1h1l1-1-1-1h1c0 1 1 1 2 1v-1z" class="E"></path><path d="M181 602v1c1 0 2 1 2 2 4 2 7 3 12 4l-2 1c-3 1-5-1-7-2-1-1-2-2-3-2l-2-3v-1z" class="b"></path><path d="M186 598v1h0c1 3 4 5 6 8-2-1-3-1-4-2h0c-1 0-2 0-3-1l-2 1c0-1-1-2-2-2v-1l-1-1c1 0 1 0 2-1h1l1-1-1-1h1c0 1 1 1 2 1v-1z" class="K"></path><path d="M181 603h1l1-1c1 0 2 1 2 2l-2 1c0-1-1-2-2-2z" class="S"></path><path d="M189 596l1-1h6c1 0 1 1 1 1h1l1 1h-2v1h2v-1c2 0 2 1 3 1 2 2 4 3 7 5l2 2 6 3c1 1 2 2 3 2h2c0 1 0 1 1 2h-2c-1 0-2-1-3 0l1 1c-2 0-4 1-6 0l-4-1h-3c-1 0-2 0-4-1v-1c-1-1-2-1-3-1s-1-1-2-1c-1-1-3-1-4-1l-1-2c-2-1-2-1-3-3h0c-1-1-1-2-1-3h1v-1-1-1z" class="J"></path><path d="M202 610c2-1 5 1 7 2h-3c-1 0-2 0-4-1v-1z" class="I"></path><path d="M202 606c1 0 2 0 3 1 1 0 2 1 3 1l3 1c-1 1 0 1-1 1-2 0-3-1-5-1-1-1-2-1-3-3z" class="N"></path><path d="M192 605h0c0-1 0-2-1-3v-1h0c2 2 4 3 6 5h0l-3-1-1 2-1-2z" class="H"></path><path d="M200 601l1 2c2 2 4 3 7 5-1 0-2-1-3-1-1-1-2-1-3-1s-2-1-3-2c0 0 0-1-1-1v-1h2v-1z" class="R"></path><path d="M201 603l3-1 13 8h-1-1l-1-1c-1 0-1 0-2 1h0 1l1 1c-2 0-3 0-4-1 1 0 0 0 1-1l-3-1c-3-2-5-3-7-5z" class="M"></path><path d="M189 596l1-1h6c1 0 1 1 1 1h1l1 1h-2v1h2 1v2c-1 0-1 1-2 1-2 0-3-1-4-2v-1h1v-1h-1c-3 0-3 2-4 4h-1 0l1-1v-2h0v-2h-1z" class="a"></path><path d="M199 598v-1c2 0 2 1 3 1 2 2 4 3 7 5l2 2 6 3c1 1 2 2 3 2h2c0 1 0 1 1 2h-2l-4-2-13-8-3 1-1-2v1h-2v1c1 0 1 1 1 1-1 0-3-2-4-2h-1c0-2-1-2 0-3 1 1 2 2 4 2 1 0 1-1 2-1v-2h-1z" class="V"></path><path d="M200 601l1-1c1 1 2 2 3 2l-3 1-1-2z" class="N"></path><path d="M225 610l2-1v1l2 2c1 1 5 5 5 7l3 3h0-1c0 1 0 1-1 2-1-1-2-1-3-1l-1-1c-1 1-3 2-4 3v1l-1 1v1l1 2h-1c1 1 2 1 3 1v2s-1 1-2 1c-2 1-4 1-6 2l-2 1-2 1-1 1c-1 0-2 1-3 1l-2-1c-1 1-1 1-1 0l-10-1c-1 0-2-1-4-1l1-1v-1h-1-2-1v-2h1l-2-2v1h0v-1l-1-1 1-1c1 1 1 1 3 1l1 1c2-1 3-1 5 0h1v-1-3l2-1v-2h0v-2s-1 0-1-1h0c1-1 1-2 2-3h0l1-1h-1c1 0 1-1 2-1 0 1 1 1 2 0v1h0c1-1 1-1 2-1s1 0 2-1 5-1 7-1v-1h-1l-1-1c1-1 2 0 3 0h2 1 1v-2z" class="Z"></path><path d="M212 618c1 0 3 0 3-1h4l-1 1s-1 0-2 1l-2 1h-3v-1l1-1z" class="Y"></path><path d="M205 617c1 0 1-1 2-1 0 1 1 1 2 0v1c-2 1-4 3-5 5 0 0-1 0-1-1h0c1-1 1-2 2-3h0l1-1h-1z" class="E"></path><path d="M219 618h4v1s-1 0-1 1l-5 3c0-1 0-2 1-2 0-1-1-1-1-2l2-1z" class="g"></path><path d="M218 618h1l-2 1c0 1 1 1 1 2-1 0-1 1-1 2h0-1c-1 0-1 0-2 1l-2-1-1 1h-1l-1 1h-1c1-1 1-2 1-2 1-1 2-2 3-2 1-1 1-1 2-1l2-1c1-1 2-1 2-1z" class="H"></path><path d="M216 623l-3-1c1-1 3-2 4-3 0 1 1 1 1 2-1 0-1 1-1 2h0-1z" class="i"></path><path d="M206 624c0-1 1-3 2-4 2-1 2-2 4-2l-1 1v1h3c-1 0-1 0-2 1-1 0-2 1-3 2 0 0 0 1-1 2 0 1 0 2-1 3s-1 0-2 0h-1v-4h2z" class="O"></path><path d="M206 624c0 1 1 1 0 2h-1c0 1 0 1-1 2v-4h2z" class="J"></path><path d="M222 620h0c-1 2-2 3-2 5h0-1v-2s-1 0-1 1c-1 2 1 3-2 3v1 1c-1 0-1 0-1 1l-2 2-2 1-1 1h0-2v-1h1c-1 0-1 0-2-1h0c-1-1-2-2-2-4 1 0 1 1 2 0s1-2 1-3h1l1-1h1l1-1 2 1c1-1 1-1 2-1h1 0l5-3z" class="G"></path><path d="M209 625l1-1h1c1 1 3 1 3 3s0 2-1 2h-1c-1-2-1-1-3-2v-2z" class="F"></path><path d="M207 628c1-1 1-2 1-3h1v2c2 1 2 0 3 2h1v1c-1 2-3 2-4 3-1 0-1 0-2-1h0c-1-1-2-2-2-4 1 0 1 1 2 0z" class="W"></path><path d="M207 628c1-1 1-2 1-3h1v2c2 1 2 0 3 2l-1 1c-1 0-1-1-1-1 0-1-1-1-1-1h-1c1 1 1 2 1 2l-1 1c0-1-1-2-1-3zm18-18l2-1v1l2 2c1 1 5 5 5 7l3 3h0-1c0 1 0 1-1 2-1-1-2-1-3-1l-1-1c-1 1-3 2-4 3v1l-1 1v1l1 2h-1l-2-1-2-2c-1 0-1 0-2 1 0 1 0 1 1 2l-1 1c-1 1-2 0-2-1l-1 1c-1 0-1 0-2-1 0-1 0-1 1-1v-1-1c3 0 1-1 2-3 0-1 1-1 1-1v2h1 0c0-2 1-3 2-5l3-1h0c1-2 4-2 5-2v-1c-1 0-2 0-3 1h-1v-1s1-1 1-2c-1-1-2-1-3-2h1v-2z" class="M"></path><path d="M225 610l2-1v1l2 2c-1 1-1 0-2 0h-2v-2z" class="O"></path><path d="M225 619c2-1 5-1 7 0h1l1 1h-2-3l-1 1h0c-3 1-5 3-7 5-1 0-1 0-1-1h0c0-2 1-3 2-5l3-1z" class="Y"></path><path d="M228 621h0c1 0 2 1 4 0h0 1v1h-2c-1 1-3 2-4 3v1l-1 1v1l1 2h-1l-2-1-2-2c-1 0-1 0-2 1 0 1 0 1 1 2l-1 1c-1 1-2 0-2-1l-1 1c-1 0-1 0-2-1 0-1 0-1 1-1v-1-1c3 0 1-1 2-3 0-1 1-1 1-1v2h1c0 1 0 1 1 1 2-2 4-4 7-5z" class="C"></path><path d="M216 629l2-1v2l-1 1c-1 0-1 0-2-1 0-1 0-1 1-1z" class="H"></path><path d="M224 625v1c1 1 1 1 2 1v1l1 2h-1l-2-1v-4z" class="W"></path><path d="M228 621h0c1 0 2 1 4 0h0 1v1h-2c-1 1-3 2-4 3v1l-1 1c-1 0-1 0-2-1v-1c1-2 2-3 3-3l1-1z" class="O"></path><path d="M202 627l2-1v-2h0 0v4h1c0 2 1 3 2 4h0c1 1 1 1 2 1h-1v1h2 0l1-1 2-1 2-2c1 1 1 1 2 1l1-1c0 1 1 2 2 1l1-1c-1-1-1-1-1-2 1-1 1-1 2-1l2 2 2 1c1 1 2 1 3 1v2s-1 1-2 1c-2 1-4 1-6 2l-2 1-2 1-1 1c-1 0-2 1-3 1l-2-1c-1 1-1 1-1 0l-10-1c-1 0-2-1-4-1l1-1v-1h-1-2-1v-2h1l-2-2v1h0v-1l-1-1 1-1c1 1 1 1 3 1l1 1c2-1 3-1 5 0h1v-1-3z" class="I"></path><path d="M200 633c0 1 0 2-1 3h-1-1v-1h0 1l2-2z" class="N"></path><path d="M213 632c1 1 1 1 1 3h-2l-1-2 2-1z" class="H"></path><path d="M210 634v1 1c-1 0-1-1-1-1h-2v-1h1 2z" class="Q"></path><path d="M198 636h0 2 0c1 1 1 1 2 1h1 2v-1-1c1 1 1 1 2 1h1c0 1 0 1 1 1v-1h1v3h0l-10-1c-1 0-2-1-4-1l1-1h1zm13 3v-2l1-1c0 1 1 1 1 2h1v-2h0v-1h5v2l-2 1-1 1c-1 0-2 1-3 1l-2-1zm10-9c-1-1-1-1-1-2 1-1 1-1 2-1l2 2 2 1c1 1 2 1 3 1v2s-1 1-2 1c-2 1-4 1-6 2v-1c0-1 0-2 1-3h1 1 1l-1-1h-1-1l-1-1h0z" class="G"></path><defs><linearGradient id="Aa" x1="196.492" y1="628.693" x2="198.663" y2="635.55" xlink:href="#B"><stop offset="0" stop-color="#6f706f"></stop><stop offset="1" stop-color="#868486"></stop></linearGradient></defs><path fill="url(#Aa)" d="M197 635h-1-2-1v-2h1l-2-2v1h0v-1l-1-1 1-1c1 1 1 1 3 1l1 1c2-1 3-1 5 0h1v-1h1c1 1 1 1 2 1l1 1-1 1c-1 1-2 2-3 2-1-1 0-2-2-3l-1 1h1l-2 2h-1 0z"></path><path d="M205 631l1 1-1 1h-2v-1s1 0 2-1z" class="M"></path><path d="M351 591v1c-1 1-3 2-5 4l1 2-1 1h3v1l-4 2-5 5c-2 1-5 3-7 3h-3-2s-1 1-1 2c-1 1-2 2-3 2h-2-2c1-1 1-1 1-2-1-1-1-1-2-1l1-1c-2 0-2 0-3 1l-8 2c1 0 1 0 1 1l-1 1c-1 0-2 1-2 1l-4 1c0 1-1 1-2 2 1 1 1 1 2 3 2 0 5-2 7-2 0 1 0 2-1 2l-9 11c-1 0-2 2-2 2-2 2-5 4-7 6h0l-2 3c-1 0-1 0-2-1 0 1-2 3-3 3h-1s-1 0-1 1l-2-2h1v-2h-4v1c0 1-1 1-1 1h-3c-1-2-3-2-5-3v1c-1 0-1-1-2 0l-1 1h0l2 2 1 1c0 1 1 3 1 4v1c-2-5-6-9-10-12l-3-2c-1-1-2-1-3-1h0l-2-1-4-3c-1-1-2-1-3-1-2 0-4 1-5 2-2 1-3 1-5 1 1 0 1-1 1-1v-1h-2-1-3v-2c-1 0-2 0-3-1h1l-1-2v-1l1-1v-1c1-1 3-2 4-3l1 1c1 0 2 0 3 1 1-1 1-1 1-2h1 0l-3-3c0-2-4-6-5-7l-2-2h1 1l1-1-1-2h0c2 0 4 1 6 2l8 3c11 3 23 5 35 5h8c11-1 23-4 33-8 2-1 4-3 6-4 3 0 5-2 8-3 6-3 12-7 18-11z" class="C"></path><path d="M271 618h12l-3 1v1c-1-1-1-1-2-1v1h0l-2 2-2-1v-1c-1-1-1-1-2-1 0 0 0-1-1-1z" class="K"></path><path d="M278 620h0l-2 2-2-1v-1h4z" class="O"></path><path d="M325 606h1l2 2v2s-1 1-1 2c-1 1-2 2-3 2h-2-2c1-1 1-1 1-2-1-1-1-1-2-1l1-1 1-1 3-2 1-1z" class="H"></path><path d="M325 606h1l2 2c-1 1-2 1-3 2v-4zm-1 1v3l-1 1-2-2 3-2z" class="a"></path><path d="M320 610l1-1 2 2h0c0 1 0 2 1 3h-2-2c1-1 1-1 1-2-1-1-1-1-2-1l1-1zm26-14l1 2-1 1h3v1l-4 2-1-1h-1c-1 2-4 3-6 3-1 0-2 1-2 1-2 1-5 2-7 2v-1c0-1 2-1 3-2l6-3 9-5z" class="F"></path><path d="M337 604c3-2 6-4 9-5h3v1l-4 2-1-1h-1c-1 2-4 3-6 3z" class="L"></path><path d="M229 607h1c0 1 0 1 1 1s3 1 4 2l10 3c8 3 16 4 25 5-1 1-1 1-1 2l-1-1c-7 1-16-1-23-3l-3-1-2 2-2-1c-2-2-5-5-8-7l-1-2z" class="W"></path><path d="M229 607h1c0 1 0 1 1 1 3 3 7 5 11 7l-2 2-2-1c-2-2-5-5-8-7l-1-2z" class="b"></path><path d="M309 613c1 0 1 0 1 1l-1 1c-1 0-2 1-2 1l-4 1c0 1-1 1-2 2 1 1 1 1 2 3l-5 1h-2c-2 1-4 1-6 1-1 0-3-1-4-2l-1 1v2l-2 3v-1c-1 1-1 1-2 1h0l-2 1-1-2v-1c1 0 1-1 1-2s0-2-1-4h0v-1c1 0 1 0 2 1v-1l3-1c9-1 18-2 26-5z" class="J"></path><path d="M284 622c0-1 0-1-1-2l1-1c6 0 13-2 19-2 0 1-1 1-2 2l-6 1h-11v1c1 0 1 1 2 1l-1 1h0l-1-1z" class="R"></path><path d="M278 620v-1c1 0 1 0 2 1 0 1 1 2 1 3l3-1 1 1h0v2l-2 3v-1c-1 1-1 1-2 1h0l-2 1-1-2v-1c1 0 1-1 1-2s0-2-1-4h0z" class="D"></path><path d="M281 628l-1-1v-1h1l2 1c-1 1-1 1-2 1h0z" class="U"></path><path d="M284 622l1 1-1 1h0c-1 0-2 0-3 1v-1-1l3-1z" class="T"></path><path d="M301 619c1 1 1 1 2 3l-5 1h-2c-2 1-4 1-6 1-1 0-3-1-4-2-1 0-1-1-2-1v-1h11l6-1z" class="L"></path><path d="M301 619c1 1 1 1 2 3l-5 1h-1c0-1 2-1 3-2 0-1-4 1-5 0v-1l6-1z" class="T"></path><path d="M242 615l3 1c7 2 16 4 23 3l1 1v1l-1 1h0l1 2h0c0 1 0 2-1 3 0 0-1 0-1 1h-3-3l1 1c-2 0-3 0-4 1l-1-1-6-2c-2-1-3-1-4-2l-1-1c-2-2-5-4-5-7h-1l2-2z" class="K"></path><path d="M247 625c2 0 7 0 9 1h0c-2 0-4 0-5 1-2-1-3-1-4-2z" class="B"></path><path d="M251 627c1-1 3-1 5-1 1 0 2 0 2 1 1 0 3-1 4 0h-1 1c1 0 2 0 2 1h-3l1 1c-2 0-3 0-4 1l-1-1-6-2z" class="F"></path><path d="M250 622h3 3 4c1 1 3 1 4 1 2 0 3 0 4-1l1 2c-6 1-13-1-19-1v-1z" class="X"></path><path d="M242 615l3 1c7 2 16 4 23 3l1 1v1l-1 1h0c-1 1-2 1-4 1-1 0-3 0-4-1h-4-3-3v1c-1 0-2 0-3-1s0-2 0-3h-2 0l-3-1h0l-1-1h-1l2-2z" class="P"></path><path d="M242 615l3 1c-2 1-2 1-3 2h0l-1-1h-1l2-2z" class="X"></path><defs><linearGradient id="Ab" x1="259.579" y1="623.091" x2="262.268" y2="618.737" xlink:href="#B"><stop offset="0" stop-color="#635f63"></stop><stop offset="1" stop-color="#6f716d"></stop></linearGradient></defs><path fill="url(#Ab)" d="M250 622h0c-1-1-2-1-2-2h1s0-1 1-1c4-1 14 4 18 2v-2l1 1v1l-1 1h0c-1 1-2 1-4 1-1 0-3 0-4-1h-4-3-3z"></path><path d="M270 618h1c1 0 1 1 1 1 1 0 1 0 2 1v1l2 1 2-2c1 2 1 3 1 4s0 2-1 2v1l1 2 2-1h0c-1 1-1 2-2 2l-3 2c0 1 3 3 3 3 0 1-1 2-1 2h0c0 1-1 1-1 1-3 0-7 1-9-1l-1 1-1-1v1c-3-3-7-4-10-8 0 0 1 0 1-1h0l1 1c1-1 2-1 4-1l-1-1h3 3c0-1 1-1 1-1 1-1 1-2 1-3h0l-1-2h0l1-1v-1c0-1 0-1 1-2z" class="H"></path><path d="M272 631l1-2h2s1-2 2-2h1c-1 1-2 2-2 3l1 1-1 1h0c-1-1-2-1-4-1z" class="W"></path><path d="M274 621l2 1 2 1c0 1-2 3-3 4s-3 1-4 2c-1 0-2-1-3-1 1-1 3 0 4-2 2-1 2-3 2-5z" class="F"></path><path d="M270 618h1c1 0 1 1 1 1 1 0 1 0 2 1v1c0 2 0 4-2 5-1 2-3 1-4 2h-1c0-1 1-1 1-1 1-1 1-2 1-3h0l-1-2h0l1-1v-1c0-1 0-1 1-2z" class="E"></path><path d="M272 619h0c0 1 0 2 1 2l-1 4h0c-1-1-1-2-2-2v-1 1l1-2c1-1 1-1 1-2z" class="Q"></path><path d="M270 618h1c1 0 1 1 1 1 0 1 0 1-1 2l-1 2v-1l-1-1v-1c0-1 0-1 1-2z" class="a"></path><path d="M278 627h0l1 2 2-1h0c-1 1-1 2-2 2l-3 2c0 1 3 3 3 3 0 1-1 2-1 2h0c0 1-1 1-1 1-3 0-7 1-9-1 0 0 0-1 1-1h1c1-1 1-2 2-3v-1-1c2 0 3 0 4 1h0l1-1-1-1c0-1 1-2 2-3z" class="O"></path><path d="M278 627h0l1 2 2-1h0c-1 1-1 2-2 2l-3 2c0 1 3 3 3 3 0 1-1 2-1 2-1 0-2-2-2-3-1 0-1 0-2 1l-1-1h1 0c1-1 2-1 2-2l1-1-1-1c0-1 1-2 2-3z" class="B"></path><path d="M267 628h1c1 0 2 1 3 1l1 1v2 1c-1 1-1 2-2 3h-1c-1 0-1 1-1 1l-1 1-1-1v1c-3-3-7-4-10-8 0 0 1 0 1-1h0l1 1c1-1 2-1 4-1l-1-1h3 3z" class="L"></path><path d="M263 629c2 0 2 2 3 2l2 1c-1 0-1 1-1 1-1 0-2-2-3-3h-1v-1z" class="C"></path><path d="M267 628h1c1 0 2 1 3 1l1 1v2 1c-1-1-2-1-2-2h-2-2c-1 0-1-2-3-2h-1l-1-1h3 3z" class="T"></path><path d="M257 629l1 1c1-1 2-1 4-1h1v1 1h0-3c1 3 5 4 6 6v1c-3-3-7-4-10-8 0 0 1 0 1-1h0z" class="C"></path><path d="M262 629h1v1 1c-2 0-3 0-5-1 1-1 2-1 4-1z" class="a"></path><path d="M230 609c3 2 6 5 8 7l2 1h1c0 3 3 5 5 7l1 1c1 1 2 1 4 2l6 2h0c0 1-1 1-1 1 3 4 7 5 10 8l-1 1v1h-2 0c0 1 1 1 1 2 1 0 1 1 1 2h0l2 2 1 1c0 1 1 3 1 4v1c-2-5-6-9-10-12l-3-2c-1-1-2-1-3-1h0l-2-1-4-3c-1-1-2-1-3-1-2 0-4 1-5 2-2 1-3 1-5 1 1 0 1-1 1-1v-1h-2-1-3v-2c-1 0-2 0-3-1h1l-1-2v-1l1-1v-1c1-1 3-2 4-3l1 1c1 0 2 0 3 1 1-1 1-1 1-2h1 0l-3-3c0-2-4-6-5-7l-2-2h1 1l1-1z" class="G"></path><path d="M237 628h2 0v1h-3 1v-1z" class="C"></path><path d="M226 628c1 1 2 2 3 2v1h3 1c2 0 2 1 4 0h0 1c-1 0-1 1-2 1l1 1c1 0 1 0 2-1 1 0 1 0 1-1 1 0 2-1 3-1l1 2c-2 0-4 1-5 2-2 1-3 1-5 1 1 0 1-1 1-1v-1h-2-1-3v-2c-1 0-2 0-3-1h1l-1-2z" class="I"></path><path d="M231 622l1 1c1 0 2 0 3 1h1v1l1 1v2 1h-1l-3 2h-1-3v-1c-1 0-2-1-3-2v-1l1-1v-1c1-1 3-2 4-3z" class="M"></path><path d="M232 623c1 0 2 0 3 1h1v1l1 1h-2c-1-1-2-2-4-2l1-1z" class="L"></path><path d="M235 626h2v2 1h-1l-3 2h-1l-1-1v-1h1 0c1 0 2-1 3-1v-2z" class="D"></path><path d="M231 622l1 1-1 1h-1v1 1h0l-1-1c-1 1-1 1-1 2v2h1v-1c2 0 2 0 3 1h-1v1l1 1h-3v-1c-1 0-2-1-3-2v-1l1-1v-1c1-1 3-2 4-3z" class="V"></path><path d="M230 609c3 2 6 5 8 7 3 4 6 7 9 11 2 2 3 5 5 7 1 1 3 2 4 4-1-1-2-1-3-1h0l-2-1-4-3c0-2-6-7-7-9-2-2-3-4-5-6-2-3-5-5-6-8l1-1z" class="H"></path><path d="M238 616l2 1h1c0 3 3 5 5 7l1 1c1 1 2 1 4 2l6 2h0c0 1-1 1-1 1 3 4 7 5 10 8l-1 1v1h-2 0c0 1 1 1 1 2 1 0 1 1 1 2h0l2 2 1 1c0 1 1 3 1 4v1c-2-5-6-9-10-12l-3-2c-1-2-3-3-4-4-2-2-3-5-5-7-3-4-6-7-9-11z" class="P"></path><path d="M303 622c2 0 5-2 7-2 0 1 0 2-1 2l-9 11c-1 0-2 2-2 2-2 2-5 4-7 6h0l-2 3c-1 0-1 0-2-1 0 1-2 3-3 3h-1s-1 0-1 1l-2-2h1v-2h-4v1c0 1-1 1-1 1h-3c-1-2-3-2-5-3v1c-1 0-1-1-2 0l-1 1c0-1 0-2-1-2 0-1-1-1-1-2h0 2v-1l1-1v-1l1 1 1-1c2 2 6 1 9 1 0 0 1 0 1-1h0s1-1 1-2c0 0-3-2-3-3l3-2c1 0 1-1 2-2 1 0 1 0 2-1v1l2-3v-2l1-1c1 1 3 2 4 2 2 0 4 0 6-1h2l5-1z" class="G"></path><path d="M282 631c1-1 2-1 2-1 2 1 1 1 2 1l1-1h1 0v2c-1 1-2 2-3 2s-1 0-1-1c-1 0-2-1-2-2z" class="I"></path><path d="M285 625h1c1 1 1 1 2 3h1v-1c2 0 4 0 6-1l1 1c-2 0-3 0-4 1l-1 1c0 1-1 1-1 1v2h-1s0-2-1-3h-2v1h0c-1 0-1-1-1-1-1 0-1 1-1 1l-2 1c0 1 1 2 2 2 0 1 0 1 1 1l-2 1-2-3h0c-1-1-2-1-2-2 1 0 1-1 2-2 1 0 1 0 2-1v1l2-3z" class="T"></path><path d="M285 625h1c1 1 1 1 2 3h-5l2-3z" class="M"></path><path d="M303 626c3-1 4-4 6-4l-9 11c-1 0-2 2-2 2-1-1 1-2 1-3 0 0 2-1 2-2h-1v-1l-1 1h0-1c-2 1-4 2-5 4h-2c0-1 0-1 1-2h0c1 0 1 0 2-1l1-1h-4v1h0c-1 0 0 0-1 1v-2s1 0 1-1l1-1c1-1 2-1 4-1l3-2h3l1 1z" class="D"></path><path d="M299 625h3l1 1c-1 0-3 1-4 1v-2z" class="M"></path><path d="M299 625v2c-1 1-2 1-3 1-2 0-3 1-4 0h0c1-1 2-1 4-1l3-2z" class="J"></path><path d="M303 622c2 0 5-2 7-2 0 1 0 2-1 2-2 0-3 3-6 4l-1-1h-3l-3 2-1-1c-2 1-4 1-6 1v1h-1c-1-2-1-2-2-3h-1v-2l1-1c1 1 3 2 4 2 2 0 4 0 6-1h2l5-1z" class="Q"></path><path d="M296 623c2 1 3 1 5 0 0 1-1 1-1 1h-2c-1 1-2 1-3 2-2 1-4 1-6 1 1 0 2-1 3-3h-2 0c2 0 4 0 6-1z" class="K"></path><path d="M285 623l1-1c1 1 3 2 4 2h0 2c-1 2-2 3-3 3v1h-1c-1-2-1-2-2-3h-1v-2z" class="F"></path><path d="M285 623l1-1c1 1 3 2 4 2h0s-1 1-2 1h-2 0-1v-2z" class="W"></path><path d="M299 630h0l1-1v1h1c0 1-2 2-2 2 0 1-2 2-1 3-2 2-5 4-7 6h0c-1 0-2 0-2 1h-1v-2h-1-1-1v-1c-1-1-4-1-5-2h-1-1s1-1 1-2c0 0-3-2-3-3l3-2c0 1 1 1 2 2h0l2 3 2-1c1 0 2-1 3-2v1c1 1 2 1 3 1v1h4c1-2 2-3 4-5z" class="D"></path><path d="M288 633c1 1 2 1 3 1 0 1 0 1-1 1 0 1-1 1-2 1v1h-1v-1c1 0 1-2 1-3z" class="L"></path><path d="M288 632v1c0 1 0 3-1 3-2 0-2 0-4-1l2-1c1 0 2-1 3-2z" class="P"></path><path d="M278 637s1-1 1-2c0 0-3-2-3-3l3-2c0 1 1 1 2 2h0v1l-1-1h-1c0 2 1 2 2 4h-1s0 1-1 1h-1z" class="E"></path><path d="M299 630h0l1-1v1h1c0 1-2 2-2 2 0 1-2 2-1 3-2 2-5 4-7 6h0c-1 0-2 0-2 1h-1v-2h-1v-2h2l1-1h1c2 0 3-2 4-2 1-2 2-3 4-5z" class="L"></path><path d="M278 637h1 1c1 1 4 1 5 2v1h1 1 1v2h1c0-1 1-1 2-1l-2 3c-1 0-1 0-2-1 0 1-2 3-3 3h-1s-1 0-1 1l-2-2h1v-2h-4v1c0 1-1 1-1 1h-3c-1-2-3-2-5-3v1c-1 0-1-1-2 0l-1 1c0-1 0-2-1-2 0-1-1-1-1-2h0 2v-1l1-1v-1l1 1 1-1c2 2 6 1 9 1 0 0 1 0 1-1h0z" class="M"></path><path d="M274 639h5v1h-1-4v-1z" class="F"></path><path d="M268 642v-1h2v-1h-2c1-2 4-1 6-1v1h4l1 1c-1 1-2 1-3 1h-1 0v1h2v1c0 1-1 1-1 1h-3c-1-2-3-2-5-3z" class="O"></path><path d="M274 640h4l1 1c-1 1-2 1-3 1h-1 0v1h2v1c0 1-1 1-1 1-1-2-4-2-5-4l3-1z" class="K"></path><path d="M280 637c1 1 4 1 5 2v1h1 1 1v2h1c0-1 1-1 2-1l-2 3c-1 0-1 0-2-1 0 1-2 3-3 3h-1s-1 0-1 1l-2-2h1v-2h-4-2v-1h0 1c1 0 2 0 3-1l-1-1h1 1s2 0 3-1c-1-1-2-1-3-2z" class="D"></path><path d="M283 642c0-1 1-2 2-2h1 1 1l-1 1h0c-1 0-2 0-3 1h-1z" class="V"></path><path d="M281 643h3 1l1 1c-1 1-2 1-3 2 0 0-1 0-1 1l-2-2h1v-2z" class="R"></path><g class="E"><path d="M281 643h3 1l-1 1c-1 0-2 2-3 1v-2z"></path><path d="M280 637c1 1 4 1 5 2v1c-1 0-2 1-2 2-2 1-6 1-8 0h0 1c1 0 2 0 3-1l-1-1h1 1s2 0 3-1c-1-1-2-1-3-2z"></path></g></svg>