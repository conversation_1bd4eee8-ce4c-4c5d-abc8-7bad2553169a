---
title: Example Markdown Document
author: Gwern Python
date: 2023-04-22
tags: [example, markdown, documentation]
draft: false
---

# Example Markdown Document

This is an example Markdown document that demonstrates various features of the Markdown parser.

## Basic Formatting

You can use **bold** and *italic* text, or even ***bold and italic*** text.

## Lists

### Unordered Lists

- Item 1
- Item 2
  - Nested item 1
  - Nested item 2
- Item 3

### Ordered Lists

1. First item
2. Second item
   1. Nested item 1
   2. Nested item 2
3. Third item

## Links and Images

[Link to Gwern.net](https://gwern.net)

![Example Image](https://example.com/image.jpg)

## Code Blocks

Inline code: `print("Hello, world!")`

```python
def hello_world():
    """Print a greeting."""
    print("Hello, world!")
    
    return True
```

## Tables

| Header 1 | Header 2 | Header 3 |
|----------|----------|----------|
| Cell 1   | Cell 2   | Cell 3   |
| Cell 4   | Cell 5   | Cell 6   |

## Blockquotes

> This is a blockquote.
>
> It can span multiple paragraphs.

## Footnotes

Here's a sentence with a footnote.[^1]

[^1]: This is the footnote content.

## Math Expressions

Inline math: $E = mc^2$

Block math:

$$
\frac{d}{dx}e^x = e^x
$$

## Horizontal Rule

---

## Tufte-Style Sidenotes

This paragraph has a sidenote.^[This is a sidenote that will appear in the margin.]

## Custom Extensions

::: note
This is a custom note block.
:::

::: warning
This is a warning block.
:::
