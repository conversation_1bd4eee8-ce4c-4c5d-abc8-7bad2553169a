<!-- cf. '/static/template/unfortunatelytheclockisticking.html'; similar idea in creating a MS Windows-style dialogue imagemap popup (<PERSON><PERSON><PERSON>, in this case) using a <PERSON><PERSON> <PERSON><PERSON> Lewis quote about perfectionism, which reminds the reader that time is passing, they will die, and they may be procrastinating and building a life of regrets. This can be popped up randomly after _X_ minutes of inactivity or activity as an easter egg. -->
<figure>
  <img src="/doc/fiction/humor/2024-11-10-bpdohwhatajoy-tumblr-clippymeme-idealconditionsdonotexistandwillneverhappen-766797325352960000.jpg" class="image-focus-not" usemap="#image-map-1" alt="[MICROSOFT CLIPPY:] Hey, it looks like you’re waiting for ideal conditions to do that thing you’ve been wanting to do. Need I remind you that ideal conditions do not exist and will never happen? / [I’m aware] / [Wow, rude]" title="Ideal conditions do not exist and will never happen. —C. S<PERSON> Lewis">
  <map name="image-map-1">
    <area target="" alt="Quote about not procrastinating due to perfectionism." title="'Learning in War-Time', <PERSON><PERSON> <PERSON><PERSON> 1939" href="/doc/existential-risk/1939-lewis.pdf" coords="34,113,822,470" shape="rect">
    <area href="https://www.lesswrong.com/posts/RWo4LwFzpHNQCTcYt/how-to-beat-procrastination" title="Yes, I know. Trying to fix it." alt="Link to LessWrong article on dealing with akrasia [‘How To Beat Procrastination’, Luke Muehlhauser 2011-02-05]" coords="43,483,414,579" shape="rect">
    <area alt="Dismiss notification" title="I choose to remain in denial and wait for ideal circumstances to do the thing." href="javascript:void(0);" coords="43,583,416,674" shape="rect">
  </map>
</figure>
