<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en"$if(dir)$ dir="$dir$"$endif$>
<head>
  <meta charset="utf-8" />

  <!-- INLINED-STANDALONE-BEGIN -->
  <!--#include virtual="/static/include/inlined-standalone.html" -->
  <!-- INLINED-STANDALONE-END -->

  <meta name="generator" content="pandoc" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />

$for(author-meta)$
  <meta name="author" content="$author-meta$" />
$endfor$
$if(date-meta)$
  <meta name="dcterms.date" content="$date-meta$" />
$endif$
  <title>/$pagetitle$ (syntax-highlighted preview)</title>
$for(css)$
  <link rel="stylesheet" href="$css$">
$endfor$
</head>
<body class="file-preview-source-code"> <!-- TODO: we should probably have a special prefix to properly namespace these sort of 'meta' body classes, to distinguish them from the page-title-derived body classes we use to CSS-style specific pages. -->
$body$
</body>
</html>
