# Product Requirements Document: Org-Roam Connector

## Overview
The Org-Roam Connector component will provide seamless integration between the gwern-python static site generator and an org-roam knowledge base. This connector will enable the processing of tagged files from an org-roam repository, converting them to the appropriate format for inclusion in the generated site.

## Goals
- Create a robust connection between org-roam and the static site generator
- Process tagged files from an org-roam repository
- Convert org-mode content to appropriate formats for the site
- Maintain bidirectional links between notes
- Support incremental updates when org-roam content changes

## User Stories
1. As a knowledge worker, I want to publish selected notes from my org-roam repository so that I can share my knowledge while maintaining my personal knowledge management system.
2. As a content creator, I want to tag specific org-roam notes for publication so that I can control which notes are public and which remain private.
3. As a researcher, I want to maintain the network of bidirectional links between my notes so that readers can navigate my knowledge graph.
4. As a writer, I want changes in my org-roam notes to automatically update my published site so that my public content stays in sync with my knowledge base.

## Requirements

### Functional Requirements
1. **Org-Roam Integration**
   - Connect to an org-roam SQLite database
   - Query notes based on tags, properties, or other criteria
   - Extract metadata from org-roam notes
   - Process org-roam's bidirectional links

2. **Content Processing**
   - Convert org-mode syntax to HTML or Markdown
   - Handle org-mode specific elements (e.g., TODO states, priorities)
   - Process org-roam's ID-based links
   - Extract and process embedded images and attachments

3. **Tag-Based Selection**
   - Select notes for publication based on tags
   - Support inclusion/exclusion patterns
   - Handle hierarchical tags
   - Process tag inheritance from parent notes

4. **Synchronization**
   - Detect changes in org-roam notes
   - Support incremental updates
   - Maintain consistency between org-roam and published content
   - Handle deleted or renamed notes

5. **Privacy Controls**
   - Redact or exclude private information
   - Support partial publication of notes
   - Handle sensitive metadata
   - Provide clear indicators of what will be published

### Non-Functional Requirements
1. **Performance**
   - Efficient processing of large org-roam repositories
   - Minimal impact on org-roam performance
   - Fast incremental updates

2. **Reliability**
   - Graceful handling of malformed org files
   - Robust error reporting
   - Data integrity checks

3. **Security**
   - Secure handling of private information
   - No leakage of unpublished content
   - Clear separation between public and private content

## Technical Specifications

### Component Structure
```
gwernpy/orgroam/
├── __init__.py
├── connector.py         # Main connector implementation
├── db.py                # Database interaction
├── parser.py            # Org-mode parsing
├── converter.py         # Format conversion
├── linker.py            # Link processing
└── utils/
    ├── __init__.py
    ├── tags.py          # Tag processing
    └── privacy.py       # Privacy controls
```

### Integration Flow
1. **Connection Phase**
   - Connect to org-roam database
   - Build note index
   - Extract tag information

2. **Selection Phase**
   - Apply tag filters
   - Identify notes for publication
   - Build dependency graph

3. **Processing Phase**
   - Parse org-mode content
   - Convert to target format
   - Process links and references
   - Handle attachments and images

4. **Publication Phase**
   - Generate output files
   - Create index and tag pages
   - Update link references

### Dependencies
- `sqlite3`: For org-roam database access
- `orgparse`: For org-mode parsing
- Core site generator components

### API Design
```python
class OrgRoamConnector:
    def __init__(self, org_roam_dir: str, config: Dict[str, Any] = None):
        """Initialize the org-roam connector with directory and configuration."""
        
    def connect(self) -> bool:
        """Connect to the org-roam database."""
        
    def get_tagged_files(self, tags: List[str], exclude_tags: List[str] = None) -> List[Dict[str, Any]]:
        """Get files with specific tags, optionally excluding others."""
        
    def process_files(self, files: List[Dict[str, Any]], output_format: str = "markdown") -> List[Dict[str, Any]]:
        """Process org-roam files and convert to the specified format."""
        
    def monitor_changes(self, callback: Callable = None) -> None:
        """Monitor org-roam directory for changes and trigger callback."""
        
    def sync(self, output_dir: str, incremental: bool = True) -> None:
        """Synchronize org-roam content to output directory."""
```

## Implementation Plan
1. **Phase 1: Core Integration**
   - Implement database connection
   - Create basic org-mode parsing
   - Add tag-based file selection
   - Implement simple format conversion

2. **Phase 2: Advanced Features**
   - Add bidirectional link processing
   - Implement attachment handling
   - Create incremental update system
   - Add privacy controls

3. **Phase 3: Monitoring and Synchronization**
   - Implement file system monitoring
   - Add full synchronization capabilities
   - Create detailed reporting
   - Add configuration options for fine-grained control

## Success Criteria
- Successfully connects to org-roam database
- Correctly identifies and processes tagged notes
- Maintains bidirectional links between published notes
- Handles incremental updates efficiently
- Respects privacy controls and tag-based selection

## Future Considerations
- Integration with org-roam-ui for visual knowledge graph
- Support for org-roam v2 and future versions
- Advanced filtering based on note properties
- Custom templates for different note types
- Integration with spaced repetition systems (org-drill, etc.)
- Support for transclusion and block references
