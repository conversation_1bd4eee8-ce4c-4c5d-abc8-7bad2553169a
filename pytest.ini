[tool:pytest]
# Pytest configuration for Blawger

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Output options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=gwernpy
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    --cov-fail-under=90
    --durations=10

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
    network: Tests requiring network access
    cli: CLI command tests
    parser: Parser module tests
    generator: Generator module tests
    config: Configuration tests
    orgroam: Org-roam integration tests
    bot: Bot control tests

# Minimum version
minversion = 7.0

# Test timeout
timeout = 300

# Parallel execution
# Uncomment to enable parallel test execution
# -n auto

# Warnings
filterwarnings =
    error
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
