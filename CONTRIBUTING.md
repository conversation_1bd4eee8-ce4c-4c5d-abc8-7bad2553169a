# Contributing to Gwern Python

Thank you for considering contributing to Gwern Python! This document provides guidelines and instructions for contributing to the project.

## Code Style

This project follows these Python coding standards:

- [PEP 8](https://www.python.org/dev/peps/pep-0008/) for code style
- [PEP 257](https://www.python.org/dev/peps/pep-0257/) for docstring conventions
- [PEP 484](https://www.python.org/dev/peps/pep-0484/) for type hints

We use the following tools to enforce these standards:

- [Black](https://black.readthedocs.io/) for code formatting
- [isort](https://pycqa.github.io/isort/) for import sorting
- [flake8](https://flake8.pycqa.org/) for linting
- [mypy](https://mypy.readthedocs.io/) for type checking

## Development Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/gwern-python.git
   cd gwern-python
   ```

2. Create and activate a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install development dependencies:
   ```bash
   pip install -e ".[dev]"
   ```

## Testing

All code changes should include appropriate tests. We use pytest for testing:

```bash
# Run all tests
pytest

# Run tests with coverage
pytest --cov=gwernpy

# Run specific tests
pytest tests/test_parser.py
```

## Pull Request Process

1. Fork the repository and create a new branch for your feature or bug fix.
2. Write tests for your changes.
3. Ensure all tests pass and code quality checks succeed.
4. Update documentation as needed.
5. Submit a pull request with a clear description of the changes.

## Commit Messages

Please use clear and descriptive commit messages that explain the purpose of your changes. Follow the conventional commits format:

```
feat: add new feature
fix: fix a bug
docs: update documentation
test: add or update tests
refactor: code refactoring without functionality changes
style: formatting changes
chore: maintenance tasks
```

## Documentation

All code should be well-documented:

- Use docstrings for modules, classes, and functions
- Update README.md and other documentation as needed
- Add examples for new features

## License

By contributing to this project, you agree that your contributions will be licensed under the project's MIT License.
