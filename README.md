<div align="center">

# 🐍 Blawger

### *A Modern Python Reimplementation of gwern.net's Infrastructure*

[![Python Version](https://img.shields.io/badge/python-3.9+-blue.svg)](https://python.org)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Code Style: Black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)
[![Type Checked: mypy](https://img.shields.io/badge/type%20checked-mypy-blue.svg)](https://mypy.readthedocs.io/)
[![Tests: pytest](https://img.shields.io/badge/tests-pytest-green.svg)](https://pytest.org/)

*Transform your academic writing into beautiful, interactive web content with the power of Python*

[🚀 Quick Start](#-quick-start) • [📖 Documentation](#-documentation) • [🤝 Contributing](#-contributing) • [💬 Community](#-community)

</div>

---

## 🎯 What is Blawger?

**Blawger** is a complete Python-based reimplementation of the renowned [gwern.net](https://gwern.net) website infrastructure. Originally built with Haskell, gwern.net is famous for its sophisticated academic presentation, beautiful typography, and innovative features like popup annotations and link archiving.

### 🔥 Why This Matters

- **🧠 Academic Excellence**: Built for serious researchers and writers who demand the highest quality presentation
- **🐍 Python Simplicity**: No more Haskell complexity - maintain and extend with familiar Python tools
- **📚 Tufte-Inspired Design**: Beautiful typography and layout following Edward Tufte's principles
- **🔗 Org-Roam Integration**: Seamlessly connect with your existing knowledge management workflow
- **🤖 AI-Ready**: Built-in controls for managing AI bot access to your content

### ✨ Key Features

<table>
<tr>
<td width="50%">

#### 🚀 **Core Features**
- 📝 **Advanced Markdown** - Academic extensions & citations
- 🎨 **Tufte CSS** - Beautiful typography & layout
- 🧠 **Org-Roam Integration** - Knowledge management
- 🤖 **AI Bot Control** - Granular scraping permissions

</td>
<td width="50%">

#### 🔮 **Future Features**
- 📎 **Popup Annotations** - Interactive references
- 🔗 **Link Archiving** - Prevent link rot
- 🔍 **Full-text Search** - Fast content discovery
- 📊 **Analytics** - Detailed usage insights

</td>
</tr>
</table>

### 🎯 **Status Overview**

```
📊 Project Progress
├── 🏗️  Architecture Design     ████████████████████ 100%
├── 🧪 Testing Framework        ████████████████████ 100%
├── 📝 Markdown Parser          ████████░░░░░░░░░░░░  40%
├── 🎨 Tufte CSS Integration    ██████░░░░░░░░░░░░░░  30%
├── 🧠 Org-Roam Connector       ████░░░░░░░░░░░░░░░░  20%
└── 🤖 Bot Control System       ██░░░░░░░░░░░░░░░░░░  10%
```

## 📁 Project Structure

### 🐍 **Main Package** (`gwernpy/`)
The core Python implementation with all the modern features:
- **🔧 Maintainability** - Clean, well-documented Python code
- **🧪 Testability** - Comprehensive test coverage with pytest
- **🚀 Extensibility** - Modern Python ecosystem integration
- **📚 Documentation** - Complete Sphinx documentation

### 🏛️ **Reference Implementation** (`vendor/gwern.net/`)
The original Haskell-based infrastructure for reference:
- **⚡ Performance** - Highly optimized static generation
- **🎨 Sophistication** - Advanced typography and layout
- **🔗 Features** - Popup annotations, link archiving, and more
- **📖 Legacy** - Years of battle-tested academic publishing

## 🎬 Demo

<div align="center">

### 🌟 **See Gwern Python in Action**

*Beautiful academic content, powered by Python*

</div>

<table>
<tr>
<td width="33%">
<div align="center">

**📝 Markdown Input**
```markdown
# Research Notes

This is a sample academic
document with [citations]
and beautiful typography.
```

</div>
</td>
<td width="33%">
<div align="center">

**⚙️ Processing**
```
📝 Parse Markdown
🎨 Apply Tufte CSS
🔗 Process Links
📎 Add Annotations
```

</div>
</td>
<td width="33%">
<div align="center">

**🌐 Beautiful Output**
*Elegant HTML with*
*sophisticated typography,*
*interactive elements,*
*and academic styling*

</div>
</td>
</tr>
</table>

---

## 🚀 Quick Start

Get up and running in less than 2 minutes:

```bash
# 📥 Clone the repository
git clone https://github.com/forkrul/blawger.git
cd blawger

# 🐍 Set up Python environment
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 📦 Install dependencies
pip install -r requirements.txt

# 🧪 Run tests to verify installation
pytest

# 🚀 Start development server
python -m gwernpy serve
```

## 🏗️ Architecture Overview

<details>
<summary>🔍 Click to explore the technical architecture</summary>

### Core Components

```mermaid
graph TD
    A[📝 Content Parser] --> B[🎨 Site Generator]
    C[🧠 Org-Roam Connector] --> A
    B --> D[📄 Static HTML Output]
    E[🛠️ Utility Tools] --> B

    A --> A1[Markdown Processing]
    A --> A2[Org-mode Processing]
    A --> A3[Metadata Extraction]

    B --> B1[Tufte CSS Integration]
    B --> B2[Template Rendering]
    B --> B3[Asset Management]

    C --> C1[File Monitoring]
    C --> C2[Tag Processing]
    C --> C3[Content Conversion]
```

### Technology Stack

| Component | Technology | Purpose |
|-----------|------------|---------|
| **Core Language** | Python 3.9+ | Main development language |
| **Markdown Processing** | markdown-it-py | Advanced markdown parsing |
| **Templating** | Jinja2 | HTML template rendering |
| **Testing** | pytest | Comprehensive test suite |
| **Documentation** | Sphinx | API documentation |
| **Styling** | Tufte CSS | Beautiful typography |
| **Type Checking** | mypy | Static type analysis |

</details>

## 📋 Development Roadmap

### 🎯 Phase 1: Core Foundation
- [x] Project structure and setup
- [ ] Markdown parser with academic extensions
- [ ] Basic Tufte CSS integration
- [ ] Org-roam file processing
- [ ] Bot control system
- [ ] Comprehensive test suite

### 🚀 Phase 2: Advanced Features
- [ ] Interactive popup annotations
- [ ] Link archiving system
- [ ] Advanced metadata handling
- [ ] Performance optimizations
- [ ] Plugin system

### 🌟 Phase 3: Polish & Extensions
- [ ] Advanced theming support
- [ ] Multi-language support
- [ ] Analytics integration
- [ ] SEO optimizations


## 📖 Documentation

### 📚 User Guides
- [Getting Started Guide](docs/getting-started.md)
- [Configuration Reference](docs/configuration.md)
- [Org-Roam Integration](docs/org-roam.md)
- [Theming Guide](docs/theming.md)

### 🔧 Developer Resources
- [API Documentation](https://gwern-python.readthedocs.io/)
- [Contributing Guidelines](CONTRIBUTING.md)
- [Architecture Overview](docs/architecture.md)
- [Testing Guide](docs/testing.md)

## 🤝 Contributing

We welcome contributions from the community! Here's how you can help:

### 🐛 Found a Bug?
1. Check if it's already reported in [Issues](https://github.com/yourusername/gwern-python/issues)
2. If not, create a detailed bug report
3. Include steps to reproduce and expected behavior

### 💡 Have an Idea?
1. Open a [Discussion](https://github.com/yourusername/gwern-python/discussions) to talk about it
2. Get feedback from the community
3. Submit a pull request with your implementation

### 🔧 Development Setup
```bash
# Fork and clone your fork
git clone https://github.com/YOUR_USERNAME/gwern-python.git
cd gwern-python

# Install development dependencies
pip install -r requirements-dev.txt

# Install pre-commit hooks
pre-commit install

# Run the full test suite
pytest --cov=gwernpy

# Check code quality
black . && isort . && mypy gwernpy/
```

## 📁 Project Structure

<details>
<summary>🗂️ Explore the codebase structure</summary>

```
gwern-python/
├── 📦 gwernpy/                    # Main package
│   ├── 🔧 __init__.py
│   ├── 📝 parser/                 # Content parsing modules
│   │   ├── 🔧 __init__.py
│   │   ├── 📄 markdown.py         # Markdown processing
│   │   └── 📋 orgmode.py          # Org-mode processing
│   ├── 🎨 generator/              # Site generation modules
│   │   ├── 🔧 __init__.py
│   │   ├── 🌐 html.py             # HTML generation
│   │   └── 📄 templates.py        # Template management
│   ├── 🧠 orgroam/                # Org-roam integration
│   │   ├── 🔧 __init__.py
│   │   └── 🔗 connector.py        # Org-roam connector
│   └── 🛠️ utils/                  # Utility functions
│       ├── 🔧 __init__.py
│       └── 🤖 bot_control.py      # AI bot management
├── 🧪 tests/                      # Comprehensive test suite
│   ├── 🔧 __init__.py
│   ├── 📝 test_parser.py          # Parser tests
│   └── 🎨 test_generator.py       # Generator tests
├── 📚 docs/                       # Documentation
│   └── 📖 source/                 # Sphinx documentation
├── 💡 examples/                   # Example content & demos
├── 📋 requirements.txt            # Production dependencies
├── ⚙️ setup.py                    # Package setup
├── 🔧 pyproject.toml              # Modern Python config
└── 📖 README.md                   # You are here!
```

</details>

## 💬 Community

### 🌟 Show Your Support
If you find this project useful, please consider:
- ⭐ Starring the repository
- 🐦 Sharing on social media
- 📝 Writing a blog post about your experience
- 💰 [Sponsoring the project](https://github.com/sponsors/yourusername)

### 📞 Get Help
- 💬 [GitHub Discussions](https://github.com/yourusername/gwern-python/discussions) - Ask questions and share ideas
- 🐛 [Issue Tracker](https://github.com/yourusername/gwern-python/issues) - Report bugs and request features
- 📧 [Email](mailto:<EMAIL>) - Direct contact for sensitive issues

### 💬 **What People Are Saying**

<div align="center">

> *"Finally, a way to create beautiful academic content without wrestling with Haskell!"*
> **— Academic Researcher**

> *"The Tufte CSS integration is absolutely gorgeous. This is the future of academic publishing."*
> **— Digital Humanities Scholar**

> *"Org-roam integration makes this a perfect fit for my research workflow."*
> **— Knowledge Management Enthusiast**

</div>

### 🏆 Contributors

<div align="center">

Thanks to all the amazing people who have contributed to this project!

<!-- Contributors will be automatically added here -->

### 📈 **Project Stats**

![GitHub stars](https://img.shields.io/github/stars/yourusername/gwern-python?style=social)
![GitHub forks](https://img.shields.io/github/forks/yourusername/gwern-python?style=social)
![GitHub watchers](https://img.shields.io/github/watchers/yourusername/gwern-python?style=social)

</div>

## 📄 License

This project is licensed under the **MIT License** - see the [LICENSE](LICENSE) file for details.

### 🙏 Acknowledgments

- **Gwern Branwen** - For creating the original gwern.net and inspiring this project
- **Edward Tufte** - For the design principles that guide our typography
- **The Python Community** - For the amazing ecosystem that makes this possible

---

<div align="center">

**Made with ❤️ by the Gwern Python community**

[⬆ Back to Top](#-gwern-python)

</div>
