<div align="center">

# 🌐 Gwern Rework

### *Complete Reimplementation of gwern.net Infrastructure*

[![Python](https://img.shields.io/badge/Python-3.9+-blue.svg)](https://python.org)
[![Haskell](https://img.shields.io/badge/Haskell-Original-purple.svg)](https://haskell.org)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

*Bridging academic excellence with modern development practices*

</div>

---

## 🎯 What's This Repository?

This repository contains both the **original gwern.net infrastructure** (Haskell-based) and a **complete Python reimplementation**. It serves as a comprehensive study and modernization of one of the web's most sophisticated academic publishing platforms.

## 📁 Repository Structure

### 🐍 [`gwern-python/`](./gwern-python/) - The Future
A complete Python reimplementation designed for:
- **🔧 Maintainability** - No more Haskell complexity
- **🧪 Testability** - Comprehensive test coverage
- **🚀 Extensibility** - Modern Python ecosystem
- **📚 Documentation** - Clear, comprehensive guides

**[→ Explore the Python Implementation](./gwern-python/)**

### 🏛️ [`gwern.net/`](./gwern.net/) - The Original
The original Haskell-based infrastructure that powers gwern.net:
- **⚡ Performance** - Highly optimized static generation
- **🎨 Sophistication** - Advanced typography and layout
- **🔗 Features** - Popup annotations, link archiving, and more
- **📖 Legacy** - Years of battle-tested academic publishing

## 🚀 Quick Start

### For Python Development
```bash
cd gwern-python
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
pytest
```

### For Haskell Exploration
```bash
cd gwern.net
# Follow the original gwern.net setup instructions
```

## 🎯 Project Goals

1. **📚 Preserve Academic Excellence** - Maintain the high standards of gwern.net
2. **🐍 Modernize Technology Stack** - Leverage Python's ecosystem and simplicity  
3. **🧪 Improve Testability** - Comprehensive test coverage for reliability
4. **📖 Enhance Documentation** - Clear guides for contributors and users
5. **🤝 Foster Community** - Make the codebase accessible to more developers

## 🤝 Contributing

We welcome contributions to both the Python reimplementation and documentation improvements for the original Haskell code.

- **Python Development**: See [`gwern-python/CONTRIBUTING.md`](./gwern-python/CONTRIBUTING.md)
- **Documentation**: Help document the original Haskell infrastructure
- **Testing**: Add tests and improve coverage
- **Features**: Implement new capabilities in the Python version

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Gwern Branwen** - For creating the original gwern.net infrastructure
- **The Haskell Community** - For the powerful tools that made the original possible
- **The Python Community** - For the ecosystem that enables the reimplementation

---

<div align="center">

**Choose your path: [🐍 Python Future](./gwern-python/) or [🏛️ Haskell Original](./gwern.net/)**

</div>
