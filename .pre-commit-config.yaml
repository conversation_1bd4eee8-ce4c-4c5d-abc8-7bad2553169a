# Pre-commit configuration for <PERSON>law<PERSON>
# See https://pre-commit.com for more information

repos:
  # General file checks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
        exclude: ^vendor/
      - id: end-of-file-fixer
        exclude: ^vendor/
      - id: check-yaml
        exclude: ^vendor/
      - id: check-toml
        exclude: ^vendor/
      - id: check-json
        exclude: ^vendor/
      - id: check-added-large-files
        args: ['--maxkb=1000']
      - id: check-case-conflict
      - id: check-merge-conflict
      - id: debug-statements
        exclude: ^vendor/
      - id: check-docstring-first
        exclude: ^vendor/

  # Python code formatting
  - repo: https://github.com/psf/black
    rev: 23.7.0
    hooks:
      - id: black
        language_version: python3
        exclude: ^vendor/

  # Import sorting
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: ["--profile", "black", "--line-length", "100"]
        exclude: ^vendor/

  # Flake8 linting
  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        exclude: ^vendor/
        additional_dependencies:
          - flake8-docstrings
          - flake8-bugbear
          - flake8-comprehensions
          - flake8-simplify
        args:
          - --max-line-length=100
          - --extend-ignore=E203,W503,D100,D101,D102,D103,D104,D105,D106,D107

  # Type checking
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.5.1
    hooks:
      - id: mypy
        exclude: ^vendor/
        additional_dependencies:
          - types-PyYAML
          - types-toml
          - types-python-dateutil
        args:
          - --ignore-missing-imports
          - --show-error-codes

  # Security linting
  - repo: https://github.com/pycqa/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        exclude: ^(vendor/|tests/)
        args: ['-c', 'pyproject.toml']

  # Documentation linting
  - repo: https://github.com/pycqa/doc8
    rev: v1.1.1
    hooks:
      - id: doc8
        exclude: ^vendor/
        args:
          - --max-line-length=100

  # YAML formatting
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.0.0
    hooks:
      - id: prettier
        types: [yaml]
        exclude: ^vendor/

  # Markdown linting
  - repo: https://github.com/igorshubovych/markdownlint-cli
    rev: v0.35.0
    hooks:
      - id: markdownlint
        exclude: ^vendor/
        args:
          - --config=.markdownlint.json

  # Shell script linting
  - repo: https://github.com/shellcheck-py/shellcheck-py
    rev: v0.9.0.5
    hooks:
      - id: shellcheck
        exclude: ^vendor/

  # Commit message linting
  - repo: https://github.com/commitizen-tools/commitizen
    rev: v3.6.0
    hooks:
      - id: commitizen
        stages: [commit-msg]

# Configuration for specific hooks
default_language_version:
  python: python3.9

# Exclude patterns
exclude: |
  (?x)^(
    vendor/.*|
    \.git/.*|
    \.tox/.*|
    \.venv/.*|
    build/.*|
    dist/.*|
    htmlcov/.*|
    \.mypy_cache/.*|
    \.pytest_cache/.*
  )$

# CI configuration
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit.com hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: []
  submodules: false
