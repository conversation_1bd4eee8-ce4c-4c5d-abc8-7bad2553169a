"""
Performance tests and benchmarks for Blawger.

These tests ensure that the system performs well under various loads
and identify performance bottlenecks.
"""

import pytest
import time
from pathlib import Path
from datetime import datetime

from gwernpy.config import Config
from gwernpy.parser.markdown import MarkdownParser
from gwernpy.parser.frontmatter import FrontMatterParser
from gwernpy.generator.html import HTMLGenerator


@pytest.mark.slow
class TestParsingPerformance:
    """Test parsing performance with various content sizes."""
    
    def test_small_document_parsing(self, benchmark, temp_dir):
        """Benchmark parsing of small documents (< 1KB)."""
        content = """---
title: Small Document
date: 2024-01-15
tags: [test]
---

# Small Document

This is a small document with minimal content for performance testing.

- List item 1
- List item 2

Some **bold** and *italic* text.
"""
        
        parser = MarkdownParser()
        
        def parse_small():
            return parser.parse(content)
        
        result = benchmark(parse_small)
        metadata, html = result
        
        assert metadata['title'] == 'Small Document'
        assert '<h1>Small Document</h1>' in html
    
    def test_medium_document_parsing(self, benchmark, temp_dir):
        """Benchmark parsing of medium documents (1-10KB)."""
        # Create medium-sized content
        sections = []
        for i in range(20):
            sections.append(f"""
## Section {i + 1}

This is section {i + 1} with some content. It includes **bold text**, 
*italic text*, and `code snippets`. Here's a list:

- Item 1 for section {i + 1}
- Item 2 for section {i + 1}
- Item 3 for section {i + 1}

And some math: $x_{i + 1} = y^2 + z$

""")
        
        content = f"""---
title: Medium Document
date: 2024-01-15
tags: [test, medium]
---

# Medium Document

This is a medium-sized document for performance testing.

{''.join(sections)}
"""
        
        parser = MarkdownParser()
        
        def parse_medium():
            return parser.parse(content)
        
        result = benchmark(parse_medium)
        metadata, html = result
        
        assert metadata['title'] == 'Medium Document'
        assert 'Section 1' in html
        assert 'Section 20' in html
    
    def test_large_document_parsing(self, benchmark, temp_dir):
        """Benchmark parsing of large documents (> 10KB)."""
        # Create large content
        sections = []
        for i in range(100):
            sections.append(f"""
## Section {i + 1}

This is a comprehensive section {i + 1} with extensive content for performance testing.
It includes multiple paragraphs, various formatting options, and complex structures.

### Subsection {i + 1}.1

Here's a detailed subsection with **bold text**, *italic text*, and `inline code`.
We also have [links](https://example.com/section-{i + 1}) and other elements.

#### Mathematical Content

Some mathematical expressions for section {i + 1}:

- Inline math: $f(x) = x^{i + 1} + \\sin(x)$
- Display math: $$\\sum_{{k=1}}^{{{i + 1}}} k^2 = \\frac{{{i + 1}({i + 1}+1)(2({i + 1})+1)}}{{6}}$$

#### Lists and Tables

1. Numbered item 1 for section {i + 1}
2. Numbered item 2 for section {i + 1}
3. Numbered item 3 for section {i + 1}

| Column A | Column B | Column C |
|----------|----------|----------|
| Data {i + 1}A | Data {i + 1}B | Data {i + 1}C |
| More {i + 1}A | More {i + 1}B | More {i + 1}C |

#### Code Blocks

```python
def function_for_section_{i + 1}():
    \"\"\"Example function for section {i + 1}.\"\"\"
    result = []
    for j in range({i + 1}):
        result.append(j * {i + 1})
    return result
```

""")
        
        content = f"""---
title: Large Document
date: 2024-01-15
tags: [test, large, performance]
author: Performance Tester
summary: A large document for performance testing with 100 sections.
---

# Large Document

This is a large document for performance testing with extensive content.

{''.join(sections)}

## Conclusion

This concludes our large document with {len(sections)} sections.
"""
        
        parser = MarkdownParser()
        
        def parse_large():
            return parser.parse(content)
        
        result = benchmark(parse_large)
        metadata, html = result
        
        assert metadata['title'] == 'Large Document'
        assert 'Section 1' in html
        assert 'Section 100' in html
        assert len(html) > 50000  # Should be substantial HTML
    
    def test_front_matter_parsing_performance(self, benchmark):
        """Benchmark front matter parsing performance."""
        content = """---
title: Performance Test Post
date: 2024-01-15T10:30:00
tags: [performance, test, benchmark, parsing, yaml]
categories: [testing, performance]
author: Performance Tester
email: <EMAIL>
description: A comprehensive test post for performance benchmarking
keywords: [performance, benchmark, test, parsing]
draft: false
featured: true
toc: true
math: true
comments: true
sharing: true
custom_field_1: value1
custom_field_2: value2
custom_field_3: value3
custom_field_4: value4
custom_field_5: value5
---

Content here.
"""
        
        parser = FrontMatterParser()
        
        def parse_frontmatter():
            return parser.parse(content)
        
        result = benchmark(parse_frontmatter)
        metadata, body = result
        
        assert metadata['title'] == 'Performance Test Post'
        assert len(metadata['tags']) == 5
        assert metadata['draft'] is False


@pytest.mark.slow
class TestGenerationPerformance:
    """Test HTML generation performance."""
    
    def test_single_page_generation(self, benchmark, temp_dir):
        """Benchmark single page generation."""
        config = Config()
        config.build.content_dir = temp_dir / "content"
        config.build.output_dir = temp_dir / "_site"
        config.build.template_dir = temp_dir / "templates"
        
        # Create content
        content_dir = temp_dir / "content"
        content_dir.mkdir()
        source_file = content_dir / "test.md"
        
        metadata = {
            'title': 'Performance Test',
            'date': datetime(2024, 1, 15),
            'tags': ['performance', 'test']
        }
        
        html_content = """<h1>Performance Test</h1>
<p>This is a performance test with <strong>bold</strong> and <em>italic</em> text.</p>
<ul>
<li>List item 1</li>
<li>List item 2</li>
</ul>
"""
        
        generator = HTMLGenerator(config)
        
        def generate_page():
            return generator.generate_page(metadata, html_content, source_file)
        
        result = benchmark(generate_page)
        assert result.exists()
    
    def test_multiple_pages_generation(self, benchmark, temp_dir):
        """Benchmark generation of multiple pages."""
        config = Config()
        config.build.content_dir = temp_dir / "content"
        config.build.output_dir = temp_dir / "_site"
        config.build.template_dir = temp_dir / "templates"
        
        content_dir = temp_dir / "content"
        content_dir.mkdir()
        
        # Prepare multiple pages data
        pages_data = []
        for i in range(50):
            metadata = {
                'title': f'Performance Test Post {i + 1}',
                'date': datetime(2024, 1, 15),
                'tags': ['performance', 'test', f'post{i + 1}']
            }
            
            html_content = f"""<h1>Performance Test Post {i + 1}</h1>
<p>This is performance test post number {i + 1} with content.</p>
<h2>Section 1</h2>
<p>Some content for section 1 of post {i + 1}.</p>
<h2>Section 2</h2>
<p>Some content for section 2 of post {i + 1}.</p>
<ul>
<li>List item 1 for post {i + 1}</li>
<li>List item 2 for post {i + 1}</li>
</ul>
"""
            
            source_file = content_dir / f"post{i + 1}.md"
            pages_data.append((metadata, html_content, source_file))
        
        generator = HTMLGenerator(config)
        
        def generate_multiple_pages():
            results = []
            for metadata, html_content, source_file in pages_data:
                result = generator.generate_page(metadata, html_content, source_file)
                results.append(result)
            return results
        
        results = benchmark(generate_multiple_pages)
        assert len(results) == 50
        assert all(result.exists() for result in results)
    
    def test_sitemap_generation_performance(self, benchmark, temp_dir):
        """Benchmark sitemap generation with many posts."""
        config = Config()
        config.build.output_dir = temp_dir / "_site"
        config.site.url = "https://performance.example.com"
        
        generator = HTMLGenerator(config)
        
        # Add many posts
        for i in range(1000):
            generator.posts.append({
                'title': f'Post {i + 1}',
                'url': f'/post{i + 1}.html',
                'date': datetime(2024, 1, 15),
                'draft': False
            })
        
        def generate_sitemap():
            return generator.generate_sitemap()
        
        result = benchmark(generate_sitemap)
        assert result.exists()
        
        # Verify sitemap contains all posts
        sitemap_content = result.read_text()
        assert 'post1.html' in sitemap_content
        assert 'post1000.html' in sitemap_content
    
    def test_rss_generation_performance(self, benchmark, temp_dir):
        """Benchmark RSS generation with many posts."""
        config = Config()
        config.build.output_dir = temp_dir / "_site"
        config.site.url = "https://performance.example.com"
        config.site.title = "Performance Test Blog"
        config.site.description = "Performance testing blog"
        
        generator = HTMLGenerator(config)
        
        # Add many posts (RSS typically shows latest 10)
        for i in range(100):
            generator.posts.append({
                'title': f'Post {i + 1}',
                'url': f'/post{i + 1}.html',
                'date': datetime(2024, 1, 15 + i),  # Different dates
                'summary': f'Summary for post {i + 1}',
                'draft': False
            })
        
        def generate_rss():
            return generator.generate_rss()
        
        result = benchmark(generate_rss)
        assert result.exists()


@pytest.mark.slow
class TestMemoryUsage:
    """Test memory usage patterns."""
    
    def test_large_content_memory_usage(self, temp_dir):
        """Test memory usage with large content."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        # Create very large content
        large_content = "# Large Content\n\n" + "This is a line of content. " * 10000
        
        parser = MarkdownParser()
        
        # Parse large content multiple times
        for _ in range(10):
            metadata, html = parser.parse(large_content)
            assert 'Large Content' in html
        
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be reasonable (less than 100MB)
        assert memory_increase < 100 * 1024 * 1024, f"Memory increased by {memory_increase / 1024 / 1024:.2f}MB"
    
    def test_multiple_files_memory_usage(self, temp_dir):
        """Test memory usage when processing multiple files."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        config = Config()
        config.build.content_dir = temp_dir / "content"
        config.build.output_dir = temp_dir / "_site"
        
        content_dir = temp_dir / "content"
        content_dir.mkdir()
        
        parser = MarkdownParser()
        generator = HTMLGenerator(config)
        
        # Process many files
        for i in range(100):
            content = f"""---
title: Memory Test Post {i + 1}
---

# Memory Test Post {i + 1}

This is post {i + 1} for memory testing.

{'Content line. ' * 100}
"""
            
            source_file = content_dir / f"post{i + 1}.md"
            metadata, html = parser.parse(content, source_file)
            generator.generate_page(metadata, html, source_file)
        
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be reasonable
        assert memory_increase < 200 * 1024 * 1024, f"Memory increased by {memory_increase / 1024 / 1024:.2f}MB"


@pytest.mark.slow
class TestConcurrencyPerformance:
    """Test performance under concurrent operations."""
    
    def test_concurrent_parsing(self, temp_dir):
        """Test concurrent parsing performance."""
        import concurrent.futures
        import threading
        
        parser = MarkdownParser()
        
        def parse_content(content_id):
            content = f"""---
title: Concurrent Test {content_id}
---

# Concurrent Test {content_id}

This is content for concurrent test {content_id}.

{'Line of content. ' * 50}
"""
            return parser.parse(content)
        
        start_time = time.time()
        
        # Parse concurrently
        with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
            futures = [executor.submit(parse_content, i) for i in range(20)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        end_time = time.time()
        duration = end_time - start_time
        
        assert len(results) == 20
        assert duration < 5.0, f"Concurrent parsing took {duration:.2f} seconds"
        
        # Verify all results are correct
        for metadata, html in results:
            assert 'Concurrent Test' in metadata['title']
            assert 'Concurrent Test' in html
