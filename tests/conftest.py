"""
Pytest configuration and fixtures for Blawger tests.

This module provides common fixtures and configuration for all tests.
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from typing import Generator, Dict, Any

import gwernpy


@pytest.fixture
def temp_dir() -> Generator[Path, None, None]:
    """Create a temporary directory for test files."""
    temp_path = Path(tempfile.mkdtemp())
    try:
        yield temp_path
    finally:
        shutil.rmtree(temp_path)


@pytest.fixture
def sample_markdown() -> str:
    """Sample markdown content for testing."""
    return """---
title: Test Post
date: 2024-01-01
tags: [test, sample]
---

# Test Post

This is a **test post** with some *markdown* content.

## Features

- Lists work
- So do [links](https://example.com)
- And `code blocks`

```python
def hello():
    return "Hello, World!"
```

Math works too: $E = mc^2$
"""


@pytest.fixture
def sample_config() -> Dict[str, Any]:
    """Sample configuration for testing."""
    return {
        "site": {
            "title": "Test Blog",
            "description": "A test blog for Blawger",
            "url": "https://example.com",
            "author": "Test Author"
        },
        "build": {
            "output_dir": "_site",
            "content_dir": "content",
            "theme": "tufte"
        },
        "features": {
            "math": True,
            "syntax_highlighting": True,
            "org_roam": False
        }
    }


@pytest.fixture
def sample_blog_structure(temp_dir: Path) -> Path:
    """Create a sample blog directory structure."""
    # Create directories
    (temp_dir / "content").mkdir()
    (temp_dir / "content" / "posts").mkdir()
    (temp_dir / "templates").mkdir()
    (temp_dir / "static").mkdir()
    
    # Create sample content
    post_content = """---
title: Sample Post
date: 2024-01-01
---

# Sample Post

This is a sample blog post.
"""
    
    (temp_dir / "content" / "posts" / "sample.md").write_text(post_content)
    
    # Create basic config
    config_content = """
site:
  title: Test Blog
  url: https://example.com
"""
    
    (temp_dir / "config.yml").write_text(config_content)
    
    return temp_dir


@pytest.fixture
def mock_org_roam_db(temp_dir: Path) -> Path:
    """Create a mock org-roam database structure."""
    org_dir = temp_dir / "org-roam"
    org_dir.mkdir()

    # Create sample org files
    sample_org = """#+title: Sample Note
#+date: [2024-01-01]
#+filetags: :sample:test:

* Sample Note

This is a sample org-roam note with [[id:123][links]] to other notes.
"""

    (org_dir / "sample.org").write_text(sample_org)

    return org_dir


@pytest.fixture
def sample_metadata() -> Dict[str, Any]:
    """Sample metadata for testing."""
    from datetime import datetime
    return {
        'title': 'Test Post',
        'date': datetime(2024, 1, 15, 10, 30),
        'tags': ['test', 'sample'],
        'author': 'Test Author',
        'draft': False,
        'summary': 'This is a test post summary.'
    }


@pytest.fixture
def sample_html_content() -> str:
    """Sample HTML content for testing."""
    return """<h1>Test Post</h1>
<p>This is a <strong>test</strong> post with <em>italic</em> text and <code>code</code>.</p>
<h2>Section</h2>
<ul>
<li>List item 1</li>
<li>List item 2</li>
</ul>
<h3>Math</h3>
<p>Inline math: <span class="math inline">\\(E = mc^2\\)</span></p>
<div class="math display">\\[
\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}
\\]</div>
"""


@pytest.fixture
def mock_config():
    """Create a mock configuration object."""
    from unittest.mock import Mock
    from gwernpy.config import Config, SiteConfig, BuildConfig

    config = Mock(spec=Config)
    config.site = Mock(spec=SiteConfig)
    config.build = Mock(spec=BuildConfig)

    # Set default values
    config.site.title = "Mock Blog"
    config.site.author = "Mock Author"
    config.site.url = "https://mock.example.com"
    config.site.language = "en"

    config.build.content_dir = Path("content")
    config.build.output_dir = Path("_site")
    config.build.template_dir = Path("templates")
    config.build.minify_html = True
    config.build.generate_sitemap = True
    config.build.generate_rss = True

    return config


# Test markers
pytest_plugins = []

# Custom markers
def pytest_configure(config):
    """Configure custom pytest markers."""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "network: mark test as requiring network access"
    )


# Test collection configuration
def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers automatically."""
    for item in items:
        # Add unit marker to tests in unit/ directory
        if "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        
        # Add integration marker to tests in integration/ directory
        elif "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
