"""
End-to-end integration tests for Blawger.

These tests verify that the complete workflow works correctly,
from content creation to site generation.
"""

import pytest
from pathlib import Path
from datetime import datetime

from gwernpy.config import Config
from gwernpy.parser.markdown import MarkdownParser
from gwernpy.parser.frontmatter import FrontMatterParser
from gwernpy.generator.html import HTMLGenerator
from gwernpy.utils.bot_control import BotController


@pytest.mark.integration
class TestEndToEndWorkflow:
    """Test complete end-to-end workflows."""
    
    def test_complete_site_generation(self, temp_dir):
        """Test complete site generation from markdown to HTML."""
        # Setup directory structure
        content_dir = temp_dir / "content"
        output_dir = temp_dir / "_site"
        template_dir = temp_dir / "templates"
        
        content_dir.mkdir()
        
        # Create sample content
        post_content = """---
title: Integration Test Post
date: 2024-01-15
tags: [integration, test]
author: Test Author
summary: This is an integration test post.
---

# Integration Test Post

This is a comprehensive test post that includes:

## Features

- **Bold text** and *italic text*
- `Inline code` and code blocks
- Mathematical expressions: $E = mc^2$
- Citations [@test2024]
- Sidenotes^[This is a sidenote for testing]
- Margin notes{-This is a margin note-}

## Lists

1. Numbered list item 1
2. Numbered list item 2

- Bullet point 1
- Bullet point 2

## Tables

| Column 1 | Column 2 | Column 3 |
|----------|----------|----------|
| Data 1   | Data 2   | Data 3   |
| More 1   | More 2   | More 3   |

## Math

Display math:
$$
\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}
$$

## Links

[External link](https://example.com)
"""
        
        post_file = content_dir / "test-post.md"
        post_file.write_text(post_content)
        
        # Setup configuration
        config = Config()
        config.build.content_dir = content_dir
        config.build.output_dir = output_dir
        config.build.template_dir = template_dir
        config.site.title = "Integration Test Blog"
        config.site.author = "Test Author"
        config.site.url = "https://test.example.com"
        
        # Initialize components
        markdown_parser = MarkdownParser()
        html_generator = HTMLGenerator(config)
        bot_controller = BotController(config.bot)
        
        # Parse markdown
        metadata, html_content = markdown_parser.parse(post_content, post_file)
        
        # Verify parsing results
        assert metadata['title'] == 'Integration Test Post'
        assert metadata['author'] == 'Test Author'
        assert 'integration' in metadata['tags']
        assert 'test' in metadata['tags']
        
        # Verify HTML content
        assert '<h1>Integration Test Post</h1>' in html_content
        assert '<strong>Bold text</strong>' in html_content
        assert '<em>italic text</em>' in html_content
        assert '<code>Inline code</code>' in html_content
        assert 'math inline' in html_content
        assert 'math display' in html_content
        assert 'sidenote' in html_content
        assert 'marginnote' in html_content
        assert '<table>' in html_content
        assert 'citation' in html_content
        
        # Generate HTML page
        output_path = html_generator.generate_page(metadata, html_content, post_file)
        
        # Verify output file
        assert output_path.exists()
        assert output_path.suffix == '.html'
        
        generated_html = output_path.read_text()
        assert 'Integration Test Post' in generated_html
        assert 'Test Author' in generated_html
        assert '2024-01-15' in generated_html
        assert html_content in generated_html
        
        # Generate additional files
        sitemap_path = html_generator.generate_sitemap()
        rss_path = html_generator.generate_rss()
        robots_path = bot_controller.generate_robots_txt(output_dir)
        
        # Verify additional files
        assert sitemap_path.exists()
        assert rss_path.exists()
        assert robots_path.exists()
        
        # Verify sitemap content
        sitemap_content = sitemap_path.read_text()
        assert 'test-post.html' in sitemap_content
        assert '2024-01-15' in sitemap_content
        
        # Verify RSS content
        rss_content = rss_path.read_text()
        assert 'Integration Test Post' in rss_content
        assert 'This is an integration test post' in rss_content
        
        # Verify robots.txt content
        robots_content = robots_path.read_text()
        assert 'User-agent: *' in robots_content
        assert 'Sitemap: /sitemap.xml' in robots_content
    
    def test_multiple_posts_workflow(self, temp_dir):
        """Test workflow with multiple posts."""
        # Setup
        content_dir = temp_dir / "content"
        output_dir = temp_dir / "_site"
        content_dir.mkdir()
        
        # Create multiple posts
        posts_data = [
            {
                'filename': 'post1.md',
                'title': 'First Post',
                'date': '2024-01-10',
                'content': '# First Post\n\nThis is the first post.'
            },
            {
                'filename': 'post2.md',
                'title': 'Second Post',
                'date': '2024-01-15',
                'content': '# Second Post\n\nThis is the second post.'
            },
            {
                'filename': 'post3.md',
                'title': 'Third Post',
                'date': '2024-01-20',
                'content': '# Third Post\n\nThis is the third post.'
            }
        ]
        
        for post_data in posts_data:
            post_content = f"""---
title: {post_data['title']}
date: {post_data['date']}
tags: [test]
---

{post_data['content']}
"""
            post_file = content_dir / post_data['filename']
            post_file.write_text(post_content)
        
        # Setup configuration
        config = Config()
        config.build.content_dir = content_dir
        config.build.output_dir = output_dir
        config.site.url = "https://test.example.com"
        
        # Process all posts
        markdown_parser = MarkdownParser()
        html_generator = HTMLGenerator(config)
        
        for post_data in posts_data:
            post_file = content_dir / post_data['filename']
            content = post_file.read_text()
            
            metadata, html_content = markdown_parser.parse(content, post_file)
            html_generator.generate_page(metadata, html_content, post_file)
        
        # Verify all posts were processed
        assert len(html_generator.posts) == 3
        
        # Verify output files
        for post_data in posts_data:
            expected_output = output_dir / post_data['filename'].replace('.md', '.html')
            assert expected_output.exists()
        
        # Generate and verify sitemap
        sitemap_path = html_generator.generate_sitemap()
        sitemap_content = sitemap_path.read_text()
        
        for post_data in posts_data:
            expected_url = post_data['filename'].replace('.md', '.html')
            assert expected_url in sitemap_content
        
        # Generate and verify RSS
        rss_path = html_generator.generate_rss()
        rss_content = rss_path.read_text()
        
        # RSS should contain all posts (or latest 10)
        for post_data in posts_data:
            assert post_data['title'] in rss_content
    
    def test_draft_posts_handling(self, temp_dir):
        """Test that draft posts are handled correctly."""
        # Setup
        content_dir = temp_dir / "content"
        output_dir = temp_dir / "_site"
        content_dir.mkdir()
        
        # Create published and draft posts
        published_content = """---
title: Published Post
date: 2024-01-15
draft: false
---

# Published Post

This post should be published.
"""
        
        draft_content = """---
title: Draft Post
date: 2024-01-16
draft: true
---

# Draft Post

This post should not be published.
"""
        
        (content_dir / "published.md").write_text(published_content)
        (content_dir / "draft.md").write_text(draft_content)
        
        # Setup configuration
        config = Config()
        config.build.content_dir = content_dir
        config.build.output_dir = output_dir
        config.site.url = "https://test.example.com"
        
        # Process posts
        markdown_parser = MarkdownParser()
        html_generator = HTMLGenerator(config)
        
        for post_file in content_dir.glob("*.md"):
            content = post_file.read_text()
            metadata, html_content = markdown_parser.parse(content, post_file)
            html_generator.generate_page(metadata, html_content, post_file)
        
        # Generate sitemap and RSS
        sitemap_path = html_generator.generate_sitemap()
        rss_path = html_generator.generate_rss()
        
        # Verify draft exclusion
        sitemap_content = sitemap_path.read_text()
        rss_content = rss_path.read_text()
        
        assert 'published.html' in sitemap_content
        assert 'draft.html' not in sitemap_content
        
        assert 'Published Post' in rss_content
        assert 'Draft Post' not in rss_content
    
    def test_configuration_integration(self, temp_dir):
        """Test integration with configuration system."""
        # Create config file
        config_content = """
site:
  title: "Config Test Blog"
  author: "Config Author"
  url: "https://config.example.com"

build:
  content_dir: "content"
  output_dir: "_output"
  minify_html: true

markdown:
  extensions: ["tables", "footnotes", "math"]
  math_engine: "katex"

bot:
  allow_ai_training: false
  robots_txt: true
"""
        
        config_file = temp_dir / "blawger.yml"
        config_file.write_text(config_content)
        
        # Load configuration
        config = Config.load(config_file)
        
        # Verify configuration loaded correctly
        assert config.site.title == "Config Test Blog"
        assert config.site.author == "Config Author"
        assert config.build.output_dir == Path("_output")
        assert config.markdown.math_engine == "katex"
        assert config.bot.allow_ai_training is False
        
        # Create content
        content_dir = temp_dir / "content"
        content_dir.mkdir()
        
        post_content = """---
title: Config Test Post
---

# Config Test

Math: $x^2 + y^2 = z^2$
"""
        
        (content_dir / "test.md").write_text(post_content)
        
        # Update config paths to use temp_dir
        config.build.content_dir = content_dir
        config.build.output_dir = temp_dir / "_output"
        
        # Process with configuration
        markdown_parser = MarkdownParser(
            extensions=config.markdown.extensions,
            config=config.markdown.__dict__
        )
        html_generator = HTMLGenerator(config)
        bot_controller = BotController(config.bot)
        
        # Parse and generate
        post_file = content_dir / "test.md"
        content = post_file.read_text()
        metadata, html_content = markdown_parser.parse(content, post_file)
        output_path = html_generator.generate_page(metadata, html_content, post_file)
        
        # Verify output
        assert output_path.exists()
        generated_html = output_path.read_text()
        assert 'Config Test Blog' in generated_html
        assert 'Config Author' in generated_html
        
        # Verify bot control
        robots_path = bot_controller.generate_robots_txt(config.build.output_dir)
        robots_content = robots_path.read_text()
        assert 'GPTBot' in robots_content  # AI training bots should be blocked
        assert 'Disallow: /' in robots_content


@pytest.mark.integration
class TestErrorHandlingIntegration:
    """Test error handling in integration scenarios."""
    
    def test_invalid_front_matter_handling(self, temp_dir):
        """Test handling of invalid front matter in integration."""
        content_dir = temp_dir / "content"
        content_dir.mkdir()
        
        # Create file with invalid front matter
        invalid_content = """---
title: "Unclosed quote
invalid: yaml: content
---

# Content

This should fail gracefully.
"""
        
        (content_dir / "invalid.md").write_text(invalid_content)
        
        config = Config()
        config.build.content_dir = content_dir
        
        markdown_parser = MarkdownParser()
        
        # Should raise appropriate error
        with pytest.raises(Exception):  # Could be MarkdownError or FrontMatterError
            post_file = content_dir / "invalid.md"
            content = post_file.read_text()
            markdown_parser.parse(content, post_file)
    
    def test_missing_template_handling(self, temp_dir):
        """Test handling of missing templates."""
        content_dir = temp_dir / "content"
        output_dir = temp_dir / "_site"
        content_dir.mkdir()
        
        # Create content
        post_content = """---
title: Test Post
template: nonexistent.html
---

# Test Post

Content here.
"""
        
        (content_dir / "test.md").write_text(post_content)
        
        config = Config()
        config.build.content_dir = content_dir
        config.build.output_dir = output_dir
        config.build.template_dir = temp_dir / "templates"  # Empty template dir
        
        # Process should work (falls back to default template)
        markdown_parser = MarkdownParser()
        html_generator = HTMLGenerator(config)
        
        post_file = content_dir / "test.md"
        content = post_file.read_text()
        metadata, html_content = markdown_parser.parse(content, post_file)
        
        # Should not raise error, should fall back
        output_path = html_generator.generate_page(metadata, html_content, post_file)
        assert output_path.exists()
