"""
Tests for the parser modules.
"""
import unittest
from gwernpy.parser.markdown import Markdown<PERSON>arser
from gwernpy.parser.orgmode import OrgModeParser


class TestMarkdownParser(unittest.TestCase):
    """Test cases for the Markdown parser."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.parser = MarkdownParser()
    
    def test_parse_empty(self):
        """Test parsing empty content."""
        metadata, content = self.parser.parse("")
        self.assertEqual(metadata, {})
        self.assertEqual(content, "")
    
    def test_parse_metadata(self):
        """Test parsing content with metadata."""
        # TODO: Implement test once parser is implemented
        pass


class TestOrgModeParser(unittest.TestCase):
    """Test cases for the org-mode parser."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.parser = OrgModeParser()
    
    def test_parse_empty(self):
        """Test parsing empty content."""
        metadata, content = self.parser.parse("")
        self.assertEqual(metadata, {})
        self.assertEqual(content, "")
    
    def test_parse_metadata(self):
        """Test parsing content with metadata."""
        # TODO: Implement test once parser is implemented
        pass


if __name__ == "__main__":
    unittest.main()
