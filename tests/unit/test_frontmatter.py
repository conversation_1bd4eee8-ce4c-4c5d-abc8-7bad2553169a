"""
Unit tests for the front matter parser.
"""

import pytest
from datetime import datetime
from unittest.mock import patch

from gwernpy.parser.frontmatter import (
    FrontMatterParser, FrontMatterError,
    parse_front_matter, create_front_matter
)


class TestFrontMatterParser:
    """Test FrontMatterParser class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.parser = FrontMatterParser()
    
    def test_parse_yaml_front_matter(self):
        """Test parsing YAML front matter."""
        content = """---
title: Test Post
date: 2024-01-15
tags: [test, sample]
draft: false
---

# Test Content

This is the main content.
"""
        metadata, body = self.parser.parse(content)
        
        assert metadata['title'] == "Test Post"
        assert isinstance(metadata['date'], datetime)
        assert metadata['date'].year == 2024
        assert metadata['tags'] == ['test', 'sample']
        assert metadata['draft'] is False
        assert body.strip() == "# Test Content\n\nThis is the main content."
    
    def test_parse_toml_front_matter(self):
        """Test parsing TOML front matter."""
        content = """+++
title = "TOML Post"
date = "2024-02-20"
tags = ["toml", "test"]
+++

TOML content here.
"""
        metadata, body = self.parser.parse(content)
        
        assert metadata['title'] == "TOML Post"
        assert isinstance(metadata['date'], datetime)
        assert metadata['tags'] == ['toml', 'test']
        assert body.strip() == "TOML content here."
    
    def test_parse_no_front_matter(self):
        """Test parsing content without front matter."""
        content = """# Regular Markdown

No front matter here.
"""
        metadata, body = self.parser.parse(content)
        
        assert metadata == {}
        assert body == content
    
    def test_parse_empty_front_matter(self):
        """Test parsing empty front matter."""
        content = """---
---

Content after empty front matter.
"""
        metadata, body = self.parser.parse(content)
        
        # Should have defaults applied
        assert metadata['title'] == 'Untitled'
        assert isinstance(metadata['date'], datetime)
        assert metadata['draft'] is False
        assert metadata['tags'] == []
    
    def test_parse_invalid_yaml(self):
        """Test handling of invalid YAML front matter."""
        content = """---
title: "Unclosed quote
invalid: yaml: content
---

Content
"""
        with pytest.raises(FrontMatterError, match="Invalid YAML front matter"):
            self.parser.parse(content)
    
    def test_parse_invalid_toml(self):
        """Test handling of invalid TOML front matter."""
        content = """+++
title = "Unclosed quote
invalid toml
+++

Content
"""
        with pytest.raises(FrontMatterError, match="Invalid TOML front matter"):
            self.parser.parse(content)
    
    def test_process_metadata_defaults(self):
        """Test metadata processing with defaults."""
        metadata = {}
        processed = self.parser._process_metadata(metadata)
        
        assert processed['title'] == 'Untitled'
        assert isinstance(processed['date'], datetime)
        assert processed['draft'] is False
        assert processed['tags'] == []
        assert processed['categories'] == []
    
    def test_process_metadata_tags_string(self):
        """Test processing tags from comma-separated string."""
        metadata = {'tags': 'python, testing, markdown'}
        processed = self.parser._process_metadata(metadata)
        
        assert processed['tags'] == ['python', 'testing', 'markdown']
    
    def test_process_metadata_tags_list(self):
        """Test processing tags from list."""
        metadata = {'tags': ['python', 'testing', 123]}
        processed = self.parser._process_metadata(metadata)
        
        assert processed['tags'] == ['python', 'testing', '123']
    
    def test_process_metadata_boolean_strings(self):
        """Test processing boolean strings."""
        metadata = {
            'draft': 'true',
            'published': 'false',
            'featured': 'yes',
            'toc': '1'
        }
        processed = self.parser._process_metadata(metadata)
        
        assert processed['draft'] is True
        assert processed['published'] is False
        assert processed['featured'] is True
        assert processed['toc'] is True
    
    def test_parse_date_formats(self):
        """Test parsing various date formats."""
        test_dates = [
            ('2024-01-15', datetime(2024, 1, 15)),
            ('2024-01-15 14:30:00', datetime(2024, 1, 15, 14, 30, 0)),
            ('2024/01/15', datetime(2024, 1, 15)),
            ('2024-01-15T14:30:00', datetime(2024, 1, 15, 14, 30, 0)),
            ('2024-01-15T14:30:00Z', datetime(2024, 1, 15, 14, 30, 0)),
        ]
        
        for date_str, expected in test_dates:
            result = self.parser._parse_date(date_str)
            assert result == expected
    
    def test_parse_date_invalid_format(self):
        """Test handling of invalid date format."""
        with pytest.raises(FrontMatterError, match="Unable to parse date"):
            self.parser._parse_date("invalid-date-format")
    
    @patch('gwernpy.parser.frontmatter.parse')
    def test_parse_date_with_dateutil(self, mock_parse):
        """Test date parsing with dateutil fallback."""
        mock_parse.return_value = datetime(2024, 1, 15)
        
        result = self.parser._parse_date("January 15, 2024")
        assert result == datetime(2024, 1, 15)
        mock_parse.assert_called_once_with("January 15, 2024")
    
    def test_create_yaml_front_matter(self):
        """Test creating YAML front matter."""
        metadata = {
            'title': 'Test Post',
            'date': datetime(2024, 1, 15, 10, 30),
            'tags': ['test', 'yaml'],
            'draft': False
        }
        
        result = self.parser.create_front_matter(metadata, 'yaml')
        
        assert result.startswith('---\n')
        assert result.endswith('---\n')
        assert 'title: Test Post' in result
        assert '2024-01-15T10:30:00' in result
        assert 'tags:' in result
        assert 'draft: false' in result
    
    def test_create_toml_front_matter(self):
        """Test creating TOML front matter."""
        metadata = {
            'title': 'TOML Test',
            'date': datetime(2024, 2, 20),
            'tags': ['toml', 'test']
        }
        
        result = self.parser.create_front_matter(metadata, 'toml')
        
        assert result.startswith('+++\n')
        assert result.endswith('+++\n')
        assert 'title = "TOML Test"' in result
        assert '2024-02-20T00:00:00' in result
    
    def test_create_front_matter_unsupported_format(self):
        """Test creating front matter with unsupported format."""
        metadata = {'title': 'Test'}
        
        with pytest.raises(FrontMatterError, match="Unsupported front matter format"):
            self.parser.create_front_matter(metadata, 'xml')
    
    def test_extract_summary(self):
        """Test summary extraction."""
        content = """This is a long piece of content that should be truncated 
        to create a summary. It has multiple sentences and should be cut off 
        at a reasonable point. This sentence should not appear in the summary 
        because it's too long for the specified limit."""
        
        summary = self.parser.extract_summary(content, max_length=100)
        
        assert len(summary) <= 100
        assert summary.endswith('...')
        assert 'This sentence should not appear' not in summary
    
    def test_extract_summary_short_content(self):
        """Test summary extraction with short content."""
        content = "Short content."
        
        summary = self.parser.extract_summary(content, max_length=100)
        
        assert summary == content
        assert not summary.endswith('...')
    
    def test_extract_summary_with_markdown(self):
        """Test summary extraction removes markdown formatting."""
        content = """# This is a **bold** title

        This content has *italic* text and `code` blocks that should be 
        removed from the summary. [Links](http://example.com) should also be cleaned."""
        
        summary = self.parser.extract_summary(content, max_length=200)
        
        assert '#' not in summary
        assert '**' not in summary
        assert '*' not in summary
        assert '`' not in summary
        assert '[' not in summary
        assert ']' not in summary
        assert '(' not in summary


class TestConvenienceFunctions:
    """Test convenience functions."""
    
    def test_parse_front_matter_function(self):
        """Test parse_front_matter convenience function."""
        content = """---
title: Convenience Test
---

Content here.
"""
        metadata, body = parse_front_matter(content)
        
        assert metadata['title'] == 'Convenience Test'
        assert body.strip() == 'Content here.'
    
    def test_create_front_matter_function(self):
        """Test create_front_matter convenience function."""
        metadata = {'title': 'Function Test', 'tags': ['test']}
        
        result = create_front_matter(metadata, 'yaml')
        
        assert result.startswith('---\n')
        assert 'title: Function Test' in result


class TestEdgeCases:
    """Test edge cases and error conditions."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.parser = FrontMatterParser()
    
    def test_front_matter_with_dashes_in_content(self):
        """Test front matter parsing when content contains dashes."""
        content = """---
title: Test
---

Content with --- dashes in it.
And more --- dashes here.
"""
        metadata, body = self.parser.parse(content)
        
        assert metadata['title'] == 'Test'
        assert '--- dashes' in body
    
    def test_front_matter_with_plus_in_content(self):
        """Test TOML front matter when content contains plus signs."""
        content = """+++
title = "Test"
+++

Content with +++ plus signs.
More +++ here.
"""
        metadata, body = self.parser.parse(content)
        
        assert metadata['title'] == 'Test'
        assert '+++ plus' in body
    
    def test_unicode_content(self):
        """Test handling of Unicode content."""
        content = """---
title: "Tëst Pöst"
author: "Ünicöde Authör"
---

Cöntënt with ünicöde characters: 你好世界
"""
        metadata, body = self.parser.parse(content)
        
        assert metadata['title'] == "Tëst Pöst"
        assert metadata['author'] == "Ünicöde Authör"
        assert "你好世界" in body
    
    def test_empty_content(self):
        """Test handling of empty content."""
        metadata, body = self.parser.parse("")
        
        assert metadata == {}
        assert body == ""
    
    def test_only_front_matter(self):
        """Test content with only front matter."""
        content = """---
title: Only Front Matter
---"""
        metadata, body = self.parser.parse(content)
        
        assert metadata['title'] == 'Only Front Matter'
        assert body == ""
