"""
Unit tests for the markdown parser.
"""

import pytest
from pathlib import Path
from unittest.mock import Mock, patch

from gwernpy.parser.markdown import <PERSON><PERSON><PERSON><PERSON><PERSON>, MarkdownError
from gwernpy.parser.frontmatter import FrontMatterError


class TestMarkdownParser:
    """Test MarkdownParser class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.parser = MarkdownParser()
    
    def test_initialization_default_extensions(self):
        """Test parser initialization with default extensions."""
        parser = MarkdownParser()
        expected_extensions = ["tables", "footnotes", "toc", "math", "citations", "containers"]
        assert parser.extensions == expected_extensions
    
    def test_initialization_custom_extensions(self):
        """Test parser initialization with custom extensions."""
        extensions = ["tables", "footnotes"]
        parser = MarkdownParser(extensions=extensions)
        assert parser.extensions == extensions
    
    def test_parse_simple_markdown(self):
        """Test parsing simple markdown content."""
        content = """---
title: Test Post
---

# Hello World

This is a **test** post with *italic* text.
"""
        metadata, html = self.parser.parse(content)
        
        assert metadata['title'] == 'Test Post'
        assert '<h1>Hello World</h1>' in html
        assert '<strong>test</strong>' in html
        assert '<em>italic</em>' in html
    
    def test_parse_with_source_path(self):
        """Test parsing with source path metadata."""
        content = """---
title: Test
---

Content here.
"""
        source_path = Path("test.md")
        metadata, html = self.parser.parse(content, source_path)
        
        assert metadata['source_path'] == str(source_path)
        assert metadata['source_name'] == 'test'
    
    def test_parse_math_expressions(self):
        """Test parsing math expressions."""
        content = """# Math Test

Inline math: $E = mc^2$

Display math:
$$
\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}
$$
"""
        metadata, html = self.parser.parse(content)
        
        assert '<span class="math inline">\\(E = mc^2\\)</span>' in html
        assert '<div class="math display">\\[' in html
        assert '\\sqrt{\\pi}' in html
    
    def test_parse_citations(self):
        """Test parsing academic citations."""
        content = """# Citations Test

This is a citation [@smith2023] and multiple [@doe2022; @jones2021].
"""
        metadata, html = self.parser.parse(content)
        
        assert '<span class="citation">' in html
        assert '<a href="#ref-smith2023" class="citation-link">smith2023</a>' in html
        assert 'doe2022' in html and 'jones2021' in html
    
    def test_parse_sidenotes(self):
        """Test parsing sidenotes."""
        content = """# Sidenotes Test

This has a sidenote^[This is a sidenote] in the text.
"""
        metadata, html = self.parser.parse(content)
        
        assert 'margin-toggle sidenote-number' in html
        assert '<span class="sidenote">This is a sidenote</span>' in html
    
    def test_parse_margin_notes(self):
        """Test parsing margin notes."""
        content = """# Margin Notes Test

This has a margin note{-This is a margin note-} in the text.
"""
        metadata, html = self.parser.parse(content)
        
        assert 'margin-toggle' in html
        assert '<span class="marginnote">This is a margin note</span>' in html
    
    def test_parse_tables(self):
        """Test parsing markdown tables."""
        content = """# Table Test

| Name | Age | City |
|------|-----|------|
| John | 25  | NYC  |
| Jane | 30  | LA   |
"""
        metadata, html = self.parser.parse(content)
        
        assert '<table>' in html
        assert '<thead>' in html
        assert '<tbody>' in html
        assert '<th>Name</th>' in html
        assert '<td>John</td>' in html
    
    def test_parse_footnotes(self):
        """Test parsing footnotes."""
        content = """# Footnotes Test

This has a footnote[^1].

[^1]: This is the footnote content.
"""
        metadata, html = self.parser.parse(content)
        
        # markdown-it footnote plugin should handle this
        assert 'footnote' in html.lower() or 'fn' in html
    
    def test_add_table_of_contents(self):
        """Test table of contents generation."""
        content = """---
toc: true
---

# First Heading

## Second Heading

### Third Heading
"""
        metadata, html = self.parser.parse(content)
        
        assert 'table-of-contents' in html
        assert 'Table of Contents' in html
        assert 'first-heading' in html
        assert 'second-heading' in html
    
    def test_reading_time_calculation(self):
        """Test reading time calculation."""
        # Create content with approximately 200 words (1 minute read)
        words = ['word'] * 200
        content = f"# Test\n\n{' '.join(words)}"
        
        metadata, html = self.parser.parse(content)
        
        assert 'word_count' in metadata
        assert 'reading_time' in metadata
        assert metadata['word_count'] == 200
        assert metadata['reading_time'] == 1
    
    def test_process_images(self):
        """Test image processing for responsive design."""
        content = """# Images Test

![Alt text](image.jpg)

<img src="another.png" alt="Another">
"""
        metadata, html = self.parser.parse(content)
        
        assert 'responsive-image' in html
        assert 'loading="lazy"' in html
    
    def test_frontmatter_error_handling(self):
        """Test handling of front matter errors."""
        content = """---
invalid: yaml: content: [
---

Content
"""
        with pytest.raises(MarkdownError, match="Front matter error"):
            self.parser.parse(content)
    
    def test_general_parsing_error(self):
        """Test handling of general parsing errors."""
        with patch.object(self.parser.frontmatter_parser, 'parse') as mock_parse:
            mock_parse.side_effect = Exception("General error")
            
            with pytest.raises(MarkdownError, match="Markdown parsing error"):
                self.parser.parse("content")
    
    def test_parsing_error_with_source_path(self):
        """Test error reporting includes source path."""
        source_path = Path("error_file.md")
        
        with patch.object(self.parser.frontmatter_parser, 'parse') as mock_parse:
            mock_parse.side_effect = Exception("Error")
            
            with pytest.raises(MarkdownError, match="error_file.md"):
                self.parser.parse("content", source_path)


class TestMarkdownPreprocessing:
    """Test markdown preprocessing methods."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.parser = MarkdownParser()
    
    def test_preprocess_content(self):
        """Test content preprocessing."""
        content = "Math: $x^2$ and citation [@ref]"
        
        processed = self.parser._preprocess_content(content)
        
        # Should process math and citations
        assert '\\(' in processed  # Math processing
        assert 'citation' in processed  # Citation processing
    
    def test_process_math_inline(self):
        """Test inline math processing."""
        content = "Inline math: $E = mc^2$ here."
        
        processed = self.parser._process_math(content)
        
        assert '<span class="math inline">\\(E = mc^2\\)</span>' in processed
    
    def test_process_math_display(self):
        """Test display math processing."""
        content = """Display math:
$$
x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}
$$
"""
        processed = self.parser._process_math(content)
        
        assert '<div class="math display">\\[' in processed
        assert 'frac{-b' in processed
    
    def test_process_citations_single(self):
        """Test single citation processing."""
        content = "Reference [@smith2023] here."
        
        processed = self.parser._process_citations(content)
        
        assert '<span class="citation">' in processed
        assert 'href="#ref-smith2023"' in processed
        assert 'smith2023</a>' in processed
    
    def test_process_citations_multiple(self):
        """Test multiple citations processing."""
        content = "References [@smith2023; @doe2022] here."
        
        processed = self.parser._process_citations(content)
        
        assert 'smith2023' in processed
        assert 'doe2022' in processed
        assert '; ' in processed  # Separator
    
    def test_process_academic_syntax_sidenotes(self):
        """Test sidenote processing."""
        content = "Text with sidenote^[Note content] here."
        
        processed = self.parser._process_academic_syntax(content)
        
        assert 'sidenote-number' in processed
        assert 'Note content' in processed
    
    def test_process_academic_syntax_margin_notes(self):
        """Test margin note processing."""
        content = "Text with margin note{-Margin content-} here."
        
        processed = self.parser._process_academic_syntax(content)
        
        assert 'marginnote' in processed
        assert 'Margin content' in processed
    
    def test_multiple_sidenotes(self):
        """Test multiple sidenotes get unique IDs."""
        content = "First^[Note 1] and second^[Note 2] sidenotes."
        
        processed = self.parser._process_academic_syntax(content)
        
        assert 'sn-1' in processed
        assert 'sn-2' in processed
        assert 'Note 1' in processed
        assert 'Note 2' in processed


class TestMarkdownPostprocessing:
    """Test markdown post-processing methods."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.parser = MarkdownParser()
    
    def test_add_table_of_contents_with_headings(self):
        """Test TOC generation with multiple headings."""
        html = """<h1>First</h1>
<p>Content</p>
<h2>Second</h2>
<p>More content</p>
<h3>Third</h3>"""
        
        result = self.parser._add_table_of_contents(html)
        
        assert 'table-of-contents' in result
        assert 'href="#first"' in result
        assert 'href="#second"' in result
        assert 'href="#third"' in result
        assert 'id="first"' in result
    
    def test_add_table_of_contents_no_headings(self):
        """Test TOC generation with no headings."""
        html = "<p>Just a paragraph.</p>"
        
        result = self.parser._add_table_of_contents(html)
        
        assert result == html  # Should be unchanged
    
    def test_add_reading_time(self):
        """Test reading time calculation."""
        html = "<p>" + " ".join(["word"] * 400) + "</p>"
        metadata = {}
        
        result = self.parser._add_reading_time(html, metadata)
        
        assert metadata['word_count'] == 400
        assert metadata['reading_time'] == 2  # 400 words / 200 wpm = 2 minutes
        assert result == html  # HTML unchanged
    
    def test_process_images_adds_responsive_class(self):
        """Test image processing adds responsive classes."""
        html = '<img src="test.jpg" alt="Test">'
        
        result = self.parser._process_images(html)
        
        assert 'class="responsive-image"' in result
        assert 'loading="lazy"' in result
    
    def test_process_images_preserves_existing_class(self):
        """Test image processing preserves existing classes."""
        html = '<img src="test.jpg" alt="Test" class="existing">'
        
        result = self.parser._process_images(html)
        
        assert 'class="existing responsive-image"' in result
    
    def test_process_images_preserves_existing_loading(self):
        """Test image processing preserves existing loading attribute."""
        html = '<img src="test.jpg" alt="Test" loading="eager">'
        
        result = self.parser._process_images(html)
        
        assert 'loading="eager"' in result
        assert 'loading="lazy"' not in result


class TestMarkdownConfiguration:
    """Test markdown parser configuration."""
    
    def test_custom_config(self):
        """Test parser with custom configuration."""
        config = {
            'math_engine': 'mathjax',
            'syntax_highlighting': False
        }
        parser = MarkdownParser(config=config)
        
        assert parser.config['math_engine'] == 'mathjax'
        assert parser.config['syntax_highlighting'] is False
    
    def test_extensions_configuration(self):
        """Test parser with specific extensions."""
        extensions = ['tables', 'footnotes']
        parser = MarkdownParser(extensions=extensions)
        
        assert 'math' not in parser.extensions
        assert 'citations' not in parser.extensions
        assert 'tables' in parser.extensions
        assert 'footnotes' in parser.extensions


class TestMarkdownEdgeCases:
    """Test edge cases and error conditions."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.parser = MarkdownParser()
    
    def test_empty_content(self):
        """Test parsing empty content."""
        metadata, html = self.parser.parse("")
        
        assert metadata == {}
        assert html == ""
    
    def test_only_front_matter(self):
        """Test content with only front matter."""
        content = """---
title: Only Front Matter
---"""
        metadata, html = self.parser.parse(content)
        
        assert metadata['title'] == 'Only Front Matter'
        assert html.strip() == ""
    
    def test_unicode_content(self):
        """Test handling of Unicode content."""
        content = """---
title: "Ünicöde"
---

# Tëst

Content with émojis: 🎉 and Chinese: 你好
"""
        metadata, html = self.parser.parse(content)
        
        assert metadata['title'] == "Ünicöde"
        assert "Tëst" in html
        assert "🎉" in html
        assert "你好" in html
    
    def test_very_long_content(self):
        """Test handling of very long content."""
        # Create content with 10,000 words
        long_content = "word " * 10000
        content = f"# Long Content\n\n{long_content}"
        
        metadata, html = self.parser.parse(content)
        
        assert metadata['word_count'] == 10000
        assert metadata['reading_time'] == 50  # 10000 / 200 = 50 minutes
    
    def test_nested_markdown_structures(self):
        """Test complex nested markdown structures."""
        content = """# Main Title

## Section with Table

| Column 1 | Column 2 |
|----------|----------|
| **Bold** | *Italic* |
| `Code`   | [Link](http://example.com) |

### Subsection with Math

Inline math $x^2$ and display:

$$
\\sum_{i=1}^{n} i = \\frac{n(n+1)}{2}
$$

With a sidenote^[This is a complex sidenote with **formatting**].
"""
        metadata, html = self.parser.parse(content)
        
        assert '<table>' in html
        assert '<strong>Bold</strong>' in html
        assert 'math inline' in html
        assert 'math display' in html
        assert 'sidenote' in html
