"""
Unit tests for the HTML generator.
"""

import pytest
from pathlib import Path
from datetime import datetime
from unittest.mock import Mock, patch, MagicMock

from gwernpy.generator.html import HTMLGenerator, HTMLGeneratorError
from gwernpy.config import Config


class TestHTMLGenerator:
    """Test HTMLGenerator class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.config = Config()
        self.config.build.content_dir = Path("content")
        self.config.build.output_dir = Path("_site")
        self.config.build.template_dir = Path("templates")
        
    def test_initialization(self, temp_dir):
        """Test HTML generator initialization."""
        self.config.build.output_dir = temp_dir / "_site"
        generator = HTMLGenerator(self.config)
        
        assert generator.config == self.config
        assert generator.posts == []
        assert generator.config.build.output_dir.exists()
    
    def test_setup_jinja_env_with_custom_templates(self, temp_dir):
        """Test Jinja environment setup with custom templates."""
        template_dir = temp_dir / "templates"
        template_dir.mkdir()
        (template_dir / "base.html").write_text("<html>{{ content }}</html>")
        
        self.config.build.template_dir = template_dir
        self.config.build.output_dir = temp_dir / "_site"
        
        generator = HTMLGenerator(self.config)
        
        assert generator.jinja_env is not None
        assert 'dateformat' in generator.jinja_env.filters
        assert 'excerpt' in generator.jinja_env.filters
    
    def test_create_default_templates(self, temp_dir):
        """Test default template creation."""
        template_dir = temp_dir / "templates"
        self.config.build.template_dir = template_dir
        self.config.build.output_dir = temp_dir / "_site"
        
        generator = HTMLGenerator(self.config)
        
        assert (template_dir / "base.html").exists()
        assert (template_dir / "post.html").exists()
        assert (template_dir / "index.html").exists()
        
        base_content = (template_dir / "base.html").read_text()
        assert "<!DOCTYPE html>" in base_content
        assert "Tufte CSS" in base_content
    
    def test_custom_filters(self, temp_dir):
        """Test custom Jinja2 filters."""
        self.config.build.output_dir = temp_dir / "_site"
        generator = HTMLGenerator(self.config)
        
        # Test dateformat filter
        date_filter = generator.jinja_env.filters['dateformat']
        test_date = datetime(2024, 1, 15, 10, 30)
        assert date_filter(test_date, '%Y-%m-%d') == '2024-01-15'
        
        # Test excerpt filter
        excerpt_filter = generator.jinja_env.filters['excerpt']
        long_text = "This is a very long text " * 20
        excerpt = excerpt_filter(long_text, 50)
        assert len(excerpt) <= 53  # 50 + "..."
        assert excerpt.endswith('...')
        
        # Test reading_time filter
        reading_time_filter = generator.jinja_env.filters['reading_time']
        assert reading_time_filter(200) == "1 min read"
        assert reading_time_filter(400) == "2 min read"
        
        # Test url_for filter
        url_filter = generator.jinja_env.filters['url_for']
        self.config.site.url = "https://example.com"
        assert url_filter("/test/path") == "https://example.com/test/path"
    
    def test_generate_page(self, temp_dir):
        """Test HTML page generation."""
        self.config.build.content_dir = temp_dir / "content"
        self.config.build.output_dir = temp_dir / "_site"
        self.config.build.template_dir = temp_dir / "templates"
        
        # Create content directory and file
        content_dir = temp_dir / "content"
        content_dir.mkdir()
        source_file = content_dir / "test.md"
        
        generator = HTMLGenerator(self.config)
        
        metadata = {
            'title': 'Test Post',
            'date': datetime(2024, 1, 15),
            'tags': ['test']
        }
        content = "<h1>Test Content</h1><p>This is test content.</p>"
        
        output_path = generator.generate_page(metadata, content, source_file)
        
        assert output_path.exists()
        assert output_path.suffix == '.html'
        
        html_content = output_path.read_text()
        assert 'Test Post' in html_content
        assert 'Test Content' in html_content
        
        # Check that post was stored
        assert len(generator.posts) == 1
        assert generator.posts[0]['title'] == 'Test Post'
        assert 'url' in generator.posts[0]
    
    def test_generate_page_with_custom_template(self, temp_dir):
        """Test page generation with custom template."""
        self.config.build.content_dir = temp_dir / "content"
        self.config.build.output_dir = temp_dir / "_site"
        self.config.build.template_dir = temp_dir / "templates"
        
        # Create custom template
        template_dir = temp_dir / "templates"
        template_dir.mkdir()
        (template_dir / "custom.html").write_text("""
<!DOCTYPE html>
<html>
<head><title>{{ post.title }}</title></head>
<body>
    <h1>Custom: {{ post.title }}</h1>
    {{ content | safe }}
</body>
</html>
""")
        
        content_dir = temp_dir / "content"
        content_dir.mkdir()
        source_file = content_dir / "test.md"
        
        generator = HTMLGenerator(self.config)
        
        metadata = {
            'title': 'Custom Template Test',
            'template': 'custom.html'
        }
        content = "<p>Custom content</p>"
        
        output_path = generator.generate_page(metadata, content, source_file)
        html_content = output_path.read_text()
        
        assert 'Custom: Custom Template Test' in html_content
        assert 'Custom content' in html_content
    
    def test_generate_page_fallback_template(self, temp_dir):
        """Test page generation falls back to post template."""
        self.config.build.content_dir = temp_dir / "content"
        self.config.build.output_dir = temp_dir / "_site"
        
        content_dir = temp_dir / "content"
        content_dir.mkdir()
        source_file = content_dir / "test.md"
        
        generator = HTMLGenerator(self.config)
        
        metadata = {
            'title': 'Fallback Test',
            'template': 'nonexistent.html'
        }
        content = "<p>Fallback content</p>"
        
        # Should not raise an error, should fall back to post.html
        output_path = generator.generate_page(metadata, content, source_file)
        assert output_path.exists()
    
    def test_minify_html(self, temp_dir):
        """Test HTML minification."""
        self.config.build.output_dir = temp_dir / "_site"
        self.config.build.minify_html = True
        generator = HTMLGenerator(self.config)
        
        html = """
        <html>
            <head>
                <!-- This is a comment -->
                <title>  Test  </title>
            </head>
            <body>
                <h1>   Title   </h1>
                <p>   Content   </p>
            </body>
        </html>
        """
        
        minified = generator._minify_html(html)
        
        assert '<!-- This is a comment -->' not in minified
        assert '   ' not in minified  # Multiple spaces removed
        assert '> <' not in minified  # Spaces between tags removed
    
    def test_generate_sitemap(self, temp_dir):
        """Test sitemap generation."""
        self.config.build.output_dir = temp_dir / "_site"
        self.config.site.url = "https://example.com"
        
        generator = HTMLGenerator(self.config)
        
        # Add some test posts
        generator.posts = [
            {
                'title': 'Post 1',
                'url': '/post1.html',
                'date': datetime(2024, 1, 15),
                'draft': False
            },
            {
                'title': 'Draft Post',
                'url': '/draft.html',
                'date': datetime(2024, 1, 16),
                'draft': True
            },
            {
                'title': 'Post 2',
                'url': '/post2.html',
                'date': datetime(2024, 1, 17),
                'draft': False
            }
        ]
        
        sitemap_path = generator.generate_sitemap()
        
        assert sitemap_path.exists()
        sitemap_content = sitemap_path.read_text()
        
        assert '<?xml version="1.0"' in sitemap_content
        assert 'https://example.com/post1.html' in sitemap_content
        assert 'https://example.com/post2.html' in sitemap_content
        assert 'draft.html' not in sitemap_content  # Drafts excluded
        assert '2024-01-15' in sitemap_content
    
    def test_generate_rss(self, temp_dir):
        """Test RSS feed generation."""
        self.config.build.output_dir = temp_dir / "_site"
        self.config.site.url = "https://example.com"
        self.config.site.title = "Test Blog"
        self.config.site.description = "Test Description"
        
        generator = HTMLGenerator(self.config)
        
        # Add test posts
        generator.posts = [
            {
                'title': 'Recent Post',
                'url': '/recent.html',
                'date': datetime(2024, 1, 20),
                'summary': 'Recent post summary',
                'draft': False
            },
            {
                'title': 'Older Post',
                'url': '/older.html',
                'date': datetime(2024, 1, 10),
                'summary': 'Older post summary',
                'draft': False
            }
        ]
        
        rss_path = generator.generate_rss()
        
        assert rss_path.exists()
        rss_content = rss_path.read_text()
        
        assert '<?xml version="1.0"' in rss_content
        assert '<rss version="2.0">' in rss_content
        assert 'Test Blog' in rss_content
        assert 'Test Description' in rss_content
        assert 'Recent Post' in rss_content
        assert 'https://example.com/recent.html' in rss_content
        assert 'Recent post summary' in rss_content
    
    def test_generate_page_error_handling(self, temp_dir):
        """Test error handling in page generation."""
        self.config.build.content_dir = temp_dir / "content"
        self.config.build.output_dir = temp_dir / "_site"
        
        generator = HTMLGenerator(self.config)
        
        # Test with invalid source path
        invalid_source = Path("nonexistent/file.md")
        metadata = {'title': 'Test'}
        content = "<p>Test</p>"
        
        with pytest.raises(HTMLGeneratorError, match="Failed to generate page"):
            generator.generate_page(metadata, content, invalid_source)


class TestHTMLGeneratorTemplates:
    """Test HTML generator template functionality."""
    
    def test_default_base_template_structure(self, temp_dir):
        """Test default base template has correct structure."""
        config = Config()
        config.build.template_dir = temp_dir / "templates"
        config.build.output_dir = temp_dir / "_site"
        
        generator = HTMLGenerator(config)
        
        base_template = (temp_dir / "templates" / "base.html").read_text()
        
        assert '<!DOCTYPE html>' in base_template
        assert '<html lang="{{ site.language }}">' in base_template
        assert '<meta charset="utf-8">' in base_template
        assert '<meta name="viewport"' in base_template
        assert 'tufte.css' in base_template
        assert '{% block content %}{% endblock %}' in base_template
        assert 'Powered by' in base_template
    
    def test_default_post_template_structure(self, temp_dir):
        """Test default post template has correct structure."""
        config = Config()
        config.build.template_dir = temp_dir / "templates"
        config.build.output_dir = temp_dir / "_site"
        
        generator = HTMLGenerator(config)
        
        post_template = (temp_dir / "templates" / "post.html").read_text()
        
        assert '{% extends "base.html" %}' in post_template
        assert '{{ post.title }}' in post_template
        assert '{{ post.date | dateformat' in post_template
        assert '{{ content | safe }}' in post_template
        assert 'post.tags' in post_template
    
    def test_default_index_template_structure(self, temp_dir):
        """Test default index template has correct structure."""
        config = Config()
        config.build.template_dir = temp_dir / "templates"
        config.build.output_dir = temp_dir / "_site"
        
        generator = HTMLGenerator(config)
        
        index_template = (temp_dir / "templates" / "index.html").read_text()
        
        assert '{% extends "base.html" %}' in index_template
        assert 'Recent Posts' in index_template
        assert '{% for post in posts %}' in index_template
        assert '{{ post.title }}' in index_template
        assert '{{ post.url }}' in index_template


class TestHTMLGeneratorEdgeCases:
    """Test edge cases and error conditions."""
    
    def test_empty_posts_list(self, temp_dir):
        """Test handling of empty posts list."""
        config = Config()
        config.build.output_dir = temp_dir / "_site"
        config.site.url = "https://example.com"
        
        generator = HTMLGenerator(config)
        
        # Test sitemap with no posts
        sitemap_path = generator.generate_sitemap()
        sitemap_content = sitemap_path.read_text()
        assert '<urlset' in sitemap_content
        assert '</urlset>' in sitemap_content
        
        # Test RSS with no posts
        rss_path = generator.generate_rss()
        rss_content = rss_path.read_text()
        assert '<rss version="2.0">' in rss_content
        assert '</rss>' in rss_content
    
    def test_unicode_content_handling(self, temp_dir):
        """Test handling of Unicode content."""
        config = Config()
        config.build.content_dir = temp_dir / "content"
        config.build.output_dir = temp_dir / "_site"
        
        content_dir = temp_dir / "content"
        content_dir.mkdir()
        source_file = content_dir / "unicode.md"
        
        generator = HTMLGenerator(config)
        
        metadata = {
            'title': 'Ünicöde Tëst 🎉',
            'author': 'Tëst Authör'
        }
        content = "<p>Cöntënt with émojis: 🚀 and Chinese: 你好世界</p>"
        
        output_path = generator.generate_page(metadata, content, source_file)
        html_content = output_path.read_text(encoding='utf-8')
        
        assert 'Ünicöde Tëst 🎉' in html_content
        assert '🚀' in html_content
        assert '你好世界' in html_content
    
    def test_very_long_content(self, temp_dir):
        """Test handling of very long content."""
        config = Config()
        config.build.content_dir = temp_dir / "content"
        config.build.output_dir = temp_dir / "_site"
        
        content_dir = temp_dir / "content"
        content_dir.mkdir()
        source_file = content_dir / "long.md"
        
        generator = HTMLGenerator(config)
        
        # Create very long content
        long_content = "<p>" + "word " * 10000 + "</p>"
        
        metadata = {'title': 'Long Content Test'}
        
        output_path = generator.generate_page(metadata, long_content, source_file)
        
        assert output_path.exists()
        # File should be created successfully even with long content
    
    def test_special_characters_in_filenames(self, temp_dir):
        """Test handling of special characters in filenames."""
        config = Config()
        config.build.content_dir = temp_dir / "content"
        config.build.output_dir = temp_dir / "_site"
        
        content_dir = temp_dir / "content"
        content_dir.mkdir()
        source_file = content_dir / "special-chars_file.md"
        
        generator = HTMLGenerator(config)
        
        metadata = {'title': 'Special Characters Test'}
        content = "<p>Test content</p>"
        
        output_path = generator.generate_page(metadata, content, source_file)
        
        assert output_path.exists()
        assert output_path.name == "special-chars_file.html"
