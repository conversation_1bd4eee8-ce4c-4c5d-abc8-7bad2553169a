"""
Unit tests for the configuration system.
"""

import os
import tempfile
import pytest
from pathlib import Path
from unittest.mock import patch

from gwernpy.config import (
    Config, SiteConfig, BuildConfig, MarkdownConfig, 
    OrgRoamConfig, BotConfig, ServerConfig,
    OutputFormat, ThemeType, load_config
)


class TestSiteConfig:
    """Test SiteConfig dataclass."""
    
    def test_default_values(self):
        """Test default configuration values."""
        config = SiteConfig()
        assert config.title == "My Academic Blog"
        assert config.description == "A blog powered by Blawger"
        assert config.url == "https://example.com"
        assert config.author == "Anonymous"
        assert config.language == "en"
        assert config.timezone == "UTC"


class TestBuildConfig:
    """Test BuildConfig dataclass."""
    
    def test_default_values(self):
        """Test default build configuration."""
        config = BuildConfig()
        assert config.content_dir == Path("content")
        assert config.output_dir == Path("_site")
        assert config.theme == ThemeType.TUFTE
        assert config.output_format == OutputFormat.HTML
        assert config.minify_html is True
        assert config.generate_sitemap is True


class TestMarkdownConfig:
    """Test MarkdownConfig dataclass."""
    
    def test_default_extensions(self):
        """Test default markdown extensions."""
        config = MarkdownConfig()
        expected_extensions = ["tables", "footnotes", "toc", "codehilite", "math"]
        assert config.extensions == expected_extensions
        assert config.math_engine == "katex"
        assert config.syntax_highlighting is True


class TestOrgRoamConfig:
    """Test OrgRoamConfig dataclass."""
    
    def test_default_disabled(self):
        """Test org-roam is disabled by default."""
        config = OrgRoamConfig()
        assert config.enabled is False
        assert config.org_directory is None
        assert config.watch_for_changes is True


class TestBotConfig:
    """Test BotConfig dataclass."""
    
    def test_default_values(self):
        """Test default bot configuration."""
        config = BotConfig()
        assert config.allow_indexing is True
        assert config.allow_ai_training is False
        assert config.robots_txt is True
        assert config.rate_limiting is True
        assert config.rate_limit_requests == 100


class TestConfig:
    """Test main Config class."""
    
    def test_default_initialization(self):
        """Test default config initialization."""
        config = Config()
        assert isinstance(config.site, SiteConfig)
        assert isinstance(config.build, BuildConfig)
        assert isinstance(config.markdown, MarkdownConfig)
        assert isinstance(config.orgroam, OrgRoamConfig)
        assert isinstance(config.bot, BotConfig)
        assert isinstance(config.server, ServerConfig)
    
    def test_load_from_yaml_file(self, temp_dir):
        """Test loading configuration from YAML file."""
        config_content = """
site:
  title: "Test Blog"
  author: "Test Author"
  url: "https://test.example.com"

build:
  output_dir: "_output"
  minify_html: false

markdown:
  math_engine: "mathjax"
  extensions: ["tables", "footnotes"]

bot:
  allow_ai_training: true
"""
        config_file = temp_dir / "test_config.yml"
        config_file.write_text(config_content)
        
        config = Config.load(config_file)
        
        assert config.site.title == "Test Blog"
        assert config.site.author == "Test Author"
        assert config.site.url == "https://test.example.com"
        assert config.build.output_dir == Path("_output")
        assert config.build.minify_html is False
        assert config.markdown.math_engine == "mathjax"
        assert config.markdown.extensions == ["tables", "footnotes"]
        assert config.bot.allow_ai_training is True
    
    def test_load_from_toml_file(self, temp_dir):
        """Test loading configuration from TOML file."""
        config_content = """
[site]
title = "TOML Test Blog"
author = "TOML Author"

[build]
output_dir = "_toml_output"
theme = "minimal"

[markdown]
math_engine = "none"
"""
        config_file = temp_dir / "test_config.toml"
        config_file.write_text(config_content)
        
        config = Config.load(config_file)
        
        assert config.site.title == "TOML Test Blog"
        assert config.site.author == "TOML Author"
        assert config.build.output_dir == Path("_toml_output")
        assert config.build.theme == ThemeType.MINIMAL
        assert config.markdown.math_engine == "none"
    
    def test_load_from_environment_variables(self):
        """Test loading configuration from environment variables."""
        env_vars = {
            "BLAWGER_SITE_TITLE": "Env Test Blog",
            "BLAWGER_SITE_URL": "https://env.example.com",
            "BLAWGER_OUTPUT_DIR": "/tmp/env_output",
            "BLAWGER_ALLOW_AI_TRAINING": "true",
            "BLAWGER_SERVER_PORT": "9000"
        }
        
        with patch.dict(os.environ, env_vars):
            config = Config.load()
            
            assert config.site.title == "Env Test Blog"
            assert config.site.url == "https://env.example.com"
            assert config.build.output_dir == Path("/tmp/env_output")
            assert config.bot.allow_ai_training is True
            assert config.server.port == 9000
    
    def test_validation_invalid_url(self):
        """Test validation fails for invalid URL."""
        config = Config()
        config.site.url = "invalid-url"
        
        with pytest.raises(ValueError, match="Site URL must start with http"):
            config._validate()
    
    def test_validation_invalid_port(self):
        """Test validation fails for invalid port."""
        config = Config()
        config.server.port = 70000
        
        with pytest.raises(ValueError, match="Server port must be between"):
            config._validate()
    
    def test_validation_missing_content_dir(self, temp_dir):
        """Test validation fails for missing content directory."""
        config = Config()
        config.build.content_dir = temp_dir / "nonexistent"
        
        with pytest.raises(ValueError, match="Content directory does not exist"):
            config._validate()
    
    def test_validation_orgroam_enabled_missing_dir(self):
        """Test validation fails for org-roam enabled but missing directory."""
        config = Config()
        config.orgroam.enabled = True
        config.orgroam.org_directory = None
        
        with pytest.raises(ValueError, match="Org-roam directory must be specified"):
            config._validate()
    
    def test_save_yaml_format(self, temp_dir):
        """Test saving configuration to YAML format."""
        config = Config()
        config.site.title = "Save Test"
        config.site.author = "Save Author"
        
        config_file = temp_dir / "saved_config.yml"
        config.save(config_file, "yaml")
        
        assert config_file.exists()
        content = config_file.read_text()
        assert "Save Test" in content
        assert "Save Author" in content
        assert "site:" in content
    
    def test_save_toml_format(self, temp_dir):
        """Test saving configuration to TOML format."""
        config = Config()
        config.site.title = "TOML Save Test"
        
        config_file = temp_dir / "saved_config.toml"
        config.save(config_file, "toml")
        
        assert config_file.exists()
        content = config_file.read_text()
        assert "TOML Save Test" in content
        assert "[site]" in content
    
    def test_auto_find_config_file(self, temp_dir):
        """Test automatic config file discovery."""
        # Create a config file with a standard name
        config_content = """
site:
  title: "Auto Found Config"
"""
        config_file = temp_dir / "blawger.yml"
        config_file.write_text(config_content)
        
        # Change to temp directory
        original_cwd = os.getcwd()
        try:
            os.chdir(temp_dir)
            config = Config.load()
            assert config.site.title == "Auto Found Config"
        finally:
            os.chdir(original_cwd)
    
    def test_load_config_convenience_function(self, temp_dir):
        """Test the load_config convenience function."""
        config_content = """
site:
  title: "Convenience Test"
"""
        config_file = temp_dir / "convenience.yml"
        config_file.write_text(config_content)
        
        config = load_config(config_file)
        assert isinstance(config, Config)
        assert config.site.title == "Convenience Test"


class TestConfigErrorHandling:
    """Test configuration error handling."""
    
    def test_invalid_yaml_file(self, temp_dir):
        """Test handling of invalid YAML file."""
        config_file = temp_dir / "invalid.yml"
        config_file.write_text("invalid: yaml: content: [")
        
        with pytest.raises(ValueError, match="Failed to load config"):
            Config.load(config_file)
    
    def test_invalid_toml_file(self, temp_dir):
        """Test handling of invalid TOML file."""
        config_file = temp_dir / "invalid.toml"
        config_file.write_text("[invalid toml content")
        
        with pytest.raises(ValueError, match="Failed to load config"):
            Config.load(config_file)
    
    def test_unsupported_file_format(self, temp_dir):
        """Test handling of unsupported file format."""
        config_file = temp_dir / "config.json"
        config_file.write_text('{"site": {"title": "JSON"}}')
        
        with pytest.raises(ValueError, match="Unsupported config file format"):
            Config.load(config_file)
    
    def test_unsupported_save_format(self, temp_dir):
        """Test handling of unsupported save format."""
        config = Config()
        config_file = temp_dir / "config.xml"
        
        with pytest.raises(ValueError, match="Unsupported format"):
            config.save(config_file, "xml")
