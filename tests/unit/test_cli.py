"""
Unit tests for the CLI interface.
"""

import pytest
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from gwernpy.cli import app
from gwernpy.config import Config


class TestCLIInit:
    """Test CLI init command."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.runner = CliRunner()
    
    def test_init_basic(self, temp_dir):
        """Test basic site initialization."""
        result = self.runner.invoke(app, ['init', str(temp_dir)])
        
        assert result.exit_code == 0
        assert "Blawger site initialized successfully" in result.stdout
        
        # Check directory structure
        assert (temp_dir / "content" / "posts").exists()
        assert (temp_dir / "templates").exists()
        assert (temp_dir / "static" / "css").exists()
        assert (temp_dir / "static" / "js").exists()
        assert (temp_dir / "static" / "images").exists()
        
        # Check configuration file
        assert (temp_dir / "blawger.yml").exists()
        
        # Check sample content
        assert (temp_dir / "content" / "posts" / "welcome.md").exists()
        welcome_content = (temp_dir / "content" / "posts" / "welcome.md").read_text()
        assert "Welcome to Blawger" in welcome_content
        assert "title: Welcome to Blawger" in welcome_content
    
    def test_init_with_template(self, temp_dir):
        """Test initialization with specific template."""
        result = self.runner.invoke(app, ['init', str(temp_dir), '--template', 'academic'])
        
        assert result.exit_code == 0
        assert "Blawger site initialized successfully" in result.stdout
    
    def test_init_non_empty_directory_without_force(self, temp_dir):
        """Test initialization in non-empty directory without force."""
        # Create a file in the directory
        (temp_dir / "existing_file.txt").write_text("existing content")
        
        result = self.runner.invoke(app, ['init', str(temp_dir)])
        
        assert result.exit_code == 1
        assert "Directory" in result.stdout
        assert "is not empty" in result.stdout
    
    def test_init_non_empty_directory_with_force(self, temp_dir):
        """Test initialization in non-empty directory with force."""
        # Create a file in the directory
        (temp_dir / "existing_file.txt").write_text("existing content")
        
        result = self.runner.invoke(app, ['init', str(temp_dir), '--force'])
        
        assert result.exit_code == 0
        assert "Blawger site initialized successfully" in result.stdout
    
    def test_init_current_directory(self, temp_dir, monkeypatch):
        """Test initialization in current directory."""
        monkeypatch.chdir(temp_dir)
        
        result = self.runner.invoke(app, ['init'])
        
        assert result.exit_code == 0
        assert (temp_dir / "blawger.yml").exists()


class TestCLIBuild:
    """Test CLI build command."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.runner = CliRunner()
    
    @patch('gwernpy.cli.load_config')
    @patch('gwernpy.cli.MarkdownParser')
    @patch('gwernpy.cli.HTMLGenerator')
    @patch('gwernpy.cli.BotController')
    def test_build_basic(self, mock_bot, mock_html, mock_parser, mock_config, temp_dir):
        """Test basic build command."""
        # Setup mocks
        config = Mock()
        config.build.content_dir = temp_dir / "content"
        config.build.output_dir = temp_dir / "_site"
        config.markdown.extensions = []
        config.markdown.__dict__ = {}
        config.bot = Mock()
        config.build.generate_sitemap = True
        config.build.generate_rss = True
        mock_config.return_value = config
        
        # Create content directory and file
        content_dir = temp_dir / "content"
        content_dir.mkdir()
        (content_dir / "test.md").write_text("# Test\n\nContent")
        
        mock_parser_instance = Mock()
        mock_parser_instance.parse.return_value = ({'title': 'Test'}, '<h1>Test</h1>')
        mock_parser.return_value = mock_parser_instance
        
        mock_html_instance = Mock()
        mock_html_instance.generate_page.return_value = temp_dir / "_site" / "test.html"
        mock_html.return_value = mock_html_instance
        
        result = self.runner.invoke(app, ['build'])
        
        assert result.exit_code == 0
        assert "Site built successfully" in result.stdout
        
        # Verify mocks were called
        mock_config.assert_called_once()
        mock_parser.assert_called_once()
        mock_html.assert_called_once()
    
    @patch('gwernpy.cli.load_config')
    def test_build_with_config_file(self, mock_config, temp_dir):
        """Test build command with custom config file."""
        config_file = temp_dir / "custom.yml"
        config_file.write_text("site:\n  title: Custom")
        
        # Mock config to avoid actual file operations
        mock_config.return_value = Mock()
        mock_config.return_value.build.content_dir = temp_dir / "content"
        mock_config.return_value.markdown.extensions = []
        mock_config.return_value.markdown.__dict__ = {}
        
        # Create empty content dir to avoid "no files" message
        (temp_dir / "content").mkdir()
        
        result = self.runner.invoke(app, ['build', '--config', str(config_file)])
        
        mock_config.assert_called_with(config_file)
    
    @patch('gwernpy.cli.load_config')
    def test_build_verbose(self, mock_config, temp_dir):
        """Test build command with verbose output."""
        mock_config.return_value = Mock()
        mock_config.return_value.build.content_dir = temp_dir / "content"
        mock_config.return_value.markdown.extensions = []
        mock_config.return_value.markdown.__dict__ = {}
        
        (temp_dir / "content").mkdir()
        
        result = self.runner.invoke(app, ['build', '--verbose'])
        
        assert result.exit_code == 0
        # Verbose mode should show additional output
    
    @patch('gwernpy.cli.load_config')
    def test_build_no_content_files(self, mock_config, temp_dir):
        """Test build command with no content files."""
        mock_config.return_value = Mock()
        mock_config.return_value.build.content_dir = temp_dir / "content"
        mock_config.return_value.markdown.extensions = []
        mock_config.return_value.markdown.__dict__ = {}
        
        (temp_dir / "content").mkdir()  # Empty content directory
        
        result = self.runner.invoke(app, ['build'])
        
        assert result.exit_code == 0
        assert "No markdown files found" in result.stdout


class TestCLIServe:
    """Test CLI serve command."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.runner = CliRunner()
    
    @patch('gwernpy.cli.load_config')
    @patch('gwernpy.cli.uvicorn.run')
    def test_serve_basic(self, mock_uvicorn, mock_config, temp_dir):
        """Test basic serve command."""
        config = Mock()
        config.build.output_dir = temp_dir / "_site"
        config.server.host = "localhost"
        config.server.port = 8000
        config.server.auto_reload = True
        config.server.open_browser = False
        config.build.content_dir = temp_dir / "content"
        mock_config.return_value = config
        
        # Create output directory
        (temp_dir / "_site").mkdir()
        
        result = self.runner.invoke(app, ['serve', '--no-open'])
        
        # Should start server (mocked)
        mock_uvicorn.assert_called_once()
    
    @patch('gwernpy.cli.load_config')
    def test_serve_with_custom_port(self, mock_config, temp_dir):
        """Test serve command with custom port."""
        config = Mock()
        config.build.output_dir = temp_dir / "_site"
        config.server.host = "localhost"
        config.server.port = 8000  # Default
        config.server.auto_reload = True
        config.server.open_browser = False
        mock_config.return_value = config
        
        (temp_dir / "_site").mkdir()
        
        with patch('gwernpy.cli.uvicorn.run') as mock_uvicorn:
            result = self.runner.invoke(app, ['serve', '--port', '9000', '--no-open'])
            
            # Port should be overridden
            assert config.server.port == 9000
    
    @patch('gwernpy.cli.load_config')
    def test_serve_with_custom_host(self, mock_config, temp_dir):
        """Test serve command with custom host."""
        config = Mock()
        config.build.output_dir = temp_dir / "_site"
        config.server.host = "localhost"  # Default
        config.server.port = 8000
        config.server.auto_reload = True
        config.server.open_browser = False
        mock_config.return_value = config
        
        (temp_dir / "_site").mkdir()
        
        with patch('gwernpy.cli.uvicorn.run') as mock_uvicorn:
            result = self.runner.invoke(app, ['serve', '--host', '0.0.0.0', '--no-open'])
            
            # Host should be overridden
            assert config.server.host == '0.0.0.0'


class TestCLICheck:
    """Test CLI check command."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.runner = CliRunner()
    
    @patch('gwernpy.cli.load_config')
    def test_check_valid_site(self, mock_config, temp_dir):
        """Test check command with valid site."""
        config = Mock()
        config.build.content_dir = temp_dir / "content"
        config.build.template_dir = temp_dir / "templates"
        config._validate = Mock()  # No exception = valid
        mock_config.return_value = config
        
        # Create directories and files
        (temp_dir / "content").mkdir()
        (temp_dir / "templates").mkdir()
        (temp_dir / "content" / "test.md").write_text("# Test")
        
        result = self.runner.invoke(app, ['check'])
        
        assert result.exit_code == 0
        assert "Configuration is valid" in result.stdout
        assert "No issues found" in result.stdout
    
    @patch('gwernpy.cli.load_config')
    def test_check_invalid_config(self, mock_config, temp_dir):
        """Test check command with invalid configuration."""
        config = Mock()
        config.build.content_dir = temp_dir / "content"
        config.build.template_dir = temp_dir / "templates"
        config._validate.side_effect = ValueError("Invalid URL")
        mock_config.return_value = config
        
        (temp_dir / "content").mkdir()
        
        result = self.runner.invoke(app, ['check'])
        
        assert result.exit_code == 1
        assert "Issues found" in result.stdout
        assert "Invalid URL" in result.stdout
    
    @patch('gwernpy.cli.load_config')
    def test_check_missing_content_dir(self, mock_config, temp_dir):
        """Test check command with missing content directory."""
        config = Mock()
        config.build.content_dir = temp_dir / "nonexistent"
        config.build.template_dir = temp_dir / "templates"
        config._validate = Mock()
        mock_config.return_value = config
        
        result = self.runner.invoke(app, ['check'])
        
        assert result.exit_code == 1
        assert "Issues found" in result.stdout
    
    @patch('gwernpy.cli.load_config')
    def test_check_with_warnings(self, mock_config, temp_dir):
        """Test check command with warnings."""
        config = Mock()
        config.build.content_dir = temp_dir / "content"
        config.build.template_dir = temp_dir / "nonexistent"
        config._validate = Mock()
        mock_config.return_value = config
        
        (temp_dir / "content").mkdir()  # No markdown files
        
        result = self.runner.invoke(app, ['check'])
        
        assert result.exit_code == 0  # Warnings don't cause failure
        assert "Warnings" in result.stdout


class TestCLIVersion:
    """Test CLI version command."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.runner = CliRunner()
    
    def test_version_command(self):
        """Test version command output."""
        result = self.runner.invoke(app, ['version'])
        
        assert result.exit_code == 0
        assert "Blawger" in result.stdout
        assert "Version Information" in result.stdout
        assert "Python" in result.stdout


class TestCLIErrorHandling:
    """Test CLI error handling."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.runner = CliRunner()
    
    @patch('gwernpy.cli.load_config')
    def test_build_config_error(self, mock_config):
        """Test build command with configuration error."""
        mock_config.side_effect = ValueError("Config error")
        
        result = self.runner.invoke(app, ['build'])
        
        assert result.exit_code == 1
        assert "Build failed" in result.stdout
    
    @patch('gwernpy.cli.load_config')
    def test_serve_config_error(self, mock_config):
        """Test serve command with configuration error."""
        mock_config.side_effect = ValueError("Config error")
        
        result = self.runner.invoke(app, ['serve', '--no-open'])
        
        assert result.exit_code == 1
        assert "Server error" in result.stdout
    
    @patch('gwernpy.cli.load_config')
    def test_check_config_error(self, mock_config):
        """Test check command with configuration error."""
        mock_config.side_effect = ValueError("Config error")
        
        result = self.runner.invoke(app, ['check'])
        
        assert result.exit_code == 1
        assert "Check failed" in result.stdout
    
    def test_init_permission_error(self, temp_dir):
        """Test init command with permission error."""
        # Create a read-only directory (simulate permission error)
        with patch('pathlib.Path.mkdir') as mock_mkdir:
            mock_mkdir.side_effect = PermissionError("Permission denied")
            
            result = self.runner.invoke(app, ['init', str(temp_dir)])
            
            assert result.exit_code == 1
            assert "Error initializing site" in result.stdout


class TestCLIIntegration:
    """Test CLI integration scenarios."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.runner = CliRunner()
    
    def test_full_workflow(self, temp_dir):
        """Test complete workflow: init -> build -> check."""
        # Step 1: Initialize site
        result = self.runner.invoke(app, ['init', str(temp_dir)])
        assert result.exit_code == 0
        
        # Step 2: Build site (mocked to avoid complex dependencies)
        with patch('gwernpy.cli.load_config') as mock_config:
            config = Mock()
            config.build.content_dir = temp_dir / "content"
            config.build.output_dir = temp_dir / "_site"
            config.markdown.extensions = []
            config.markdown.__dict__ = {}
            config.bot = Mock()
            config.build.generate_sitemap = True
            config.build.generate_rss = True
            mock_config.return_value = config
            
            with patch('gwernpy.cli.MarkdownParser'), \
                 patch('gwernpy.cli.HTMLGenerator'), \
                 patch('gwernpy.cli.BotController'):
                
                result = self.runner.invoke(app, ['build'], cwd=str(temp_dir))
                assert result.exit_code == 0
        
        # Step 3: Check site
        with patch('gwernpy.cli.load_config') as mock_config:
            config = Mock()
            config.build.content_dir = temp_dir / "content"
            config.build.template_dir = temp_dir / "templates"
            config._validate = Mock()
            mock_config.return_value = config
            
            result = self.runner.invoke(app, ['check'], cwd=str(temp_dir))
            assert result.exit_code == 0
