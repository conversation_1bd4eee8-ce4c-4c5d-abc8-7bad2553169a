# Blawger Documentation

Welcome to the **Blawger** documentation! Blawger is a modern Python-based static site generator designed specifically for academic writing, research blogging, and knowledge management.

## 🚀 Quick Start

New to Blawger? Start here:

1. **[Getting Started](user-guide/getting-started.md)** - Install Blawger and create your first site
2. **[Configuration](user-guide/configuration.md)** - Configure your site settings
3. **[Writing Content](user-guide/writing-content.md)** - Learn the markdown syntax and features

## 📚 Documentation Sections

### 👤 User Guide
Perfect for content creators, researchers, and bloggers who want to use Blawger to publish their work.

- **[Getting Started](user-guide/getting-started.md)** - Installation and first site
- **[Configuration Reference](user-guide/configuration.md)** - Complete configuration options
- **[Writing Content](user-guide/writing-content.md)** - Markdown syntax and academic features
- **[Org-roam Integration](user-guide/org-roam-integration.md)** - Connect with your knowledge management
- **[Theming Guide](user-guide/theming.md)** - Customize the appearance
- **[Deployment Guide](user-guide/deployment.md)** - Publish your site
- **[FAQ](user-guide/faq.md)** - Common questions and solutions

### 🛠️ Developer Guide
For developers who want to contribute to Blawger or extend its functionality.

- **[Contributing](developer-guide/contributing.md)** - How to contribute to the project
- **[Architecture Overview](developer-guide/architecture.md)** - System design and components
- **[Testing Guide](developer-guide/testing.md)** - Running and writing tests
- **[Plugin Development](developer-guide/plugins.md)** - Creating custom extensions
- **[Release Process](developer-guide/releases.md)** - How releases are made

### 📖 API Reference
Detailed documentation for all public APIs and modules.

- **[API Overview](api-reference/index.md)** - Complete API reference
- **[Configuration System](api-reference/config.md)** - Config classes and methods
- **[Markdown Parser](api-reference/parser/markdown.md)** - Markdown processing API
- **[HTML Generator](api-reference/generator/html.md)** - Site generation API
- **[CLI Interface](api-reference/cli.md)** - Command-line interface
- **[Utilities](api-reference/utils/)** - Helper functions and classes

### 💡 Examples
Real-world examples and templates to get you started quickly.

- **[Academic Blog](examples/academic-blog/)** - Research blog with math and citations
- **[Personal Blog](examples/personal-blog/)** - Simple personal blog setup
- **[Research Group](examples/research-group/)** - Multi-author research website
- **[Course Website](examples/course-website/)** - Educational content site
- **[Documentation Site](examples/documentation/)** - Technical documentation

### 🎓 Tutorials
Step-by-step tutorials for specific use cases and advanced features.

- **[Academic Writing Tutorial](tutorials/academic-writing.md)** - Mathematical expressions and citations
- **[Knowledge Management](tutorials/knowledge-management.md)** - Using org-roam integration
- **[Custom Themes](tutorials/custom-themes.md)** - Creating your own theme
- **[Performance Optimization](tutorials/performance.md)** - Optimizing large sites
- **[SEO Best Practices](tutorials/seo.md)** - Search engine optimization

## ✨ Key Features

### 🎨 Beautiful Typography
- **Tufte CSS Integration** - Edward Tufte-inspired design for academic writing
- **Responsive Design** - Looks great on all devices
- **Mathematical Expressions** - KaTeX/MathJax support for complex equations
- **Sidenotes & Margin Notes** - Elegant annotation system

### 📝 Advanced Markdown
- **Academic Extensions** - Citations, footnotes, tables, and more
- **Custom Syntax** - Sidenotes (`^[note]`), margin notes (`{-note-}`)
- **Math Support** - Inline (`$E=mc^2$`) and display math (`$$...$$`)
- **Code Highlighting** - Syntax highlighting for 100+ languages

### 🧠 Knowledge Management
- **Org-roam Integration** - Connect with your Emacs org-roam workflow
- **Backlink Generation** - Automatic cross-referencing
- **Tag Management** - Organize content with flexible tagging
- **Search & Discovery** - Find connections in your knowledge base

### 🤖 AI Bot Protection
- **Training Bot Blocking** - Protect your content from AI training
- **Custom User Agents** - Fine-grained bot control
- **Security Headers** - Modern web security practices
- **robots.txt Generation** - Automated crawler management

### ⚡ Developer Experience
- **Modern Python** - Type hints, dataclasses, async support
- **Rich CLI** - Beautiful command-line interface with progress bars
- **Live Reload** - Development server with automatic rebuilding
- **Comprehensive Testing** - 90% test coverage with benchmarks

## 🎯 Use Cases

### Academic Researchers
- **Research Blogs** - Share your latest findings and thoughts
- **Course Websites** - Create beautiful course materials
- **Lab Websites** - Showcase your research group's work
- **Conference Proceedings** - Publish academic papers online

### Knowledge Workers
- **Personal Knowledge Base** - Organize your thoughts and research
- **Technical Documentation** - Create professional documentation
- **Learning Notes** - Document your learning journey
- **Project Portfolios** - Showcase your work and projects

### Content Creators
- **Technical Blogs** - Write about programming and technology
- **Educational Content** - Create tutorials and guides
- **Professional Websites** - Build your online presence
- **Community Resources** - Share knowledge with others

## 🚀 Getting Started

Ready to start? Here's the quickest way to get up and running:

```bash
# Install Blawger
git clone https://github.com/forkrul/blawger.git
cd blawger
pip install -e ".[dev]"

# Create your first site
blawger init my-blog
cd my-blog

# Build and serve
blawger build
blawger serve
```

Your site will be available at `http://localhost:8000` with live reload!

## 🤝 Community

- **[GitHub Repository](https://github.com/forkrul/blawger)** - Source code and issues
- **[Discussions](https://github.com/forkrul/blawger/discussions)** - Ask questions and share ideas
- **[Contributing Guide](developer-guide/contributing.md)** - How to contribute
- **[Code of Conduct](https://github.com/forkrul/blawger/blob/master/CODE_OF_CONDUCT.md)** - Community guidelines

## 📄 License

Blawger is released under the [MIT License](https://github.com/forkrul/blawger/blob/master/LICENSE). You're free to use it for any purpose, including commercial projects.

## 🙏 Acknowledgments

Blawger is inspired by:
- **[gwern.net](https://gwern.net)** - The original inspiration for academic web publishing
- **[Tufte CSS](https://edwardtufte.github.io/tufte-css/)** - Beautiful typography for the web
- **[Org-roam](https://www.orgroam.com/)** - Knowledge management with Emacs

---

**Need help?** Check the [FAQ](user-guide/faq.md) or [create an issue](https://github.com/forkrul/blawger/issues) on GitHub.

**Want to contribute?** See the [Contributing Guide](developer-guide/contributing.md) to get started.
