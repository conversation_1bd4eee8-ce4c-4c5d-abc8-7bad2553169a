"""
Setup script for gwern-python.
"""
from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = fh.read().splitlines()

setup(
    name="gwernpy",
    version="0.1.0",
    author="Your Name",
    author_email="<EMAIL>",
    description="A Python-based reimplementation of the gwern.net website infrastructure",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/gwern-python",
    packages=find_packages(),
    classifiers=[
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
    ],
    python_requires=">=3.9",
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "gwernpy=gwernpy.cli:main",
        ],
    },
)
