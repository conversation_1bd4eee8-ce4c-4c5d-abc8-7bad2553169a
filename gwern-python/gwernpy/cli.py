"""
Command-line interface for gwern-python.

This module provides a command-line interface for the gwern-python package.
"""
from typing import Optional
import typer
from pathlib import Path
import os

app = typer.Typer(help="Gwern Python - Static site generator")


@app.command()
def build(
    source_dir: Path = typer.Argument(
        ..., help="Directory containing source files", exists=True, dir_okay=True
    ),
    output_dir: Path = typer.Argument(
        ..., help="Directory to output generated site", dir_okay=True
    ),
    template_dir: Optional[Path] = typer.Option(
        None, "--templates", "-t", help="Directory containing templates"
    ),
    org_roam_dir: Optional[Path] = typer.Option(
        None, "--org-roam", "-o", help="Directory containing org-roam files"
    ),
    allow_bots: bool = typer.Option(
        True, "--allow-bots/--disallow-bots", help="Allow or disallow AI bot scraping"
    ),
):
    """
    Build the static site.
    """
    typer.echo(f"Building site from {source_dir} to {output_dir}")
    # TODO: Implement site building
    typer.echo("Site built successfully")


@app.command()
def serve(
    directory: Path = typer.Argument(
        ..., help="Directory to serve", exists=True, dir_okay=True
    ),
    port: int = typer.Option(8000, "--port", "-p", help="Port to serve on"),
):
    """
    Serve the static site locally.
    """
    typer.echo(f"Serving site from {directory} on port {port}")
    # TODO: Implement local server
    typer.echo("Press Ctrl+C to stop")


def main():
    """Main entry point for the CLI."""
    app()


if __name__ == "__main__":
    main()
