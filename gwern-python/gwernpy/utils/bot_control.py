"""
Bot control utilities for gwern-python.

This module provides utilities for controlling AI bot scraping through
robots.txt and HTTP headers.
"""
from typing import List, Dict, Optional
import os
from pathlib import Path


class BotController:
    """Controller for AI bot scraping."""
    
    def __init__(self, allow_bots: bool = True, bot_exceptions: Optional[List[str]] = None) -> None:
        """
        Initialize the bot controller.
        
        Args:
            allow_bots: Whether to allow bots by default
            bot_exceptions: List of bot user agents to make exceptions for
        """
        self.allow_bots = allow_bots
        self.bot_exceptions = bot_exceptions or []
    
    def generate_robots_txt(self, output_path: str) -> None:
        """
        Generate a robots.txt file.
        
        Args:
            output_path: Path to write the robots.txt file
        """
        content = []
        
        if not self.allow_bots:
            content.append("User-agent: *")
            content.append("Disallow: /")
            
            # Add exceptions
            for bot in self.bot_exceptions:
                content.append(f"\nUser-agent: {bot}")
                content.append("Allow: /")
        else:
            # Allow all bots by default
            content.append("User-agent: *")
            content.append("Allow: /")
            
            # Add exceptions (disallow specific bots)
            for bot in self.bot_exceptions:
                content.append(f"\nUser-agent: {bot}")
                content.append("Disallow: /")
        
        with open(output_path, 'w') as f:
            f.write('\n'.join(content))
    
    def get_http_headers(self) -> Dict[str, str]:
        """
        Get HTTP headers for bot control.
        
        Returns:
            Dictionary of HTTP headers
        """
        headers = {}
        
        if not self.allow_bots:
            headers['X-Robots-Tag'] = 'noindex, nofollow'
        
        return headers
