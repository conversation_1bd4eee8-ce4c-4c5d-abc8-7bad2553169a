# Gwern Python

## Product Requirements Document (PRD)

### Overview
Gwern Python is a Python-based reimplementation of the gwern.net website infrastructure, designed to replace the existing Haskell-based system. This project aims to provide a more maintainable and extensible codebase for someone familiar with Python, while preserving the core functionality and concepts of the original system.

### Background
The current gwern.net website is built using a custom Haskell-based static site generator with numerous specialized tools. This Python reimplementation will provide a 1:1 functional replacement with comprehensive unit tests, making it easier to maintain and extend without requiring Haskell expertise.

### Goals
1. Create a complete Python replacement for the gwern.net infrastructure
2. Implement Tufte CSS for visual styling
3. Support org-roam integration for content management
4. Maintain the same directory structure and organization
5. Provide extensive unit tests for all functionality
6. Include controls for allowing/denying AI bot scraping

### Non-Goals
1. Backward compatibility with existing content (as this is for a new blog)
2. Performance optimizations beyond what's necessary for static content generation

### Technical Requirements

#### Code Quality
- Adhere to PEP 8 (style guide)
- Follow PEP 257 (docstring conventions)
- Implement PEP 484 type hinting throughout the codebase
- Comprehensive unit tests for all functionality

#### Core Functionality (Phase 1)
1. **Static Site Generation**
   - Parse Markdown content
   - Generate HTML with Tufte CSS styling
   - Handle metadata and front matter

2. **Content Organization**
   - Maintain the same directory structure as the original
   - Support hierarchical content organization

3. **Org-Roam Integration**
   - Process tagged files from an org-roam repository
   - Convert org-mode content to the appropriate format

4. **Bot Control**
   - Implement flags for allowing/denying AI bot scraping
   - Generate appropriate robots.txt and HTTP headers

#### Future Phases
1. **Annotation System**
   - Implement GTX file parsing and processing
   - Generate popup annotations for references

2. **Link Management**
   - Archive external links to prevent link rot
   - Generate proper redirects for renamed content



### Technical Architecture

#### Components
1. **Content Parser**
   - Markdown processing
   - Org-mode processing
   - Metadata extraction

2. **Site Generator**
   - Tufte CSS integration
   - Template rendering
   - Output generation

3. **Org-Roam Connector**
   - File monitoring
   - Tag processing
   - Content conversion

4. **Utility Tools**
   - Bot control configuration
   - Development server
   - Deployment helpers

#### Technology Stack
- Python 3.9+
- Markdown processing: markdown-it-py or similar
- HTML templating: Jinja2
- Testing: pytest
- Documentation: Sphinx
- CSS: Tufte CSS



## Getting Started

### Installation
```bash
# Clone the repository
git clone https://github.com/yourusername/gwern-python.git
cd gwern-python

# Create and activate virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### Development
```bash
# Run tests
pytest

# Generate documentation
sphinx-build -b html docs/source docs/build
```

### Usage
Documentation for usage will be added as components are implemented.

## Project Structure
```
gwern-python/
├── gwernpy/                  # Main package
│   ├── __init__.py
│   ├── parser/               # Content parsing modules
│   │   ├── __init__.py
│   │   ├── markdown.py
│   │   └── orgmode.py
│   ├── generator/            # Site generation modules
│   │   ├── __init__.py
│   │   ├── html.py
│   │   └── templates.py
│   ├── orgroam/              # Org-roam integration
│   │   ├── __init__.py
│   │   └── connector.py
│   └── utils/                # Utility functions
│       ├── __init__.py
│       └── bot_control.py
├── tests/                    # Test suite
│   ├── __init__.py
│   ├── test_parser.py
│   └── test_generator.py
├── docs/                     # Documentation
│   └── source/
├── examples/                 # Example content
├── requirements.txt          # Dependencies
├── setup.py                  # Package setup
├── pyproject.toml            # Project configuration
└── README.md                 # Project documentation
```

## License
This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
