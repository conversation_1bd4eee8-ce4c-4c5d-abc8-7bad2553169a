[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "blawger"
version = "0.1.0"
description = "A modern Python reimplementation of gwern.net's infrastructure"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Blawger Contributors", email = "<EMAIL>"}
]
keywords = ["blog", "static-site-generator", "academic", "tufte", "markdown"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Internet :: WWW/HTTP :: Site Management",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Text Processing :: Markup :: Markdown",
    "Topic :: Scientific/Engineering",
]
requires-python = ">=3.9"
dependencies = [
    "markdown-it-py>=2.2.0",
    "mdit-py-plugins>=0.3.3",
    "jinja2>=3.1.2",
    "pyyaml>=6.0",
    "toml>=0.10.2",
    "typer>=0.9.0",
    "rich>=13.4.2",
    "watchdog>=3.0.0",
    "python-dateutil>=2.8.2",
    "uvicorn>=0.22.0",
    "fastapi>=0.100.0",
    "orgparse>=0.3.2",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.11.0",
    "pytest-asyncio>=0.21.0",
    "pytest-xdist>=3.3.0",
    "pytest-benchmark>=4.0.0",
    "black>=23.7.0",
    "isort>=5.12.0",
    "mypy>=1.5.0",
    "flake8>=6.0.0",
    "bandit>=1.7.5",
    "safety>=2.3.0",
    "types-PyYAML>=6.0.0",
    "types-toml>=0.10.0",
    "types-python-dateutil>=2.8.0",
    "factory-boy>=3.3.0",
    "freezegun>=1.2.0",
    "responses>=0.23.0",
    "faker>=19.0.0",
    "coverage[toml]>=7.3.0",
    "pre-commit>=3.3.0",
]

[project.urls]
Homepage = "https://github.com/forkrul/blawger"
Repository = "https://github.com/forkrul/blawger.git"
"Bug Tracker" = "https://github.com/forkrul/blawger/issues"

[project.scripts]
blawger = "gwernpy.cli:main"

[tool.setuptools.packages.find]
include = ["gwernpy*"]
exclude = ["tests*"]

# Black configuration
[tool.black]
line-length = 100
target-version = ['py39', 'py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | vendor
)/
'''

# isort configuration
[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
src_paths = ["gwernpy", "tests"]
skip_glob = ["vendor/*"]

# mypy configuration
[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true
show_column_numbers = true
show_error_context = true
pretty = true

[[tool.mypy.overrides]]
module = [
    "orgparse.*",
    "markdown_it.*",
    "mdit_py_plugins.*",
]
ignore_missing_imports = true

# Coverage configuration
[tool.coverage.run]
source = ["gwernpy"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/vendor/*",
]
branch = true

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
ignore_errors = true
show_missing = true
precision = 2

[tool.coverage.html]
directory = "htmlcov"

[tool.coverage.xml]
output = "coverage.xml"

# Pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--tb=short",
    "--cov=gwernpy",
    "--cov-report=term-missing",
    "--cov-report=html:htmlcov",
    "--cov-report=xml",
    "--cov-fail-under=90",
    "--durations=10",
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "slow: Slow running tests",
    "network: Tests requiring network access",
    "cli: CLI command tests",
    "parser: Parser module tests",
    "generator: Generator module tests",
    "config: Configuration tests",
    "orgroam: Org-roam integration tests",
    "bot: Bot control tests",
]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]
