"""
Command-line interface for Blawger.

This module provides a command-line interface for the Blawger static site generator.
"""

import os
import sys
from typing import Optional
from pathlib import Path

import typer
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.panel import Panel
from rich.table import Table

from .config import Config, load_config
from .parser.markdown import MarkdownParser
from .generator.html import HTMLGenerator
from .utils.bot_control import BotController

# Initialize rich console for beautiful output
console = Console()
app = typer.Typer(
    help="🐍 Blawger - A modern Python reimplementation of gwern.net's infrastructure",
    rich_markup_mode="rich"
)


@app.command()
def init(
    path: Path = typer.Argument(
        Path("."), help="Directory to initialize as a Blawger site"
    ),
    template: str = typer.Option(
        "basic", "--template", "-t", help="Template to use (basic, academic, minimal)"
    ),
    force: bool = typer.Option(
        False, "--force", "-f", help="Overwrite existing files"
    ),
):
    """
    🚀 Initialize a new Blawger site.
    """
    try:
        site_path = Path(path).resolve()

        if site_path.exists() and any(site_path.iterdir()) and not force:
            console.print(f"[red]Directory {site_path} is not empty. Use --force to overwrite.[/red]")
            raise typer.Exit(1)

        console.print(f"[green]Initializing Blawger site in {site_path}[/green]")

        # Create directory structure
        directories = [
            "content/posts",
            "templates",
            "static/css",
            "static/js",
            "static/images"
        ]

        for dir_path in directories:
            (site_path / dir_path).mkdir(parents=True, exist_ok=True)

        # Create sample configuration
        config = Config()
        config.site.title = "My Academic Blog"
        config.site.description = "A blog powered by Blawger"
        config.build.content_dir = Path("content")
        config.build.output_dir = Path("_site")

        config.save(site_path / "blawger.yml")

        # Create sample content
        sample_post = """---
title: Welcome to Blawger
date: 2024-01-01
tags: [welcome, getting-started]
---

# Welcome to Blawger

This is your first post! Blawger supports:

- **Beautiful typography** with Tufte CSS
- Mathematical expressions: $E = mc^2$
- Footnotes^[Like this one]
- And much more!

## Getting Started

Edit this file in `content/posts/welcome.md` and run:

```bash
blawger build
blawger serve
```

Happy writing! 🎉
"""

        (site_path / "content/posts/welcome.md").write_text(sample_post)

        console.print(Panel.fit(
            f"[green]✅ Blawger site initialized successfully![/green]\n\n"
            f"📁 Site created in: {site_path}\n"
            f"📝 Edit content in: content/posts/\n"
            f"⚙️  Configuration: blawger.yml\n\n"
            f"Next steps:\n"
            f"  1. cd {site_path.name if site_path != Path('.') else '.'}\n"
            f"  2. blawger build\n"
            f"  3. blawger serve",
            title="🎉 Success"
        ))

    except Exception as e:
        console.print(f"[red]Error initializing site: {e}[/red]")
        raise typer.Exit(1)


@app.command()
def build(
    config_file: Optional[Path] = typer.Option(
        None, "--config", "-c", help="Configuration file path"
    ),
    watch: bool = typer.Option(
        False, "--watch", "-w", help="Watch for changes and rebuild"
    ),
    verbose: bool = typer.Option(
        False, "--verbose", "-v", help="Verbose output"
    ),
):
    """
    🏗️ Build the static site.
    """
    try:
        # Load configuration
        config = load_config(config_file)

        if verbose:
            console.print(f"[blue]Configuration loaded from: {config_file or 'default locations'}[/blue]")

        # Initialize components
        markdown_parser = MarkdownParser(
            extensions=config.markdown.extensions,
            config=config.markdown.__dict__
        )

        html_generator = HTMLGenerator(config)
        bot_controller = BotController(config.bot)

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:

            # Build site
            task = progress.add_task("Building site...", total=None)

            try:
                # Find all markdown files
                content_files = list(config.build.content_dir.rglob("*.md"))

                if not content_files:
                    console.print(f"[yellow]No markdown files found in {config.build.content_dir}[/yellow]")
                    return

                progress.update(task, description=f"Processing {len(content_files)} files...")

                # Create output directory
                config.build.output_dir.mkdir(parents=True, exist_ok=True)

                # Process each file
                for file_path in content_files:
                    if verbose:
                        console.print(f"[dim]Processing: {file_path}[/dim]")

                    # Read and parse content
                    content = file_path.read_text(encoding='utf-8')
                    metadata, html_content = markdown_parser.parse(content, file_path)

                    # Generate HTML page
                    output_path = html_generator.generate_page(
                        metadata, html_content, file_path
                    )

                    if verbose:
                        console.print(f"[dim]Generated: {output_path}[/dim]")

                # Generate additional files
                progress.update(task, description="Generating additional files...")

                # Generate robots.txt
                if config.bot.robots_txt:
                    bot_controller.generate_robots_txt(config.build.output_dir)

                # Generate sitemap
                if config.build.generate_sitemap:
                    html_generator.generate_sitemap()

                # Generate RSS feed
                if config.build.generate_rss:
                    html_generator.generate_rss()

                progress.update(task, description="✅ Build complete!")

                console.print(Panel.fit(
                    f"[green]✅ Site built successfully![/green]\n\n"
                    f"📁 Output directory: {config.build.output_dir}\n"
                    f"📄 Files processed: {len(content_files)}\n"
                    f"🌐 Ready to serve or deploy!",
                    title="🎉 Build Complete"
                ))

            except Exception as e:
                progress.update(task, description="❌ Build failed!")
                raise e

    except Exception as e:
        console.print(f"[red]Build failed: {e}[/red]")
        if verbose:
            console.print_exception()
        raise typer.Exit(1)


@app.command()
def serve(
    config_file: Optional[Path] = typer.Option(
        None, "--config", "-c", help="Configuration file path"
    ),
    port: Optional[int] = typer.Option(
        None, "--port", "-p", help="Port to serve on"
    ),
    host: Optional[str] = typer.Option(
        None, "--host", "-h", help="Host to bind to"
    ),
    auto_build: bool = typer.Option(
        True, "--auto-build/--no-auto-build", help="Automatically build on file changes"
    ),
    open_browser: bool = typer.Option(
        True, "--open/--no-open", help="Open browser automatically"
    ),
):
    """
    🚀 Serve the static site locally with live reload.
    """
    try:
        # Load configuration
        config = load_config(config_file)

        # Override with command line options
        if port:
            config.server.port = port
        if host:
            config.server.host = host
        if not auto_build:
            config.server.auto_reload = False
        if not open_browser:
            config.server.open_browser = False

        # Check if output directory exists
        if not config.build.output_dir.exists():
            console.print(f"[yellow]Output directory {config.build.output_dir} doesn't exist. Building first...[/yellow]")

            # Build the site first
            from .cli import build as build_command
            # Note: This is a simplified approach - in a real implementation,
            # we'd call the build function directly

        console.print(Panel.fit(
            f"[green]🚀 Starting development server[/green]\n\n"
            f"📁 Serving: {config.build.output_dir}\n"
            f"🌐 URL: http://{config.server.host}:{config.server.port}\n"
            f"🔄 Auto-reload: {'enabled' if config.server.auto_reload else 'disabled'}\n\n"
            f"Press Ctrl+C to stop",
            title="Development Server"
        ))

        # Import and start server
        import uvicorn
        from fastapi import FastAPI
        from fastapi.staticfiles import StaticFiles
        from fastapi.responses import FileResponse

        app_server = FastAPI(title="Blawger Development Server")

        # Mount static files
        app_server.mount("/", StaticFiles(directory=config.build.output_dir, html=True), name="static")

        # Handle index route
        @app_server.get("/")
        async def read_index():
            index_path = config.build.output_dir / "index.html"
            if index_path.exists():
                return FileResponse(index_path)
            else:
                return {"message": "Welcome to Blawger! No index.html found."}

        # Open browser if requested
        if config.server.open_browser:
            import webbrowser
            webbrowser.open(f"http://{config.server.host}:{config.server.port}")

        # Start server
        uvicorn.run(
            app_server,
            host=config.server.host,
            port=config.server.port,
            reload=config.server.auto_reload,
            reload_dirs=[str(config.build.content_dir)] if config.server.auto_reload else None,
        )

    except KeyboardInterrupt:
        console.print("\n[yellow]Server stopped by user[/yellow]")
    except Exception as e:
        console.print(f"[red]Server error: {e}[/red]")
        raise typer.Exit(1)


@app.command()
def check(
    config_file: Optional[Path] = typer.Option(
        None, "--config", "-c", help="Configuration file path"
    ),
    fix: bool = typer.Option(
        False, "--fix", help="Attempt to fix issues automatically"
    ),
):
    """
    🔍 Check site for issues and validate configuration.
    """
    try:
        config = load_config(config_file)

        console.print("[blue]🔍 Checking Blawger site...[/blue]\n")

        issues = []
        warnings = []

        # Check configuration
        try:
            config._validate()
            console.print("[green]✅ Configuration is valid[/green]")
        except Exception as e:
            issues.append(f"Configuration error: {e}")

        # Check content directory
        if not config.build.content_dir.exists():
            issues.append(f"Content directory does not exist: {config.build.content_dir}")
        else:
            content_files = list(config.build.content_dir.rglob("*.md"))
            console.print(f"[green]✅ Found {len(content_files)} markdown files[/green]")

            if len(content_files) == 0:
                warnings.append("No markdown files found in content directory")

        # Check template directory
        if config.build.template_dir.exists():
            console.print(f"[green]✅ Template directory exists[/green]")
        else:
            warnings.append(f"Template directory does not exist: {config.build.template_dir}")

        # Check for broken links (simplified)
        # In a real implementation, this would be more comprehensive

        # Display results
        if issues:
            console.print("\n[red]❌ Issues found:[/red]")
            for issue in issues:
                console.print(f"  • {issue}")

        if warnings:
            console.print("\n[yellow]⚠️  Warnings:[/yellow]")
            for warning in warnings:
                console.print(f"  • {warning}")

        if not issues and not warnings:
            console.print("\n[green]🎉 No issues found! Your site looks good.[/green]")

        if issues:
            raise typer.Exit(1)

    except Exception as e:
        console.print(f"[red]Check failed: {e}[/red]")
        raise typer.Exit(1)


@app.command()
def version():
    """
    📋 Show version information.
    """
    from . import __version__, __author__

    table = Table(title="Blawger Version Information")
    table.add_column("Component", style="cyan")
    table.add_column("Version", style="green")

    table.add_row("Blawger", __version__)
    table.add_row("Python", f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")

    console.print(table)
    console.print(f"\n[dim]Created by {__author__}[/dim]")


def main():
    """Main entry point for the CLI."""
    app()


if __name__ == "__main__":
    main()
