"""
Command-line interface for Blawger.

This module provides a command-line interface for the Blawger static site generator.
"""

import os
import sys
import time
from typing import Optional
from pathlib import Path

import typer
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.panel import Panel
from rich.table import Table

from .config import Config, load_config
from .parser.markdown import MarkdownParser
from .parser.frontmatter import FrontMatterParser
from .generator.html import HTMLGenerator
from .utils.bot_control import BotController
from .utils.performance import PerformanceOptimizer, timing_decorator, memory_monitor

# Initialize rich console for beautiful output
console = Console()
app = typer.Typer(
    help="🐍 Blawger - A modern Python reimplementation of gwern.net's infrastructure",
    rich_markup_mode="rich"
)


@app.command()
def init(
    path: Path = typer.Argument(
        Path("."), help="Directory to initialize as a Blawger site"
    ),
    template: str = typer.Option(
        "basic", "--template", "-t", help="Template to use (basic, academic, minimal)"
    ),
    force: bool = typer.Option(
        False, "--force", "-f", help="Overwrite existing files"
    ),
):
    """
    🚀 Initialize a new Blawger site.
    """
    try:
        site_path = Path(path).resolve()

        if site_path.exists() and any(site_path.iterdir()) and not force:
            console.print(f"[red]Directory {site_path} is not empty. Use --force to overwrite.[/red]")
            raise typer.Exit(1)

        console.print(f"[green]Initializing Blawger site in {site_path}[/green]")

        # Create directory structure
        directories = [
            "content/posts",
            "templates",
            "static/css",
            "static/js",
            "static/images"
        ]

        for dir_path in directories:
            (site_path / dir_path).mkdir(parents=True, exist_ok=True)

        # Create sample configuration
        config = Config()
        config.site.title = "My Academic Blog"
        config.site.description = "A blog powered by Blawger"
        config.build.content_dir = Path("content")
        config.build.output_dir = Path("_site")

        config.save(site_path / "blawger.yml")

        # Create sample content
        sample_post = """---
title: Welcome to Blawger
date: 2024-01-01
tags: [welcome, getting-started]
---

# Welcome to Blawger

This is your first post! Blawger supports:

- **Beautiful typography** with Tufte CSS
- Mathematical expressions: $E = mc^2$
- Footnotes^[Like this one]
- And much more!

## Getting Started

Edit this file in `content/posts/welcome.md` and run:

```bash
blawger build
blawger serve
```

Happy writing! 🎉
"""

        (site_path / "content/posts/welcome.md").write_text(sample_post)

        console.print(Panel.fit(
            f"[green]✅ Blawger site initialized successfully![/green]\n\n"
            f"📁 Site created in: {site_path}\n"
            f"📝 Edit content in: content/posts/\n"
            f"⚙️  Configuration: blawger.yml\n\n"
            f"Next steps:\n"
            f"  1. cd {site_path.name if site_path != Path('.') else '.'}\n"
            f"  2. blawger build\n"
            f"  3. blawger serve",
            title="🎉 Success"
        ))

    except Exception as e:
        console.print(f"[red]Error initializing site: {e}[/red]")
        raise typer.Exit(1)


@app.command()
def build(
    config_file: Optional[Path] = typer.Option(
        None, "--config", "-c", help="Configuration file path"
    ),
    watch: bool = typer.Option(
        False, "--watch", "-w", help="Watch for changes and rebuild"
    ),
    verbose: bool = typer.Option(
        False, "--verbose", "-v", help="Verbose output"
    ),
    optimize: bool = typer.Option(
        True, "--optimize/--no-optimize", help="Enable performance optimizations"
    ),
    parallel: bool = typer.Option(
        True, "--parallel/--no-parallel", help="Enable parallel processing"
    ),
):
    """
    🏗️ Build the static site.
    """
    try:
        # Load configuration
        config = load_config(config_file)

        if verbose:
            console.print(f"[blue]Configuration loaded from: {config_file or 'default locations'}[/blue]")

        # Initialize components
        markdown_parser = MarkdownParser(
            extensions=config.markdown.extensions,
            config=config.markdown.__dict__
        )
        frontmatter_parser = FrontMatterParser()

        html_generator = HTMLGenerator(config)
        bot_controller = BotController(config.bot)

        # Initialize performance optimizer
        performance_optimizer = PerformanceOptimizer(config) if optimize else None

        # Track build start time
        build_start_time = time.time()

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:

            # Build site
            task = progress.add_task("Building site...", total=None)

            try:
                # Find all markdown files
                content_files = list(config.build.content_dir.rglob("*.md"))

                if not content_files:
                    console.print(f"[yellow]No markdown files found in {config.build.content_dir}[/yellow]")
                    return

                progress.update(task, description=f"Processing {len(content_files)} files...")

                # Create output directory
                config.build.output_dir.mkdir(parents=True, exist_ok=True)

                # Process files with optional parallelization
                if parallel and len(content_files) > 4:
                    # Process files in parallel for better performance
                    def process_file(file_path):
                        if performance_optimizer and not performance_optimizer.should_rebuild_file(
                            file_path, config.build.output_dir / file_path.relative_to(config.build.content_dir).with_suffix('.html')
                        ):
                            return None  # Skip if not changed

                        content = file_path.read_text(encoding='utf-8')
                        metadata, body = frontmatter_parser.parse(content)
                        metadata, html_content = markdown_parser.parse(body, file_path, metadata)

                        return html_generator.generate_page(metadata, html_content, file_path, content)

                    # Use performance optimizer for parallel processing
                    if performance_optimizer:
                        results = performance_optimizer.parallel_process_files(content_files, process_file)
                    else:
                        import concurrent.futures
                        with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
                            results = list(executor.map(process_file, content_files))

                    processed_count = sum(1 for r in results if r is not None)

                else:
                    # Process files sequentially
                    processed_count = 0
                    for file_path in content_files:
                        if verbose:
                            console.print(f"[dim]Processing: {file_path}[/dim]")

                        # Check if file needs rebuilding
                        output_path = config.build.output_dir / file_path.relative_to(config.build.content_dir).with_suffix('.html')
                        if performance_optimizer and not performance_optimizer.should_rebuild_file(file_path, output_path):
                            if verbose:
                                console.print(f"[dim]Skipped (cached): {file_path}[/dim]")
                            continue

                        # Read and parse content
                        content = file_path.read_text(encoding='utf-8')
                        metadata, body = frontmatter_parser.parse(content)
                        metadata, html_content = markdown_parser.parse(body, file_path, metadata)

                        # Generate HTML page with advanced features
                        output_path = html_generator.generate_page(
                            metadata, html_content, file_path, content
                        )
                        processed_count += 1

                        if verbose:
                            console.print(f"[dim]Generated: {output_path}[/dim]")

                # Generate additional files
                progress.update(task, description="Generating additional files...")

                # Generate robots.txt
                if config.bot.robots_txt:
                    bot_controller.generate_robots_txt(config.build.output_dir)

                # Generate sitemap
                if config.build.generate_sitemap:
                    html_generator.generate_sitemap()

                # Generate RSS feed
                if config.build.generate_rss:
                    html_generator.generate_rss()

                # Optimize assets if enabled
                if optimize and performance_optimizer:
                    progress.update(task, description="Optimizing assets...")
                    static_dir = config.build.output_dir / "static"
                    if static_dir.exists():
                        performance_optimizer.optimize_assets(static_dir)

                # Finalize performance tracking
                build_time = time.time() - build_start_time
                if performance_optimizer:
                    performance_optimizer.metrics.build_time = build_time
                    performance_optimizer.metrics.total_pages = processed_count
                    performance_optimizer.metrics.memory_usage_mb = memory_monitor()
                    performance_optimizer.finalize()

                progress.update(task, description="✅ Build complete!")

                # Build summary
                summary_text = (
                    f"[green]✅ Site built successfully![/green]\n\n"
                    f"📁 Output directory: {config.build.output_dir}\n"
                    f"📄 Files processed: {processed_count}/{len(content_files)}\n"
                    f"⏱️  Build time: {build_time:.2f}s\n"
                )

                if performance_optimizer:
                    cache_hits = performance_optimizer.metrics.cache_hits
                    cache_misses = performance_optimizer.metrics.cache_misses
                    total_ops = cache_hits + cache_misses
                    cache_rate = (cache_hits / total_ops * 100) if total_ops > 0 else 0
                    summary_text += f"🚀 Cache hit rate: {cache_rate:.1f}%\n"

                summary_text += "🌐 Ready to serve or deploy!"

                console.print(Panel.fit(summary_text, title="🎉 Build Complete"))

            except Exception as e:
                progress.update(task, description="❌ Build failed!")
                raise e

    except Exception as e:
        console.print(f"[red]Build failed: {e}[/red]")
        if verbose:
            console.print_exception()
        raise typer.Exit(1)


@app.command()
def serve(
    config_file: Optional[Path] = typer.Option(
        None, "--config", "-c", help="Configuration file path"
    ),
    port: Optional[int] = typer.Option(
        None, "--port", "-p", help="Port to serve on"
    ),
    host: Optional[str] = typer.Option(
        None, "--host", "-h", help="Host to bind to"
    ),
    auto_build: bool = typer.Option(
        True, "--auto-build/--no-auto-build", help="Automatically build on file changes"
    ),
    open_browser: bool = typer.Option(
        True, "--open/--no-open", help="Open browser automatically"
    ),
):
    """
    🚀 Serve the static site locally with live reload.
    """
    try:
        # Load configuration
        config = load_config(config_file)

        # Override with command line options
        if port:
            config.server.port = port
        if host:
            config.server.host = host
        if not auto_build:
            config.server.auto_reload = False
        if not open_browser:
            config.server.open_browser = False

        # Check if output directory exists
        if not config.build.output_dir.exists():
            console.print(f"[yellow]Output directory {config.build.output_dir} doesn't exist. Building first...[/yellow]")

            # Build the site first
            from .cli import build as build_command
            # Note: This is a simplified approach - in a real implementation,
            # we'd call the build function directly

        console.print(Panel.fit(
            f"[green]🚀 Starting development server[/green]\n\n"
            f"📁 Serving: {config.build.output_dir}\n"
            f"🌐 URL: http://{config.server.host}:{config.server.port}\n"
            f"🔄 Auto-reload: {'enabled' if config.server.auto_reload else 'disabled'}\n\n"
            f"Press Ctrl+C to stop",
            title="Development Server"
        ))

        # Import and start server
        import uvicorn
        from fastapi import FastAPI
        from fastapi.staticfiles import StaticFiles
        from fastapi.responses import FileResponse

        app_server = FastAPI(title="Blawger Development Server")

        # Mount static files
        app_server.mount("/", StaticFiles(directory=config.build.output_dir, html=True), name="static")

        # Handle index route
        @app_server.get("/")
        async def read_index():
            index_path = config.build.output_dir / "index.html"
            if index_path.exists():
                return FileResponse(index_path)
            else:
                return {"message": "Welcome to Blawger! No index.html found."}

        # Open browser if requested
        if config.server.open_browser:
            import webbrowser
            webbrowser.open(f"http://{config.server.host}:{config.server.port}")

        # Start server
        uvicorn.run(
            app_server,
            host=config.server.host,
            port=config.server.port,
            reload=config.server.auto_reload,
            reload_dirs=[str(config.build.content_dir)] if config.server.auto_reload else None,
        )

    except KeyboardInterrupt:
        console.print("\n[yellow]Server stopped by user[/yellow]")
    except Exception as e:
        console.print(f"[red]Server error: {e}[/red]")
        raise typer.Exit(1)


@app.command()
def check(
    config_file: Optional[Path] = typer.Option(
        None, "--config", "-c", help="Configuration file path"
    ),
    fix: bool = typer.Option(
        False, "--fix", help="Attempt to fix issues automatically"
    ),
):
    """
    🔍 Check site for issues and validate configuration.
    """
    try:
        config = load_config(config_file)

        console.print("[blue]🔍 Checking Blawger site...[/blue]\n")

        issues = []
        warnings = []

        # Check configuration
        try:
            config._validate()
            console.print("[green]✅ Configuration is valid[/green]")
        except Exception as e:
            issues.append(f"Configuration error: {e}")

        # Check content directory
        if not config.build.content_dir.exists():
            issues.append(f"Content directory does not exist: {config.build.content_dir}")
        else:
            content_files = list(config.build.content_dir.rglob("*.md"))
            console.print(f"[green]✅ Found {len(content_files)} markdown files[/green]")

            if len(content_files) == 0:
                warnings.append("No markdown files found in content directory")

        # Check template directory
        if config.build.template_dir.exists():
            console.print(f"[green]✅ Template directory exists[/green]")
        else:
            warnings.append(f"Template directory does not exist: {config.build.template_dir}")

        # Check for broken links (simplified)
        # In a real implementation, this would be more comprehensive

        # Display results
        if issues:
            console.print("\n[red]❌ Issues found:[/red]")
            for issue in issues:
                console.print(f"  • {issue}")

        if warnings:
            console.print("\n[yellow]⚠️  Warnings:[/yellow]")
            for warning in warnings:
                console.print(f"  • {warning}")

        if not issues and not warnings:
            console.print("\n[green]🎉 No issues found! Your site looks good.[/green]")

        if issues:
            raise typer.Exit(1)

    except Exception as e:
        console.print(f"[red]Check failed: {e}[/red]")
        raise typer.Exit(1)


@app.command()
def performance(
    config_file: Optional[Path] = typer.Option(
        None, "--config", "-c", help="Configuration file path"
    ),
    report: bool = typer.Option(
        True, "--report/--no-report", help="Generate performance report"
    ),
    optimize: bool = typer.Option(
        False, "--optimize", help="Run performance optimizations"
    ),
    clean_cache: bool = typer.Option(
        False, "--clean-cache", help="Clean performance cache"
    ),
):
    """
    📊 Performance analysis and optimization tools.
    """
    try:
        config = load_config(config_file)
        performance_optimizer = PerformanceOptimizer(config)

        console.print("[blue]📊 Performance Analysis[/blue]\n")

        if clean_cache:
            console.print("🧹 Cleaning performance cache...")
            performance_optimizer.cleanup_cache()
            console.print("[green]✅ Cache cleaned[/green]")

        if optimize:
            console.print("🚀 Running performance optimizations...")

            # Optimize static assets
            static_dir = config.build.output_dir / "static"
            if static_dir.exists():
                performance_optimizer.optimize_assets(static_dir)
                console.print("[green]✅ Assets optimized[/green]")
            else:
                console.print("[yellow]⚠️  No static directory found[/yellow]")

        if report:
            console.print("📋 Generating performance report...")

            # Load existing metrics if available
            cache_file = performance_optimizer.cache_dir / "file_cache.json"
            if cache_file.exists():
                performance_optimizer._load_file_cache()

                # Generate and display report
                report_html = performance_optimizer.generate_performance_report()

                # Save report to file
                report_path = config.build.output_dir / "performance_report.html"
                report_path.write_text(report_html, encoding='utf-8')

                console.print(f"[green]✅ Performance report saved to: {report_path}[/green]")

                # Display summary in terminal
                metrics = performance_optimizer.metrics

                table = Table(title="Performance Summary")
                table.add_column("Metric", style="cyan")
                table.add_column("Value", style="green")

                table.add_row("Build Time", f"{metrics.build_time:.2f}s")
                table.add_row("Total Pages", str(metrics.total_pages))
                table.add_row("Cache Hits", str(metrics.cache_hits))
                table.add_row("Cache Misses", str(metrics.cache_misses))

                if metrics.total_size_bytes > 0:
                    table.add_row("Original Size", f"{metrics.total_size_bytes / 1024:.1f} KB")
                    table.add_row("Compressed Size", f"{metrics.compressed_size_bytes / 1024:.1f} KB")
                    table.add_row("Compression Ratio", f"{metrics.compression_ratio:.1f}%")

                console.print(table)
            else:
                console.print("[yellow]⚠️  No performance data available. Run 'blawger build' first.[/yellow]")

    except Exception as e:
        console.print(f"[red]Performance analysis failed: {e}[/red]")
        raise typer.Exit(1)


@app.command()
def version():
    """
    📋 Show version information.
    """
    from . import __version__, __author__

    table = Table(title="Blawger Version Information")
    table.add_column("Component", style="cyan")
    table.add_column("Version", style="green")

    table.add_row("Blawger", __version__)
    table.add_row("Python", f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")

    console.print(table)
    console.print(f"\n[dim]Created by {__author__}[/dim]")


def main():
    """Main entry point for the CLI."""
    app()


if __name__ == "__main__":
    main()
