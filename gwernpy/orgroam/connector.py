"""
Org-roam connector for Blawger.

This module provides integration with org-roam for knowledge management workflows.
"""

import re
import sqlite3
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

try:
    import orgparse
    ORGPARSE_AVAILABLE = True
except ImportError:
    ORGPARSE_AVAILABLE = False

from ..config import OrgRoamConfig


class OrgRoamError(Exception):
    """Exception raised for org-roam related errors."""
    pass


class OrgRoamConnector:
    """Connector for org-roam integration."""

    def __init__(self, config: OrgRoamConfig):
        """
        Initialize the org-roam connector.

        Args:
            config: Org-roam configuration

        Raises:
            OrgRoamError: If org-roam is enabled but requirements are not met
        """
        self.config = config

        if self.config.enabled:
            if not ORGPARSE_AVAILABLE:
                raise OrgRoamError(
                    "orgparse library is required for org-roam integration. "
                    "Install with: pip install orgparse"
                )

            if not self.config.org_directory or not self.config.org_directory.exists():
                raise OrgRoamError(
                    f"Org-roam directory does not exist: {self.config.org_directory}"
                )

    def get_org_files(self) -> List[Path]:
        """
        Get all org files in the org-roam directory.

        Returns:
            List of org file paths
        """
        if not self.config.enabled:
            return []

        org_files = []

        for pattern in ["*.org"]:
            files = list(self.config.org_directory.rglob(pattern))

            # Filter out excluded patterns
            filtered_files = []
            for file_path in files:
                excluded = False
                for exclude_pattern in self.config.exclude_patterns:
                    if file_path.match(exclude_pattern):
                        excluded = True
                        break

                if not excluded:
                    filtered_files.append(file_path)

            org_files.extend(filtered_files)

        return org_files

    def parse_org_file(self, file_path: Path) -> Tuple[Dict[str, Any], str]:
        """
        Parse an org file and extract metadata and content.

        Args:
            file_path: Path to org file

        Returns:
            Tuple of (metadata dict, markdown content)

        Raises:
            OrgRoamError: If parsing fails
        """
        if not ORGPARSE_AVAILABLE:
            raise OrgRoamError("orgparse library not available")

        try:
            # Parse org file
            org_node = orgparse.load(str(file_path))

            # Extract metadata
            metadata = self._extract_org_metadata(org_node, file_path)

            # Convert to markdown
            markdown_content = self._org_to_markdown(org_node)

            return metadata, markdown_content

        except Exception as e:
            raise OrgRoamError(f"Failed to parse org file {file_path}: {e}")

    def _extract_org_metadata(self, org_node, file_path: Path) -> Dict[str, Any]:
        """
        Extract metadata from org node.

        Args:
            org_node: Parsed org node
            file_path: Source file path

        Returns:
            Metadata dictionary
        """
        metadata = {
            'source_path': str(file_path),
            'source_name': file_path.stem,
            'format': 'org',
        }

        # Extract title
        if hasattr(org_node, 'get_property') and org_node.get_property('TITLE'):
            metadata['title'] = org_node.get_property('TITLE')
        elif org_node.children and org_node.children[0].heading:
            metadata['title'] = org_node.children[0].heading
        else:
            metadata['title'] = file_path.stem.replace('-', ' ').title()

        # Extract date
        if hasattr(org_node, 'get_property') and org_node.get_property('DATE'):
            try:
                date_str = org_node.get_property('DATE')
                # Parse org date format [2024-01-01 Mon]
                date_match = re.search(r'\[(\d{4}-\d{2}-\d{2})', date_str)
                if date_match:
                    metadata['date'] = datetime.strptime(date_match.group(1), '%Y-%m-%d')
            except:
                pass

        if 'date' not in metadata:
            # Use file modification time as fallback
            metadata['date'] = datetime.fromtimestamp(file_path.stat().st_mtime)

        # Extract tags
        tags = []
        if hasattr(org_node, 'get_property') and org_node.get_property('FILETAGS'):
            filetags = org_node.get_property('FILETAGS')
            # Parse :tag1:tag2: format
            tag_matches = re.findall(r':([^:]+):', filetags)
            tags.extend(tag_matches)

        # Filter tags if configured
        if self.config.tag_filter:
            tags = [tag for tag in tags if tag in self.config.tag_filter]

        metadata['tags'] = tags

        # Extract other properties
        if hasattr(org_node, 'get_property'):
            for prop in ['AUTHOR', 'EMAIL', 'DESCRIPTION']:
                value = org_node.get_property(prop)
                if value:
                    metadata[prop.lower()] = value

        return metadata

    def _org_to_markdown(self, org_node) -> str:
        """
        Convert org content to markdown.

        Args:
            org_node: Parsed org node

        Returns:
            Markdown content
        """
        # This is a simplified conversion
        # In a full implementation, you'd want more sophisticated org->markdown conversion

        lines = []

        def process_node(node, level=0):
            if hasattr(node, 'heading') and node.heading:
                # Convert heading
                heading_prefix = '#' * (level + 1)
                lines.append(f"{heading_prefix} {node.heading}")
                lines.append("")

            if hasattr(node, 'body') and node.body:
                # Process body content
                body = node.body.strip()
                if body:
                    # Basic org->markdown conversions
                    body = self._convert_org_markup(body)
                    lines.append(body)
                    lines.append("")

            # Process children
            if hasattr(node, 'children'):
                for child in node.children:
                    process_node(child, level + 1)

        process_node(org_node)

        return "\n".join(lines).strip()

    def _convert_org_markup(self, text: str) -> str:
        """
        Convert org markup to markdown.

        Args:
            text: Org text content

        Returns:
            Markdown text
        """
        # Bold: *text* -> **text**
        text = re.sub(r'\*([^*]+)\*', r'**\1**', text)

        # Italic: /text/ -> *text*
        text = re.sub(r'/([^/]+)/', r'*\1*', text)

        # Code: =text= -> `text`
        text = re.sub(r'=([^=]+)=', r'`\1`', text)

        # Verbatim: ~text~ -> `text`
        text = re.sub(r'~([^~]+)~', r'`\1`', text)

        # Links: [[url][description]] -> [description](url)
        text = re.sub(r'\[\[([^\]]+)\]\[([^\]]+)\]\]', r'[\2](\1)', text)

        # Simple links: [[url]] -> [url](url)
        text = re.sub(r'\[\[([^\]]+)\]\]', r'[\1](\1)', text)

        return text

    def sync_to_markdown(self, output_dir: Path) -> List[Path]:
        """
        Sync org-roam files to markdown format.

        Args:
            output_dir: Directory to write markdown files

        Returns:
            List of generated markdown file paths
        """
        if not self.config.enabled:
            return []

        output_dir.mkdir(parents=True, exist_ok=True)
        generated_files = []

        for org_file in self.get_org_files():
            try:
                # Parse org file
                metadata, markdown_content = self.parse_org_file(org_file)

                # Create markdown file
                md_filename = org_file.stem + '.md'
                md_path = output_dir / md_filename

                # Create front matter
                from ..parser.frontmatter import create_front_matter
                front_matter = create_front_matter(metadata, 'yaml')

                # Write markdown file
                full_content = front_matter + '\n' + markdown_content
                md_path.write_text(full_content, encoding='utf-8')

                generated_files.append(md_path)

            except Exception as e:
                print(f"Warning: Failed to convert {org_file}: {e}")
                continue

        return generated_files
