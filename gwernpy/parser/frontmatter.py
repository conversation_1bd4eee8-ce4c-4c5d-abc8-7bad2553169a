"""
Front matter parser for Blawger.

This module handles parsing of YAML and TOML front matter from markdown files.
"""

import re
import yaml
import toml
from typing import Dict, Any, Tuple, Optional, Union
from datetime import datetime
from pathlib import Path


class FrontMatterError(Exception):
    """Exception raised for front matter parsing errors."""
    pass


class FrontMatterParser:
    """Parser for YAML and TOML front matter in markdown files."""
    
    # Regex patterns for different front matter formats
    YAML_PATTERN = re.compile(
        r'^---\s*\n(.*?)\n---\s*\n',
        re.DOTALL | re.MULTILINE
    )
    
    TOML_PATTERN = re.compile(
        r'^\+\+\+\s*\n(.*?)\n\+\+\+\s*\n',
        re.DOTALL | re.MULTILINE
    )
    
    def __init__(self):
        """Initialize the front matter parser."""
        pass
    
    def parse(self, content: str) -> <PERSON><PERSON>[Dict[str, Any], str]:
        """
        Parse front matter from content.
        
        Args:
            content: Raw content with potential front matter
            
        Returns:
            Tuple of (metadata dict, content without front matter)
            
        Raises:
            FrontMatterError: If front matter is malformed
        """
        # Try YAML first
        yaml_match = self.YAML_PATTERN.match(content)
        if yaml_match:
            try:
                metadata = yaml.safe_load(yaml_match.group(1))
                content_without_fm = content[yaml_match.end():]
                return self._process_metadata(metadata or {}), content_without_fm
            except yaml.YAMLError as e:
                raise FrontMatterError(f"Invalid YAML front matter: {e}")
        
        # Try TOML
        toml_match = self.TOML_PATTERN.match(content)
        if toml_match:
            try:
                metadata = toml.loads(toml_match.group(1))
                content_without_fm = content[toml_match.end():]
                return self._process_metadata(metadata), content_without_fm
            except toml.TomlDecodeError as e:
                raise FrontMatterError(f"Invalid TOML front matter: {e}")
        
        # No front matter found
        return {}, content
    
    def _process_metadata(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process and validate metadata.
        
        Args:
            metadata: Raw metadata dictionary
            
        Returns:
            Processed metadata dictionary
        """
        processed = {}
        
        for key, value in metadata.items():
            # Convert string dates to datetime objects
            if key in ['date', 'created', 'modified', 'published'] and isinstance(value, str):
                processed[key] = self._parse_date(value)
            # Ensure tags is a list
            elif key == 'tags':
                if isinstance(value, str):
                    # Split comma-separated tags
                    processed[key] = [tag.strip() for tag in value.split(',')]
                elif isinstance(value, list):
                    processed[key] = [str(tag).strip() for tag in value]
                else:
                    processed[key] = [str(value)]
            # Ensure categories is a list
            elif key == 'categories':
                if isinstance(value, str):
                    processed[key] = [cat.strip() for cat in value.split(',')]
                elif isinstance(value, list):
                    processed[key] = [str(cat).strip() for cat in value]
                else:
                    processed[key] = [str(value)]
            # Convert boolean strings
            elif key in ['draft', 'published', 'featured', 'toc'] and isinstance(value, str):
                processed[key] = value.lower() in ('true', 'yes', '1', 'on')
            else:
                processed[key] = value
        
        # Set defaults for required fields
        if 'title' not in processed:
            processed['title'] = 'Untitled'
        
        if 'date' not in processed:
            processed['date'] = datetime.now()
        
        if 'draft' not in processed:
            processed['draft'] = False
        
        if 'tags' not in processed:
            processed['tags'] = []
        
        if 'categories' not in processed:
            processed['categories'] = []
        
        return processed
    
    def _parse_date(self, date_str: str) -> datetime:
        """
        Parse date string into datetime object.
        
        Args:
            date_str: Date string in various formats
            
        Returns:
            Parsed datetime object
            
        Raises:
            FrontMatterError: If date format is not recognized
        """
        # Common date formats
        formats = [
            '%Y-%m-%d',           # 2024-01-15
            '%Y-%m-%d %H:%M:%S',  # 2024-01-15 14:30:00
            '%Y-%m-%d %H:%M',     # 2024-01-15 14:30
            '%Y/%m/%d',           # 2024/01/15
            '%d/%m/%Y',           # 15/01/2024
            '%m/%d/%Y',           # 01/15/2024
            '%Y-%m-%dT%H:%M:%S',  # ISO format without timezone
            '%Y-%m-%dT%H:%M:%SZ', # ISO format with Z
        ]
        
        for fmt in formats:
            try:
                return datetime.strptime(date_str, fmt)
            except ValueError:
                continue
        
        # Try parsing with dateutil if available
        try:
            from dateutil.parser import parse
            return parse(date_str)
        except ImportError:
            pass
        except Exception:
            pass
        
        raise FrontMatterError(f"Unable to parse date: {date_str}")
    
    def create_front_matter(self, metadata: Dict[str, Any], format: str = "yaml") -> str:
        """
        Create front matter string from metadata.
        
        Args:
            metadata: Metadata dictionary
            format: Output format ("yaml" or "toml")
            
        Returns:
            Front matter string with delimiters
            
        Raises:
            FrontMatterError: If format is not supported
        """
        # Prepare metadata for serialization
        serializable_metadata = {}
        for key, value in metadata.items():
            if isinstance(value, datetime):
                serializable_metadata[key] = value.isoformat()
            else:
                serializable_metadata[key] = value
        
        if format.lower() == "yaml":
            try:
                yaml_content = yaml.dump(
                    serializable_metadata,
                    default_flow_style=False,
                    sort_keys=False,
                    allow_unicode=True
                )
                return f"---\n{yaml_content}---\n"
            except Exception as e:
                raise FrontMatterError(f"Failed to create YAML front matter: {e}")
        
        elif format.lower() == "toml":
            try:
                toml_content = toml.dumps(serializable_metadata)
                return f"+++\n{toml_content}+++\n"
            except Exception as e:
                raise FrontMatterError(f"Failed to create TOML front matter: {e}")
        
        else:
            raise FrontMatterError(f"Unsupported front matter format: {format}")
    
    def extract_summary(self, content: str, max_length: int = 200) -> str:
        """
        Extract a summary from content.
        
        Args:
            content: Content to extract summary from
            max_length: Maximum length of summary
            
        Returns:
            Extracted summary
        """
        # Remove markdown formatting for summary
        clean_content = re.sub(r'[#*`_\[\]()]', '', content)
        clean_content = re.sub(r'\n+', ' ', clean_content)
        clean_content = clean_content.strip()
        
        if len(clean_content) <= max_length:
            return clean_content
        
        # Find the last complete sentence within the limit
        truncated = clean_content[:max_length]
        last_sentence = truncated.rfind('.')
        
        if last_sentence > max_length * 0.5:  # If we found a sentence break in the latter half
            return truncated[:last_sentence + 1]
        else:
            # Just truncate at word boundary
            last_space = truncated.rfind(' ')
            if last_space > 0:
                return truncated[:last_space] + '...'
            else:
                return truncated + '...'


def parse_front_matter(content: str) -> Tuple[Dict[str, Any], str]:
    """
    Convenience function to parse front matter.
    
    Args:
        content: Content with potential front matter
        
    Returns:
        Tuple of (metadata dict, content without front matter)
    """
    parser = FrontMatterParser()
    return parser.parse(content)


def create_front_matter(metadata: Dict[str, Any], format: str = "yaml") -> str:
    """
    Convenience function to create front matter.
    
    Args:
        metadata: Metadata dictionary
        format: Output format ("yaml" or "toml")
        
    Returns:
        Front matter string with delimiters
    """
    parser = FrontMatterParser()
    return parser.create_front_matter(metadata, format)
