"""
Markdown parser for Blawger.

This module handles parsing of Markdown content, including metadata extraction
and custom syntax extensions for academic writing.
"""

import re
from typing import Dict, Any, Optional, List, Tuple, Union
from pathlib import Path
from markdown_it import MarkdownIt
from markdown_it.extensions.footnote import footnote_plugin
from markdown_it.extensions.front_matter import front_matter_plugin
from mdit_py_plugins.tasklists import tasklists_plugin
from mdit_py_plugins.container import container_plugin

from .frontmatter import FrontMatterParser, FrontMatterError


class MarkdownError(Exception):
    """Exception raised for markdown parsing errors."""
    pass


class MarkdownParser:
    """Parser for Markdown content with academic extensions."""

    def __init__(self, extensions: Optional[List[str]] = None, config: Optional[Dict[str, Any]] = None) -> None:
        """
        Initialize the Markdown parser.

        Args:
            extensions: List of extensions to enable
            config: Configuration dictionary for markdown processing
        """
        self.extensions = extensions or [
            "tables", "footnotes", "toc", "math", "citations", "containers"
        ]
        self.config = config or {}

        # Initialize front matter parser
        self.frontmatter_parser = FrontMatterParser()

        # Initialize markdown-it parser
        self.md = MarkdownIt("commonmark", {
            "html": True,
            "linkify": True,
            "typographer": True,
        })

        # Enable plugins based on extensions
        self._setup_plugins()

    def _setup_plugins(self) -> None:
        """Setup markdown-it plugins based on enabled extensions."""
        if "footnotes" in self.extensions:
            self.md.use(footnote_plugin)

        if "tasklists" in self.extensions:
            self.md.use(tasklists_plugin)

        if "containers" in self.extensions:
            self.md.use(container_plugin, "note")
            self.md.use(container_plugin, "warning")
            self.md.use(container_plugin, "tip")
            self.md.use(container_plugin, "important")

        # Enable tables (built-in)
        if "tables" in self.extensions:
            self.md.enable(["table"])

        # Enable strikethrough
        if "strikethrough" in self.extensions:
            self.md.enable(["strikethrough"])

    def parse(self, content: str, source_path: Optional[Path] = None) -> Tuple[Dict[str, Any], str]:
        """
        Parse Markdown content and extract metadata.

        Args:
            content: Raw Markdown content with optional front matter
            source_path: Optional path to source file for error reporting

        Returns:
            Tuple containing (metadata dict, processed HTML content)

        Raises:
            MarkdownError: If parsing fails
        """
        try:
            # Extract front matter
            metadata, markdown_content = self.frontmatter_parser.parse(content)

            # Add source path to metadata if provided
            if source_path:
                metadata['source_path'] = str(source_path)
                metadata['source_name'] = source_path.stem

            # Process markdown content
            html_content = self._process_markdown(markdown_content)

            # Post-process HTML
            html_content = self._post_process_html(html_content, metadata)

            return metadata, html_content

        except FrontMatterError as e:
            raise MarkdownError(f"Front matter error: {e}")
        except Exception as e:
            source_info = f" in {source_path}" if source_path else ""
            raise MarkdownError(f"Markdown parsing error{source_info}: {e}")

    def _process_markdown(self, content: str) -> str:
        """
        Process markdown content to HTML.

        Args:
            content: Markdown content without front matter

        Returns:
            Processed HTML content
        """
        # Pre-process content for custom syntax
        content = self._preprocess_content(content)

        # Convert to HTML
        html = self.md.render(content)

        return html

    def _preprocess_content(self, content: str) -> str:
        """
        Pre-process content for custom syntax extensions.

        Args:
            content: Raw markdown content

        Returns:
            Pre-processed content
        """
        # Process math expressions if enabled
        if "math" in self.extensions:
            content = self._process_math(content)

        # Process citations if enabled
        if "citations" in self.extensions:
            content = self._process_citations(content)

        # Process custom academic syntax
        content = self._process_academic_syntax(content)

        return content

    def _process_math(self, content: str) -> str:
        """
        Process math expressions for KaTeX or MathJax.

        Args:
            content: Content with potential math expressions

        Returns:
            Content with processed math expressions
        """
        # Inline math: $...$
        inline_math_pattern = r'\$([^$\n]+)\$'
        content = re.sub(
            inline_math_pattern,
            r'<span class="math inline">\\(\1\\)</span>',
            content
        )

        # Display math: $$...$$
        display_math_pattern = r'\$\$([^$]+)\$\$'
        content = re.sub(
            display_math_pattern,
            r'<div class="math display">\\[\1\\]</div>',
            content,
            flags=re.DOTALL
        )

        return content

    def _process_citations(self, content: str) -> str:
        """
        Process academic citations.

        Args:
            content: Content with potential citations

        Returns:
            Content with processed citations
        """
        # Simple citation format: [@key] or [@key1; @key2]
        citation_pattern = r'\[@([^\]]+)\]'

        def replace_citation(match):
            citation_keys = match.group(1)
            keys = [key.strip().lstrip('@') for key in citation_keys.split(';')]

            # Generate citation HTML
            citation_html = '<span class="citation">'
            for i, key in enumerate(keys):
                if i > 0:
                    citation_html += '; '
                citation_html += f'<a href="#ref-{key}" class="citation-link">{key}</a>'
            citation_html += '</span>'

            return citation_html

        content = re.sub(citation_pattern, replace_citation, content)
        return content

    def _process_academic_syntax(self, content: str) -> str:
        """
        Process custom academic syntax extensions.

        Args:
            content: Content with potential academic syntax

        Returns:
            Content with processed academic syntax
        """
        # Sidenotes: ^[note content]
        sidenote_pattern = r'\^\[([^\]]+)\]'
        sidenote_counter = 0

        def replace_sidenote(match):
            nonlocal sidenote_counter
            sidenote_counter += 1
            note_content = match.group(1)
            return (
                f'<label for="sn-{sidenote_counter}" class="margin-toggle sidenote-number"></label>'
                f'<input type="checkbox" id="sn-{sidenote_counter}" class="margin-toggle"/>'
                f'<span class="sidenote">{note_content}</span>'
            )

        content = re.sub(sidenote_pattern, replace_sidenote, content)

        # Margin notes: {-note content-}
        marginnote_pattern = r'\{-([^}]+)-\}'
        marginnote_counter = 0

        def replace_marginnote(match):
            nonlocal marginnote_counter
            marginnote_counter += 1
            note_content = match.group(1)
            return (
                f'<label for="mn-{marginnote_counter}" class="margin-toggle">&#8853;</label>'
                f'<input type="checkbox" id="mn-{marginnote_counter}" class="margin-toggle"/>'
                f'<span class="marginnote">{note_content}</span>'
            )

        content = re.sub(marginnote_pattern, replace_marginnote, content)

        return content

    def _post_process_html(self, html: str, metadata: Dict[str, Any]) -> str:
        """
        Post-process HTML content.

        Args:
            html: Generated HTML content
            metadata: Document metadata

        Returns:
            Post-processed HTML content
        """
        # Add table of contents if requested
        if metadata.get('toc', False) or 'toc' in self.extensions:
            html = self._add_table_of_contents(html)

        # Add reading time estimate
        html = self._add_reading_time(html, metadata)

        # Process images for responsive design
        html = self._process_images(html)

        return html

    def _add_table_of_contents(self, html: str) -> str:
        """
        Generate and add table of contents.

        Args:
            html: HTML content

        Returns:
            HTML with table of contents
        """
        # Extract headings
        heading_pattern = r'<h([1-6])(?:[^>]*)>([^<]+)</h[1-6]>'
        headings = re.findall(heading_pattern, html)

        if not headings:
            return html

        # Generate TOC HTML
        toc_html = '<div class="table-of-contents">\n<h2>Table of Contents</h2>\n<ul>\n'

        for level, title in headings:
            # Create anchor ID from title
            anchor_id = re.sub(r'[^\w\s-]', '', title).strip()
            anchor_id = re.sub(r'[-\s]+', '-', anchor_id).lower()

            # Add ID to heading in content
            heading_with_id = f'<h{level} id="{anchor_id}">{title}</h{level}>'
            html = html.replace(f'<h{level}>{title}</h{level}>', heading_with_id, 1)

            # Add to TOC
            indent = '  ' * (int(level) - 1)
            toc_html += f'{indent}<li><a href="#{anchor_id}">{title}</a></li>\n'

        toc_html += '</ul>\n</div>\n\n'

        # Insert TOC at the beginning
        return toc_html + html

    def _add_reading_time(self, html: str, metadata: Dict[str, Any]) -> str:
        """
        Add reading time estimate to metadata.

        Args:
            html: HTML content
            metadata: Document metadata

        Returns:
            HTML content (unchanged, but metadata is modified)
        """
        # Extract text content for word count
        text_content = re.sub(r'<[^>]+>', '', html)
        word_count = len(text_content.split())

        # Estimate reading time (average 200 words per minute)
        reading_time = max(1, round(word_count / 200))

        # Add to metadata
        metadata['word_count'] = word_count
        metadata['reading_time'] = reading_time

        return html

    def _process_images(self, html: str) -> str:
        """
        Process images for responsive design and accessibility.

        Args:
            html: HTML content

        Returns:
            HTML with processed images
        """
        # Add responsive classes and lazy loading
        img_pattern = r'<img([^>]+)>'

        def replace_img(match):
            attrs = match.group(1)

            # Add responsive class if not present
            if 'class=' not in attrs:
                attrs += ' class="responsive-image"'
            else:
                attrs = re.sub(r'class="([^"]*)"', r'class="\1 responsive-image"', attrs)

            # Add lazy loading if not present
            if 'loading=' not in attrs:
                attrs += ' loading="lazy"'

            return f'<img{attrs}>'

        html = re.sub(img_pattern, replace_img, html)
        return html
