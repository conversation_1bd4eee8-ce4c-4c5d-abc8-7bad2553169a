"""
Configuration management for Blawger.

This module handles loading and validating configuration from various sources
including YAML files, TOML files, and environment variables.
"""

import os
import yaml
import toml
from pathlib import Path
from typing import Dict, Any, Optional, Union, List
from dataclasses import dataclass, field
from enum import Enum


class OutputFormat(Enum):
    """Supported output formats."""
    HTML = "html"
    JSON = "json"
    XML = "xml"


class ThemeType(Enum):
    """Supported theme types."""
    TUFTE = "tufte"
    MINIMAL = "minimal"
    ACADEMIC = "academic"
    CUSTOM = "custom"


@dataclass
class SiteConfig:
    """Site-level configuration."""
    title: str = "My Academic Blog"
    description: str = "A blog powered by Blawger"
    url: str = "https://example.com"
    author: str = "Anonymous"
    email: str = ""
    language: str = "en"
    timezone: str = "UTC"


@dataclass
class BuildConfig:
    """Build-related configuration."""
    content_dir: Path = Path("content")
    output_dir: Path = Path("_site")
    template_dir: Path = Path("templates")
    static_dir: Path = Path("static")
    theme: ThemeType = ThemeType.TUFTE
    output_format: OutputFormat = OutputFormat.HTML
    minify_html: bool = True
    minify_css: bool = True
    minify_js: bool = True
    generate_sitemap: bool = True
    generate_rss: bool = True


@dataclass
class MarkdownConfig:
    """Markdown processing configuration."""
    extensions: List[str] = field(default_factory=lambda: [
        "tables", "footnotes", "toc", "codehilite", "math"
    ])
    math_engine: str = "katex"  # katex, mathjax, or none
    syntax_highlighting: bool = True
    syntax_theme: str = "github"
    smart_quotes: bool = True
    auto_links: bool = True


@dataclass
class OrgRoamConfig:
    """Org-roam integration configuration."""
    enabled: bool = False
    org_directory: Optional[Path] = None
    database_path: Optional[Path] = None
    tag_filter: List[str] = field(default_factory=list)
    exclude_patterns: List[str] = field(default_factory=lambda: ["*.org~", "*.org#"])
    watch_for_changes: bool = True


@dataclass
class BotConfig:
    """Bot control configuration."""
    allow_indexing: bool = True
    allow_ai_training: bool = False
    robots_txt: bool = True
    custom_user_agents: Dict[str, bool] = field(default_factory=dict)
    rate_limiting: bool = True
    rate_limit_requests: int = 100
    rate_limit_window: int = 3600  # seconds


@dataclass
class ServerConfig:
    """Development server configuration."""
    host: str = "localhost"
    port: int = 8000
    auto_reload: bool = True
    open_browser: bool = True
    livereload: bool = True


@dataclass
class Config:
    """Main configuration class."""
    site: SiteConfig = field(default_factory=SiteConfig)
    build: BuildConfig = field(default_factory=BuildConfig)
    markdown: MarkdownConfig = field(default_factory=MarkdownConfig)
    orgroam: OrgRoamConfig = field(default_factory=OrgRoamConfig)
    bot: BotConfig = field(default_factory=BotConfig)
    server: ServerConfig = field(default_factory=ServerConfig)
    
    @classmethod
    def load(cls, config_path: Optional[Union[str, Path]] = None) -> "Config":
        """
        Load configuration from file and environment variables.
        
        Args:
            config_path: Path to configuration file (YAML or TOML)
            
        Returns:
            Loaded configuration instance
        """
        config = cls()
        
        # Load from file if provided
        if config_path:
            config_path = Path(config_path)
            if config_path.exists():
                config._load_from_file(config_path)
        else:
            # Try to find config file automatically
            for filename in ["blawger.yml", "blawger.yaml", "blawger.toml", "config.yml", "config.yaml", "config.toml"]:
                config_file = Path(filename)
                if config_file.exists():
                    config._load_from_file(config_file)
                    break
        
        # Override with environment variables
        config._load_from_env()
        
        # Validate configuration
        config._validate()
        
        return config
    
    def _load_from_file(self, config_path: Path) -> None:
        """Load configuration from YAML or TOML file."""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.suffix.lower() in ['.yml', '.yaml']:
                    data = yaml.safe_load(f)
                elif config_path.suffix.lower() == '.toml':
                    data = toml.load(f)
                else:
                    raise ValueError(f"Unsupported config file format: {config_path.suffix}")
            
            if data:
                self._update_from_dict(data)
                
        except Exception as e:
            raise ValueError(f"Failed to load config from {config_path}: {e}")
    
    def _load_from_env(self) -> None:
        """Load configuration from environment variables."""
        # Site configuration
        if os.getenv("BLAWGER_SITE_TITLE"):
            self.site.title = os.getenv("BLAWGER_SITE_TITLE")
        if os.getenv("BLAWGER_SITE_URL"):
            self.site.url = os.getenv("BLAWGER_SITE_URL")
        if os.getenv("BLAWGER_SITE_AUTHOR"):
            self.site.author = os.getenv("BLAWGER_SITE_AUTHOR")
        
        # Build configuration
        if os.getenv("BLAWGER_OUTPUT_DIR"):
            self.build.output_dir = Path(os.getenv("BLAWGER_OUTPUT_DIR"))
        if os.getenv("BLAWGER_CONTENT_DIR"):
            self.build.content_dir = Path(os.getenv("BLAWGER_CONTENT_DIR"))
        
        # Bot configuration
        if os.getenv("BLAWGER_ALLOW_AI_TRAINING"):
            self.bot.allow_ai_training = os.getenv("BLAWGER_ALLOW_AI_TRAINING").lower() == "true"
        
        # Server configuration
        if os.getenv("BLAWGER_SERVER_PORT"):
            self.server.port = int(os.getenv("BLAWGER_SERVER_PORT"))
    
    def _update_from_dict(self, data: Dict[str, Any]) -> None:
        """Update configuration from dictionary."""
        if "site" in data:
            site_data = data["site"]
            for key, value in site_data.items():
                if hasattr(self.site, key):
                    setattr(self.site, key, value)
        
        if "build" in data:
            build_data = data["build"]
            for key, value in build_data.items():
                if hasattr(self.build, key):
                    if key in ["content_dir", "output_dir", "template_dir", "static_dir"]:
                        setattr(self.build, key, Path(value))
                    elif key == "theme":
                        setattr(self.build, key, ThemeType(value))
                    elif key == "output_format":
                        setattr(self.build, key, OutputFormat(value))
                    else:
                        setattr(self.build, key, value)
        
        if "markdown" in data:
            markdown_data = data["markdown"]
            for key, value in markdown_data.items():
                if hasattr(self.markdown, key):
                    setattr(self.markdown, key, value)
        
        if "orgroam" in data:
            orgroam_data = data["orgroam"]
            for key, value in orgroam_data.items():
                if hasattr(self.orgroam, key):
                    if key in ["org_directory", "database_path"] and value:
                        setattr(self.orgroam, key, Path(value))
                    else:
                        setattr(self.orgroam, key, value)
        
        if "bot" in data:
            bot_data = data["bot"]
            for key, value in bot_data.items():
                if hasattr(self.bot, key):
                    setattr(self.bot, key, value)
        
        if "server" in data:
            server_data = data["server"]
            for key, value in server_data.items():
                if hasattr(self.server, key):
                    setattr(self.server, key, value)
    
    def _validate(self) -> None:
        """Validate configuration values."""
        # Validate paths exist or can be created
        if not self.build.content_dir.exists():
            raise ValueError(f"Content directory does not exist: {self.build.content_dir}")
        
        # Validate URL format
        if not self.site.url.startswith(("http://", "https://")):
            raise ValueError(f"Site URL must start with http:// or https://: {self.site.url}")
        
        # Validate port range
        if not (1 <= self.server.port <= 65535):
            raise ValueError(f"Server port must be between 1 and 65535: {self.server.port}")
        
        # Validate org-roam configuration
        if self.orgroam.enabled:
            if not self.orgroam.org_directory:
                raise ValueError("Org-roam directory must be specified when org-roam is enabled")
            if not self.orgroam.org_directory.exists():
                raise ValueError(f"Org-roam directory does not exist: {self.orgroam.org_directory}")
    
    def save(self, config_path: Union[str, Path], format: str = "yaml") -> None:
        """
        Save configuration to file.
        
        Args:
            config_path: Path to save configuration file
            format: File format ("yaml" or "toml")
        """
        config_path = Path(config_path)
        
        # Convert to dictionary
        config_dict = {
            "site": {
                "title": self.site.title,
                "description": self.site.description,
                "url": self.site.url,
                "author": self.site.author,
                "email": self.site.email,
                "language": self.site.language,
                "timezone": self.site.timezone,
            },
            "build": {
                "content_dir": str(self.build.content_dir),
                "output_dir": str(self.build.output_dir),
                "template_dir": str(self.build.template_dir),
                "static_dir": str(self.build.static_dir),
                "theme": self.build.theme.value,
                "output_format": self.build.output_format.value,
                "minify_html": self.build.minify_html,
                "minify_css": self.build.minify_css,
                "minify_js": self.build.minify_js,
                "generate_sitemap": self.build.generate_sitemap,
                "generate_rss": self.build.generate_rss,
            },
            "markdown": {
                "extensions": self.markdown.extensions,
                "math_engine": self.markdown.math_engine,
                "syntax_highlighting": self.markdown.syntax_highlighting,
                "syntax_theme": self.markdown.syntax_theme,
                "smart_quotes": self.markdown.smart_quotes,
                "auto_links": self.markdown.auto_links,
            },
            "orgroam": {
                "enabled": self.orgroam.enabled,
                "org_directory": str(self.orgroam.org_directory) if self.orgroam.org_directory else None,
                "database_path": str(self.orgroam.database_path) if self.orgroam.database_path else None,
                "tag_filter": self.orgroam.tag_filter,
                "exclude_patterns": self.orgroam.exclude_patterns,
                "watch_for_changes": self.orgroam.watch_for_changes,
            },
            "bot": {
                "allow_indexing": self.bot.allow_indexing,
                "allow_ai_training": self.bot.allow_ai_training,
                "robots_txt": self.bot.robots_txt,
                "custom_user_agents": self.bot.custom_user_agents,
                "rate_limiting": self.bot.rate_limiting,
                "rate_limit_requests": self.bot.rate_limit_requests,
                "rate_limit_window": self.bot.rate_limit_window,
            },
            "server": {
                "host": self.server.host,
                "port": self.server.port,
                "auto_reload": self.server.auto_reload,
                "open_browser": self.server.open_browser,
                "livereload": self.server.livereload,
            },
        }
        
        # Save to file
        with open(config_path, 'w', encoding='utf-8') as f:
            if format.lower() == "yaml":
                yaml.dump(config_dict, f, default_flow_style=False, sort_keys=False)
            elif format.lower() == "toml":
                toml.dump(config_dict, f)
            else:
                raise ValueError(f"Unsupported format: {format}")


def load_config(config_path: Optional[Union[str, Path]] = None) -> Config:
    """
    Convenience function to load configuration.
    
    Args:
        config_path: Optional path to configuration file
        
    Returns:
        Loaded configuration instance
    """
    return Config.load(config_path)
