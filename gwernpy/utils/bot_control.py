"""
Bot control utilities for Blawger.

This module provides utilities for controlling AI bot access to the generated site,
including robots.txt generation and user-agent specific controls.
"""

from pathlib import Path
from typing import Dict, List, Optional
from ..config import BotConfig


class BotController:
    """Controller for managing bot access to the site."""

    def __init__(self, config: BotConfig):
        """
        Initialize the bot controller.

        Args:
            config: Bot configuration
        """
        self.config = config

    def generate_robots_txt(self, output_dir: Path) -> Path:
        """
        Generate robots.txt file.

        Args:
            output_dir: Directory to write robots.txt

        Returns:
            Path to generated robots.txt file
        """
        robots_content = []

        # General indexing rules
        if self.config.allow_indexing:
            robots_content.append("User-agent: *")
            robots_content.append("Allow: /")
        else:
            robots_content.append("User-agent: *")
            robots_content.append("Disallow: /")

        # AI training specific rules
        if not self.config.allow_ai_training:
            ai_bots = [
                "GPTBot",           # OpenAI
                "ChatGPT-User",     # OpenAI
                "CCBot",            # Common Crawl (used by many AI companies)
                "anthropic-ai",     # Anthropic
                "Claude-Web",       # Anthropic
                "Bard",             # Google Bard
                "PaLM",             # Google PaLM
                "Gemini",           # Google Gemini
                "FacebookBot",      # Meta
                "Applebot",         # Apple
                "Bytespider",       # ByteDance/TikTok
                "Amazonbot",        # Amazon
                "PerplexityBot",    # Perplexity
                "YouBot",           # You.com
                "Diffbot",          # Diffbot
                "ImagesiftBot",     # ImagesiftBot
                "Omgilibot",        # Omgili
                "Omgili",           # Omgili
            ]

            robots_content.append("")
            robots_content.append("# AI Training Bots - Disallowed")
            for bot in ai_bots:
                robots_content.append(f"User-agent: {bot}")
                robots_content.append("Disallow: /")
                robots_content.append("")

        # Custom user agent rules
        if self.config.custom_user_agents:
            robots_content.append("# Custom User Agent Rules")
            for user_agent, allowed in self.config.custom_user_agents.items():
                robots_content.append(f"User-agent: {user_agent}")
                if allowed:
                    robots_content.append("Allow: /")
                else:
                    robots_content.append("Disallow: /")
                robots_content.append("")

        # Add sitemap reference
        robots_content.append("# Sitemap")
        robots_content.append("Sitemap: /sitemap.xml")

        # Write robots.txt
        robots_path = output_dir / "robots.txt"
        robots_path.write_text("\n".join(robots_content), encoding='utf-8')

        return robots_path

    def get_security_headers(self) -> Dict[str, str]:
        """
        Get security headers for web server configuration.

        Returns:
            Dictionary of security headers
        """
        headers = {}

        # Basic security headers
        headers["X-Content-Type-Options"] = "nosniff"
        headers["X-Frame-Options"] = "DENY"
        headers["X-XSS-Protection"] = "1; mode=block"
        headers["Referrer-Policy"] = "strict-origin-when-cross-origin"

        # AI training control headers
        if not self.config.allow_ai_training:
            headers["X-Robots-Tag"] = "noai, noimageai"

        # Rate limiting headers (if enabled)
        if self.config.rate_limiting:
            headers["X-RateLimit-Limit"] = str(self.config.rate_limit_requests)
            headers["X-RateLimit-Window"] = str(self.config.rate_limit_window)

        return headers

    def is_bot_allowed(self, user_agent: str) -> bool:
        """
        Check if a bot is allowed based on user agent.

        Args:
            user_agent: User agent string

        Returns:
            True if bot is allowed, False otherwise
        """
        user_agent_lower = user_agent.lower()

        # Check custom user agents first
        for custom_agent, allowed in self.config.custom_user_agents.items():
            if custom_agent.lower() in user_agent_lower:
                return allowed

        # Check AI training bots
        if not self.config.allow_ai_training:
            ai_bot_patterns = [
                "gptbot", "chatgpt", "ccbot", "anthropic", "claude",
                "bard", "palm", "gemini", "facebookbot", "applebot",
                "bytespider", "amazonbot", "perplexitybot", "youbot",
                "diffbot", "imagesiftbot", "omgilibot", "omgili"
            ]

            for pattern in ai_bot_patterns:
                if pattern in user_agent_lower:
                    return False

        # Default to allowing if general indexing is allowed
        return self.config.allow_indexing

    def generate_meta_tags(self) -> List[str]:
        """
        Generate meta tags for HTML head section.

        Returns:
            List of meta tag strings
        """
        meta_tags = []

        # Robots meta tag
        robots_directives = []

        if self.config.allow_indexing:
            robots_directives.append("index")
            robots_directives.append("follow")
        else:
            robots_directives.append("noindex")
            robots_directives.append("nofollow")

        if not self.config.allow_ai_training:
            robots_directives.extend(["noai", "noimageai"])

        meta_tags.append(f'<meta name="robots" content="{", ".join(robots_directives)}">')

        return meta_tags
