"""
Blawger - A Python-based reimplementation of the gwern.net website infrastructure.

This package provides tools for generating a static blog with features similar to gwern.net,
including Tufte CSS styling, org-roam integration, and bot control.
"""

__version__ = "0.1.0"
__author__ = "Blawger Contributors"
__email__ = "<EMAIL>"
__description__ = "A modern Python reimplementation of gwern.net's infrastructure"

# Import main classes for easy access
from .parser.markdown import MarkdownParser
from .parser.frontmatter import FrontMatterParser
from .generator.html import HTMLGenerator
from .generator.templates import TemplateManager
from .orgroam.connector import OrgRoamConnector
from .utils.bot_control import BotController
from .config import Config

__all__ = [
    "MarkdownParser",
    "FrontMatterParser",
    "HTMLGenerator",
    "TemplateManager",
    "OrgRoamConnector",
    "BotController",
    "Config",
]
