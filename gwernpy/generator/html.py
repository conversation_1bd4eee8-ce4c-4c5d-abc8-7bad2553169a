"""
HTML generator for Blawger.

This module handles generating HTML output from parsed content with Tufte CSS styling.
"""

import os
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
from urllib.parse import urljoin

from jinja2 import Environment, FileSystemLoader, select_autoescape, Template
from jinja2.exceptions import TemplateNotFound

from ..config import Config


class HTMLGeneratorError(Exception):
    """Exception raised for HTML generation errors."""
    pass


class HTMLGenerator:
    """Generator for HTML output with Tufte CSS styling."""

    def __init__(self, config: Config):
        """
        Initialize the HTML generator.

        Args:
            config: Configuration object
        """
        self.config = config
        self.posts = []  # Store all posts for sitemap/RSS generation

        # Initialize Jinja2 environment
        self._setup_jinja_env()

        # Ensure output directory exists
        self.config.build.output_dir.mkdir(parents=True, exist_ok=True)

    def _setup_jinja_env(self) -> None:
        """Setup Jinja2 environment with templates."""
        template_dirs = []

        # Add custom template directory if it exists
        if self.config.build.template_dir.exists():
            template_dirs.append(str(self.config.build.template_dir))

        # Add built-in templates (we'll create these)
        builtin_templates = Path(__file__).parent / "templates"
        if builtin_templates.exists():
            template_dirs.append(str(builtin_templates))

        if not template_dirs:
            # Create a minimal template directory
            self._create_default_templates()
            template_dirs.append(str(self.config.build.template_dir))

        self.jinja_env = Environment(
            loader=FileSystemLoader(template_dirs),
            autoescape=select_autoescape(['html', 'xml']),
            trim_blocks=True,
            lstrip_blocks=True,
        )

        # Add custom filters
        self._add_custom_filters()

    def _add_custom_filters(self) -> None:
        """Add custom Jinja2 filters."""

        def dateformat(value: datetime, format: str = '%Y-%m-%d') -> str:
            """Format datetime objects."""
            if isinstance(value, datetime):
                return value.strftime(format)
            return str(value)

        def excerpt(text: str, length: int = 200) -> str:
            """Create an excerpt from text."""
            if len(text) <= length:
                return text
            return text[:length].rsplit(' ', 1)[0] + '...'

        def reading_time(word_count: int) -> str:
            """Calculate reading time from word count."""
            minutes = max(1, round(word_count / 200))
            return f"{minutes} min read"

        def url_for(path: str) -> str:
            """Generate URL for a path."""
            return urljoin(self.config.site.url, path.lstrip('/'))

        # Register filters
        self.jinja_env.filters['dateformat'] = dateformat
        self.jinja_env.filters['excerpt'] = excerpt
        self.jinja_env.filters['reading_time'] = reading_time
        self.jinja_env.filters['url_for'] = url_for

    def _create_default_templates(self) -> None:
        """Create default templates if none exist."""
        template_dir = self.config.build.template_dir
        template_dir.mkdir(parents=True, exist_ok=True)

        # Base template
        base_template = '''<!DOCTYPE html>
<html lang="{{ site.language }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{% block title %}{{ site.title }}{% endblock %}</title>
    <meta name="description" content="{% block description %}{{ site.description }}{% endblock %}">
    <meta name="author" content="{{ site.author }}">

    <!-- Tufte CSS -->
    <link rel="stylesheet" href="{{ '/static/css/tufte.css' | url_for }}">
    <link rel="stylesheet" href="{{ '/static/css/custom.css' | url_for }}">

    {% block head %}{% endblock %}
</head>
<body>
    <article>
        <header>
            <h1><a href="{{ '/' | url_for }}">{{ site.title }}</a></h1>
            <p class="subtitle">{{ site.description }}</p>
        </header>

        <main>
            {% block content %}{% endblock %}
        </main>

        <footer>
            <p>&copy; {{ "now" | dateformat("%Y") }} {{ site.author }}.
               Powered by <a href="https://github.com/forkrul/blawger">Blawger</a>.</p>
        </footer>
    </article>

    {% block scripts %}{% endblock %}
</body>
</html>'''

        # Post template
        post_template = '''{% extends "base.html" %}

{% block title %}{{ post.title }} - {{ site.title }}{% endblock %}
{% block description %}{{ post.summary or post.title }}{% endblock %}

{% block content %}
<section>
    <h1>{{ post.title }}</h1>

    <p class="subtitle">
        <time datetime="{{ post.date | dateformat('%Y-%m-%d') }}">
            {{ post.date | dateformat('%B %d, %Y') }}
        </time>
        {% if post.reading_time %}
        • {{ post.word_count | reading_time }}
        {% endif %}
        {% if post.tags %}
        • Tagged:
        {% for tag in post.tags %}
            <span class="tag">{{ tag }}</span>{% if not loop.last %}, {% endif %}
        {% endfor %}
        {% endif %}
    </p>

    <div class="content">
        {{ content | safe }}
    </div>
</section>
{% endblock %}'''

        # Index template
        index_template = '''{% extends "base.html" %}

{% block content %}
<section>
    <h1>Recent Posts</h1>

    {% for post in posts %}
    <article class="post-preview">
        <h2><a href="{{ post.url }}">{{ post.title }}</a></h2>
        <p class="post-meta">
            <time datetime="{{ post.date | dateformat('%Y-%m-%d') }}">
                {{ post.date | dateformat('%B %d, %Y') }}
            </time>
            {% if post.reading_time %}
            • {{ post.word_count | reading_time }}
            {% endif %}
        </p>
        {% if post.summary %}
        <p>{{ post.summary }}</p>
        {% endif %}
    </article>
    {% endfor %}
</section>
{% endblock %}'''

        # Write templates
        (template_dir / "base.html").write_text(base_template)
        (template_dir / "post.html").write_text(post_template)
        (template_dir / "index.html").write_text(index_template)

    def generate_page(self, metadata: Dict[str, Any], content: str, source_path: Path) -> Path:
        """
        Generate an HTML page from metadata and content.

        Args:
            metadata: Page metadata
            content: Processed HTML content
            source_path: Source file path

        Returns:
            Path to generated HTML file

        Raises:
            HTMLGeneratorError: If generation fails
        """
        try:
            # Determine output path
            relative_path = source_path.relative_to(self.config.build.content_dir)
            output_path = self.config.build.output_dir / relative_path.with_suffix('.html')

            # Create output directory
            output_path.parent.mkdir(parents=True, exist_ok=True)

            # Prepare template context
            context = {
                'site': self.config.site.__dict__,
                'post': metadata,
                'content': content,
                'config': self.config,
            }

            # Add URL to metadata
            url_path = str(relative_path.with_suffix('.html')).replace('\\', '/')
            metadata['url'] = '/' + url_path

            # Store post for later use (sitemap, RSS, etc.)
            self.posts.append(metadata.copy())

            # Choose template
            template_name = metadata.get('template', 'post.html')

            try:
                template = self.jinja_env.get_template(template_name)
            except TemplateNotFound:
                # Fall back to post template
                template = self.jinja_env.get_template('post.html')

            # Render template
            html_output = template.render(**context)

            # Minify HTML if enabled
            if self.config.build.minify_html:
                html_output = self._minify_html(html_output)

            # Write output file
            output_path.write_text(html_output, encoding='utf-8')

            return output_path

        except Exception as e:
            raise HTMLGeneratorError(f"Failed to generate page for {source_path}: {e}")

    def _minify_html(self, html: str) -> str:
        """
        Minify HTML content.

        Args:
            html: HTML content to minify

        Returns:
            Minified HTML content
        """
        # Simple minification - remove extra whitespace
        import re

        # Remove comments
        html = re.sub(r'<!--.*?-->', '', html, flags=re.DOTALL)

        # Remove extra whitespace
        html = re.sub(r'\s+', ' ', html)
        html = re.sub(r'>\s+<', '><', html)

        return html.strip()

    def generate_sitemap(self) -> Path:
        """Generate XML sitemap."""
        sitemap_content = '''<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
'''

        for post in self.posts:
            if not post.get('draft', False):
                url = urljoin(self.config.site.url, post['url'])
                date = post.get('date', datetime.now()).strftime('%Y-%m-%d')
                sitemap_content += f'''    <url>
        <loc>{url}</loc>
        <lastmod>{date}</lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.8</priority>
    </url>
'''

        sitemap_content += '</urlset>'

        sitemap_path = self.config.build.output_dir / 'sitemap.xml'
        sitemap_path.write_text(sitemap_content, encoding='utf-8')

        return sitemap_path

    def generate_rss(self) -> Path:
        """Generate RSS feed."""
        # Sort posts by date, most recent first
        recent_posts = sorted(
            [p for p in self.posts if not p.get('draft', False)],
            key=lambda p: p.get('date', datetime.min),
            reverse=True
        )[:10]  # Latest 10 posts

        rss_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0">
    <channel>
        <title>{self.config.site.title}</title>
        <link>{self.config.site.url}</link>
        <description>{self.config.site.description}</description>
        <language>{self.config.site.language}</language>
        <lastBuildDate>{datetime.now().strftime('%a, %d %b %Y %H:%M:%S %z')}</lastBuildDate>
'''

        for post in recent_posts:
            url = urljoin(self.config.site.url, post['url'])
            pub_date = post.get('date', datetime.now()).strftime('%a, %d %b %Y %H:%M:%S %z')

            rss_content += f'''        <item>
            <title>{post.get('title', 'Untitled')}</title>
            <link>{url}</link>
            <description>{post.get('summary', '')}</description>
            <pubDate>{pub_date}</pubDate>
            <guid>{url}</guid>
        </item>
'''

        rss_content += '''    </channel>
</rss>'''

        rss_path = self.config.build.output_dir / 'rss.xml'
        rss_path.write_text(rss_content, encoding='utf-8')

        return rss_path
