# Development dependencies for Blawger

# Testing framework
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
pytest-asyncio>=0.21.0
pytest-xdist>=3.3.0  # Parallel test execution
pytest-benchmark>=4.0.0  # Performance testing

# Code quality
black>=23.7.0
isort>=5.12.0
mypy>=1.5.0
flake8>=6.0.0
bandit>=1.7.5  # Security linting
safety>=2.3.0  # Dependency vulnerability scanning

# Type checking support
types-PyYAML>=6.0.0
types-toml>=0.10.0
types-python-dateutil>=2.8.0

# Testing utilities
factory-boy>=3.3.0  # Test data factories
freezegun>=1.2.0  # Time mocking
responses>=0.23.0  # HTTP mocking
faker>=19.0.0  # Fake data generation

# Coverage reporting
coverage[toml]>=7.3.0

# Documentation testing
sphinx>=5.0.0
sphinx-rtd-theme>=1.0.0
doc8>=1.1.0  # Documentation linting

# Pre-commit hooks
pre-commit>=3.3.0
